CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_PICK_LIST]
  (
    @C_PICK_LIST_ID BIGINT
  )
RETURNS @T_RESULT TABLE
(
  PICK_LIST_NAME			   VARCHAR(MAX),
  P<PERSON><PERSON>_LIST_ID				   BIGINT,
  PIC<PERSON>_LIST_COMMENT			   NVARCHAR(MAX),
  PICK_LIST_RESOLUTION_TYPE_ID INT
)
AS
  BEGIN
    INSERT INTO @T_RESULT
      (
        PICK_LIST_NAME,
        PICK_LIST_ID,
		PICK_LIST_COMMENT,
		PICK_LIST_RESOLUTION_TYPE_ID
      )
    SELECT
      PL.Name,
      PL.Id,
	  PL.Comment,
	  PL.ResolutionTypeId
    FROM dbo.F_PickList PL WITH (NOLOCK )
    WHERE PL.Id = @C_PICK_LIST_ID

    RETURN
  END
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE FUNCTION [dbo].[fn_float_GET_ITEM_QUANTITY_LOCKED]
(
	@ITEM_ID BIGINT
)
RETURNS FLOAT
AS
BEGIN
	
	DECLARE @ITEM_LOT_VALUE INT = (SELECT TOP(1) ITEM_LOT_VALUE FROM dbo.F_ITEM_LOT WHERE ITEM_ID = @ITEM_ID)

	IF @ITEM_LOT_VALUE IS NOT NULL
	BEGIN
		
		IF @ITEM_LOT_VALUE = 0
			RETURN 0.0

		RETURN (
			SELECT
				ISNULL(SUM(FSOI_U.ITEM_QUANTITY),0) as ITEM_QUANTITY
			FROM  F_SALES_ORDER_ITEM				FSOI_U	WITH(NOLOCK)
			INNER JOIN [dbo].[F_SALES_ORDER]		SO  WITH (NOLOCK)
				ON FSOI_U.SALES_ORDER_ID = SO.SALES_ORDER_ID
				AND FSOI_U.ITEM_INVENTORY_ID IS NULL
				AND SO.[IS_QUOTE] = 0
				AND SO.STATUS_ID NOT IN(2)
			WHERE FSOI_U.ITEM_ID= @ITEM_ID
		) / @ITEM_LOT_VALUE
	END

	RETURN (
		SELECT
			SUM(ITEM_QUANTITY)
		FROM (
			SELECT
				ISNULL(SUM(FSOI_U.ITEM_QUANTITY),0) as ITEM_QUANTITY
			FROM F_SALES_ORDER_ITEM				FSOI_U	WITH(NOLOCK)	
			INNER JOIN [dbo].[F_SALES_ORDER]	SO		WITH(NOLOCK)
				ON FSOI_U.SALES_ORDER_ID = SO.SALES_ORDER_ID
				AND FSOI_U.ITEM_INVENTORY_ID IS NULL
				AND SO.IS_QUOTE = 0
				AND SO.STATUS_ID NOT IN(2)
			WHERE FSOI_U.ITEM_ID = @ITEM_ID
			-- bundle
			UNION ALL
			SELECT
				ISNULL(SUM(FSOI_U.ITEM_QUANTITY),0) as ITEM_QUANTITY
			FROM  F_SALES_ORDER_ITEM				FSOI_U	WITH(NOLOCK)
			INNER JOIN [dbo].[F_SALES_ORDER]		SO		WITH(NOLOCK)
				ON FSOI_U.SALES_ORDER_ID = SO.SALES_ORDER_ID
				AND FSOI_U.ITEM_INVENTORY_ID IS NULL
				AND SO.IS_QUOTE = 0
				AND SO.STATUS_ID NOT IN(2)
			INNER JOIN dbo.F_ITEM_LOT				IL		WITH(NOLOCK)
				ON IL.ITEM_ID = FSOI_U.ITEM_ID
			WHERE IL.ITEM_ID_MASTER = @ITEM_ID
		) T
	)
END
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT DISTINCT [ITEM_LIST_STATUS_CD] FROM [TBS_WMI].[dbo].[S_ITEM_LIST]
-- SELECT * FROM IQRESELLER.TBS_WMI.dbo.S_ITEM_LIST
-- SELECT dbo.fn_decimal_F_ITEM_LIST_COMMON_CATEGORY('EBAY_API', 'SL7EM')
-- SELECT dbo.fn_decimal_F_ITEM_LIST_COMMON_CATEGORY('ALL', 'SL7EM')
CREATE FUNCTION [dbo].[fn_varchar_F_ITEM_LIST_COMMON_CATEGORY]
(
	@C_SOURCE_SYS_CD varchar(50) = 'ALL'
	,@ITEM_NUMBER nvarchar(256)
)
RETURNS varchar(150)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @returnVal varchar(150)
	DECLARE @ITEM_ID bigint 
	SELECT @ITEM_ID = dbo.fn_bigint_F_ITEM_ID(@ITEM_NUMBER)

	SELECT TOP 1
		@returnVal = filco.ITEM_LIST_COMMON_CATEGORY
	FROM
		F_ITEM_LIST fil
		INNER JOIN C_SOURCE_SYS css 
			ON fil.SOURCE_SYS_ID = css.SOURCE_SYS_ID
		INNER JOIN F_ITEM_LIST_CATEGORY_OPTIONS filco 
			ON fil.ITEM_MASTER_ID = filco.ITEM_MASTER_ID
	WHERE
		fil.ITEM_MASTER_ID = @ITEM_ID
		AND (css.SOURCE_SYS_CD = @C_SOURCE_SYS_CD OR @C_SOURCE_SYS_CD = 'ALL')
	ORDER BY
		ITEM_LIST_COMMON_CATEGORY_COUNT DESC

	-- Return the result of the function
	RETURN @returnVal

END
GO


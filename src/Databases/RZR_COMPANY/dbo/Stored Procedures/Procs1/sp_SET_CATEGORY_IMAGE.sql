-- =============================================
-- Author:		<VERONICA DREBEZOVA>
-- Create date: <02/25/2014>
-- Description:	<>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CATEGORY_IMAGE]
	@CategoryId				BIGINT,	
	@FileName				NVARCHAR(256),	
	@BaseByteCount			BIGINT	
AS
BEGIN	

	DECLARE
		 @spName				nvarchar(250)	= isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid)
		,@utcNow				datetime		= getutcdate()

	INSERT INTO [dbo].[F_CATEGORY_IMAGE]
	(
		[CATEGORY_ID],		
		[NAME],	
		[BASE_BYTE_COUNT],	
		POSITION_INDEX,		
		INSERTED_BY,
		INSERTED_DT
	)
	VALUES
	(
		@CategoryId,	
		@FileName,	
		@BaseByteCount,		
		(ISNULL((SELECT MAX(POSITION_INDEX) FROM [F_CATEGORY_IMAGE] with (nolock) WHERE [CATEGORY_ID] = @CategoryId ), -1) + 1),	
		@spName,
		@utcNow
	)

	SELECT  SCOPE_IDENTITY() AS Id
	
			
END
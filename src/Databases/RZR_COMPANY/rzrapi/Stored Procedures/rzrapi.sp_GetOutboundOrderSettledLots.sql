/*
	entity model that takes the result:
		\razor.api\src\Razor.Api.Entities\Domain\Lot.cs
	
	If you change the set of fields,
	don't forget to do the same in SPs that have the same comment
*/
create PROCEDURE [rzrapi].[sp_GetOutboundOrderSettledLots]
	@RecyclingOrderIds	[dbo].[bigint_ID_ARRAY] readonly,
	@Limit				int				= 20,
	@Offset				int				= 0,
	@SearchPhrase		VARCHAR(max)	= N'',
	@SortOrder			INT				= 0,
	@SortDirect			INT				= 0,
	@IsDebug			bit				= 0
AS
BEGIN
	SET @SearchPhrase = nullif(ltrim('%' + @SearchPhrase + '%'), '%%');

	DECLARE
		@filterCondition varchar(max) = N' (@SearchPhrase is null or [AutoName] like @SearchPhrase)';

	DECLARE @query nvarchar(max) = '
	declare
		@utcNow datetime = getutcdate(),
		@isAllOrders		bit = iif(exists(select 1 from @RecyclingOrderIds), 0, 1)


	;WITH m_data AS (
		SELECT
			FROI.RECYCLING_ORDER_ITEM_ID				AS Id,
			FROI.RECYCLING_ORDER_ID						AS RecyclingOrderId,
			FROIN.AUTO_NAME								AS RecyclingOrderAutoName,
			FROINT.RECYCLING_ORDER_ID					AS TransferRecyclingOrderId,
			FROINT.AUTO_NAME							AS TransferRecyclingOrderAutoName,
			FROI.OUTBOUND_ORDER_ID						AS OutboundOrderId,
			FROOT.[AUTO_NAME]							AS OutboundOrderAutoName,
			FROOUTT.RECYCLING_ORDER_ID					AS TransferOutboundOrderId,
			FROOUTTO.AUTO_NAME							AS TransferOutboundOrderAutoName,
			FROI.RECYCLING_ITEM_MASTER_ID				AS CommodityId,
			FRIM.RECYCLING_ITEM_MASTER_NAME				AS CommodityName,
			FROI.LOT_AUTO_NAME							AS AutoName,
			FROI.ShortLotAutoName						AS ShortAutoName,
			FROI.ITEM_COUNT								AS ItemCount,
			FROI.ITEM_PRICE								AS ItemPrice,
			FROI.ITEM_PRICE_OUTBOUND					AS ItemPriceOutbound,
			FROI.PRICE_TYPE_ID							AS PriceTypeId,
			FROI.PRICE_TYPE_ID_OUTBOUND					AS PriceTypeIdOutbound,
			FROI.WEIGHT_RECEIVED						AS WeightReceived,
			FROI.WEIGHT_TARE							AS WeightTare,
			FROI.PACKAGING_TYPE_ID						AS PackagingTypeId,
			DRPT.PACKAGING_TYPE_DESC					AS PackagingTypeName,
			FROI.WORKFLOW_STEP_ID						AS WorkflowStepId,
			CRWT.WORKFLOW_TYPE_DESC						AS WorkflowStepName,
			FROI.LOCATION_ID							AS LocationId,
			dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FROI.LOCATION_ID)	AS LocationName,
			WH.WAREHOUSE_ID								AS WarehouseId,
		    WH.WAREHOUSE_CD								AS WarehouseName,
			C.CUSTOMER_ID								AS CustomerId,
		    C.CUSTOMER_NAME								AS CustomerName,
			DATEDIFF(hh, FROI.INSERTED_DT, @utcNow)		AS LotAge,
			FROI.NOTES									AS Notes,
			FROI.PRICE_CHANGE_COMMENT					AS PriceChangeComment,
			FROI.PARENT_ID								AS ParentId,
			FROI.PARENTS_KEY							AS ParentsKey,
			FROI.WEIGHT_REMAIN							AS WeightRemain,
			FROI.WEIGHT_LOOSE_LOAD						AS WeightLooseLoad,
			FROI.EXPORTED_DT							AS ExportedDate,
			FROI.CONSUMED_IN_WAREHOUSE_ID				AS ConsumedWarehouseId,
			FROI.CONSUMED_DT							AS ConsumedDate,
			FROI.IS_GET_AFTER_SETTLE					AS IsGetAfterSettle,
			FROI.REFERENCE								AS Reference,
			
			IIF(EXISTS(SELECT TOP(1) 1
				FROM [dbo].[F_RECYCLING_ORDER_ITEM_MERGE] WITH(NOLOCK)
				WHERE RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID),
				1, 0)									AS IsMerged,'
	DECLARE @query1 nvarchar(max) = '
			FROI.MERGE_DT								AS MergeDate,
			FROI.IS_MERGED								AS IsMergePrimary,
			FROI.RECYCLING_ORDER_ITEM_MERGED_ID			AS MergedIntoLotId,
			FROI.[INSERTED_DT]							AS CreatedDate,
			FROI.UPDATED_DT								AS UpdatedDate,
			json_query([recycling].[fn_nvarchar_json_GetLotImages](FROI.RECYCLING_ORDER_ITEM_ID))	AS Images,
			json_query([recycling].[fn_nvarchar_json_OrderItemTags](FROI.RECYCLING_ORDER_ID, FROI.RECYCLING_ORDER_ITEM_ID))	AS Tags,
			FROI.IS_AUDIT_COMPLETE						AS IsAuditComplete,
			IIF(FROIME.RECYCLING_ORDER_ITEM_ID is not null, 1, 0)	AS IsMainInnerLotInMerge,
		
			IIF(exists(select top(1) 1
					from recycling.F_Asset a with(nolock)
					where a.RecyclingOrderItemId = FROI.RECYCLING_ORDER_ITEM_ID), 1, 0)			AS IsAudited,

			IIF(exists(select 1
					from dbo.F_RECYCLING_ORDER_ITEM s with(nolock)
					where s.PARENT_ID = FROI.RECYCLING_ORDER_ITEM_ID
						and s.IS_DELETED = 0), 1, 0)		AS IsSorted,
			FROIN.IsFromConsumable							as isFromConsumable,
			FROOT.SHIPPED_DT								AS OutboundOrderShippedDate,
			FROI.USE_FOR_PRICE_CALCULATION					AS IsSelectedInOutboundOrderSettlement,
			FSP.Id                                          as StateProgramId,
            FSP.Name                                        as StateProgramName,
            FSP.Description                                 as StateProgramDescription,
            FSP.IsInactive                                  as StateProgramIsInactive
		FROM dbo.F_RECYCLING_ORDER_ITEM					FROI	WITH(NOLOCK)
		INNER JOIN [dbo].[F_RECYCLING_ORDER]			FRO		WITH(NOLOCK)
			ON FRO.[RECYCLING_ORDER_ID] = FROI.[RECYCLING_ORDER_ID]
		INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND		FROIN	WITH(NOLOCK)
			ON FROIN.RECYCLING_ORDER_ID = FROI.RECYCLING_ORDER_ID
		INNER JOIN [dbo].[F_CUSTOMER]					C		WITH(NOLOCK)
			ON FRO.[CUSTOMER_ID] = C.[CUSTOMER_ID]
		INNER JOIN dbo.F_RECYCLING_ITEM_MASTER			FRIM	WITH(NOLOCK)
			ON FRIM.RECYCLING_ITEM_MASTER_ID = FROI.RECYCLING_ITEM_MASTER_ID
		INNER JOIN dbo.D_RECYCLING_PACKAGING_TYPE		DRPT	WITH(NOLOCK)
			ON DRPT.RECYCLING_PACKAGING_TYPE_ID = FROI.PACKAGING_TYPE_ID
		INNER JOIN dbo.C_RECYCLING_WORKFLOW_TYPE		CRWT	WITH(NOLOCK)
			ON CRWT.WORKFLOW_TYPE_ID = FROI.WORKFLOW_STEP_ID
		LEFT JOIN [dbo].[F_LOCATION]					LOC		WITH(NOLOCK)
			ON LOC.[LOCATION_ID] = FROI.[LOCATION_ID]		
		LEFT JOIN [dbo].[F_RECYCLING_ORDER]				OO		WITH(NOLOCK)
			ON OO.[RECYCLING_ORDER_ID] = FROI.[OUTBOUND_ORDER_ID]
			AND OO.[RECYCLING_ORDER_STATUS_ID] != 4
		LEFT JOIN [dbo].[F_RECYCLING_ORDER_OUTBOUND]	FROOT	WITH(NOLOCK)
			ON FROOT.[RECYCLING_ORDER_ID] = OO.[RECYCLING_ORDER_ID]

		LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM_TRANSFER	ROIT	WITH(NOLOCK)
			ON [dbo].[fn_bigint_GET_RECYCLING_ORDER_ITEM_TRANSFER](FROI.RECYCLING_ORDER_ITEM_ID) = ROIT.RECYCLING_ORDER_ITEM_TRANSFER_ID
		LEFT JOIN [dbo].[F_RECYCLING_ORDER]				FROOUTT WITH(NOLOCK)
			ON FROOUTT.RECYCLING_ORDER_ID = ROIT.OUTBOUND_ORDER_ID
			AND FROOUTT.RECYCLING_ORDER_STATUS_ID != 4
		LEFT JOIN [dbo].F_RECYCLING_ORDER_OUTBOUND		FROOUTTO WITH(NOLOCK)
			ON FROOUTTO.RECYCLING_ORDER_ID = FROOUTT.RECYCLING_ORDER_ID
		LEFT JOIN [dbo].[F_RECYCLING_ORDER_INBOUND]		FROINT	WITH(NOLOCK)
			ON FROINT.[RECYCLING_ORDER_ID] = ROIT.[INBOUND_ORDER_ID]
		LEFT JOIN [dbo].[D_WAREHOUSE]					WH		WITH(NOLOCK)
			ON WH.[WAREHOUSE_ID] = COALESCE(LOC.[WAREHOUSE_ID], FRO.[WAREHOUSE_ID])
		LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM			FROIME with(nolock)
			ON FROIME.MAIN_INNER_LOT_IN_MERGE_ID = FROI.RECYCLING_ORDER_ITEM_ID
	    left join [recycling].[F_StateProgram] FSP with(nolock)
		    on FROI.[StateProgramId] = FSP.[Id]

		WHERE	FROI.OUTBOUND_ORDER_ID is not null
				AND FROI.IS_DELETED = 0
				AND FROI.USE_FOR_PRICE_CALCULATION = 1
				AND OO.RECYCLING_ORDER_STATUS_ID = 5 -- Settlement Complete
				AND (@isAllOrders = 1 OR FROI.OUTBOUND_ORDER_ID IN (select ID from @RecyclingOrderIds) )

	)
	SELECT TOP(1)
		COUNT(Id)	AS Id,
		cast(null as bigint)			AS RecyclingOrderId,
		cast(null as VARCHAR (250))		AS RecyclingOrderAutoName,
		NULL		AS TransferRecyclingOrderId,
		NULL		AS TransferRecyclingOrderAutoName,
		NULL		AS OutboundOrderId,
		NULL		AS OutboundOrderAutoName,
		NULL		AS TransferOutboundOrderId,
		NULL		AS TransferOutboundOrderAutoName,
		NULL		AS CommodityId,
		NULL		AS CommodityName,
		NULL		AS AutoName,
		NULL		AS ShortAutoName,
		NULL		AS ItemCount,
		NULL		AS ItemPrice,
		NULL		AS ItemPriceOutbound,
		NULL		AS PriceTypeId,
		NULL		AS PriceTypeIdOutbound,
		NULL		AS WeightReceived,
		NULL		AS WeightTare,
		NULL		AS PackagingTypeId,
		NULL		AS PackagingTypeName,
		NULL		AS WorkflowStepId,
		NULL		AS WorkflowStepName,
		NULL		AS LocationId,
		NULL		AS LocationName,
		NULL		AS WarehouseId,
		NULL		AS WarehouseName,
		NULL		AS CustomerId,
		NULL		AS CustomerName,
		NULL		AS LotAge,
		NULL		AS Notes,
		NULL		AS PriceChangeComment,
		NULL		AS ParentId,
		NULL		AS ParentsKey,
		NULL		AS WeightRemain,
		NULL		AS WeightLooseLoad,
		NULL		AS ExportedDate,
		NULL		AS ConsumedWarehouseId,
		NULL		AS ConsumedDate,
		NULL		AS IsGetAfterSettle,
		NULL		AS Reference,
		NULL		AS IsMerged,
		NULL		AS MergeDate,
		NULL		AS IsMergePrimary,
		NULL		AS MergedIntoLotId,
		NULL		AS CreatedDate,
		NULL		AS UpdatedDate,
		NULL		AS Images,
		NULL		AS Tags,
		NULL		AS IsAuditComplete,
		NULL		AS IsMainInnerLotInMerge,
		NULL		AS IsAudited,
		NULL		AS IsSorted,
		NULL		AS OutboundOrderShippedDate,
		NULL		AS IsSelectedInOutboundOrderSettlement,
		NULL		AS StateProgramId,
		NULL		AS StateProgramName,
		NULL		AS StateProgramDescription,
		NULL		AS StateProgramIsInactive
	FROM m_data	m
	WHERE ' + @filterCondition + N'
	UNION ALL
	SELECT
		t.Id,
		iif(t.isFromConsumable = 1, null, t.RecyclingOrderId)       as RecyclingOrderId,
		iif(t.isFromConsumable = 1, null, t.RecyclingOrderAutoName) as RecyclingOrderAutoName,
		t.TransferRecyclingOrderId,
		t.TransferRecyclingOrderAutoName,
		t.OutboundOrderId,
		t.OutboundOrderAutoName,
		t.TransferOutboundOrderId,
		t.TransferOutboundOrderAutoName,
		t.CommodityId,
		t.CommodityName,
		t.AutoName,
		t.ShortAutoName,
		t.ItemCount,
		t.ItemPrice,
		t.ItemPriceOutbound,
		t.PriceTypeId,
		t.PriceTypeIdOutbound,
		t.WeightReceived,
		t.WeightTare,
		t.PackagingTypeId,
		t.PackagingTypeName,
		t.WorkflowStepId,
		t.WorkflowStepName,
		t.LocationId,
		t.LocationName,
		t.WarehouseId,
		t.WarehouseName,
		t.CustomerId,
		t.CustomerName,
		t.LotAge,
		t.Notes,
		t.PriceChangeComment,
		t.ParentId,
		t.ParentsKey,
		t.WeightRemain,
		t.WeightLooseLoad,
		t.ExportedDate,
		t.ConsumedWarehouseId,
		t.ConsumedDate,
		t.IsGetAfterSettle,
		t.Reference,
		t.IsMerged,
		t.MergeDate,
		t.IsMergePrimary,
		t.MergedIntoLotId,
		t.CreatedDate,
		t.UpdatedDate,
		t.Images,
		t.Tags,
		t.IsAuditComplete,
		t.IsMainInnerLotInMerge,
		t.IsAudited,
		t.IsSorted,
		t.OutboundOrderShippedDate,
		t.IsSelectedInOutboundOrderSettlement,
		t.StateProgramId,
		t.StateProgramName,
		t.StateProgramDescription,
		t.StateProgramIsInactive
	FROM (
		SELECT
			m.*
		FROM m_data	m
		WHERE ' + @filterCondition + N'
		ORDER BY
		case when @SortOrder = 0 AND @SortDirect = 0 then m.AutoName end asc,
			case when @SortOrder = 0 AND @SortDirect = 1 then m.AutoName end desc,
			case when @SortOrder = 1 AND @SortDirect = 0 then m.CreatedDate end asc,
			case when @SortOrder = 1 AND @SortDirect = 1 then m.CreatedDate end desc,
			case when @SortOrder = 2 AND @SortDirect = 0 then m.UpdatedDate end asc,
			case when @SortOrder = 2 AND @SortDirect = 1 then m.UpdatedDate end desc,
			case when @SortOrder = 3 AND @SortDirect = 0 then m.WeightReceived end asc,
			case when @SortOrder = 3 AND @SortDirect = 1 then m.WeightReceived end desc,
			case when @SortOrder = 4 AND @SortDirect = 0 then m.CommodityName end asc,
			case when @SortOrder = 4 AND @SortDirect = 1 then m.CommodityName end desc,
			case when @SortOrder = 5 AND @SortDirect = 0 then m.ExportedDate end asc,
			case when @SortOrder = 5 AND @SortDirect = 1 then m.ExportedDate end desc
		OFFSET (@Offset) ROWS
		FETCH NEXT (@Limit) ROWS ONLY
	) t
	'

	select @query = @query + @query1
	IF (@IsDebug = 1) BEGIN
		PRINT(CAST(@query AS NTEXT))
	END
	
    EXEC sp_executesql @query, N'
		@RecyclingOrderIds	[dbo].[bigint_ID_ARRAY] readonly,
		@SearchPhrase		varchar(max),
		@Offset				int,
		@Limit				int,
		@SortOrder			int,
		@SortDirect			int',
		@RecyclingOrderIds	= @RecyclingOrderIds,
		@SearchPhrase		= @SearchPhrase,
		@Offset				= @Offset,
		@Limit				= @Limit,
		@SortOrder			= @SortOrder,
		@SortDirect			= @SortDirect;


END


-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 04/03/2014
-- Description:	get permissions for role
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ROLE_PERMISSIONS]
	@ROLE_ID	 BIGINT,
	@CATEGORY_ID BIGINT
AS
BEGIN
	IF (@CATEGORY_ID = 0)
	BEGIN
		--select * from D_PERMISSION_CATEGORY
		SELECT
			P.PERMISSION_CATEGORY_ENTITY_ID,
			P.PERMISSION_CATEGORY_ACTION_ID
		FROM F_PERMISSION_ROLE_CATEGORY_ACTIONS	 P	WITH(NOLOCK)
		INNER JOIN D_PERMISSION_CATEGORY_ENTITY  E	WITH(NOLOCK)
		  ON P.PERMISSION_CATEGORY_ENTITY_ID = E.PERMISSION_CATEGORY_ENTITY_ID
		INNER JOIN C_PERMISSION_ENTITY_TYPE      ET WITH(NOLOCK)
		  ON  E.ENTITY_TYPE_ID = ET.ENTITY_TYPE_ID
		  AND ET.ENTITY_TYPE_ID IN (1,2, 4)
		WHERE p.ROLE_ID = @ROLE_ID

	END
	ELSE IF (@CATEGORY_ID = 99)
	BEGIN
		/*		
		SELECT * FROM C_ENTITY_ACTION
		SELECT * FROM C_ENTITY_TYPE
		SELECT 
			CET.ENTITY_CD,
			CEA.ENTITY_ACTION_CD,
			CEA.IS_MAIN_ENTITY_ACTION,
			FPREA.*
		FROM F_PERMISSION_ROLE_ENTITY_ACTIONS		FPREA	WITH(NOLOCK)
		INNER JOIN C_ENTITY_ACTION					CEA	WITH(NOLOCK)
			ON CEA.ENTITY_ACTION_ID = FPREA.ENTITY_ACTION_ID
		INNER JOIN C_ENTITY_TYPE					CET	WITH(NOLOCK)
			ON CET.ENTITY_TYPE_ID	= CEA.ENTITY_TYPE_ID
		*/
		SELECT
			ENTITY_ACTION_ID AS PERMISSION_CATEGORY_ENTITY_ID,
			1				 AS PERMISSION_CATEGORY_ACTION_ID
		FROM F_PERMISSION_ROLE_ENTITY_ACTIONS
		WHERE ROLE_ID = @ROLE_ID

	END
	ELSE BEGIN

		SELECT
			PERMISSION_CATEGORY_ENTITY_ID,
			PERMISSION_CATEGORY_ACTION_ID
		FROM F_PERMISSION_ROLE_CATEGORY_ACTIONS
		WHERE PERMISSION_CATEGORY_ID =  @CATEGORY_ID
		  AND ROLE_ID = @ROLE_ID

	END
END
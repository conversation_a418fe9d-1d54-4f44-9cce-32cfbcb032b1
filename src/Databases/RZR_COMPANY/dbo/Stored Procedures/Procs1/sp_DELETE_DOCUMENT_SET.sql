-- =============================================
-- Author:		R.SAPRYKIN
-- Create date: 08/01/2014
-- Description:	delete document set
-- =============================================
CREATE PROCEDURE sp_DELETE_DOCUMENT_SET
	@DOCUMENT_SETS XML
AS
BEGIN

DECLARE @idoc INT

	EXEC sp_xml_preparedocument @idoc OUTPUT, @DOCUMENT_SETS
		
	DECLARE @tDOCUMENT_SETS TABLE (ID BIGINT)
	INSERT INTO @tDOCUMENT_SETS (
		ID
	)
	SELECT 
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( 
		ID BIGINT 
		) AS x		         
	EXEC sp_xml_removedocument @idoc

	DELETE
	FROM F_DOCUMENT_SET
	WHERE DOCUMENT_SET_ID IN (SELECT T.ID FROM @tDOCUMENT_SETS AS T)
END
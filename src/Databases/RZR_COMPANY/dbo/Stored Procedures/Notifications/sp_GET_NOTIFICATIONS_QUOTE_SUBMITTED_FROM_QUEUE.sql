CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_QUOTE_SUBMITTED_FROM_QUEUE]
	@C_COUNT INT = 40
AS
BEGIN

	DECLARE @NOTIFICATION_TYPE_ID		BIGINT = 12;
	DECLARE @IS_ALLOWED					BIT = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID);

	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE [dbo].[F_NOTIFICATION_QUEUE] WITH(ROWLOCK) SET
			[IS_INACTIVE] = 1,
			[UPDATED_BY] = N'sp_GET_NOTIFICATIONS_QUOTE_SUBMITTED_FROM_QUEUE',
			[UPDATED_DT] = GETUTCDATE()
		WHERE [IS_INACTIVE] = 0 AND [NOTIFICATION_TYPE_ID] = @NOTIFICATION_TYPE_ID;
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP(@C_COUNT)
		NQ.NotificationQueueId		AS [NOTIFICATION_QUEUE_ID],
		DC.*,
		DU.*,
		SO.*,
		[dbo].[fn_str_GET_NOTIFICATION_EMAIL_LIST](@NOTIFICATION_TYPE_ID, 
		C.[REP_ID], 
		DC.[CUSTOMER_PRIMARY_CONTACT_ID], 
		SO.[CustomerContactId], 
		NQ.NotificationSetId,
		NQ.CustomerId) AS EMAILS
	FROM @tNotifyItemWithNotifySet AS NQ
	CROSS APPLY [dbo].[tvf_GET_NOTIFICATION_QUEUE_PARAMS](NQ.NotificationQueueId) AS QP
	INNER JOIN [dbo].[F_CUSTOMER] AS C WITH(NOLOCK)
		ON C.[CUSTOMER_ID] = QP.[CUSTOMER_ID]
	CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER](QP.[CUSTOMER_ID], @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId) AS DC
	CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER](C.[REP_ID]) AS DU
	CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_SCHEDULED_ORDER](QP.[SCHEDULED_ORDER_ID]) AS SO
	WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND QP.[CUSTOMER_ID] IS NOT NULL;

END
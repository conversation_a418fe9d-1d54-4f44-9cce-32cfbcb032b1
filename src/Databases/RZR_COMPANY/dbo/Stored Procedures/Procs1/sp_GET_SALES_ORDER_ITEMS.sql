CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ITEMS]
	@ORDER_ID			BIGINT,
	@ORDER_COLUMN_NAME	NVARCHAR(150)	= N'SalesOrderItemId',
	@ORDER_DIRECTION	NVARCHAR(20)	= N'DESC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		NVARCHAR(MAX)	= NULL,
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN

	SET @FILTER_WHERE = ISNULL(N'WHERE (' + NULLIF(NULLIF(LTRIM(RTRIM(@FILTER_WHERE)), N''), N'NULL') + N')', N'');
	SET @ITEMS_PER_PAGE = IIF(@ITEMS_PER_PAGE = 0, 10000000, @ITEMS_PER_PAGE);
	DECLARE @isLocationAndCustomerGrouping BIT = ISNULL((SELECT TOP(1) USS.[IS_EXTRA_COLUMNS_FOR_UNALLOCATED] FROM [dbo].[U_SYSTEM_SETTINGS] USS WITH(NOLOCK)), 0);
	DECLARE @isRedeploymentSalesOrder BIT = [dbo].[fn_bit_IsRedeploymentSalesOrder](@ORDER_ID);

	DECLARE @query NVARCHAR (MAX) = N'
	DECLARE @INVENTORY_CAPABILITY_TYPE_ID BIGINT;

	SELECT @INVENTORY_CAPABILITY_TYPE_ID = [INVENTORY_CAPABILITY_TYPE_ID]
	FROM [dbo].[C_INVENTORY_CAPABILITY_TYPE] WITH (NOLOCK)
	WHERE [INVENTORY_CAPABILITY_VALUE] IN (''REVISION'', ''REV'');

	CREATE TABLE #CTE_AVAILABLE_QTY (
		[PRODUCT_MASTER_ID]				BIGINT,
		[ITEM_MASTER_ID]				BIGINT,
		[CONDITION_ID]					BIGINT,
		[ITEM_ID]						BIGINT,
		[ITEM_ID_MASTER]				BIGINT,
		[ITEM_MASTER_PRODUCT_CODE_ID]	BIGINT,
		[INVENTORY_CAPABILITY_ID]		BIGINT,
		[CUSTOMER_ID]					BIGINT,
		[LOCATION_ID]					BIGINT,
		[ITEM_PRICE]					FLOAT,
		[QUANTITY_AVAILABLE]			BIGINT
	);

	DECLARE @GradeId INT = ISNULL((
	SELECT TOP 1
		[INVENTORY_CAPABILITY_TYPE_ID]
	FROM [dbo].[C_INVENTORY_CAPABILITY_TYPE] WITH (NOLOCK)
	WHERE [INVENTORY_CAPABILITY_VALUE] = ''GRADE''
	AND IS_DELETED = 0), 0);
	';

	SET @query = @query + N'
	INSERT INTO #CTE_AVAILABLE_QTY
	SELECT
		SOI.[PRODUCT_MASTER_ID],
		PM.[ITEM_MASTER_ID],
		SOI.[CONDITION_ID],
		SOI.[ITEM_ID],
		IL.[ITEM_ID_MASTER],
		SOI.[ITEM_MASTER_PRODUCT_CODE_ID],
		SOI.[INVENTORY_CAPABILITY_ID],
		' + IIF(@isLocationAndCustomerGrouping = 1, N'SOI.[CUSTOMER_ID]', N'NULL') + N',
		' + IIF(@isLocationAndCustomerGrouping = 1, N'SOI.[LOCATION_ID]', N'NULL') + N',
		SOI.[ITEM_PRICE],
		NULL
	FROM [dbo].[F_SALES_ORDER_ITEM]					AS SOI	WITH (NOLOCK)
	LEFT JOIN [dbo].[F_ITEM]						AS FI	WITH (NOLOCK)
		ON SOI.[ITEM_ID] = FI.[ITEM_ID]
	LEFT JOIN [dbo].[F_PRODUCT_MASTER]				AS PM	WITH (NOLOCK)
		ON SOI.[PRODUCT_MASTER_ID] = PM.[PRODUCT_MASTER_ID]
	LEFT JOIN [dbo].[F_ITEM_LOT]					AS IL	WITH (NOLOCK)
		ON SOI.[ITEM_ID] = IL.[ITEM_ID]
	WHERE SOI.[ITEM_INVENTORY_ID] IS NULL
		AND SOI.[SALES_ORDER_ID] = @salesOrderId
		AND ISNULL(FI.[ITEM_TYPE_ID], 0) != 3 -- Combo/Kit
	GROUP BY
		SOI.[PRODUCT_MASTER_ID],
		PM.[ITEM_MASTER_ID],
		SOI.[CONDITION_ID],
		SOI.[ITEM_ID],
		IL.[ITEM_ID_MASTER],
		SOI.[ITEM_MASTER_PRODUCT_CODE_ID],
		SOI.[INVENTORY_CAPABILITY_ID],'
		+ IIF(@isLocationAndCustomerGrouping = 1, N'
		SOI.[CUSTOMER_ID],
		SOI.[LOCATION_ID],', N'') + N'
		SOI.[ITEM_PRICE];
	';

	SET @query = @query + N'
	UPDATE CTE SET [QUANTITY_AVAILABLE] = (
		SELECT SUM(FII.[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED])
		FROM [dbo].[F_ITEM_INVENTORY]						AS FII		WITH (NOLOCK)
		LEFT JOIN [dbo].[F_ITEM_INVENTORY_CAPABILITY]		AS IIC		WITH (NOLOCK)
			ON IIC.[INVENTORY_CAPABILITY_TYPE_ID] = @INVENTORY_CAPABILITY_TYPE_ID
			AND IIC.[ITEM_INVENTORY_ID] = FII.[ITEM_INVENTORY_ID]
		LEFT JOIN [dbo].[F_ITEM_INVENTORY_PRODUCT_CODE]		AS FIIPC	WITH (NOLOCK)
			ON FIIPC.[ITEM_INVENTORY_ID] = FII.[ITEM_INVENTORY_ID]'
		+ IIF(@isRedeploymentSalesOrder = 1, N'
		LEFT JOIN [dbo].[vw_RedeploymentInventory]			AS RI		WITH (NOLOCK)
			ON RI.[InventoryId] = FII.[ITEM_INVENTORY_ID]
			AND RI.[IsAvailable] = 1', N'') + N'
		WHERE FII.[IS_DELETED] = 0
			AND FII.[IS_VIRTUAL] = 0
			AND FII.[AUDIT_STATUS_ID] IN (0, 1, 2)
			AND (FII.[ITEM_STATUS_ID] = 1' + IIF(@isRedeploymentSalesOrder = 1, N' OR RI.[InventoryId] IS NOT NULL', N'') + ')
			AND ((CTE.[ITEM_ID] IS NULL AND FII.[ITEM_MASTER_ID] = CTE.[ITEM_MASTER_ID] AND FII.[CONDITION_ID] = CTE.[CONDITION_ID])
				OR (FII.[ITEM_ID] IN (CTE.[ITEM_ID], CTE.[ITEM_ID_MASTER])))
			AND (CTE.[INVENTORY_CAPABILITY_ID] IS NULL OR CTE.[INVENTORY_CAPABILITY_ID] = IIC.[INVENTORY_CAPABILITY_ID])
			AND (CTE.[ITEM_MASTER_PRODUCT_CODE_ID] IS NULL OR CTE.[ITEM_MASTER_PRODUCT_CODE_ID] = FIIPC.[ITEM_MASTER_PRODUCT_CODE_ID])'
			+ IIF(@isLocationAndCustomerGrouping = 1, N'
			AND (CTE.[LOCATION_ID] IS NULL OR FII.[LOCATION_ID] = CTE.[LOCATION_ID])
			AND (CTE.[CUSTOMER_ID] IS NULL OR FII.[CUSTOMER_ID] = CTE.[CUSTOMER_ID])', N'') + N'
	)
	FROM #CTE_AVAILABLE_QTY AS CTE;
	';

	SET @query = @query + N'
	INSERT INTO #CTE_AVAILABLE_QTY
	SELECT
		SOI.[PRODUCT_MASTER_ID],
		NULL,
		SOI.[CONDITION_ID],
		SOI.[ITEM_ID],
		NULL,
		SOI.[ITEM_MASTER_PRODUCT_CODE_ID],
		SOI.[INVENTORY_CAPABILITY_ID],
		' + IIF(@isLocationAndCustomerGrouping = 1, N'SOI.[CUSTOMER_ID]', N'NULL') + N',
		' + IIF(@isLocationAndCustomerGrouping = 1, N'SOI.[LOCATION_ID]', N'NULL') + N',
		SOI.[ITEM_PRICE],
		FI.[QUANTITY_AVAILABLE_FOR_ECOMMERCE]
	FROM [dbo].[F_SALES_ORDER_ITEM]					AS SOI	WITH (NOLOCK)
	LEFT JOIN [dbo].[F_ITEM]						AS FI	WITH (NOLOCK)
		ON SOI.[ITEM_ID] = FI.[ITEM_ID]
	WHERE SOI.[ITEM_INVENTORY_ID] IS NULL
		AND SOI.[SALES_ORDER_ID] = @salesOrderId
		AND FI.[ITEM_TYPE_ID] = 3 -- Combo/Kit
		AND FI.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] > 0;
	';

	SET @query = @query + N'
	DECLARE @taxPc MONEY = 0;
	SELECT @taxPc = IIF(
		EXISTS(
			SELECT TOP(1) 1 FROM [dbo].[F_INVOICE] IV WITH(NOLOCK)
			WHERE IV.[INVOICE_TYPE_ID] = 1 AND IV.[ORDER_ID] = @salesOrderId AND IV.[IS_VOIDED] = 0 AND IV.[PAID_DATE] IS NOT NULL
		),
		FSO.[TAX],
		[dbo].[fn_float_GET_TAX_RATE](FSO.[TAX_ID], 1)
	)
	FROM [dbo].[F_SALES_ORDER] AS FSO WITH (NOLOCK)
	WHERE FSO.[SALES_ORDER_ID] = @salesOrderId;
	';

	SET @query = @query + N'
	;WITH m_data AS (
		SELECT T.*
		FROM (
			SELECT
				SOI.[SALES_ORDER_ITEM_ID]						AS [SalesOrderItemId],
				SOI.[SALES_ORDER_ID]							AS [SalesOrderId],
				PM.[ITEM_MASTER_ID]								AS [MasterItemId],
				M.[ITEM_NUMBER]									AS [MasterItemNumber],
				M.[ITEM_IPN]									AS [IPN],
				M.[ITEM_MPN]									AS [MPN],
				[dbo].[fn_str_GET_SALES_ORDER_ITEM_TITLE](SOI.[SALES_ORDER_ITEM_ID]) AS [MasterItemTitle],
				M.[MANUFACTURER_ID]								AS [MFGId],
				MFG.[MANUFACTURER_CD]							AS [MFGCd],
				PC.[PRODUCT_CODE_ID]							AS [ProductCodeId],
				PC.[PRODUCT_CODE_VALUE]							AS [ProductCodeValue],
				DIC.[INVENTORY_CAPABILITY_ID]					AS [RevisionId],
				DIC.[INVENTORY_CAPABILITY_VALUE]				AS [Revision],
				FL.[WAREHOUSE_ID]								AS [WarehouseId],
				DW.[WAREHOUSE_CD]								AS [WarehouseName],
				SOI.[LOCATION_ID]								AS [LocationId],
				FL.[LOCATION_NAME]								AS [LocationName],
				SOI.[CONDITION_ID]								AS [RealConditionId],
				C.[ITEM_CONDITION_DESC]							AS [RealConditionCd],
				SOI.[ITEM_QUANTITY]								AS [Quantity],
				SOI.[ITEM_QUANTITY_ORDERED]						AS [QuantityOrdered],
				FI.[QUANTITY_AVAILABLE]							AS [Available],
				1												AS [StatusId],
				N''Unallocated''								AS [StatusCd],
				SOI.[ITEM_PRICE]								AS [Price],
				SOI.[ITEM_PRICE]								AS [PriceInitial],
				SOI.[ITEM_PRICE] * SOI.[ITEM_QUANTITY] * isnull(SOI.TaxRate, @TaxPc)	AS [ItemTax],
				SOI.[ITEM_NOTES]								AS [Notes],
				SOI.[ITEM_PRICE] * SOI.[ITEM_QUANTITY]			AS [Amount],
				SOI.[ITEM_ID]									AS [Sku],
				IL.[ITEM_LOT_VALUE]								AS [LotValue],
				SOI.[CUSTOMER_ID]								AS [CustomerId],
				FC.[CUSTOMER_NAME]								AS [CustomerName],
				IIKH.[Id]										AS [ItemInventoryKitHeaderId],
				W.[WAREHOUSE_CD]								AS [ItemWarehouse],
				FISAV.INVENTORY_CAPABILITY_VALUE				AS [GradeLevel],
				SOI.TaxRate                                     AS [TaxRate]';

	SET @query = @query + N'
			FROM [dbo].[F_SALES_ORDER_ITEM]						AS SOI	WITH (NOLOCK)
			LEFT JOIN [dbo].[F_PRODUCT_MASTER]					AS PM	WITH (NOLOCK)
				ON SOI.[PRODUCT_MASTER_ID] = PM.[PRODUCT_MASTER_ID]
			LEFT JOIN [dbo].[F_ITEM_MASTER]						AS M	WITH (NOLOCK)
				ON PM.[ITEM_MASTER_ID] = M.[ITEM_MASTER_ID]
			LEFT JOIN [dbo].[D_MANUFACTURER]					AS MFG	WITH (NOLOCK)
				ON M.[MANUFACTURER_ID] = MFG.[MANUFACTURER_ID]
			LEFT JOIN [dbo].[D_ITEM_CONDITION]					AS C	WITH (NOLOCK)
				ON SOI.[CONDITION_ID] = C.[ITEM_CONDITION_ID]
			LEFT JOIN [dbo].[F_CUSTOMER]						AS FC	WITH (NOLOCK)
				ON FC.[CUSTOMER_ID] = SOI.[CUSTOMER_ID]
			LEFT JOIN [dbo].[F_LOCATION]						AS FL	WITH (NOLOCK)
				ON FL.[LOCATION_ID] = SOI.[LOCATION_ID]
			LEFT JOIN [dbo].[D_WAREHOUSE]						AS DW	WITH (NOLOCK)
				ON DW.[WAREHOUSE_ID] = FL.[WAREHOUSE_ID]
			LEFT JOIN #CTE_AVAILABLE_QTY						AS FI
				ON SOI.[PRODUCT_MASTER_ID] = FI.[PRODUCT_MASTER_ID]
				AND SOI.[CONDITION_ID] = FI.[CONDITION_ID]
				AND ISNULL(SOI.[ITEM_ID], -1) = ISNULL(FI.[ITEM_ID], -1)
				AND ISNULL(SOI.[ITEM_MASTER_PRODUCT_CODE_ID], -1) = ISNULL(FI.[ITEM_MASTER_PRODUCT_CODE_ID], -1)
				AND ISNULL(SOI.[INVENTORY_CAPABILITY_ID], -1) = ISNULL(FI.[INVENTORY_CAPABILITY_ID], -1)
				AND SOI.[ITEM_PRICE] = FI.[ITEM_PRICE]'
				+ IIF(@isLocationAndCustomerGrouping = 1, N'
				AND ISNULL(SOI.[CUSTOMER_ID], -1) = ISNULL(FI.[CUSTOMER_ID], -1)
				AND ISNULL(SOI.[LOCATION_ID], -1) = ISNULL(FI.[LOCATION_ID], -1)', N'') + N'
			LEFT JOIN [dbo].[F_ITEM_LOT]						AS IL	WITH (NOLOCK)
				ON SOI.[ITEM_ID] = IL.[ITEM_ID]
			LEFT JOIN [dbo].[F_ITEM_MASTER_PRODUCT_CODE]		AS IMPC	WITH (NOLOCK)
				ON SOI.[ITEM_MASTER_PRODUCT_CODE_ID] = IMPC.[ITEM_MASTER_PRODUCT_CODE_ID]
			LEFT JOIN [dbo].[F_PRODUCT_CODE]					AS PC	WITH (NOLOCK)
				ON IMPC.[PRODUCT_CODE_ID] = PC.[PRODUCT_CODE_ID]
				AND PC.[PRODUCT_CODE_TYPE_ID] = 1 -- CLEI/HECI
			LEFT JOIN [dbo].[D_INVENTORY_CAPABILITY]			AS DIC	WITH (NOLOCK)
				ON DIC.[INVENTORY_CAPABILITY_ID] = SOI.[INVENTORY_CAPABILITY_ID]
			LEFT JOIN [dbo].[F_ItemInventoryKitHeader]			AS IIKH	WITH (NOLOCK)
				ON SOI.[ITEM_ID] = IIKH.[ItemId]
			LEFT JOIN [dbo].[D_WAREHOUSE]						AS W	WITH (NOLOCK)
				ON SOI.[SkuWarehouseId] = W.[WAREHOUSE_ID]
			LEFT JOIN [dbo].[F_ITEM]							AS FIT	WITH (NOLOCK)
				ON SOI.[ITEM_ID] = FIT.[ITEM_ID]
			LEFT JOIN (
				SELECT TOP 100 PERCENT
					ITEM_MASTER_ID,
					INVENTORY_CAPABILITY_VALUE,
					ITEM_MASTER_SKU_ATTRB_ID
				FROM F_ITEM_SKU_ATTRB_VALUE		WITH(NOLOCK)
				WHERE INVENTORY_CAPABILITY_TYPE_ID = @GradeId
				GROUP BY 
					ITEM_MASTER_ID,
					INVENTORY_CAPABILITY_VALUE,
					ITEM_MASTER_SKU_ATTRB_ID
				ORDER BY
					ITEM_MASTER_ID,
					INVENTORY_CAPABILITY_VALUE,
					ITEM_MASTER_SKU_ATTRB_ID
			) AS FISAV
				ON FISAV.ITEM_MASTER_SKU_ATTRB_ID = FIT.ITEM_MASTER_SKU_ATTRB_ID
			WHERE SOI.[SALES_ORDER_ID] = @salesOrderId
				AND SOI.[ITEM_INVENTORY_ID] IS NULL
				AND SOI.[ITEM_QUANTITY] > 0
		) AS T
		' + @FILTER_WHERE + N'
	)';

	SET @query = @query + N'
	SELECT TOP(1)
		COUNT(1) AS SalesOrderItemId
		,NULL AS SalesOrderId
		,NULL AS MasterItemId
		,NULL AS MasterItemNumber
		,NULL AS IPN
		,NULL AS MPN
		,NULL AS MasterItemTitle
		,NULL AS MFGId
		,NULL AS MFGCd
		,NULL AS ProductCodeId
		,NULL AS ProductCodeValue
		,NULL AS RevisionId
		,NULL AS Revision
		,NULL AS InventoryItemId
		,NULL AS Serial
		,NULL AS UniqueId
		,NULL AS WarehouseId
		,NULL AS WarehouseName
		,NULL AS LocationId
		,NULL AS LocationName
		,NULL AS RealConditionId
		,NULL AS RealConditionCd
		,NULL AS Quantity
		,NULL AS QuantityOrdered
		,NULL AS Available
		,NULL AS StatusId
		,NULL AS StatusCd
		,NULL AS Price
		,NULL AS PriceInitial
		,NULL AS ItemTax
		,NULL as TaxPc
		,NULL AS Notes
		,NULL AS Amount
		,NULL AS Sku
		,NULL AS LotValue
		,NULL AS CustomerId
		,NULL AS CustomerName
		,NULL as ItemInventoryKitHeaderId
		,NULL as ItemWarehouse
		,NULL as Locked
		,NULL AS GradeLevel
	FROM m_data
	UNION ALL
	SELECT
		m.SalesOrderItemId
		,m.SalesOrderId
		,m.MasterItemId
		,m.MasterItemNumber
		,m.IPN
		,m.MPN
		,m.MasterItemTitle
		,m.MFGId
		,m.MFGCd
		,m.ProductCodeId
		,m.ProductCodeValue
		,m.RevisionId
		,m.Revision
		,NULL -- InventoryItemId
		,NULL -- Serial
		,NULL -- UniqueId
		,m.WarehouseId
		,m.WarehouseName
		,m.LocationId
		,m.LocationName
		,m.RealConditionId
		,m.RealConditionCd
		,m.Quantity
		,m.QuantityOrdered
		,m.Available
		,1 -- StatusId
		,N''Unallocated'' -- StatusCd
		,m.Price
		,m.PriceInitial
		,m.ItemTax
		,isnull(m.TaxRate, @TaxPc)
		,m.Notes
		,m.Amount
		,m.Sku
		,m.LotValue
		,m.CustomerId
		,m.CustomerName
		,m.ItemInventoryKitHeaderId
		,m.ItemWarehouse
		,[dbo].[fn_float_GET_ITEM_QUANTITY_LOCKED](m.Sku) -- Locked
		,m.GradeLevel
	FROM (
		SELECT *
		FROM m_data
		ORDER BY '+ @ORDER_COLUMN_NAME + N' ' + @ORDER_DIRECTION + N'
		OFFSET (@PageIndex * @ItemsPerPage) ROWS
		FETCH NEXT (@ItemsPerPage) ROWS ONLY
	) AS M;

	DROP TABLE #CTE_AVAILABLE_QTY;
	';

	IF (@C_IS_DEBUG = 1) PRINT CAST(@query AS NTEXT);

	EXEC sp_executesql @query, N'
		@salesOrderId BIGINT,
		@PageIndex INT,
		@ItemsPerPage INT',
		@salesOrderId = @ORDER_ID,
		@PageIndex = @PAGE_INDEX,
		@ItemsPerPage = @ITEMS_PER_PAGE;

END
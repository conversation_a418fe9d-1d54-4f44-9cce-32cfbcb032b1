CREATE TABLE [dbo].[C_PROCESSING_REQUIREMENTS] (
    [PROCESSING_REQUIREMENTS_ID]   BIGINT        NOT NULL,
    [PROCESSING_REQUIREMENTS_CD]   VARCHAR (250) NOT NULL,
    [PROCESSING_REQUIREMENTS_DESC] VARCHAR (250) NOT NULL,
    [IS_INACTIVE]                  BIT           CONSTRAINT [DF_C_PROCESSING_REQUIREMENTS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                   BIT           CONSTRAINT [DF_C_PROCESSING_REQUIREMENTS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                  VARCHAR (250) NOT NULL,
    [INSERTED_DT]                  DATETIME      NOT NULL,
    [UPDATED_BY]                   VARCHAR (250) NULL,
    [UPDATED_DT]                   DATETIME      NULL,
    [DELETED_BY]                   VARCHAR (250) NULL,
    [DELETED_DT]                   DATETIME      NULL,
    CONSTRAINT [PK_C_PROCESSING_REQUIREMENTS] PRIMARY KEY CLUSTERED ([PROCESSING_REQUIREMENTS_ID] ASC)
);


-- =============================================
-- Author:		
-- Create date: 
-- =============================================
-- SELECT dbo.fn_float_GET_MASTER_PRODUCT_PRICE_AVG(285574)
CREATE FUNCTION [dbo].[fn_float_GET_MASTER_PRODUCT_PURCHASE_PRICE_AVG]
(
	@PRODUCT_MASTER_ID BIGINT
)
RETURNS FLOAT
AS
BEGIN
	DECLARE 
		@PRODUCT_MASTER_TYPE_ID INT,
		@ITEM_MASTER_ID			BIGINT,
		@SERVICE_MASTER_ID		BIGINT,
		@MODULE_MASTER_ID		BIGINT

		
		SELECT
			@PRODUCT_MASTER_TYPE_ID = PRODUCT_MASTER_TYPE_ID,
			@ITEM_MASTER_ID			= ITEM_MASTER_ID,
			@SERVICE_MASTER_ID		= SERVICE_MASTER_ID,
			@MODULE_MASTER_ID		= MODULE_MASTER_ID
		FROM [dbo].[F_PRODUCT_MASTER] WITH (NOLOCK)
		WHERE PRODUCT_MASTER_ID = @PRODUCT_MASTER_ID
		

	RETURN (
			CASE @PRODUCT_MASTER_TYPE_ID
				WHEN 1 THEN (
					SELECT AVG(FII.ITEM_INVENTORY_UNIT_COST_ORIGINAL)
					FROM [dbo].[F_ITEM_INVENTORY] FII WITH (NOLOCK)					
					WHERE FII.IS_DELETED  = 0
					  --AND FII.ITEM_INVENTORY_STATUS_ID IN (1,2,3) -- Unallocated, Allocated, Sold
					  AND FII.ITEM_MASTER_ID = @ITEM_MASTER_ID)
				WHEN 2 THEN 0 --(SELECT SERVICE_TYPE_CD FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] WITH (NOLOCK) WHERE SERVICE_TYPE_ID = @SERVICE_MASTER_ID)
				WHEN 3 THEN 0 --(SELECT RECYCLING_ITEM_MASTER_NAME FROM [dbo].[F_RECYCLING_ITEM_MASTER] WITH (NOLOCK) WHERE RECYCLING_ITEM_MASTER_ID = @MODULE_MASTER_ID)
				ELSE NULL
			END
		 ) 
END
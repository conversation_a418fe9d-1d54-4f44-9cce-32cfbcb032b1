CREATE TABLE [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] (
    [INVENTORY_ATTRIBUTE_TYPE_ID]   INT            IDENTITY (1, 1) NOT NULL,
    [INVENTORY_ATTRIBUTE_NAME]      NVARCHAR (150) CONSTRAINT [DF_C_INVENTORY_ATTRIBUTE_TYPE_INVENTORY_ATTRIBUTE_NAME] DEFAULT ((0)) NOT NULL,
    [IS_FILE_UPLOAD_ENABLED]        BIT            CONSTRAINT [DF_C_INVENTORY_ATTRIBUTE_TYPE_IS_FILE_UPLOAD_ENABLED] DEFAULT ((0)) NOT NULL,
    [IS_FILE_UPLOAD_REQUIRED]       BIT            CONSTRAINT [DF_C_INVENTORY_ATTRIBUTE_TYPE_IS_FILE_UPLOAD_REQUIRED] DEFAULT ((0)) NOT NULL,
    [GLOBAL_ATTRIBUTE_SET_ID]       INT            NULL,
    [IS_INACTIVE]                   BIT            CONSTRAINT [DF_C_INVENTORY_ATTRIBUTE_TYPE_IS_ACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT            CONSTRAINT [DF_C_INVENTORY_ATTRIBUTE_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   VARCHAR (150)  NOT NULL,
    [INSERTED_DT]                   DATETIME       NOT NULL,
    [UPDATED_BY]                    VARCHAR (150)  NULL,
    [UPDATED_DT]                    DATETIME       NULL,
    [DELETED_BY]                    VARCHAR (150)  NULL,
    [DELETED_DT]                    DATETIME       NULL,
    [IS_DATA_DESTRUCTION_AVAILABLE] BIT            DEFAULT ((0)) NOT NULL,
    [IsErasureRequired]             BIT            DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_C_INVENTORY_ATTRIBUTE_TYPE] PRIMARY KEY CLUSTERED ([INVENTORY_ATTRIBUTE_TYPE_ID] ASC)
);










GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of device types such as desktop, laptop etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_INVENTORY_ATTRIBUTE_TYPE';


GO

GO


GO

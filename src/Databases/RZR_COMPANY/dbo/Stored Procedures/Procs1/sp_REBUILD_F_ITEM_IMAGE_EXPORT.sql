-- SELECT * FROM F_ITEM_IMAGE_EXPORT
-- EXEC [sp_REBUILD_F_ITEM_IMAGE_EXPORT]
CREATE PROCEDURE [dbo].[sp_REBUILD_F_ITEM_IMAGE_EXPORT]
(@C_BUILD_TYPE_CD varchar(50) = 'DEFAULT'
	,@C_BUILD_LOCATION_TYPE_CD varchar(50) = 'DEFAULT'
	,@C_IS_FULL_REBUILD char(1) = 'N'
	,@C_IS_PARTIAL_REBUILD char(1) = 'N'
	,@C_START_DATE datetime = '1900-01-01'
	,@C_END_DATE datetime = '2100-01-01'
	,@C_JOB_QUEUE_BUILD_ID bigint = 0
	,@C_JOB_QUEUE_D bigint = 0
	,@C_BUILD_ID int = 0
)
AS
BEGIN
	DECLARE 
		@alias_gallery			  NVARCHAR(50) = N'media_gallery'
		,@alias_position_gallerey NVARCHAR(50) = N'media_gallery_position'
		,@alias_image_base		  NVARCHAR(50) = N'base_image'
		,@alias_image_small		  NVARCHAR(50) = N'small_image'
		,@alias_image_thumbnail	  NVARCHAR(50) = N'thumbnail'
	
		,@alias_label_gallerey	  NVARCHAR(50) = N'media_gallery_label'
		,@alias_label_base		  NVARCHAR(50) = N'base_image_label'
		,@alias_label_small		  NVARCHAR(50) = N'small_image_label'
		,@alias_label_thumbnail   NVARCHAR(50) = N'thumbnail_image_label'
		
		,@alias_is_featured		  NVARCHAR(50) = N'item_is_featured'
		,@alias_is_deal_off       NVARCHAR(50) = N'item_is_deal_10_perc_off_ebay'				
		
	DECLARE @C_SQL_DECLARE_HEADER_00 NVARCHAR(max) = 
	N'DECLARE @ItemImage table(
		  ITEM_ID bigint NOT NULL
		, '+ @alias_gallery			  +N' NVARCHAR(MAX)
		, '+ @alias_position_gallerey +N' NVARCHAR(MAX)
		, '+ @alias_image_base  	  +N' NVARCHAR(MAX)
		, '+ @alias_image_small 	  +N' NVARCHAR(MAX)
		, '+ @alias_image_thumbnail   +N' NVARCHAR(MAX)
		, '+ @alias_label_gallerey	  +N' NVARCHAR(MAX)
		, '+ @alias_label_base		  +N' NVARCHAR(MAX)
		, '+ @alias_label_small		  +N' NVARCHAR(MAX)
		, '+ @alias_label_thumbnail   +N' NVARCHAR(MAX));
	INSERT INTO @ItemImage (
		  ITEM_ID 
		, '+ @alias_gallery	          +N'
		, '+ @alias_position_gallerey +N'
		, '+ @alias_image_base		  +N'
		, '+ @alias_image_small		  +N'
		, '+ @alias_image_thumbnail	  +N'
		, '+ @alias_label_gallerey	  +N'
		, '+ @alias_label_base		  +N'
		, '+ @alias_label_small		  +N'
		, '+ @alias_label_thumbnail   +N')
	SELECT 	
		fi.ITEM_ID
		, STUFF(
			(	SELECT
					'', '' + MEDIA_GALLERY AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.MEDIA_GALLERY, 						
						II.POSITION_INDEX
					FROM 
						F_ITEM_IMAGE II	WITH(NOLOCK)					
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                         @alias_gallery +N'
		, STUFF(
			(	SELECT
					'', '' + CAST((POSITION_INDEX + 1) AS NVARCHAR(20)) AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.POSITION_INDEX
					FROM 
						F_ITEM_IMAGE II						
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                          @alias_position_gallerey + N''

	DECLARE @C_SQL_DECLARE_HEADER_01 NVARCHAR(max) = '
		, (SELECT TOP(1)
					BASE_IMAGE AS "data()" 
				FROM F_ITEM_IMAGE WITH (NOLOCK)			
				WHERE ITEM_ID = fi.ITEM_ID 
					AND BASE_SELECTED = 1) AS '+  @alias_image_base  +N'
		, (SELECT TOP(1)
					SMALL_IMAGE AS "data()" 
			FROM F_ITEM_IMAGE WITH (NOLOCK)							
			WHERE ITEM_ID = fi.ITEM_ID 
				AND SMALL_SELECTED = 1) AS '+     @alias_image_small +N'
		, (SELECT TOP(1)
				THUMBNAIL_IMAGE AS "data()" 
			FROM F_ITEM_IMAGE WITH (NOLOCK)					
			WHERE ITEM_ID = fi.ITEM_ID 
				AND THUMBNAIL_SELECTED = 1) AS '+ @alias_image_thumbnail   +N'
		, STUFF(
			(	SELECT
					'', '' + MEDIA_GALLERY_LABEL AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.MEDIA_GALLERY_LABEL,
						II.POSITION_INDEX
					FROM 
						F_ITEM_IMAGE II	WITH (NOLOCK)				
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                        @alias_label_gallerey	+N'
		, (SELECT TOP(1)
				BASE_IMAGE_LABEL AS "data()"
			FROM F_ITEM_IMAGE 	WITH (NOLOCK)					
			WHERE ITEM_ID = fi.ITEM_ID 
				AND BASE_SELECTED = 1) AS '+	@alias_label_base		+N'
		, (SELECT TOP(1)
				SMALL_IMAGE_LABEL AS "data()"
			FROM F_ITEM_IMAGE 	WITH (NOLOCK)					
			WHERE ITEM_ID = fi.ITEM_ID 
				AND SMALL_SELECTED = 1) AS '+   @alias_label_small		+N'
		, (SELECT TOP(1)
				THUMBNAIL_IMAGE_LABEL AS "data()" 
			FROM F_ITEM_IMAGE WITH (NOLOCK)					
			WHERE ITEM_ID = fi.ITEM_ID 
				AND THUMBNAIL_SELECTED = 1) AS '+@alias_label_thumbnail +N'
	FROM 
	(
		SELECT DISTINCT ITEM_ID, ITEM_NUMBER
		FROM F_ITEM WITH (NOLOCK)
	)	fi'

	DECLARE @C_SQL_HEADER_SELECT NVARCHAR(max) = 
	N' SELECT ' +
	N'  CONVERT(NVARCHAR(MAX), ITEM_EXPORT_SYS_CD)					ITEM_EXPORT_SYS_CD'			+
	N', CONVERT(NVARCHAR(MAX), SKU)									SKU '						+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_gallery			+N')	'+ @alias_gallery           +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_position_gallerey	+N')	'+ @alias_position_gallerey +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_gallerey		+N')	'+ @alias_label_gallerey    +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_base			+N')	'+ @alias_image_base        +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_base			+N')	'+ @alias_label_base		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_small		+N')	'+ @alias_image_small		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_small		+N')	'+ @alias_label_small		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_thumbnail	+N')	'+ @alias_image_thumbnail   +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_thumbnail	+N')	'+ @alias_label_thumbnail	+
	N', CONVERT(NVARCHAR(MAX),  visibility)							 visibility'			+N'
	'

	DECLARE @C_SQL_HEADER NVARCHAR(max) = '' +
	' SELECT ' +
	'	0										as ORDER_BY_KEY'						+
	', ''ALL'' as ITEM_EXPORT_SYS_CD ' + -- *** DO NOT CHANGE THIS ROW it is used by REPLACE() later ***	
		', ''SKU''									as SKU'								+
	', '''+ @alias_gallery				 + N''' as '+ @alias_gallery					+
	', '''+ @alias_position_gallerey	 + N''' as '+ @alias_position_gallerey			+
	', '''+ @alias_label_gallerey		 + N''' as '+ @alias_label_gallerey				+
	', '''+ @alias_image_base			 + N''' as '+ @alias_image_base					+
	', '''+ @alias_label_base			 + N''' as '+ @alias_label_base					+
	', '''+ @alias_image_small			 + N''' as '+ @alias_image_small				+
	', '''+ @alias_label_small			 + N''' as '+ @alias_label_small				+
	', '''+ @alias_image_thumbnail		 + N''' as '+ @alias_image_thumbnail			+
	', '''+ @alias_label_thumbnail		 + N''' as '+ @alias_label_thumbnail			+
	', '' visibility''							as  visibility'							+N'
	'
	
	DECLARE @C_SQL_BODY NVARCHAR(max) = 
	' 			
	SELECT ' +
	'  1 ORDER_BY_KEY ' +
	', ''ALL'' as ITEM_EXPORT_SYS_CD ' + -- *** DO NOT CHANGE THIS ROW it is used by REPLACE() later ***
	', CONVERT(NVARCHAR(20), fi.ITEM_ID)	as SKU'					+
	', fii.'+ @alias_gallery										+
	', fii.'+ @alias_position_gallerey								+
	', fii.'+ @alias_label_gallerey									+
	', fii.'+ @alias_image_base										+
	', fii.'+ @alias_label_base										+
	', fii.'+ @alias_image_small									+
	', fii.'+ @alias_label_small									+
	', fii.'+ @alias_image_thumbnail								+
	', fii.'+ @alias_label_thumbnail								+
	', ''4'' as  visibility'	+N'
	'

	IF (OBJECT_ID('dbo.F_ITEM_IMAGE_EXPORT', 'U') IS NOT NULL)
	BEGIN
		DROP TABLE F_ITEM_IMAGE_EXPORT
	END
	
	EXEC
	(		
		@C_SQL_DECLARE_HEADER_00 + 
		@C_SQL_DECLARE_HEADER_01 + 
		@C_SQL_HEADER_SELECT +
		N' INTO F_ITEM_IMAGE_EXPORT 
		   FROM 
		   ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM 
				F_ITEM fi					
				LEFT JOIN D_ITEM_CONDITION dic
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
				LEFT JOIN @ItemImage fii
					ON fi.ITEM_ID = fii.ITEM_ID				
			 WHERE
				dic.IS_ECOMMERCE = 1
				AND fi.IS_NEEDING_EXPORT = 1
				AND fi.IS_QUALIFIED = 1 
		 ) t 
		ORDER BY ORDER_BY_KEY 
		'
	)

	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''ALL'' as ITEM_EXPORT_SYS_CD', '''EBAY'' as ITEM_EXPORT_SYS_CD')
	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''4'' as  visibility', '''1'' as  visibility')
	SET @C_SQL_HEADER = REPLACE(@C_SQL_HEADER, '''ALL'' as ITEM_EXPORT_SYS_CD', '''EBAY'' as ITEM_EXPORT_SYS_CD')
	--SELECT @C_SQL_BODY

	EXEC
	(
		@C_SQL_DECLARE_HEADER_00 +
		@C_SQL_DECLARE_HEADER_01 +
		N' INSERT INTO F_ITEM_IMAGE_EXPORT ' +
		@C_SQL_HEADER_SELECT + 		
		N' FROM 
		 ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM
				F_ITEM fi					
				LEFT JOIN D_ITEM_CONDITION dic
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID				
				LEFT JOIN @ItemImage fii
					ON fi.ITEM_ID = fii.ITEM_ID
			 WHERE
				dic.IS_ECOMMERCE = 1
				AND fi.IS_NEEDING_EXPORT = 1
				AND fi.IS_QUALIFIED = 1 
		 ) t 
		 ORDER BY ORDER_BY_KEY 
		 '
	)

	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''EBAY'' as ITEM_EXPORT_SYS_CD', '''AMAZON'' as ITEM_EXPORT_SYS_CD')
	SET @C_SQL_HEADER = REPLACE(@C_SQL_HEADER, '''EBAY'' as ITEM_EXPORT_SYS_CD', '''AMAZON'' as ITEM_EXPORT_SYS_CD')
	--SELECT @C_SQL_BODY

	EXEC
	(
		@C_SQL_DECLARE_HEADER_00 +
		@C_SQL_DECLARE_HEADER_01 +
		N' INSERT INTO F_ITEM_IMAGE_EXPORT ' +
		@C_SQL_HEADER_SELECT + 		
		N' FROM 
		 ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM
				F_ITEM fi					
				LEFT JOIN D_ITEM_CONDITION dic
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID				
				LEFT JOIN @ItemImage fii
					ON fi.ITEM_ID = fii.ITEM_ID 
			' +
			N' WHERE
				dic.IS_ECOMMERCE = 1
				AND fi.IS_NEEDING_EXPORT = 1
				AND fi.IS_QUALIFIED = 1
		 ) t 
		ORDER BY ORDER_BY_KEY 
		'
	)	
END

			--N' WHERE  
			--	dic.IS_ECOMMERCE = 1
			--	AND fi.IS_QUALIFIED = 1
			--    AND
			--    (
			--		(	
			--			fi.IS_NEEDING_EXPORT = 1
			--			AND fip.SALES_SELL_THRU_RATE >= 20.0
			--			AND fip.QTY_LISTED >= 25 )
			--		OR
			--		(   fi.IS_NEEDING_EXPORT = 1
			--			AND fip.SALES_SELL_THRU_RATE != 0
			--			AND fip.SALES_PRICE_AVG >= 50
			--			AND fip.QTY_LISTED >= 5 )
			--		OR
			--		(   fi.IS_NEEDING_EXPORT = 1
			--			AND fip.SALES_SELL_THRU_RATE != 0
			--			AND fip.SALES_PRICE_AVG >= 500 )
			--	)
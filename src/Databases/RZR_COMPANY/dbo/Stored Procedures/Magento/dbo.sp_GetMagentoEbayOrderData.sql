-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GetMagentoEbayOrderData]
	@MagentoShopId bigint,
	@DomainName nvarchar(100),
	@MagentoOrderId bigint
AS
BEGIN	

	declare 
		@TablesPrefix nvarchar(100) = '',
        @MagentoVersion int = 1,
		@query nvarchar(max)

	select
		@TablesPrefix = isnull([MAGENTO_TABLE_PREFIX], '')
	   ,@MagentoVersion = isnull([MAGENTO_VERSION], 1)
	from [DW_CONFIG].[dbo].[F_COMPANY_ECOMM] with (nolock)
	where [COMPANY_ECOMM_ID] = @MagentoShopId
	
	if (@MagentoVersion = 1) begin
			select @query = 'SELECT
	                eo.ebay_order_id EbayOrderId,
	                eoi.final_fee EbayFinalFee,
	                eoet.transaction_id TransactionId,
	                eoet.fee Fee,
                    eoet.sum Amount,
                    eoet.transaction_date TransactionDate,
                    eo.buyer_message BuyerMessage
                FROM m2epro_order o
	            LEFT JOIN (
                    SELECT
					    SUM(final_fee) as FINAL_FEE,
                        oi.order_id
				    FROM m2epro_order_item oi
		            LEFT JOIN m2epro_ebay_order_item eoi
				        ON oi.id = eoi.order_item_id
		            GROUP BY oi.order_id
                ) eoi
                    ON o.id = eoi.order_id
                LEFT JOIN m2epro_ebay_order eo
                    ON eo.order_id = o.id	
	            LEFT JOIN m2epro_ebay_order_external_transaction eoet 
		            ON eo.order_id = eoet.order_id	
                WHERE magento_order_id = ' + cast(@MagentoOrderId as varchar(20))               
	end else
	begin
            select @query = 'SELECT
	                eo.ebay_order_id EbayOrderId,
	                eoi.final_fee EbayFinalFee,
	                eoet.transaction_id TransactionId,
	                IFNULL(eoet.fee, 0) Fee,
                    IFNULL(eoet.sum, 0) Amount,
                    eoet.transaction_date TransactionDate,
                    eo.buyer_message BuyerMessage
                FROM ' + @TablesPrefix + 'm2epro_order o
	            LEFT JOIN (
                    SELECT
					    SUM(final_fee) as FINAL_FEE,
                        oi.order_id
				    FROM ' + @TablesPrefix + 'm2epro_order_item oi
		            LEFT JOIN ' + @TablesPrefix + 'm2epro_ebay_order_item eoi
				        ON oi.id = eoi.order_item_id
		            GROUP BY oi.order_id
                ) eoi
                    ON o.id = eoi.order_id
                LEFT JOIN ' + @TablesPrefix + 'm2epro_ebay_order eo
                    ON eo.order_id = o.id
	            LEFT JOIN ' + @TablesPrefix + 'm2epro_ebay_order_external_transaction eoet
		            ON eo.order_id = eoet.order_id
                WHERE magento_order_id = ' + cast(@MagentoOrderId as varchar(20))     
	end

	select @query = 'select * from openquery([' + @DomainName + '], ''' + @query + ''')'
	
	EXEC (@query) AT [LS_CHANNEL_DB];	

END
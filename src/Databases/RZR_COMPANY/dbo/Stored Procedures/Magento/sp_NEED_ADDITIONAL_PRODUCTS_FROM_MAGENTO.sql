-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_NEED_ADDITIONAL_PRODUCTS_FROM_MAGENTO]
	@MAGENTO_ORDER_ID BIGINT,
	@MAGENTO_SHOP_ID INT
AS
BEGIN
	DECLARE	@temp TABLE ( M_PRODUCT_ID	BIGINT)
	
	INSERT INTO @temp 
	(
	 M_PRODUCT_ID
	)
	SELECT 
		mop.M_PRODUCT_ID 
	FROM MAGENTO_ORDER_PRODUCTS AS mop
	WHERE mop.M_ORDER_ID = @MAGENTO_ORDER_ID AND
	mop.SHOP_ID = @MAGENTO_SHOP_ID
			
	SELECT 
		t.M_PRODUCT_ID
	FROM @temp AS t
	WHERE t.M_PRODUCT_ID NOT IN (SELECT mp.M_PRODUCT_ID FROM MAGENTO_PRODUCTS AS mp WHERE mp.SHOP_ID = @MAGENTO_SHOP_ID)
END
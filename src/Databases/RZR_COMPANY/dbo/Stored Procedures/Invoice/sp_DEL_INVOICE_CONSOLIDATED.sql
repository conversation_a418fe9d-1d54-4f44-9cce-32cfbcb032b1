CREATE PROCEDURE dbo.sp_DEL_INVOICE_CONSOLIDATED
	@C_INVOICE_CONSOLIDATED_IDs dbo.[bigint_ID_ARRAY] READONLY,
	@C_USER_ID BIGINT,
	@C_USER_IP BIGINT
AS
BEGIN
	BEGIN TRY
		DECLARE @UTC_NOW			DATETIME		= GETUTCDATE()
				,@SP_NAME			NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)

		BEGIN TRAN DEL_INVOICE_CONSOLIDATED

			--set blockings
			DECLARE @OPERATION_BLOCKINGS [dbo].[OPERATION_BLOCKING]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,3	--Invoice
				,i.[INVOICE_ID]
				,1
			FROM [dbo].[F_INVOICE]						i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_CONSOLIDATED_IDs		ic_ids
				ON i.[INVOICE_CONSOLIDATED_ID] = ic_ids.[ID]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,1	--SalesOrder
				,i.[ORDER_ID]
				,1
			FROM [dbo].[F_INVOICE]						i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_CONSOLIDATED_IDs		ic_ids
				ON i.[INVOICE_CONSOLIDATED_ID] = ic_ids.[ID]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,5	--RecyclingOrder
				,so.[RECYCLING_ORDER_ID]
				,1
			FROM [dbo].[F_INVOICE]						i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_CONSOLIDATED_IDs		ic_ids
				ON i.[INVOICE_CONSOLIDATED_ID] = ic_ids.[ID]
			INNER JOIN [dbo].[F_SALES_ORDER]			so	WITH(NOLOCK)
				ON so.[SALES_ORDER_ID] = i.[ORDER_ID]
			WHERE so.[RECYCLING_ORDER_ID] IS NOT NULL

			exec [dbo].[sp_DEL_OPERATION_BLOCKINGS]
				@C_OPERATION_BLOCKINGS = @OPERATION_BLOCKINGS
				,@C_USER_ID = @C_USER_ID
				,@C_USER_IP = @C_USER_IP
				,@C_OVERRIDEN_CHANGE_BY = @SP_NAME

		
			UPDATE T 
				SET T.INVOICE_CONSOLIDATED_ID = NULL
				,[UPDATED_BY] = @SP_NAME
				,[UPDATED_DT] = @UTC_NOW
			FROM F_INVOICE  T
				INNER JOIN  @C_INVOICE_CONSOLIDATED_IDs S ON s.ID  = T.INVOICE_CONSOLIDATED_ID

			DELETE  T FROM F_INVOICE_CONSOLIDATED t
			INNER JOIN  @C_INVOICE_CONSOLIDATED_IDs S ON s.ID  = T.INVOICE_CONSOLIDATED_ID
		
		COMMIT TRAN DEL_INVOICE_CONSOLIDATED
	END TRY
	BEGIN CATCH
		DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
		DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
		DECLARE @ErrorState INT = ERROR_STATE();
		IF (XACT_STATE()) = -1
		BEGIN
			ROLLBACK TRANSACTION;
		END 
		RAISERROR (@ErrorMessage, -- Message text.
				@ErrorSeverity, -- Severity.
				@ErrorState -- State.
				);
	END CATCH	
END
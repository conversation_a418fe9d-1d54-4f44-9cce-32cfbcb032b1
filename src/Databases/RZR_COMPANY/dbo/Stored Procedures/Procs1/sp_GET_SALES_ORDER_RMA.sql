-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_RMA]
	@RMA_ID BIGINT
AS
BEGIN
	SELECT
		fso.CUSTOMER_ID,
		fsor.SALES_ORDER_ID,
		fsor.RMA_TYPE_ID,
		fsor.RMA_STATUS_ID,
		fsor.EMAIL,
		fsor.[DESCRIPTION],
		fsor.PROVIDE_RETURN_LABEL,
		fsor.RMA_ID,
		fsor.INSERTED_DT,
		fsor.INTERNAL_COMMENT,
		fso.SALES_ORDER_ID AS GENERATED_ORDER_ID
	FROM F_SALES_ORDER_RMA	fsor WITH (NOLOCK)
	LEFT JOIN F_SALES_ORDER fso	 WITH (NOLOCK)
		ON fsor.RMA_ID = fso.RMA_ID
	WHERE fsor.RMA_ID = @RMA_ID
END
 
-- =============================================
-- Author:		<Oleg K.Evseev>
-- Create date: <2013.09.25>
-- Description:	<Returns a page of RMAs>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_RMAS]
	@REP_IDS			bigint_ID_ARRAY	READONLY,
	@ORDER_COLUMN_NAME	varchar(150)	= 'RMAId',
	@ORDER_DIRECTION	varchar(20)		= 'asc',
	@ITEMS_PER_PAGE		int				= 20,
	@PAGE_INDEX			int				= 0,
	@FILTER_WHERE		varchar(2000)	= N'',
	@C_IS_DEBUG			bit				= 0
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE

	DECLARE @filterCondition nvarchar(max) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END

	DECLARE @rep_condition		NVARCHAR(MAX) = N'';

	-- rep restricion
	IF (EXISTS(SELECT ID FROM @REP_IDS)) BEGIN
	   SELECT ID  INTO #REP_IDS FROM @REP_IDS
	   -- RSW-9614 TODO: restrict only by rep, make it "inner join" instead of "where" condition
	   SET @rep_condition = 
			N' AND EXISTS(
				SELECT TOP(1) 1 
                FROM [dbo].[F_SalesOrderRepUser] SoRep WITH (NOLOCK) 
                WHERE SoRep.SalesOrderId = O.SALES_ORDER_ID AND SoRep.RepUserId in (SELECT ID FROM #REP_IDS)
			)'
    END

	DECLARE 
		@query NVARCHAR (MAX) = N'
			declare @now datetime = getutcdate();
			WITH m_data AS	(
				SELECT
					 R.RMA_ID											AS RMAId
					,R.RMA_TYPE_ID										AS RMATypeId
					,T.RMA_TYPE_CD										AS RMATypeCd
					,O.CUSTOMER_ID										AS CustomerId
					,C.CUSTOMER_NAME									AS CustomerName
					,R.RMA_STATUS_ID									AS StatusId
					,RS.RMA_STATUS_CD									AS StatusCd
					,STUFF((
						SELECT DISTINCT '', '' + reason.REASON_CD
						  FROM [dbo].[F_RMA_ITEM]				AS item
						 INNER JOIN [dbo].[D_RMA_RETURN_REASON]	AS reason
							ON item.RETURN_REASON_ID = reason.REASON_TYPE_ID
							WHERE item.RMA_ID = R.RMA_ID
							FOR XML PATH(''''), type
					).value(''.'', ''nvarchar(max)''), 1, 1, '''')		AS ReturnReasons
					,RINV.InvoiceId										AS RMAInvoiceId
					,RINV.InvoiceAutoName								AS RMAInvoiceAutoName
					,RINV.InvoiceStatus									AS RmaInvoiceState
					,(SELECT 
					    COUNT(RP.PACKAGE_ID)
					  FROM F_RMA_PACKAGE RP
					  WHERE RP.RMA_ID = R.RMA_ID 
					    AND DATALENGTH(RP.LABEL_RETURN) > 0)			AS LabelCount
					,(SELECT 
					    COUNT(RI.RMA_ITEM_ID)
					  FROM F_RMA_ITEM	RI	WITH(NOLOCK)
					  WHERE RI.RMA_ID = R.RMA_ID)						AS ItemCount
					,(SELECT
						SUM(SI.ITEM_PRICE * SI.ITEM_QUANTITY)
					  FROM F_RMA_ITEM					RI WITH(NOLOCK)
					  INNER JOIN F_SALES_ORDER_ITEM		SI WITH(NOLOCK)
						  ON RI.SALES_ORDER_ITEM_ID = SI.SALES_ORDER_ITEM_ID
					  WHERE RI.RMA_ID = R.RMA_ID) 						AS Amount
					,CASE 
						WHEN R.PROVIDER_ID IS NULL THEN 
							''Other''
						ELSE DSP.PROVIDER_NAME
					 END											AS [ProviderName]
					,STUFF((
						SELECT DISTINCT '', '' + rma_track.[PACKAGE_TRACKING_NO] + '' '' + rma_track.LAST_STATUS
						  FROM [dbo].[F_RMA_PACKAGE_TRACKING]	AS rma_track
							WHERE rma_track.RMA_ID = R.RMA_ID
							FOR XML PATH(''''), type
					).value(''.'', ''nvarchar(max)''), 1, 1, '''')	AS TrackingDetails
					,O.SALES_ORDER_ID								AS SalesOrderId
					,O.SALES_ORDER_NUMBER							AS SoNo
					,O.PO_NUMBER									AS PoNo    
					,O.SALES_ORDER_DATE								AS OrderDate
					,OU.UserName									AS UserCreated
					,R.OPENED_DT									AS CreatedDate
					,VU.UserName									AS UserVerified
					,R.VERIFIED_DT									AS VerifiedDate
					,CU.UserName									AS UserClosed
					,R.CLOSED_DT									AS ClosedDate
					,isnull(cm.CreditMemosCount, 0)					as CreditMemosCount
					,isnull(cm.CreditMemosAmount, 0.0)				as CreditMemosAmount
					,R.TRACKING_NO								    AS TrackingNumber
					,R.FREIGHT
			FROM [dbo].F_SALES_ORDER_RMA				R	WITH(NOLOCK)			
			INNER JOIN [dbo].D_RMA_STATUS				RS	WITH(NOLOCK)
				ON R.RMA_STATUS_ID		= RS.RMA_STATUS_ID
			INNER JOIN D_RMA_TYPE				T	WITH(NOLOCK)
				ON R.RMA_TYPE_ID		= T.RMA_TYPE_ID
			LEFT JOIN D_SHIPPING_PROVIDER			DSP	 WITH(NOLOCK)
				ON DSP.PROVIDER_ID = R.PROVIDER_ID + 1			
			-- SALES ORDER
			INNER JOIN F_SALES_ORDER				O	WITH(NOLOCK)
				ON R.SALES_ORDER_ID		= O.SALES_ORDER_ID
			INNER JOIN C_CUSTOMER_TRANSACTION_TERM	CTT	WITH(NOLOCK)
				ON O.TERM_ID = CTT.CUSTOMER_TRANSACTION_TERM_ID
			OUTER APPLY (
				SELECT top(1) 
					cmi.InvoiceId		AS InvoiceId,
					CASE
						WHEN I.IS_VOIDED = 0 AND I.IS_PAID = 0 AND DATEDIFF(Day, I.DATE_CREATED, CONVERT(VARCHAR, @now, 21)) <= CTT.DUE_DAYS AND (I.AMOUNT_DUE - I.MISC_CHARGE) > 0 THEN N''current''
						WHEN I.IS_PAID = 1 OR (I.AMOUNT_DUE - I.MISC_CHARGE) = 0 OR I.IS_VOIDED = 1 THEN N''history''
						WHEN I.IS_PAID = 0 AND I.IS_VOIDED = 0 AND DATEDIFF(Day, I.DATE_CREATED, CONVERT(VARCHAR, @now, 21)) > CTT.DUE_DAYS AND (I.AMOUNT_DUE - I.MISC_CHARGE) > 0 THEN ''open''
						ELSE N''none''
					END												AS InvoiceStatus,
					cmi.[INVOICE_AUTO_NAME]							AS InvoiceAutoName
				FROM [dbo].[vw_CreditMemoInvoiced]		cmi WITH(NOLOCK)
				inner join dbo.F_INVOICE				I	WITH(NOLOCK)
					ON  cmi.RmaId = R.RMA_ID
					AND I.INVOICE_ID = cmi.InvoiceId
				ORDER BY
					 cmi.[INVOICE_IS_VOIDED] ASC
					,cmi.InvoiceId			 DESC
			)									RINV

			INNER JOIN F_CUSTOMER				C	WITH(NOLOCK)
				ON O.CUSTOMER_ID		= C.CUSTOMER_ID			
			LEFT JOIN tb_User					OU	WITH(NOLOCK)
				ON R.OPENED_BY_ID = OU.UserID
			LEFT JOIN tb_User					VU	WITH(NOLOCK)
				ON R.VERIFIED_BY_ID = VU.UserID
			LEFT JOIN tb_User					CU	WITH(NOLOCK)
				ON R.CLOSED_BY_ID = CU.UserID
			left join (
				select
					cm.RmaId		as RmaId
					,count(cm.Id)	as CreditMemosCount
					,sum(cm.Credit)	as CreditMemosAmount
				from [dbo].[vw_CreditMemo] cm with(nolock)
				group by cm.RmaId
			) cm
				on cm.RmaId = R.RMA_ID
			WHERE R.IS_INTERNAL_RMA = 0
			  AND R.IS_DELETED		= 0
			  AND O.IS_DELETED		= 0
			  AND R.IS_INACTIVE		= 0
			  AND O.IS_INACTIVE		= 0
			  ' + @rep_condition + N'
			)'
			+ N'
			SELECT TOP(1) 
				-1				AS RowID
				,COUNT(RMAId)	AS RMAId
				,0				AS RMATypeId
				,NULL 			AS RMATypeCd
				,0				AS CustomerId
				,NULL 			AS CustomerName
				,0				AS StatusId
				,NULL 			AS StatusCd
				,0				AS ItemCount
				,0				AS RMAInvoiceId
				,NULL			AS RMAInvoiceAutoName
				,NULL			AS RmaInvoiceState
				,0				AS LabelCount
				,NULL 			AS ReturnReasons
				,0.00			AS Amount
				,null			as ProviderName
				,NULL 			AS TrackingDetails
				,0				AS SalesOrderId
				,NULL 			AS SoNo
				,NULL			AS PoNo
				,NULL 			AS OrderDate
				,NULL			AS UserCreated
				,NULL 			AS CreatedDate
				,NULL			AS UserVerified
				,NULL			AS VerifiedDate
				,NULL			AS UserClosed
				,NULL			AS ClosedDate
				,0				as CreditMemosCount
				,0.0			as CreditMemosAmount
				,NULL           AS TrackingNumber
				,NULL			AS Freight
				FROM m_data ' + @filterCondition + N'
			UNION'
			+ N'
			SELECT
				t.*
			FROM
				(SELECT	
					ROW_NUMBER() OVER ( ORDER BY  M.'+ @ORDER_COLUMN_NAME +  N' '+ @ORDER_DIRECTION + N')	AS RowID
					,M.RMAId
					,M.RMATypeId
					,M.RMATypeCd
					,M.CustomerId
					,M.CustomerName
					,M.StatusId
					,M.StatusCd
					,M.ItemCount
					,M.RMAInvoiceId
					,M.RMAInvoiceAutoName
					,M.RmaInvoiceState
					,M.LabelCount
					,M.ReturnReasons
					,M.Amount
					,M.ProviderName
					,M.TrackingDetails
					,M.SalesOrderId
					,M.SoNo
					,M.PoNo
					,M.OrderDate
					,M.UserCreated
					,M.CreatedDate
					,M.UserVerified
					,M.VerifiedDate
					,M.UserClosed
					,M.ClosedDate
					,M.CreditMemosCount
					,M.CreditMemosAmount
					,M.TrackingNumber
					,M.Freight
				FROM m_data M ' + @filterCondition + N'
			) t	WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))

	IF (@C_IS_DEBUG = 1)
		PRINT CAST(@query AS NTEXT)
	ELSE
		EXEC sp_executesql @query;
	IF OBJECT_ID('tempdb..#REP_IDS') IS NOT NULL 
		DROP TABLE #REP_IDS;

END
CREATE TABLE [dbo].[C_TRUCK_TYPE] (
    [TRUCK_TYPE_ID]  INT           IDENTITY (1, 1) NOT NULL,
    [TRUCK_TYPE_CD]  VARCHAR (250) NOT NULL,
    [LOAD_WEIGHT]    FLOAT (53)    CONSTRAINT [DF_C_TRUCK_TYPE_LOAD_WEIGHT] DEFAULT ((0)) NOT NULL,
    [LOAD_WEIGHT_KG] AS            ([LOAD_WEIGHT]*(0.45359237)) PERSISTED,
    CONSTRAINT [PK_C_TRUCK_TYPE] PRIMARY KEY CLUSTERED ([TRUCK_TYPE_ID] ASC)
);


















































GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Truck type depends on weight', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_TRUCK_TYPE';


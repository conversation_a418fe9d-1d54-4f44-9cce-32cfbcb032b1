CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_PURCHASE_ORDER_ITEM_STATUS_CHANGE_FROM_QUEUE]
	 @C_COUNT		INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID BIGINT = 13
    DECLARE @IS_ALLOWED		     BIT	  = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID);

	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY = N'sp_GET_NOTIFICATIONS_PURCHASE_ORDER_ITEM_STATUS_CHANGE_FROM_QUEUE',
			UPDATED_DT = GETUTCDATE()
		WHERE IS_INACTIVE = 0 AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID		
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT
			 NQ.NotificationQueueId			AS [NOTIFICATION_QUEUE_ID]
			,DC.*
			,DU.*
			,DPO.*
      ,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, PO.[USER_ID], NULL, NULL, NQ.NotificationSetId, NQ.CustomerId) AS EMAILS		
		FROM @tNotifyItemWithNotifySet		NQ
 	    CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
		INNER JOIN F_PURCHASE_ORDER_ITEM POI WITH (NOLOCK)
			ON POI.PURCHASE_ORDER_ITEM_ID = QP.PURCHASE_ORDER_ITEM_ID
 		INNER JOIN F_PURCHASE_ORDER		PO	WITH(NOLOCK)
			ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER]		(PO.CUSTOMER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)	DC
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER]			(PO.[USER_ID])								DU
		CROSS APPLY	[dbo].[tvf_GET_SNIPPET_DATA_PURCHASE_ORDER_ITEM] (QP.PURCHASE_ORDER_ITEM_ID)			DPO
		WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
			AND QP.PURCHASE_ORDER_ITEM_ID IS NOT NULL	
	
END
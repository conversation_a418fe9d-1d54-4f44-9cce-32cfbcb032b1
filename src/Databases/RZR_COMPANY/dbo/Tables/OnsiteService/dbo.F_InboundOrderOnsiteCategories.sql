CREATE TABLE [dbo].[F_InboundOrderOnsiteCategories] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [OnsiteTypeId]                              INT             NULL,
    [CategoryId]                                INT             NOT NULL,
    [FinalScannedQty]                           INT             NULL,
    [ManualQty]                                 INT             NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderOnsiteCategories_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderOnsiteCategories_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteCategories_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteCategories_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderOnsiteCategories] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderOnsiteCategories_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderOnsiteCategories_D_CATEGORY_HIERARCHY] FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderOnsiteCategories]([RecyclingOrderId])
    INCLUDE([CategoryId]);
GO

CREATE NONCLUSTERED INDEX [IX_CategoryId]
    ON [dbo].[F_InboundOrderOnsiteCategories]([CategoryId])
    INCLUDE ([RecyclingOrderId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Onsite Categories for Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderOnsiteCategories';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_OnsiteTypeId]
    ON [dbo].[F_InboundOrderOnsiteCategories]([RecyclingOrderId] ASC, [OnsiteTypeId] ASC);

GO


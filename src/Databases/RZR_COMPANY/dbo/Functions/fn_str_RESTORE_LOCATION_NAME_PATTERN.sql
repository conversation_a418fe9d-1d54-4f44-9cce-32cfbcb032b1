-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_RESTORE_LOCATION_NAME_PATTERN]
    (@LOCATION_NAME NVARCHAR(128))
RETURNS NVARCHAR(128)
BEGIN	

	DECLARE @TO_REPLACE NVARCHAR(16)

	-- Replace Letters by 'L'
	SET @TO_REPLACE = '%[A-Z]%'
	WHILE PATINDEX(@TO_REPLACE, @LOCATION_NAME) > 0
		SET @LOCATION_NAME = STUFF(@LOCATION_NAME, PATINDEX(@TO_REPLACE, @LOCATION_NAME), 1, '<') -- 'L' is a letter too, so using a neutral replacement first    
	SET @LOCATION_NAME = REPLACE(@LOCATION_NAME, '<', 'L')
	    
	-- Replace Diggits by 'D'
	SET @TO_REPLACE = '%[0-9]%'
	WHILE PATINDEX(@TO_REPLACE, @LOCATION_NAME) > 0
		SET @LOCATION_NAME = STUFF(@LOCATION_NAME, PATINDEX(@TO_REPLACE, @LOCATION_NAME), 1, 'D')


	-- The heading group could has been replicated - remove duplicates
	SET @LOCATION_NAME = @LOCATION_NAME + '>' -- for the trim function to work on 'DDDDL' -> 'DL' or 'LLLL' -> 'L'
	
	DECLARE @HEADING CHAR = SUBSTRING(@LOCATION_NAME, 1, 1);		   	
	SET @LOCATION_NAME = @HEADING + SUBSTRING(@LOCATION_NAME, PATINDEX('%[^'+ @HEADING +']%', @LOCATION_NAME), 1000)
	SET @LOCATION_NAME = REPLACE(@LOCATION_NAME, '>', '') -- Remove '>'

	RETURN @LOCATION_NAME;
END
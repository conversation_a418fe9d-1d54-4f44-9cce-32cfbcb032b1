-- =============================================
-- Author:		<O.Evseev>
-- Create date: <05.02.2017>
-- Description:	Collects totals of a sales order. Returns user defined table type [dbo].[SalesOrderTotals]
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_SALES_ORDER_TOTALS]
(
	@SALES_ORDER_ID BIGINT = NULL
)
RETURNS TABLE
/*
TABLE( --returns user defined table type [dbo].[SalesOrderTotals]
	SalesOrderId	[bigint],
	Cost			float default(0.0),
	Subtotal		float default(0.0),
	Tax				float default(0.0),
	GrossProfit		float default(0.0),
	PaymentsApplied float default(0.0),
	ShippingCost	float default(0.0),
	HandingFee		float default(0.0),
	Amount			float default(0.0),
	AmountDue		float default(0.0),
	Margin			float default(0.0),
	ECommerceFee	float default(0.0)
)*/
AS
RETURN (
	SELECT
		 SalesOrderId
		,Cost + RepairCost as Cost
		,Subtotal
		,Tax
		,(Subtotal - Cost - RepairCost - InternalCost)							AS GrossProfit
		,PaymentsApplied
		,ShippingCost
		,HandingFee
		,ROUND(Subtotal + Tax + ShippingCost + HandingFee, 2)		AS Amount
		,ROUND(Subtotal + Tax + ShippingCost + HandingFee - PaymentsApplied, 2)		AS AmountDue
		,Subtotal + ShippingCost + HandingFee - Cost - TotalFees - RepairCost - InternalCost - DistributedCost AS Margin
		,ECommerceFee
	FROM (
		SELECT
			 @SALES_ORDER_ID										AS SalesOrderId
			,ISNULL(O.SHIPPING_COST, 0.0)							AS ShippingCost
			,ISNULL(O.HANDLING_FEE, 0.0)							AS HandingFee
			,o.SUBTOTAL												AS Subtotal
			,dbo.sp_CALCULATE_SALES_ORDER_COST(O.SALES_ORDER_ID)	AS Cost
			,dbo.sp_CALCULATE_SALES_ORDER_TAX(O.SALES_ORDER_ID)		AS Tax
			,ISNULL(I.PAID_AMOUNT, 0.0)								AS PaymentsApplied
			,O.TotalFees
			,ISNULL(IC.InternalCost, 0)								AS InternalCost
			,ISNULL(IC.DistributedCost, 0)							AS DistributedCost
			,ISNULL(RC.RepairCost, 0)							    AS RepairCost
			,O.ECommerceFee
		FROM F_SALES_ORDER											AS O	WITH (NOLOCK)
		LEFT JOIN (
			SELECT
				SUM(FI.PAID_AMOUNT) as PAID_AMOUNT,
				FI.ORDER_ID
			FROM dbo.F_INVOICE										AS FI	WITH (NOLOCK) -- There is always 1 active invoice
			WHERE FI.INVOICE_TYPE_ID = 1
				AND FI.IS_VOIDED = 0
			GROUP BY FI.ORDER_ID
		)															AS I
			ON I.ORDER_ID = O.SALES_ORDER_ID
		LEFT JOIN (
			SELECT
				SOI.SALES_ORDER_ID,
				SUM(ISNULL(POI.InternalCost, 0))					AS InternalCost,
				SUM(ISNULL(FA.[DistributedCost], 0))				AS DistributedCost
			FROM F_SALES_ORDER_ITEM									AS SOI	WITH (NOLOCK)
			INNER JOIN F_ITEM_INVENTORY								AS INV	WITH (NOLOCK)
				ON INV.ITEM_INVENTORY_ID = SOI.ITEM_INVENTORY_ID
			INNER JOIN F_PURCHASE_ORDER_ITEM						AS POI	WITH (NOLOCK)
				ON POI.INVENTORY_ITEM_ID = INV.ITEM_INVENTORY_ID
			LEFT JOIN recycling.F_Asset								AS FA	WITH (NOLOCK)
				on FA.Id = INV.AssetId
			GROUP BY SOI.SALES_ORDER_ID
		)															AS IC
			ON IC.SALES_ORDER_ID = O.SALES_ORDER_ID
        left join F_ORDER_ORDER_SUBJECT_TYPE FOOST with (nolock)
                  on FOOST.[ENTITY_TYPE_ID] = 1 and O.[SALES_ORDER_ID] = FOOST.[ORDER_ID]
        outer apply (select sum(RPOI.PRICE) as RepairCost
                     from F_SALES_ORDER_ITEM FSOI with (nolock)
                              left join F_PURCHASE_ORDER RPO with (nolock)
                                        on FOOST.ENTITY_SUBJECT_TYPE_ID = 12 and
                                           RPO.REPAIR_SALES_ORDER_ID = O.SALES_ORDER_ID
                              left join F_PURCHASE_ORDER_ITEM RPOI with (nolock)
                                        on (FOOST.ENTITY_SUBJECT_TYPE_ID = 12 and
                                            RPOI.PURCHASE_ORDER_ID = RPO.PURCHASE_ORDER_ID and
                                            FSOI.ITEM_INVENTORY_ID = RPOI.REPAIR_INVENTORY_ITEM_ID) or
                                           (FOOST.ENTITY_SUBJECT_TYPE_ID != 12 and
                                            RPOI.REPAIR_INVENTORY_ITEM_ID = FSOI.ITEM_INVENTORY_ID)


                     where FSOI.SALES_ORDER_ID = O.SALES_ORDER_ID
                       and RPOI.IS_DELETED = 0
                       and RPOI.RECEIVE_STATUS_ID = 3) as RC

        where O.SALES_ORDER_ID = @SALES_ORDER_ID
	) T
)

CREATE FUNCTION [dbo].[fn_table_GET_RECYCLING_ORDER_WEIGHT]
(
	@ORDER_ID bigint
)
RETURNS @returntable TABLE
(
	RECYCLING_ORDER_ID bigint,
	WEIGHT_RECEIVED FLOAT,
	WEIGHT_TARE FLOAT,
	ITEM_COUNT bigint
)
AS
BEGIN
	DECLARE @IS_TRANSFER BIT
	SELECT 
		@IS_TRANSFER = IS_TRANSFER
	FROM dbo.F_RECYCLING_ORDER WITH (NOLOCK)
	WHERE RECYCLING_ORDER_ID = @ORDER_ID
	
	IF (@IS_TRANSFER = 1) B<PERSON>IN

		INSERT INTO @returntable
		SELECT 
			ROIT.INBOUND_ORDER_ID,
			SUM(I.WEIGHT_RECEIVED) AS WEIGHT_RECEIVED,
			SUM(I.WEIGHT_TARE) AS WEIGHT_TARE,
			SUM(I.ITEM_COUNT) as ITEM_COUNT
		FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER_ITEM_TRANSFER ROIT WITH(NOLOCK)
			ON I.RECYCLING_ORDER_ITEM_ID = ROIT.RECYCLING_ORDER_ITEM_ID
		WHERE ROIT.INBOUND_ORDER_ID = @ORDER_ID 
			--AND I.IS_CONSUMED_OR_PROCESSED = 0
			AND I.PARENT_ID IS NULL AND I.IS_MERGED = 0
		GROUP BY ROIT.INBOUND_ORDER_ID		
	END
	ELSE 
	BEGIN
		INSERT INTO @returntable
		SELECT 
			I.RECYCLING_ORDER_ID,
			SUM(I.WEIGHT_RECEIVED) AS WEIGHT_RECEIVED,
			SUM(I.WEIGHT_TARE) AS WEIGHT_TARE,
			SUM(I.ITEM_COUNT) as ITEM_COUNT
		FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
		WHERE I.RECYCLING_ORDER_ID = @ORDER_ID 
			--AND I.IS_CONSUMED_OR_PROCESSED = 0
			AND I.PARENT_ID IS NULL AND I.IS_MERGED = 0
		GROUP BY I.RECYCLING_ORDER_ID
	END
	RETURN
END

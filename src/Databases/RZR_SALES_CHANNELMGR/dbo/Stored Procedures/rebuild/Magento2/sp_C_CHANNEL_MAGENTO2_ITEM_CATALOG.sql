CREATE PROCEDURE [dbo].[sp_C_CHANNEL_MAGENTO2_ITEM_CATALOG]
(
	@C_IS_FULL_REBUILD		CHAR(1)			= 'N'
	,@C_ITEM_MASTER_ID		BIGINT			= 0
	,@C_ITEM_ID				BIGINT			= 0
	,@C_ITEM_MASTER_IDS		NVARCHAR(MAX)	= N'<Root/>'
	,@C_ITEM_IDs			NVARCHAR(MAX)	= N'<Root/>'
	,@C_COMPANY_ID			BIGINT			= 0
	,@C_COMPANY_ECOMM_ID	BIGINT			= 0
	,@C_IS_DEBUG			BIT				= 0
)
AS
BEGIN
	DECLARE @T_ITEM_MASTER_IDs [dbo].[bigint_ID_ARRAY];

	INSERT INTO @T_ITEM_MASTER_IDs
	SELECT DISTINCT [ID] FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](@C_ITEM_MASTER_IDS, N'Items')
	UNION 
	SELECT @C_ITEM_MASTER_ID WHERE @C_ITEM_MASTER_ID != 0;

	DECLARE @T_ITEM_IDs [dbo].[bigint_ID_ARRAY];
	DECLARE @IS_ALL_ITEMS BIT = 0;

	INSERT INTO @T_ITEM_IDs
	SELECT DISTINCT [ID] FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](@C_ITEM_IDs, N'Items')
	UNION
	SELECT @C_ITEM_ID WHERE @C_ITEM_ID != 0;
	
	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_IDs) AND EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_MASTER_IDs))
	BEGIN
		INSERT INTO @T_ITEM_IDs
		SELECT DISTINCT [ITEM_ID]
		FROM [dbo].[F_COMPANY_SALES_ITEM] WITH (NOLOCK)
		WHERE [ITEM_MASTER_ID] IN (SELECT [ID] FROM @T_ITEM_MASTER_IDs)
			AND [IS_DELETED] = 0
			AND [IS_INACTIVE] = 0
			AND [ITEM_MASTER_TYPE_ID] = 1;
	END
	
	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_IDs))
	BEGIN
		SET @IS_ALL_ITEMS = 1;
	END

	DECLARE @C_COMPANY_RZR_ERP_DATABASE_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_RZR_ERP_DATABASE_NAME_FROM_COMPANY_ID](@C_COMPANY_ID);
	DECLARE @C_CHANNEL_MAGENTO_DOMAIN_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_CHANNEL_ECOMM_DOMAIN_NAME](@C_COMPANY_ID, @C_COMPANY_ECOMM_ID);
	DECLARE @C_CHANNEL_MAGENTO_DATABASE_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_CHANNEL_ECOMM_DATABASE_NAME](@C_COMPANY_ID, @C_COMPANY_ECOMM_ID);

	DECLARE @magentoTablePrefix NVARCHAR (100) = (
		SELECT ISNULL([MAGENTO_TABLE_PREFIX], N'')
		FROM [dbo].[vw_F_COMPANY_ECOMM] WITH (NOLOCK)
		WHERE [COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	);

	DECLARE @C_SQL_PARAM_DEFINITION_INIT NVARCHAR(MAX) = N'';
	DECLARE @C_SQL_BUILD_PRODUCT_01 NVARCHAR(MAX) = N'';
	DECLARE @C_SQL_INIT_01 NVARCHAR(MAX) = N'';

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT @C_COMPANY_ID AS [COMPANY_ID], @C_COMPANY_RZR_ERP_DATABASE_NAME AS [COMPANY_RZR_ERP_DATABASE_NAME], @C_CHANNEL_MAGENTO_DOMAIN_NAME AS [CHANNEL_MAGENTO_DOMAIN_NAME], @C_CHANNEL_MAGENTO_DATABASE_NAME AS [CHANNEL_MAGENTO_DATABASE_NAME];
	END

	IF (@C_IS_FULL_REBUILD = 'Y')
	BEGIN
		DECLARE @C_SQL_DELETE NVARCHAR(MAX) = N'
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[catalog_product_website]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[cataloginventory_stock_item]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[cataloginventory_stock_status]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[cataloginventory_stock_status_idx]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[catalog_category_product]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[catalog_category_product_index]
		DELETE FROM [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + ']...[catalogsearch_fulltext]';

		IF (@C_IS_DEBUG = 1) SELECT (@C_SQL_DELETE)
		ELSE EXEC (@C_SQL_DELETE);
	END

	DECLARE @tax_class_id_attribute_id INT;
	
	SET @C_SQL_INIT_01 = N'
	SELECT @tax_class_id_attribute_id = attribute_id
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT eas.attribute_id
		FROM ' + @magentoTablePrefix + 'eav_entity_type eet
		INNER JOIN ' + @magentoTablePrefix + 'eav_attribute eas
			ON eas.entity_type_id = eet.entity_type_id AND eas.attribute_code = ''''tax_class_id''''
		WHERE eet.entity_type_code = ''''catalog_product'''' 
	'')';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'@tax_class_id_attribute_id INT OUT';

	EXEC sys.sp_executesql 
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@tax_class_id_attribute_id = @tax_class_id_attribute_id OUT

	IF (@C_IS_DEBUG = 1) PRINT N'@tax_class_id_attribute_id = ' + CAST(@tax_class_id_attribute_id AS NVARCHAR);


	-- *************************************************************************************************
	-- *******************		Product Entity - START CODE	      **************************************
	-- *************************************************************************************************

	DECLARE @C_ENTITY_IDS NVARCHAR(MAX);
	DECLARE @C_CATEGORY_IDS NVARCHAR(MAX);

	EXEC [dbo].[sp_GET_CHANNEL_MAGENTO2_KEYS]
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_CHANNEL_MAGENTO_DOMAIN_NAME = @C_CHANNEL_MAGENTO_DOMAIN_NAME,
		@MagentoTablePrefix = @magentoTablePrefix,
		@C_ENTITY_IDS = @C_ENTITY_IDS OUTPUT,
		@C_CATEGORY_IDS = @C_CATEGORY_IDS OUTPUT;
	
	IF (@C_IS_DEBUG = 1)
	BEGIN
		PRINT N'@C_ENTITY_IDS = ' + @C_ENTITY_IDS;
		PRINT N'@C_CATEGORY_IDS = ' + @C_CATEGORY_IDS;
	END

	DECLARE @T_ENTITY_IDS [dbo].[bigint_ID_ARRAY];
	DECLARE @T_CATEGORY_IDS [dbo].[bigint_ID_ARRAY];

	INSERT INTO @T_ENTITY_IDS
	SELECT DISTINCT CAST([VALUE] AS BIGINT)
	FROM [dbo].[fnSplit](@C_ENTITY_IDS, ',');

	INSERT INTO @T_CATEGORY_IDS 
	SELECT DISTINCT CAST([VALUE] AS BIGINT)
	FROM [dbo].[fnSplit](@C_CATEGORY_IDS, ',');
	
	PRINT N'DELETE FROM [magento].[catalog_product_website]';
	DELETE FROM [magento].[catalog_product_website]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND [COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID;

	PRINT N'DELETE FROM [magento].[cataloginventory_stock_item]';
	DELETE FROM [magento].[cataloginventory_stock_item]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	PRINT N'DELETE FROM magento.cataloginventory_stock_status';
	DELETE FROM [magento].[cataloginventory_stock_status]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	PRINT N'DELETE FROM magento.cataloginventory_stock_status_idx';
	DELETE FROM [magento].[cataloginventory_stock_status_idx]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	PRINT N'DELETE FROM magento.catalog_category_product';
	DELETE FROM [magento].[catalog_category_product]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	PRINT N'DELETE FROM magento.catalog_category_product_index';
	DELETE FROM [magento].[catalog_category_product_index]
	WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	--PRINT N'DELETE FROM magento.catalogsearch_fulltext';
	--DELETE FROM [magento].[catalogsearch_fulltext]
	--WHERE (@IS_ALL_ITEMS = 1 OR [product_id] IN (SELECT [ID] FROM @T_ENTITY_IDS))
	--	AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

	
	PRINT N'INSERT INTO [magento].[catalog_product_website]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[catalog_product_website]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID],	@C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID], CPW.[product_id], CPW.[website_id]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id FROM ' + @magentoTablePrefix + 'catalog_product_website
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CPW';
	
	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	PRINT N'INSERT INTO [magento].[cataloginventory_stock_item]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[cataloginventory_stock_item]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID],
		CISI.[item_id],
		CISI.[product_id],
		CISI.[stock_id],
		CISI.[qty],
		CISI.[min_qty],
		CISI.[use_config_min_qty],
		CISI.[is_qty_decimal],
		CISI.[backorders],
		CISI.[use_config_backorders],
		CISI.[min_sale_qty],
		CISI.[use_config_min_sale_qty],
		CISI.[max_sale_qty],
		CISI.[use_config_max_sale_qty],
		CISI.[is_in_stock],
		CISI.[low_stock_date],
		CISI.[notify_stock_qty],
		CISI.[use_config_notify_stock_qty],
		CISI.[manage_stock],
		CISI.[use_config_manage_stock],
		CISI.[stock_status_changed_auto],
		CISI.[use_config_qty_increments],
		CISI.[qty_increments],
		CISI.[use_config_enable_qty_inc],
		CISI.[enable_qty_increments],
		CISI.[is_decimal_divided]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT * FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item 
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CISI'; -- website_id
	
	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID bigint';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	PRINT N'INSERT INTO [magento].[cataloginventory_stock_status]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[cataloginventory_stock_status]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID],
		CISS.[product_id], CISS.[website_id], CISS.[stock_id], CISS.[qty], CISS.[stock_status]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT * FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CISS';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	PRINT N'INSERT INTO [magento].[cataloginventory_stock_status_idx]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[cataloginventory_stock_status_idx]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID],
		CISSI.[product_id], CISSI.[website_id], CISSI.[stock_id], CISSI.[qty], CISSI.[stock_status]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT * FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status_idx
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CISSI';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	PRINT N'INSERT INTO [magento].[catalog_category_product]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[catalog_category_product]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID as C_COMPANY_ECOMM_ID,
		CCP.[category_id], CCP.[product_id], CCP.[position]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT * FROM ' + @magentoTablePrefix + 'catalog_category_product
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CCP'; -- entity_id
	
	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	PRINT N'INSERT INTO [magento].[catalog_category_product_index]';
	SET @C_SQL_INIT_01 = N'
	INSERT INTO [magento].[catalog_category_product_index]
	SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID],
		CCPI.[category_id], CCPI.[product_id], CCPI.[position], CCPI.[is_parent], CCPI.[store_id], CCPI.[visibility]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], N''
		SELECT * FROM ' + @magentoTablePrefix + 'catalog_category_product_index
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS CCPI';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01;

	EXEC sys.sp_executesql
		@C_SQL_INIT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	--PRINT N'INSERT INTO [magento].[catalogsearch_fulltext]';
	--SET @C_SQL_INIT_01 = N'
	--INSERT INTO [magento].[catalogsearch_fulltext] (COMPANY_ID, COMPANY_ECOMM_ID, fulltext_id, product_id, store_id, data_index)
	--SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_ECOMM_ID AS [C_COMPANY_ECOMM_ID],
	--	*  
	--FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
	--	SELECT fulltext_id, product_id, store_id, data_index FROM catalogsearch_fulltext 
	--WHERE ' + CASE @C_ENTITY_IDS WHEN '0'THEN '0 = 0' ELSE 'product_id IN (' + @C_ENTITY_IDS + ') '  END + ''')'
	
	--SET @C_SQL_PARAM_DEFINITION_INIT = 
	--	'@C_COMPANY_ID bigint,
	--	 @C_COMPANY_ECOMM_ID bigint' 

	--IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01
	--EXEC sp_executesql 
	--	@C_SQL_INIT_01,
	--	@C_SQL_PARAM_DEFINITION_INIT,
	--	@C_COMPANY_ID = @C_COMPANY_ID,
	--	@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID


	-- *************************************************************************************************
	-- *******************		Catalog Product Website		  ******************************************
	-- *************************************************************************************************

	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'].[' + @magentoTablePrefix + 'catalog_product_website]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id
		FROM ' + @magentoTablePrefix + 'catalog_product_website'')
	SELECT DISTINCT
		cpe.[entity_id] as [product_id],
		cw.[website_id]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN [magento].[core_website] AS cw WITH (NOLOCK)
		ON cw.[website_id] > 0
		AND cw.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND cw.[COMPANY_ID] = fcsi.[COMPANY_ID]
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT website_id, product_id FROM ' + @magentoTablePrefix + 'catalog_product_website
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cpw
		ON cpw.[product_id] = cpe.[entity_id] AND cpw.[website_id] = cw.[website_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND cpw.[product_id] IS NULL';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sys.sp_executesql 
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Product Entity - Index Eav	************************************
	-- *************************************************************************************************
/* Table [' + @magentoTablePrefix + 'catalog_product_index_eav] do not have any values for tax_class_id attribute
	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'catalog_product_index_eav]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	INSERT OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_id, attribute_id, store_id, value, source_id
		FROM ' + @magentoTablePrefix + 'catalog_product_index_eav'')
	SELECT DISTINCT
		cpe.[entity_id],
		@tax_class_id_attribute_id AS [attribute_id],
		0 AS [store_id], --is default
		0 AS [value],
		cpe.[entity_id] AS [source_id]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[ntity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON CONVERT(NVARCHAR(20), fcsi.[ITEM_ID]) = cpe.[sku]
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_id FROM ' + @magentoTablePrefix + 'catalog_product_index_eav
	) AS cpie
		ON cpe.[entity_id] = cpie.[entity_id]
	WHERE fcsi.COMPANY_ID = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND cpie.[entity_id] IS NULL';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@T_ITEM_IDs dbo.[bigint_ID_ARRAY] READONLY, 
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT,
		@tax_class_id_attribute_id INT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sp_executesql 
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs	 = @T_ITEM_IDs,
		@IS_ALL_ITEMS  = @IS_ALL_ITEMS,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID,
		@tax_class_id_attribute_id = @tax_class_id_attribute_id
*/

	-- *************************************************************************************************
	-- *******************		Catalog Product - Index Price	    ************************************
	-- *************************************************************************************************

	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[catalog_product_index_price]';

	DECLARE @C_TAX_CLASS_VALUE INT = 2; -- Tax class "Taxable Goods"

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	UPDATE cpip
	SET
		cpip.[price] = fcsi.[PRICE],
		cpip.[final_price] = fcsi.[PRICE],
		cpip.[min_price] = fcsi.[PRICE],
		cpip.[max_price] = fcsi.[PRICE],
		cpip.[tier_price] = fcsi.[PRICE]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN [magento].[core_website] AS cw WITH (NOLOCK)
		ON cw.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	INNER JOIN [magento].[customer_group] AS cg WITH (NOLOCK)
		ON cg.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_id, customer_group_id, website_id, tax_class_id, price, final_price, min_price, max_price, tier_price
		FROM ' + @magentoTablePrefix + 'catalog_product_index_price
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE entity_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cpip
		ON cpip.[entity_id] = cpe.[entity_id]
		AND cpip.[customer_group_id] = cg.[customer_group_id]
		AND cpip.[website_id] = cw.[website_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND	fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND	(
			cpip.[price] != fcsi.[PRICE]
			OR cpip.[final_price] != fcsi.[PRICE]
			OR cpip.[min_price] != fcsi.[PRICE]
			OR cpip.[max_price] != fcsi.[PRICE]
			OR cpip.[tier_price] != fcsi.[PRICE]
		);

	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_id, customer_group_id, website_id, tax_class_id, price, final_price, min_price, max_price, tier_price
		FROM ' + @magentoTablePrefix + 'catalog_product_index_price'')
	SELECT DISTINCT
		cpe.[entity_id],
		cg.[customer_group_id],
		cw.[website_id],
		' + CAST(@C_TAX_CLASS_VALUE AS NVARCHAR(20)) + N' AS [tax_class_id],
		fcsi.[PRICE] AS [price],
		fcsi.[PRICE] AS [final_price],
		fcsi.[PRICE] AS [min_price],
		fcsi.[PRICE] AS [max_price],
		fcsi.[PRICE] AS [tier_price]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CONVERT(NVARCHAR(20), fcsi.[ITEM_ID])
	INNER JOIN [magento].[core_website] AS cw WITH (NOLOCK)
		ON cw.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
			AND cw.[website_id] > 0
	INNER JOIN [magento].[customer_group] AS cg WITH (NOLOCK)
		ON cg.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_id, customer_group_id, website_id
		FROM ' + @magentoTablePrefix + 'catalog_product_index_price 
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE entity_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cpip
		ON cpip.[entity_id] = cpe.[entity_id]
		AND cpip.[customer_group_id] = cg.[customer_group_id]
		AND cpip.[website_id] = cw.[website_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND cpip.[entity_id] IS NULL;';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@T_ITEM_IDs dbo.[bigint_ID_ARRAY] READONLY,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
	
	EXEC sys.sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID  = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Inventory Stock - Item	    ************************************
	-- *************************************************************************************************
	
	PRINT N'Check exists m2epro_order table';

	DECLARE @is_exist_table BIT = 0;

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	SELECT TOP(1) @is_exist_table = 1 FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], ''SHOW TABLES LIKE ''''' + @magentoTablePrefix + 'm2epro_order'''''')';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'@is_exist_table BIT OUT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@is_exist_table = @is_exist_table OUT;
	
	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'cataloginventory_stock_item]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT
			product_id,
			stock_id,
			qty,
			min_qty,
			use_config_min_qty,
			is_qty_decimal,
			backorders,
			use_config_backorders,
			min_sale_qty,
			use_config_min_sale_qty,
			max_sale_qty,
			use_config_max_sale_qty,
			is_in_stock,
			low_stock_date,
			notify_stock_qty,
			use_config_notify_stock_qty,
			manage_stock,
			use_config_manage_stock,
			stock_status_changed_auto,
			use_config_qty_increments,
			qty_increments,
			use_config_enable_qty_inc,
			enable_qty_increments,
			is_decimal_divided,
			website_id
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item''
	)
	SELECT DISTINCT
		cpe.[entity_id] AS [product_id],
		1 AS [stock_id],
		IIF(fcsi.[IS_QUALIFIED] = 1, fcsi.[QUANTITY], 0) AS [qty],
		0 AS [min_qty],
		1 AS [use_config_min_qty],
		0 AS [is_qty_decimal],
		0 AS [backorders],
		1 AS [use_config_backorders],
		1.0 AS [min_sale_qty],
		1 AS [use_config_min_sale_qty],
		10000.0 AS [max_sale_qty],
		1 AS [use_config_max_sale_qty],
		IIF(fcsi.[QUANTITY] > 0 AND fcsi.[IS_DELETED] = 0 AND fcsi.[IS_QUALIFIED] = 1, 1, 0) AS [is_in_stock],
		NULL AS [low_stock_date],
		NULL AS [notify_stock_qty],
		1 AS [use_config_notify_stock_qty],
		0 AS [manage_stock],
		1 AS [use_config_manage_stock],
		0 AS [stock_status_changed_auto],
		1 AS [use_config_qty_increments],
		0 AS [qty_increments],
		1 AS [use_config_enable_qty_inc],
		0 AS [enable_qty_increments],
		0 AS [is_decimal_divided],
		0 AS [website_id]
	FROM dbo.[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, item_id
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item 
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cisi
		ON cpe.[entity_id] = cisi.[product_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND	fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND cisi.[item_id] is NULL;

	DECLARE @T_RESERVED_ME2 TABLE (product_id BIGINT, qty_reserved BIGINT);
	' + IIF(ISNULL(@is_exist_table, 0) = 1,
	N'INSERT INTO @T_RESERVED_ME2
	SELECT product_id, qty_reserved
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT oi.product_id, sum(oi.qty_reserved) AS qty_reserved
		FROM ' + @magentoTablePrefix + 'm2epro_order AS o
		INNER JOIN ' + @magentoTablePrefix + 'm2epro_order_item AS oi ON oi.order_id = o.Id
		WHERE o.reservation_state = 1 /* Item is reserved */
		AND o.magento_order_id IS NULL
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'AND oi.product_id IN (' + @C_ENTITY_IDS + N')') + N'
		GROUP BY oi.product_id''
	);', N'') + N'
	
	UPDATE cisi
	SET
		cisi.[qty] = IIF(fcsi.[IS_QUALIFIED] = 1, fcsi.[QUANTITY] - ISNULL(r.[qty_reserved], 0), 0),
		cisi.[is_in_stock] = IIF((fcsi.[QUANTITY] - ISNULL(r.[qty_reserved], 0)) > 0 AND fcsi.[IS_DELETED] = 0 AND fcsi.[IS_QUALIFIED] = 1, 1, 0)
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT qty, is_in_stock, product_id
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item 
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cisi
		ON cpe.[entity_id] = cisi.[product_id]
	LEFT JOIN @T_RESERVED_ME2 R ON r.[product_id] = cisi.[product_id]
	WHERE fcsi.COMPANY_ID = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND	(
			ISNULL(cisi.[qty], 0) != IIF(fcsi.[IS_QUALIFIED] = 1, fcsi.[QUANTITY] - ISNULL(r.[qty_reserved], 0), 0)
			OR (cisi.[is_in_stock] != IIF((fcsi.[QUANTITY] - ISNULL(r.[qty_reserved], 0)) > 0 AND fcsi.[IS_DELETED] = 0 AND fcsi.[IS_QUALIFIED] = 1, 1, 0))
		)';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
	@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
	@C_COMPANY_ID BIGINT,
	@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sp_executesql 
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Inventory Stock - Status	************************************
	-- *************************************************************************************************

	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'cataloginventory_stock_status]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	DECLARE @temp_cataloginventory_stock_status TABLE
	(
		[product_id] NUMERIC (10) NOT NULL,
		[website_id] NUMERIC (5) NOT NULL,
		[stock_id] NUMERIC (5) NOT NULL,
		[qty] NUMERIC (12, 4),
		[stock_status] NUMERIC (5) NOT NULL
	);

	INSERT INTO @temp_cataloginventory_stock_status
	SELECT DISTINCT
		cisi.[product_id],
		cisi.[website_id],
		cisi.[stock_id],
		cisi.[qty],
		cisi.[is_in_stock] AS [stock_status]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT si.product_id, si.stock_id, si.website_id, si.qty, si.is_in_stock, cpe.sku
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item AS si
		INNER JOIN ' + @magentoTablePrefix + 'catalog_product_entity AS cpe
			ON cpe.entity_id = si.product_id
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE si.product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cisi
		ON cisi.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1;

	UPDATE ciss
	SET
		ciss.qty = st.qty,
		ciss.stock_status = st.stock_status
	FROM @temp_cataloginventory_stock_status AS st
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id, stock_id, qty, stock_status
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS ciss
		ON st.[product_id] = ciss.[product_id]
		AND st.[website_id] = ciss.[website_id]
		AND st.[stock_id] = ciss.[stock_id]
	WHERE (
		ISNULL(ciss.[qty], 0) != ISNULL(st.[qty], 0)
		OR ISNULL(ciss.[stock_status], 0) != ISNULL(st.[stock_status], 0)
	);

	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id, stock_id, qty, stock_status
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status''
	)
	SELECT
		st.product_id,
		st.website_id,
		st.stock_id,
		st.qty,
		st.stock_status
	FROM @temp_cataloginventory_stock_status AS st
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id, stock_id
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS ciss
		ON st.[product_id] = ciss.[product_id]
		AND st.[website_id] = ciss.[website_id]
		AND st.[stock_id] = ciss.[stock_id]
	WHERE ciss.[product_id] IS NULL;';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
	@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
	@C_COMPANY_ID BIGINT,
	@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
		
	EXEC sys.sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs	 = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Inventory Stock	- Status Index	********************************
	-- *************************************************************************************************

	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'cataloginventory_stock_status_idx]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	UPDATE cissi
	SET
		cissi.[qty] = cisi.[qty],
		cissi.[stock_status] = cisi.[is_in_stock]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, qty, is_in_stock
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item 
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cisi
		ON cpe.[entity_id] = cisi.[product_id]
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, qty, stock_status
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status_idx
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cissi
		ON cisi.[product_id] = cissi.[product_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND (
			ISNULL(cissi.[qty], 0) != ISNULL(cisi.[qty], 0)
			OR cissi.[stock_status] != cisi.[is_in_stock]
		);

	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id, stock_id, qty, stock_status
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_status_idx'')
	SELECT DISTINCT
		cisi.[product_id],
		cisi.[website_id],
		cisi.[stock_id],
		cisi.[qty],
		cisi.[is_in_stock] AS [stock_status]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT product_id, website_id, stock_id, qty, is_in_stock
		FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cisi
		ON cpe.[entity_id] = cisi.[product_id]
	LEFT JOIN [magento].[cataloginventory_stock_status_idx] AS cissi WITH (NOLOCK)
		ON cissi.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND cisi.[product_id] = cissi.[product_id]
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND cissi.[product_id] IS NULL;';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
		@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';
		
	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sys.sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs	 = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

		
	-- *************************************************************************************************
	-- ****************************      Inventory Source Item      ************************************
	-- *************************************************************************************************

	PRINT N'Check if inventory_source_item table exists';

	DECLARE @is_inventory_source_item_table_exists BIT = 0;

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	SELECT TOP(1) @is_inventory_source_item_table_exists = 1 FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], ''SHOW TABLES LIKE ''''' + @magentoTablePrefix + 'inventory_source_item'''''')';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'@is_inventory_source_item_table_exists BIT OUT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;

	EXEC sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@is_inventory_source_item_table_exists = @is_inventory_source_item_table_exists OUT;

	IF (ISNULL(@is_inventory_source_item_table_exists, 0) = 1)
	BEGIN

		PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'inventory_source_item]';

		SET @C_SQL_BUILD_PRODUCT_01 = N'
		DECLARE @temp_inventory_source_item TABLE
		(
			[source_code] VARCHAR(255) NOT NULL,
			[sku] VARCHAR(64) NOT NULL,
			[quantity] NUMERIC(12,4) NOT NULL,
			[status] NUMERIC(5)
		);

		INSERT INTO @temp_inventory_source_item
		SELECT DISTINCT
			''default'' AS [source_code],
			cisi.[sku],
			cisi.[qty] AS [quantity],
			cisi.[is_in_stock] AS [status]
		FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
			SELECT si.qty, si.is_in_stock, cpe.sku
			FROM ' + @magentoTablePrefix + 'cataloginventory_stock_item AS si
			INNER JOIN ' + @magentoTablePrefix + 'catalog_product_entity AS cpe
				ON cpe.entity_id = si.product_id
			' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE si.product_id IN (' + @C_ENTITY_IDS + N')') + N'''
		) AS cisi;

		UPDATE isi
		SET
			isi.[quantity] = tisi.[quantity],
			isi.[status] = tisi.[status]
		FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
			SELECT source_code, sku, quantity, status
			FROM ' + @magentoTablePrefix + 'inventory_source_item
		'') AS isi
		INNER JOIN @temp_inventory_source_item AS tisi
			ON tisi.[source_code] = isi.[source_code]
			AND tisi.[sku] = isi.[sku]
		WHERE isi.[quantity] != tisi.[quantity]
			OR isi.[status] != tisi.[status];

		INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
			SELECT source_code, sku, quantity, status
			FROM ' + @magentoTablePrefix + 'inventory_source_item
		'')
		SELECT
			tisi.[source_code],
			tisi.[sku],
			tisi.[quantity],
			tisi.[status]
		FROM @temp_inventory_source_item AS tisi
		LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
			SELECT source_item_id, source_code, sku
			FROM ' + @magentoTablePrefix + 'inventory_source_item
		'') AS isi
			ON isi.[source_code] = tisi.[source_code]
			AND isi.[sku] = tisi.[sku]
		WHERE isi.[source_item_id] IS NULL;';

		IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
		
		EXEC [sys].[sp_executesql] @C_SQL_BUILD_PRODUCT_01;

	END
	ELSE
	BEGIN

		PRINT N'inventory_source_item table does not exists';

	END


	-- *************************************************************************************************
	-- *******************		Catalog Category Product	   *****************************************
	-- *************************************************************************************************

	PRINT N'INSERT INTO ['+ @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'catalog_category_product]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	UPDATE cpp
	SET [position] = t.[position]
	FROM (
		SELECT 
			fcsi.[CATEGORY_ID],
			cpe.[entity_id] AS [product_id],
			(ROW_NUMBER() OVER(PARTITION BY cpe.[entity_id] ORDER BY fcsi.[CATEGORY_ID])) - 1 AS [position]
		FROM (
			SELECT DISTINCT
				[COMPANY_ID],
				CAST(fcsi.ITEM_ID AS NVARCHAR(20)) AS [ITEM_ID],
				t.[ITEM_MASTER_CATEGORY_ID] AS [CATEGORY_ID]
			FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
			' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
			OUTER APPLY (
				SELECT [ID] AS [ITEM_MASTER_CATEGORY_ID]
				FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](fcsi.[ITEM_MASTER_CATEGORY_IDs], ''Items'')
				UNION
				SELECT fcsi.[ITEM_MASTER_CATEGORY_ID]
			) AS T
			WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
				AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		) AS fcsi
		INNER JOIN (
			SELECT DISTINCT cpe.[sku], cpe.[entity_id]
			FROM [magento].[catalog_product_entity] cpe WITH (NOLOCK)
			WHERE cpe.[entity_id] IS NOT NULL
				AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
				AND cpe.[COMPANY_ID] = @C_COMPANY_ID
		) AS cpe
			ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
		INNER JOIN [magento].[catalog_category_entity] AS cce WITH (NOLOCK)
			ON cce.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
			AND cce.[entity_id] = fcsi.[CATEGORY_ID]
	) AS T
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT category_id, position, product_id
		FROM ' + @magentoTablePrefix + 'catalog_category_product
		' + IIF(@C_ENTITY_IDS = N'0', N'', N'WHERE product_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cpp
		ON t.[CATEGORY_ID] = cpp.[category_id]
		AND t.[product_id] = cpp.[product_id]
	WHERE cpp.[position] != t.[position];

	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT category_id, product_id, position
		FROM ' + @magentoTablePrefix + 'catalog_category_product'')
	SELECT DISTINCT
		fcsi.[CATEGORY_ID] AS [category_id],
		cpe.[entity_id] AS [product_id],
		(ROW_NUMBER() OVER(PARTITION BY cpe.[entity_id] ORDER BY fcsi.[CATEGORY_ID])) - 1 AS [position]
	FROM (
		SELECT DISTINCT
			[COMPANY_ID],
			CAST(fcsi.[ITEM_ID] AS NVARCHAR(20)) AS [ITEM_ID],
			T.[ITEM_MASTER_CATEGORY_ID] AS [CATEGORY_ID]
		FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
		' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
		OUTER APPLY (
			SELECT [ID] AS [ITEM_MASTER_CATEGORY_ID] FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](fcsi.[ITEM_MASTER_CATEGORY_IDs], ''Items'')
			UNION 
			SELECT fcsi.[ITEM_MASTER_CATEGORY_ID]
		) AS T
		WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
			AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
	) AS fcsi
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN (
		SELECT DISTINCT cce.[entity_id]
		FROM [magento].[catalog_category_entity] AS cce WITH (NOLOCK)
		WHERE cce.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cce
		ON cce.[entity_id] = fcsi.[CATEGORY_ID]
	LEFT JOIN [magento].[catalog_category_product] AS cpp WITH (NOLOCK)
		ON cpp.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND fcsi.[CATEGORY_ID] = cpp.[category_id]
		AND cpe.[entity_id] = cpp.[product_id]
	WHERE cpp.[product_id] IS NULL;';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
	@T_ITEM_IDs dbo.[bigint_ID_ARRAY] READONLY, 
	@C_COMPANY_ID BIGINT,
	@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
	
	EXEC sys.sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Category Product - Index	************************************
	-- *************************************************************************************************

	PRINT N'INSER INTO ['+ @C_CHANNEL_MAGENTO_DOMAIN_NAME + '].[' + @magentoTablePrefix + 'catalog_category_product_index]';

	SET @C_SQL_BUILD_PRODUCT_01 = N'
	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT category_id, product_id, position, is_parent, store_id, visibility
		FROM ' + @magentoTablePrefix + 'catalog_category_product_index'')
	SELECT DISTINCT
		cce.[entity_id] AS [category_id],
		cpe.[entity_id] AS [product_id],
		0 AS [position],
		1 AS [is_parent],
		0 AS [store_id], --is default
		IIF(fcsi.[IS_HIDE_ECOMMERCE_ITEM] = 0, 4, 1) AS [visibility]
	FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	INNER JOIN (
		SELECT DISTINCT cpe.[sku], cpe.[entity_id]
		FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
		WHERE cpe.[entity_id] IS NOT NULL
			AND cpe.[COMPANY_ID] = @C_COMPANY_ID
			AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	) AS cpe
		ON cpe.[sku] = CAST(fcsi.[ITEM_ID] AS NVARCHAR(20))
	INNER JOIN [magento].[catalog_category_entity] AS cce WITH (NOLOCK)
		ON cce.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND fcsi.[ITEM_MASTER_CATEGORY_ID] = cce.[entity_id]
	LEFT JOIN [magento].[catalog_category_product_index] AS ccpi WITH (NOLOCK)
		ON ccpi.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND ccpi.[product_id] = cpe.[entity_id]
		AND ccpi.[store_id] = 0 -- is default
	WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
		AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		AND ccpi.[product_id] IS NULL
	ORDER BY cce.[entity_id], cpe.[entity_id];';

	SET @C_SQL_PARAM_DEFINITION_INIT = N'
	@T_ITEM_IDs dbo.[bigint_ID_ARRAY] READONLY, 
	@C_COMPANY_ID BIGINT,
	@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
		
	EXEC sys.sp_executesql
		@C_SQL_BUILD_PRODUCT_01,
		@C_SQL_PARAM_DEFINITION_INIT,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;


	-- *************************************************************************************************
	-- *******************		Catalog Search Full Text	   *****************************************
	-- *************************************************************************************************

	--PRINT N'INSERT INTO ['+ @C_CHANNEL_MAGENTO_DOMAIN_NAME + N']. catalogsearch_fulltext';

	--SET @C_SQL_BUILD_PRODUCT_01 = N'
	--INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
	--	SELECT product_id, store_id
	--	FROM catalogsearch_fulltext'')
	--SELECT DISTINCT
	--	cpe.[entity_id] AS [product_id],
	--	0 AS [store_id] --is default
	--FROM [dbo].[F_COMPANY_SALES_ITEM] AS fcsi WITH (NOLOCK)
	--' + IIF(@IS_ALL_ITEMS = 1, N'', N'INNER JOIN @T_ITEM_IDs AS IDs ON IDs.[ID] = fcsi.[ITEM_ID]') + N'
	--INNER JOIN (
	--	SELECT DISTINCT cpe.[sku], cpe.[entity_id]
	--	FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
	--	WHERE cpe.[entity_id] IS NOT NULL
	--		AND cpe.[COMPANY_ID] = @C_COMPANY_ID
	--		AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	--) AS cpe
	--	ON CONVERT(NVARCHAR(20), fcsi.[ITEM_ID]) = cpe.[sku]
	--LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
	--	SELECT product_id, store_id, fulltext_id
	--	FROM catalogsearch_fulltext ''
	--) AS csft
	--	ON cpe.[entity_id] = csft.[product_id]
	--	AND csft.[store_id] = 0
	--WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
	--	AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
	--	AND csft.[fulltext_id] IS NULL';

	--SET @C_SQL_PARAM_DEFINITION_INIT = N'
	--	@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
	--	@C_COMPANY_ID BIGINT,
	--	@C_COMPANY_ECOMM_ID BIGINT';

	--IF (@C_IS_DEBUG = 1) PRINT @C_SQL_BUILD_PRODUCT_01;
	
	--EXEC sys.sp_executesql
	--	@C_SQL_BUILD_PRODUCT_01,
	--	@C_SQL_PARAM_DEFINITION_INIT,
	--	@T_ITEM_IDs = @T_ITEM_IDs,
	--	@C_COMPANY_ID = @C_COMPANY_ID,
	--	@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;
		
END

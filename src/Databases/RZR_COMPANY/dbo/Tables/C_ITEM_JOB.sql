CREATE TABLE [dbo].[C_ITEM_JOB] (
    [ITEM_JOB_ID]           BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]        BIGINT         NULL,
    [ITEM_ID]               BIGINT         NULL,
    [BUILD_TYPE_CD]         NVARCHAR (100) NULL,
    [NEXT_BUILD_TYPE_CD]    NVARCHAR (100) NULL,
    [IS_INACTIVE]           BIT            CONSTRAINT [DF_S_ITEM_INVENTORY_JOB_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]            BIT            CONSTRAINT [DF_S_ITEM_INVENTORY_JOB_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]           VARCHAR (550)  NOT NULL,
    [INSERTED_DT]           DATETIME       CONSTRAINT [DF_S_ITEM_INVENTORY_JOB_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]            VARCHAR (150)  NULL,
    [UPDATED_DT]            DATETIME       NULL,
    [DELETED_BY]            VARCHAR (150)  NULL,
    [DELETED_DT]            DATETIME       NULL,
    CONSTRAINT [PK_S_ITEM_INVENTORY_JOB] PRIMARY KEY CLUSTERED ([ITEM_JOB_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX__dbo_C_ITEM_JOB__IS_INACTIVE_ITEM_ID__BUILD_TYPE_CD__INCLUDE__NEXT_BUILD_TYPE_CD]
  ON [dbo].[C_ITEM_JOB] (
    [IS_INACTIVE],[ITEM_ID], [BUILD_TYPE_CD]
  )
  INCLUDE (
    [NEXT_BUILD_TYPE_CD]
  )


GO
CREATE NONCLUSTERED INDEX [IX_C_ITEM_JOB_ITEM_MASTER_ID]
    ON [dbo].[C_ITEM_JOB]([ITEM_MASTER_ID] ASC)
    INCLUDE([ITEM_JOB_ID], [IS_INACTIVE]);


GO

CREATE NONCLUSTERED INDEX [IX_C_ITEM_JOB_ACTIVE_JOBS]
    ON [dbo].[C_ITEM_JOB]([BUILD_TYPE_CD] ASC)
    INCLUDE([ITEM_JOB_ID], [ITEM_MASTER_ID], [IS_INACTIVE], [IS_DELETED]) WHERE ([IS_INACTIVE]=(0));


GO

CREATE NONCLUSTERED INDEX [IX_C_ITEM_JOB_FOR_DELETE]
    ON [dbo].[C_ITEM_JOB]([BUILD_TYPE_CD] ASC) WHERE ([IS_INACTIVE]=(1) AND [IS_DELETED]=(0));


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The intermediate table where we put requests to DW_COM', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_ITEM_JOB';


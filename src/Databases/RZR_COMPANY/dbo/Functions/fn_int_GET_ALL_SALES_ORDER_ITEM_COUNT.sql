-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_ALL_SALES_ORDER_ITEM_COUNT]
(
	@C_SALES_ORDER_ITEM_ID	BIGINT
)
RETURNS INT
AS
BEGIN
	DECLARE 
		@RESULT							INT		= 0		
		,@ITEM_ID						BIGINT
		,@CONDITION_ID					BIGINT
		,@LOCATION_ID					BIGINT
		,@CUSTOMER_ID					BIGINT
		,@ITEM_MASTER_ID				BIGINT
		,@ITEM_MASTER_PRODUCT_CODE_ID	BIGINT
		,@INVENTORY_CAPABILITY_ID		BIGINT

	SELECT
		 @ITEM_ID						= FSOI.ITEM_ID
		,@CONDITION_ID					= FSOI.CONDITION_ID
		,@LOCATION_ID					= FSOI.LOCATION_ID
		,@CUSTOMER_ID					= FSOI.CUSTOMER_ID
		,@ITEM_MASTER_ID				= PM.ITEM_MASTER_ID
		,@ITEM_MASTER_PRODUCT_CODE_ID	= FSOI.ITEM_MASTER_PRODUCT_CODE_ID
		,@INVENTORY_CAPABILITY_ID		= FSOI.INVENTORY_CAPABILITY_ID
	FROM F_SALES_ORDER_ITEM		AS FSOI	WITH(NOLOCK)	
	INNER JOIN [dbo].[F_PRODUCT_MASTER] AS PM WITH (NOLOCK)
		ON FSOI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
	WHERE FSOI.SALES_ORDER_ITEM_ID = @C_SALES_ORDER_ITEM_ID	

	SELECT 
		@RESULT = ISNULL(CAST(SUM(ISNULL(QTY, 0)) AS INT), 0)
	FROM 
	(
		SELECT
			FII.SkuId
			,FII.ConditionId
			,FII.ItemMasterId
			,FII.LocationId
			,FII.CustomerId
			,codeI.ITEM_MASTER_PRODUCT_CODE_ID
			,fii.RevisionId
			,ISNULL(FII.Qty, 0) as QTY
		FROM [dbo].[vw_InventoryDetailsMain]					as FII		WITH(NOLOCK)
		LEFT JOIN [F_ITEM_INVENTORY_PRODUCT_CODE]		codeI	WITH (NOLOCK)			
			ON FII.Id = codeI.ITEM_INVENTORY_ID								
		WHERE FII.IsAvailable = 1 AND FII.ItemMasterId = @ITEM_MASTER_ID
	) AS ITEMS
	WHERE (@ITEM_ID IS NULL OR ITEMS.SkuId = @ITEM_ID) --PICKLIST ITEM ATTR
		AND ITEMS.ConditionId = @CONDITION_ID		--PICKLIST ITEM ATTR
		AND (@ITEM_MASTER_PRODUCT_CODE_ID IS NULL OR ITEMS.ITEM_MASTER_PRODUCT_CODE_ID = @ITEM_MASTER_PRODUCT_CODE_ID) --PICK LIST ITEM ATTR
		AND (@INVENTORY_CAPABILITY_ID IS NULL	  OR ITEMS.RevisionId = @INVENTORY_CAPABILITY_ID) --PICK LIST ITEM ATTR
		AND (@LOCATION_ID IS NULL				  OR ITEMS.LocationId = @LOCATION_ID) --PICK LIST ITEM ATTR
		AND (@CUSTOMER_ID IS NULL				  OR ITEMS.CustomerId = @CUSTOMER_ID) --PICK LIST ITEM ATTR

	RETURN @RESULT
END
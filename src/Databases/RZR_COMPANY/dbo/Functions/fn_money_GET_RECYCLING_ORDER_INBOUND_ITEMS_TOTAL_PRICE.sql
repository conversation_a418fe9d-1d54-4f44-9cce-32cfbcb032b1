-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <02/03/2014>
-- Description: <Returns the Sub Total of the account>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_ORDER_INBOUND_ITEMS_TOTAL_PRICE]
(
	@ORDER_ID				BIGINT,
	@SETTLEMENT_STATE_ID	INT
)
RETURNS FLOAT
BEGIN
	DECLARE @PRICE FLOAT;
	
	--"Initial" OR "Processed"
	IF (@SETTLEMENT_STATE_ID = 1 OR @SETTLEMENT_STATE_ID = 2) BEGIN
		SELECT @PRICE = SUM([dbo].fn_money_GET_RECYCLING_ITEM_PRICE(OI.RECYCLING_ORDER_ITEM_ID, @SETTLEMENT_STATE_ID))
		FROM F_RECYCLING_ORDER_ITEM					OI	WITH(NOLOCK)
		LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
			ON FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
		WHERE OI.RECYCLING_ORDER_ID = @ORDER_ID
			AND OI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
			AND OI.IS_MERGED = 0
			AND (@SETTLEMENT_STATE_ID = 2 OR @SETTLEMENT_STATE_ID = 1 AND OI.IS_INCLUDE_IN_INITIAL = 1)
			AND FSL.InputId IS NULL
	END

	--"Processed & Audit"
	ELSE IF (@SETTLEMENT_STATE_ID = 3) BEGIN
		SELECT @PRICE = SUM(BOTH.SUBTOTAL)
		FROM
		(
			SELECT SUM([dbo].fn_money_GET_RECYCLING_ITEM_PRICE(OI.RECYCLING_ORDER_ITEM_ID, @SETTLEMENT_STATE_ID)) AS SUBTOTAL
			FROM F_RECYCLING_ORDER_ITEM					OI	WITH(NOLOCK)
			LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
				ON FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
			WHERE OI.RECYCLING_ORDER_ID = @ORDER_ID 
				AND OI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
				AND OI.IS_MERGED = 0
				AND FSL.InputId IS NULL
				AND NOT EXISTS(
					SELECT TOP(1) 1
					FROM [recycling].F_Asset
					WHERE [RecyclingOrderItemId] = OI.RECYCLING_ORDER_ITEM_ID
						AND [IsGetAfterSettle] = 0
					)
			UNION ALL
			SELECT
				SUM(OI.[ItemPrice] *
					CASE OI.[PriceTypeId]
						WHEN 1 THEN OI.[Weight]
						WHEN 2 THEN OI.[Quantity]
						WHEN 3 THEN 1
					END) AS SUBTOTAL
			FROM [recycling].F_Asset					OI	WITH(NOLOCK)
			LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM		ROI	WITH(NOLOCK)
				ON OI.RecyclingOrderItemId = ROI.RECYCLING_ORDER_ITEM_ID
			LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
				ON FSL.LotId = ROI.RECYCLING_ORDER_ITEM_ID
			WHERE OI.[IsGetAfterSettle] = 0
				AND ROI.[RECYCLING_ORDER_ID] = @ORDER_ID
				AND ROI.NO_EXTERNAL_LOTS_MERGED_INTO = 1
				AND ROI.IS_DELETED = 0
				AND FSL.InputId IS NULL
		) AS BOTH
	END

	--"Audit"
	ELSE IF (@SETTLEMENT_STATE_ID = 4) BEGIN
		SELECT @PRICE = SUM(OI.[ItemPrice] *
			CASE OI.[PriceTypeId]
				WHEN 1 THEN OI.[Weight]
				WHEN 2 THEN OI.[Quantity]
				WHEN 3 THEN 1
			END)
		FROM [recycling].F_Asset					OI	WITH(NOLOCK)
		LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM		ROI	WITH(NOLOCK)
			ON OI.RecyclingOrderItemId = ROI.RECYCLING_ORDER_ITEM_ID
		LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
			ON FSL.LotId = ROI.RECYCLING_ORDER_ITEM_ID
		WHERE OI.[IsGetAfterSettle] = 0
			AND ROI.[RECYCLING_ORDER_ID] = @ORDER_ID
			AND ROI.NO_EXTERNAL_LOTS_MERGED_INTO = 1
			AND ROI.IS_DELETED = 0
			AND FSL.InputId IS NULL
	END

	--"Initial & Audit"
	ELSE IF (@SETTLEMENT_STATE_ID = 5) BEGIN
		SELECT @PRICE = SUM(BOTH.SUBTOTAL)
		FROM
		(
			SELECT SUM([dbo].fn_money_GET_RECYCLING_ITEM_PRICE(OI.RECYCLING_ORDER_ITEM_ID, @SETTLEMENT_STATE_ID)) AS SUBTOTAL
			FROM F_RECYCLING_ORDER_ITEM					OI	WITH(NOLOCK)
			LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
				ON FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
			WHERE OI.RECYCLING_ORDER_ID = @ORDER_ID
				AND OI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
				AND OI.IS_MERGED = 0
				AND OI.IS_INCLUDE_IN_INITIAL = 1
				AND FSL.InputId IS NULL
			UNION ALL
			SELECT
				SUM(OI.[ItemPrice] *
					CASE OI.[PriceTypeId]
						WHEN 1 THEN OI.[Weight]
						WHEN 2 THEN OI.[Quantity]
						WHEN 3 THEN 1
				END) AS SUBTOTAL
			FROM [recycling].F_Asset					OI	WITH(NOLOCK)
			LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM		ROI	WITH(NOLOCK)
				ON OI.RecyclingOrderItemId = ROI.RECYCLING_ORDER_ITEM_ID
			LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
				ON FSL.LotId = ROI.RECYCLING_ORDER_ITEM_ID
			WHERE OI.[IsGetAfterSettle] = 0
				AND ROI.[RECYCLING_ORDER_ID] = @ORDER_ID
				AND ROI.NO_EXTERNAL_LOTS_MERGED_INTO = 1
				AND ROI.IS_DELETED = 0
				AND FSL.InputId IS NULL
		) AS BOTH
	END

	RETURN ISNULL(@PRICE, 0);
END
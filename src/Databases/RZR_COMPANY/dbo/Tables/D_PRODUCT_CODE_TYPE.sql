CREATE TABLE [dbo].[D_PRODUCT_CODE_TYPE] (
    [PRODUCT_CODE_TYPE_ID]   INT            NOT NULL,
    [PRODUCT_CODE_TYPE_NAME] NVARCHAR (128) NOT NULL,
    [INSERTED_BY]            VARCHAR (150)  CONSTRAINT [DF_D_PRODUCT_CODE_TYPE_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]            DATETIME       CONSTRAINT [DF_D_PRODUCT_CODE_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_D_PRODUCT_CODE_TYPE] PRIMARY KEY CLUSTERED ([PRODUCT_CODE_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The codes types list', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_PRODUCT_CODE_TYPE';


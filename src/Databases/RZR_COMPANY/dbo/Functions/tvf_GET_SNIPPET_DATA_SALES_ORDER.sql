CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_SALES_ORDER]
(
	@C_SALES_ORDER_ID BIGINT
)
RETURNS @T_RESULT TABLE
(
  SALES_ORDER_ID				BIGINT,
  SHIPPING_ID					BIGINT,
  SALES_ORDER_AUTO_NAME			VARCHAR(250),
  IS_RECYCLING					BIT,
  SHIP_TO_CUSTOMER_NAME			VARCHAR(150),
  PURSHASE_ORDER_NUMBER			NVARCHAR(250),
  SHIPPING_DATE					DATETIME,
  CUSTOMER_ID					BIGINT,
  SALES_ORDER_CUSTOMER_NAME		NVARCHAR(150),
  CUSTOMER_PRIMARY_CONTACT_ID	BIGINT,
  IsFinanceRelease				VARCHAR(50)
)
AS
BEGIN
    INSERT INTO @T_RESULT
    (
		SALES_ORDER_ID,
		SHIPPING_ID,
		SALES_ORDER_AUTO_NAME,
		IS_RECYCLING,
		SHIP_TO_CUSTOMER_NAME,
		PURSHASE_ORDER_NUMBER,
		SHIPPING_DATE,
		CUSTOMER_ID,
		SALES_ORDER_CUSTOMER_NAME,
		CUSTOMER_PRIMARY_CONTACT_ID,
		IsFinanceRelease
    )
    SELECT
      SO.SALES_ORDER_ID,
	  SOS.SHIPPING_ID,
	  SO.SALES_ORDER_NUMBER,
	  CASE WHEN (SO.RECYCLING_ORDER_ID IS NULL)
		   THEN 0
		   ELSE 1
	  END	AS IS_RECYCLING,
	  C.CUSTOMER_NAME,
	  SO.PO_NUMBER,
	  SOS.SHIPPING_DATE,
	  SO.CUSTOMER_ID,
	  SOC.CUSTOMER_NAME,
	  NULL,
	  case when SO.IsFinanceRelease = 1 then 'Yes' else 'No' end
    FROM dbo.F_SALES_ORDER				SO  WITH (NOLOCK)
	LEFT JOIN [F_SHIPPING]				SOS WITH (NOLOCK)
		ON SO.SALES_ORDER_ID = SOS.SALES_ORDER_ID
	LEFT JOIN F_CUSTOMER				C	WITH (NOLOCK)
		ON C.CUSTOMER_ID = SOS.RECIPIENT_CUSTOMER_ID
	LEFT JOIN F_CUSTOMER				SOC	WITH (NOLOCK)
		ON SOC.CUSTOMER_ID = SO.CUSTOMER_ID
    WHERE SO.SALES_ORDER_ID = @C_SALES_ORDER_ID

	UPDATE @T_RESULT SET
		CUSTOMER_PRIMARY_CONTACT_ID = FCC.CUSTOMER_CONTACT_ID
	FROM F_CUSTOMER_CONTACT	FCC	WITH(NOLOCK)
	INNER JOIN @T_RESULT	TR
		ON TR.CUSTOMER_ID = FCC.CUSTOMER_ID
		AND FCC.IS_MAIN = 1
		AND FCC.IS_DELETED = 0
		AND FCC.IS_INACTIVE = 0
		--AND FCC.IS_NOTIFICATIONS_ENABLED = 1
		--AND FCC.IS_MAILING_ENABLED = 1

    RETURN;
END

-- =============================================
-- Author:	 <Author,,Name>
-- Create date: <Create Date, ,>
-- Description: <Description, ,>
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_SKU_QTY_ON_HAND]
    (@SKU_ID BIGINT)
RETURNS INT
AS
BEGIN
	RETURN (
		SELECT 
		   SUM(ISNULL(FII.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 0)) AS QTY_ON_HAND
		FROM	  F_ITEM							FI		WITH (NOLOCK)
		LEFT JOIN F_ITEM_MASTER_SKU_ATTRB			FIMSA	WITH (NOLOCK)
			ON FIMSA.ITEM_MASTER_SKU_ATTRB_ID = FI.ITEM_MASTER_SKU_ATTRB_ID		
		INNER JOIN F_ITEM_INVENTORY	FII		WITH (NOLOCK)
		  ON FI.ITEM_MASTER_ID = FII.ITEM_MASTER_ID
		  AND FI.CONDITION_ID = FII.CONDITION_ID
		  AND ISNULL(FIMSA.ITEM_MASTER_SKU_ATTRB_CD, 0) = ISNULL(FII.ITEM_INVENTORY_SKU_ATTRB, 0)
		left join [dbo].[F_SALES_ORDER_ITEM] soi with (nolock)
			on FII.SalesOrderItemId = soi.SALES_ORDER_ITEM_ID
		LEFT JOIN dbo.F_ORDER_ORDER_SUBJECT_TYPE	OOST	WITH (NOLOCK)
			ON  OOST.ENTITY_TYPE_ID = 1 AND soi.SALES_ORDER_ID = OOST.ORDER_ID			
		WHERE FI.ITEM_ID = @SKU_ID and
			fii.IS_DELETED = 0 and
			CASE
				WHEN  FII.ITEM_STATUS_ID IN (1) 
					AND (FII.AUDIT_STATUS_ID IN (0, 1, 2))
					and (FII.SalesOrderItemId IS NULL OR OOST.ENTITY_SUBJECT_TYPE_ID = 12) THEN 1 --12 is repair sales order
						ELSE 0					
			END	 = 1
	)
END
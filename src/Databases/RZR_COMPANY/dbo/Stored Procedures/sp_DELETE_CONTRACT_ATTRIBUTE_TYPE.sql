-- Create Procedure sp_DELETE_CONTRACTS

-- =============================================
-- Author:		I.Orobets
-- Create date: 11/30/2015
-- Description: Delete Contracts Attribute Type
-- =============================================
CREATE PROCEDURE [dbo].[sp_DELETE_CONTRACT_ATTRIBUTE_TYPE]
	@C_CONTRACT_ATTRIBUTE_TYPE_IDS bigint_ID_ARRAY READONLY
AS
BEGIN

	SET XACT_ABORT ON
    BEGIN TRAN

		DECLARE @CONTRACT_CAPABILITY_IDS bigint_ID_ARRAY
		INSERT INTO @CONTRACT_CAPABILITY_IDS (ID)
		SELECT
			CC.CONTRACT_CAPABILITY_ID
		FROM F_CONTRACT_ATTRIBUTE_TYPE  CAT WITH(NOLOCK)
		LEFT JOIN F_CONTRACT_CAPABILITY CC  WITH(NOLOCK)
			ON CC.CONTRACT_ATTRIBUTE_TYPE_ID = CAT.CONTRACT_ATTRIBUTE_TYPE_ID
		WHERE CC.CONTRACT_ATTRIBUTE_TYPE_ID IN (SELECT ID FROM @C_CONTRACT_ATTRIBUTE_TYPE_IDS)
		
		DELETE FROM F_CONTRACT_CAPABILITY
		WHERE CONTRACT_CAPABILITY_ID IN (SELECT ID FROM @CONTRACT_CAPABILITY_IDS)

		DELETE FROM F_CONTRACT_ATTRIBUTE_TYPE
		WHERE CONTRACT_ATTRIBUTE_TYPE_ID IN (SELECT ID FROM @C_CONTRACT_ATTRIBUTE_TYPE_IDS)

	COMMIT TRAN

END
CREATE TABLE [dbo].[F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY] (
    [ITEM_INVENTORY_RECYCLING_ITEM_HISTORY_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_INVENTORY_ID]                        BIGINT         NOT NULL,
    [RECYCLING_ORDER_ITEM_ID]                  BIGINT         NULL,
    [INSERTED_BY]                              NVARCHAR (250) NOT NULL,
    [INSERTED_DT]                              DATETIME       NOT NULL,
    CONSTRAINT [PK_F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_RECYCLING_ITEM_HISTORY_ID] ASC),
    CONSTRAINT [FK_F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY_F_RECYCLING_ORDER_ITEM] FOREIGN KEY ([RECYCLING_ORDER_ITEM_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID])
);






GO
CREATE NONCLUSTERED INDEX [IX_RECYCLING_ORDER_ITEM_ID_ITEM_INVENTORY_ID]
    ON [dbo].[F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY]([RECYCLING_ORDER_ITEM_ID] ASC, [ITEM_INVENTORY_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ID]
    ON [dbo].[F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY]([ITEM_INVENTORY_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores which items came from which recycling items', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY';


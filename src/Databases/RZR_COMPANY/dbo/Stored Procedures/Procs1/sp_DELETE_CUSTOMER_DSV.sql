
CREATE PROCEDURE [dbo].[sp_DELETE_CUSTOMER_DSV] 
	@DSV_ID BIGINT	
AS
BEGIN

	IF (not EXISTS(
			SELECT TOP(1) 1 
			FROM dbo.F_CUSTOMER_DSV	CA WITH (NOLOCK) 
			WHERE CA.CUSTOMER_DSV_ID = @DSV_ID ))
	BEGIN
		SELECT 'Main Billing and Shipping addresses can''t be deleted.' AS [MESSAGE]
		RETURN;
	END	
	

	BEGIN TRANSACTION
		DELETE FROM dbo.F_CUSTOMER_DSV
		WHERE CUSTOMER_DSV_ID = @DSV_ID
	COMMIT TRANSACTION

	SELECT NULL AS [MESSAGE]			
						
END
CREATE procedure [rzrapi].[sp_GetSkuAll] 
	@Limit				INT			= 20,
	@Offset				INT			= 0,
	@Ids				[dbo].[bigint_ID_ARRAY] readonly ,
	@EcomAvailableQtyGreaterThan	BIGINT	= NULL
AS
BEGIN

	declare @IsIdsSet bit = iif(exists(select top(1) 1 from @Ids), 1, 0)

	;WITH m_data AS (
	   SELECT 
		   fi.ITEM_ID									AS Id
		   ,fi.ITEM_MASTER_ID							AS ItemMasterId
		   ,fi.ITEM_DESC								AS ItemTitle
		   ,fi.LONG_DESC								AS LongDescription
		   ,IIF(fi.UNIQUE_INVERNTORY_ID IS NULL, 0, 1)	AS IsUnique
		   ,fi.IS_QUALIFIED								AS IsQualified	   
		   ,fi.PRICE									AS Price
		   ,fi.QUANTITY									AS Quantity
		   ,fi.ITEM_CONDITION_DESC						AS [Description]
		   ,fi.IS_FEATURED								AS IsFeatured
		   ,fi.IS_DEAL_10_PERC_OFF_EBAY					AS IsBestBuy
		   ,fi.ITEM_TYPE_ID								AS ItemTypeId
		   ,fi.IS_HIDE_ECOMMERCE_ITEM					AS HideFromECommerce
		   ,fi.[ITEM_NUMBER]							AS ItemNumber
		   ,fi.[CONDITION_ID]							AS ConditionId
		   ,fi.[QUANTITY_AVAILABLE]						AS QuantityAvailable   
		   ,fi.[QUANTITY_ALLOCATED]						AS QuantityAllocated   
		   ,fi.[QUANTITY_AVAILABLE_FOR_ECOMMERCE]		AS QuantityAvailableForEcommerce
		   ,fi.[QUALIFIED_DT]							AS QualifiedDate
		FROM dbo.F_ITEM								fi  WITH (NOLOCK) 
		WHERE fi.[IS_DELETED] = 0
			AND fi.[IS_INACTIVE] = 0
			and (@IsIdsSet = 0 or exists(select top(1) 1 from @Ids ui where ui.[ID] = fi.ITEM_ID ))
			AND (@EcomAvailableQtyGreaterThan IS NULL OR fi.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] > @EcomAvailableQtyGreaterThan)
	)
	
	SELECT TOP(1)
		COUNT(Id)				AS Id		
		,NULL AS ItemMasterId
		,NULL AS ItemTitle
		,NULL AS LongDescription
		,NULL AS IsUnique
		,NULL AS IsQualified	   
		,NULL AS Price
		,NULL AS Quantity
		,NULL AS [Description]
		,NULL AS IsFeatured
		,NULL AS IsBestBuy
		,NULL AS ItemTypeId
		,NULL AS HideFromECommerce
		,NULL AS ItemNumber
		,NULL AS ConditionId
		,NULL AS QuantityAvailable   
		,NULL AS QuantityAllocated   
		,NULL AS QuantityAvailableForEcommerce
		,NULL AS QualifiedDate
	FROM m_data m	
	UNION ALL
	select *
	from 
	(	
	SELECT
		M.*
	from m_data M 		
	order by M.Id
	OFFSET (@Offset) ROWS
	FETCH NEXT (@Limit) ROWS ONLY
	) t
	
END
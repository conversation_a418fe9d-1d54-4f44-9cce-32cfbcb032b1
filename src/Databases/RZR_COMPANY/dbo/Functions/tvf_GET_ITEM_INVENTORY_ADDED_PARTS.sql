-- =============================================
-- Author:		O.Evseev
-- Create date: 12/30/16
-- Description: collects the added parts of the inventory
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_ITEM_INVENTORY_ADDED_PARTS]
(		
	 @C_PARENT_PARTS	bigint_ID_ARRAY READONLY
	,@C_INCLUDE_PARENTS BIT = 0
	,@MAX_LEVEL			INT = 64
)
RETURNS @C_ADDED_PARTS TABLE (ID BIGINT, [VALUE] BIGINT) --bigint_PARE_ARRAY
AS
BEGIN

	WITH CTE AS (
		-- The anchor members
		SELECT 
			 CPP.ID	AS ITEM_INVENTORY_ID 
			,0		AS LEVEL_ID			
		FROM @C_PARENT_PARTS	CPP
		-- Add the child parts recursively
		UNION ALL
		SELECT
			 FII.ITEM_INVENTORY_ID	AS ITEM_INVENTORY_ID
			,C.LEVEL_ID + 1 		AS LEVEL_ID		
		FROM CTE									C
		INNER JOIN dbo.F_ITEM_INVENTORY_ADDED_PART	FIIAP	WITH(NOLOCK)
			ON FIIAP.PARENT_ITEM_INVENTORY_ID = C.ITEM_INVENTORY_ID
		INNER JOIN dbo.F_ITEM_INVENTORY				FII		WITH(NOLOCK)
			ON  FIIAP.PART_ITEM_INVENTORY_ID = FII.ITEM_INVENTORY_ID
			AND	FII.IS_DELETED   = 0
		WHERE C.LEVEL_ID <= @MAX_LEVEL
	)

	INSERT INTO @C_ADDED_PARTS (ID, [VALUE])
	SELECT DISTINCT
		 C.ITEM_INVENTORY_ID
		,C.LEVEL_ID
	FROM CTE					C
	LEFT JOIN @C_PARENT_PARTS	CPP
		ON CPP.ID = C.ITEM_INVENTORY_ID
	WHERE @C_INCLUDE_PARENTS = 1
	   OR CPP.ID IS NULL
	
	RETURN;
END
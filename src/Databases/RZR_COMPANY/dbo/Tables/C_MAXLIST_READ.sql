CREATE TABLE [dbo].[C_MAXLIST_READ] (
    [MAXLIST_READ_ID]           BIGINT        IDENTITY (1, 1) NOT NULL,
    [MAXLIST_READ_READ_DT]      DATETIME      NOT NULL,
    [MAXLIST_READ_FILENAME]     VARCHAR (100) NOT NULL,
    [MAXLIST_READ_RECORD_COUNT] BIGINT        NOT NULL,
    [IS_INACTIVE]               BIT           CONSTRAINT [DF_C_MAXLIST_READ_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                BIT           CONSTRAINT [DF_C_MAXLIST_READ_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]               VARCHAR (50)  CONSTRAINT [DF_C_MAXLIST_READ_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]               DATETIME      CONSTRAINT [DF_C_MAXLIST_READ_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                VARCHAR (50)  NULL,
    [UPDATED_DT]                DATETIME      NULL,
    [DELETED_BY]                VARCHAR (50)  NULL,
    [DELETED_DT]                DATETIME      NULL,
    CONSTRAINT [PK_C_MAXLIST_READ] PRIMARY KEY CLUSTERED ([MAXLIST_READ_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Seems is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_MAXLIST_READ';


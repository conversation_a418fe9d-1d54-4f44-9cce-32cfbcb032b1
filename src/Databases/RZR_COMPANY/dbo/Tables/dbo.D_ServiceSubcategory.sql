CREATE TABLE [dbo].[D_ServiceSubcategory] (
    [Id]                         BIGINT         IDENTITY (1, 1) NOT NULL,
    [Name]                       NVARCHAR (128) NOT NULL,
    [IsRadioButton]              BIT            CONSTRAINT [DF_D_ServiceSubcategory_IsRadioButton] DEFAULT ((0)) NOT NULL,
    [IsDeleted]                  BIT            CONSTRAINT [DF_D_ServiceSubcategory_IsDeleted] DEFAULT ((0)) NOT NULL,
    [InsertedBy]                 NVARCHAR (150) CONSTRAINT [DF_D_ServiceSubcategory_InsetedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]               DATETIME       CONSTRAINT [DF_D_ServiceSubcategory_InsetedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]           BIGINT         CONSTRAINT [DF_D_ServiceSubcategory_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]           BIGINT         CONSTRAINT [DF_D_ServiceSubcategory_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                  NVARCHAR (150) NULL,
    [UpdatedDate]                DATETIME       NULL,
    [UpdatedByUserId]            BIGINT         NULL,
    [UpdatedByUserIp]            BIGINT         NULL,
    [DeletedBy]                  NVARCHAR (150) NULL,
    [DeletedDate]                DATETIME       NULL,
    [DeletedByUserId]            BIGINT         NULL,
    [DeletedByUserIp]            BIGINT         NULL,
    [ServiceId]                  INT            NULL,
    CONSTRAINT [PK_D_ServiceSubcategory] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_D_ServiceSubcategory_tb_USER_DeletedByUserId] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_D_ServiceSubcategory_tb_USER_InsertedByUserId] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_D_ServiceSubcategory_tb_USER_UpdatedByUserId] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_D_ServiceSubcategory_C_RECYCLING_ITEM_SERVICE_TYPE] FOREIGN KEY ([ServiceId]) REFERENCES [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] ([SERVICE_TYPE_ID])
);
GO


EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Subcategory for Services', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ServiceSubcategory';


CREATE TABLE [dbo].[F_SalesOrderRepUser] (
    [SalesOrderRepUserId]       BIGINT IDENTITY (1, 1) NOT NULL,
    [SalesOrderId]              BIGINT NOT NULL,
    [RepUserId]                 BIGINT NOT NULL,
    CONSTRAINT [PK_F_SalesOrderRepUser] PRIMARY KEY CLUSTERED ([SalesOrderRepUserId] ASC),
    CONSTRAINT [FK_F_SalesOrderRepUser_F_SALES_ORDER] FOREIGN KEY ([SalesOrderId]) REFERENCES [dbo].[F_SALES_ORDER] ([SALES_ORDER_ID]),
    CONSTRAINT [FK_F_SalesOrderRepUser_tb_User] FOREIGN KEY ([RepUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);



GO
CREATE NONCLUSTERED INDEX [IX_SalesOrderId]
    ON [dbo].[F_SalesOrderRepUser]([SalesOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RepUserId]
    ON [dbo].[F_SalesOrderRepUser]([RepUserId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'One sales order may have multiples sales reps, one sales rep can have several sales orders', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_SalesOrderRepUser';

GO
CREATE TRIGGER [dbo].[trg_SalesOrderRepUser_PUT_CHANGE]
    ON  [dbo].[F_SalesOrderRepUser]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN
    declare @ids dbo.bigint_id_array;
   
    insert into @ids
	select distinct
		isnull(i.[SalesOrderId], d.[SalesOrderId])
	from inserted		i
	full join deleted	d
		on i.[SalesOrderRepUserId] = d.[SalesOrderRepUserId]	
	where isnull(i.[RepUserId], 0) <> isnull(d.[RepUserId], 0)
    -- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 9 --SalesOrder
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

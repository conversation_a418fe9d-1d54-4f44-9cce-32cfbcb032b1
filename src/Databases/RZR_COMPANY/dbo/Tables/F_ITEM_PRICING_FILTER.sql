CREATE TABLE [dbo].[F_ITEM_PRICING_FILTER] (
    [ITEM_PRICING_FILTER_ID]  BIGINT          IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]          BIGINT          NOT NULL,
    [ITEM_NUMBER]             VARCHAR (50)    NOT NULL,
    [SOURCE_SYS_ID]           BIGINT          NOT NULL,
    [SALES_PRICE_AVG]         DECIMAL (19, 2) NULL,
    [SALES_PRICE_AVG_STD_DEV] DECIMAL (19, 2) NULL,
    [IS_INACTIVE]             BIT             CONSTRAINT [DF_F_ITEM_PRICING_FILTER_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]              BIT             CONSTRAINT [DF_F_ITEM_PRICING_FILTER_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]             VARCHAR (150)   CONSTRAINT [DF_F_ITEM_PRICING_FILTER_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]             DATETIME        CONSTRAINT [DF_F_ITEM_PRICING_FILTER_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]              VARCHAR (150)   NULL,
    [UPDATED_DT]              DATETIME        NULL,
    [DELETED_BY]              VARCHAR (150)   NULL,
    [DELETED_DT]              DATETIME        NULL,
    CONSTRAINT [PK_F_ITEM_PRICING_FILTER] PRIMARY KEY CLUSTERED ([ITEM_PRICING_FILTER_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems it is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_PRICING_FILTER';


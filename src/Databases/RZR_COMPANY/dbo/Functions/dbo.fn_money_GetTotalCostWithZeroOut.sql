
CREATE FUNCTION [dbo].[fn_money_GetTotalCostWithZeroOut]
	(
		@ItemPrice			float
		,@PriceTypeid		int
		,@WeightReceived	float
		,@WeightTare		float
		,@ItemCount			int
		,@IsZeroOut			bit
	)
RETURNS FLOAT
BEGIN
	DECLARE @Total money = 0.0
	
	SELECT
		@Total = iif(@IsZeroOut = 1 and @ItemPrice < 0, 0, @ItemPrice * 
							CASE @PriceTypeid
								WHEN 1 THEN ISNULL(@WeightReceived - @WeightTare, 0)
								WHEN 2 THEN @ItemCount
								WHEN 3 THEN 1.0
								ELSE 0.0
							END)

	RETURN isnull(@Total, 0)
END
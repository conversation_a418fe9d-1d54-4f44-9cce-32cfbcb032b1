CREATE TABLE [dbo].[F_SIGNATURE_TEMPLATE] (
    [SIGNATURE_TEMPLATE_ID]      BIGINT        IDENTITY (1, 1) NOT NULL,
    [SIGNATURE_TEMPLATE_NAME]    VARCHAR (50)  NOT NULL,
    [SIGNATURE_PERSON_NAME]      VARCHAR (50)  NOT NULL,
    [SIGNATURE_PERSON_JOB_TITLE] VARCHAR (50)  NOT NULL,
    [SIGNATURE_IMAGE_BASE_64]    VARCHAR (MAX) NULL,
    [IS_INACTIVE]                BIT           CONSTRAINT [DF_F_SIGNATURE_TEMPLATE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                 BIT           CONSTRAINT [DF_F_SIGNATURE_TEMPLATE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                VARCHAR (150) CONSTRAINT [DF_F_SIGNATURE_TEMPLATE_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]                DATETIME      CONSTRAINT [DF_F_SIGNATURE_TEMPLATE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 VARCHAR (150) NULL,
    [UPDATED_DT]                 DATETIME      NULL,
    [DELETED_BY]                 VARCHAR (150) NULL,
    [DELETED_DT]                 DATETIME      NULL,
    CONSTRAINT [PK_F_SIGNATURE_TEMPLATE] PRIMARY KEY CLUSTERED ([SIGNATURE_TEMPLATE_ID] ASC)
);




GO
CREATE UNIQUE NONCLUSTERED INDEX [UX__F_SIGNATURE_TEMPLATE__SIGNATURE_TEMPLATE_NAME__ACTIVE]
    ON [dbo].[F_SIGNATURE_TEMPLATE]([SIGNATURE_TEMPLATE_NAME] ASC) WHERE ([IS_DELETED]=(0) AND [IS_INACTIVE]=(0));


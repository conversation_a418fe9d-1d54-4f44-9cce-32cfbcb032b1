/*
	declare @warehouseIds bigint_ID_ARRAY
	insert into @warehouseIds
	select WAREHOUSE_ID
	from dbo.D_WAREHOUS<PERSON>

	declare @statusIds bigint_ID_ARRAY
	insert into @statusIds
	values (1), (2)

	exec [rzrapi].[sp_GetRecyclingOrders]
		@IsDebug = 0
		,@StatusIds = @StatusIds
		,@WarehouseIds = @WarehouseIds
		,@OrderBy = N'QtyStatePrograms'
*/
CREATE procedure [rzrapi].[sp_GetRecyclingOrders]
	@Limit						INT	= 20,
	@Offset						BIGINT	= 0,
	@StatusIds					bigint_ID_ARRAY	READONLY,
	@FromUpdatedDate			DATETIME = NULL,
	@ToUpdatedDate				DATETIME = NULL,
	@FromCreatedDate			DATETIME = NULL,
	@ToCreatedDate				DATETIME = NULL,
	@FromReceivedDate			DATETIME = NULL,
	@ToReceivedDate				DATETIME = NULL,
	@FromReceiveDate			DATETIME = NULL,
	@ToReceiveDate				DATETIME = NULL,
	@FromScheduledDate			DATETIME = NULL,
	@ToScheduledDate			DATETIME = NULL,
	@FromSettledDate			DATETIME = NULL,
	@ToSettledDate				DATETIME = NULL,
	@FromDeliveryDate			DATETIME = NULL,
	@ToDeliveryDate				DATETIME = NULL,
	@SettlementStateId			INT	= NULL,
	@WarehouseIds				bigint_ID_ARRAY	READONLY,
	@PriorityIds				bigint_ID_ARRAY	READONLY,
	@CustomerIds				bigint_ID_ARRAY	READONLY,
	@RecyclingOrderIds			bigint_ID_ARRAY	READONLY,
	@PortalQuoteIds				bigint_ID_ARRAY	READONLY,
	@SearchTerm					NVARCHAR(128) = NULL,
	@SortOrder					NVARCHAR(8) = 'Desc',
	@OrderBy					NVARCHAR(128) = 'Id',
	@includingFromConsumable	bit = 0,
	@RestrictUserIds			bigint_ID_ARRAY	READONLY,
	@OrderType					int = 1,
	@ServiceTypeIds				bigint_ID_ARRAY	READONLY,
	@RecyclingQuoteStatusIds	bigint_ID_ARRAY	READONLY,
	@IsDebug					bit = 0
AS
BEGIN

	DECLARE @isStatusSet bit = iif(exists(select top(1) 1 from @statusIds), 1, 0);
	DECLARE @filterById bit = iif(exists(select top(1) 1 from @recyclingOrderIds), 1, 0);
	DECLARE @isPrioritySet bit = iif(exists(select top(1) 1 from @priorityIds), 1, 0);
	DECLARE @isCustomerSet bit = iif(exists(select top(1) 1 from @customerIds), 1, 0);
	DECLARE @isPortalQuoteSet bit = iif(exists(select top(1) 1 from @portalQuoteIds), 1, 0);
	DECLARE @isUserRestrictSet bit = iif(exists(select top(1) 1 from @RestrictUserIds), 1, 0); 
	DECLARE @isServiceTypeSet bit = iif(exists(select top(1) 1 from @ServiceTypeIds), 1, 0); 
	DECLARE @isRecyclingQuoteStatusSet bit = iif(exists(select top(1) 1 from @RecyclingQuoteStatusIds), 1, 0);

	SET @searchTerm = N'%' + NULLIF(RTRIM(LTRIM(@searchTerm)), N'') + N'%';

	declare @q0 nvarchar(max) = N'
	declare
		@utcNow	datetime = getutcdate()

	;WITH m_data AS (
		SELECT 
			FRO.RECYCLING_ORDER_ID									AS Id
			,FROI.AUTO_NAME											AS AutoName
			,FROI.IS_QUOTE											AS IsQuote
			,FROI.IS_REVISION										AS IsRevision
			,FRO.CUSTOMER_ID										AS CustomerId
			,FC.CUSTOMER_CODE										AS CustomerCode
			,FC.CUSTOMER_NAME										AS CustomerName
			,FROI.StatusId											AS StatusId
			,IIF(FROI.IS_QUOTE = 1, crqs.[Name], IOS.[Name])		AS StatusName
			,FRO.[USER_ID]											AS RepUserId
			,dbo.fn_str_GET_USER_AUTO_NAME(FRO.[USER_ID], 1)		AS RepUserName
			,REP.Email												AS RepUserEmail
			,ROC.CONTRACT_ID										AS ContractId
			,C.[CONTRACT_NAME]										AS ContractName
			,FRO.SO_TERM_ID											AS SoTransactionTermId
			,SOT.TRANSACTION_TERM_VALUE								AS SoTransactionTermName
			,FRO.WAREHOUSE_ID										AS WarehouseId
			,W.WAREHOUSE_CD											AS WarehouseName
			,W.TIME_ZONE_ID											AS WarehouseTimeZoneId
			,FROI.CUSTOMER_ADDRESS_ID								AS CustomerAddressId
			,CLCA.LOCATION_NAME										AS CustomerLocationName
			,CA.[CustomerAddress]									AS CustomerAddress
			,FROI.CUSTOMER_CONTACT_ID								AS OnsiteContactId
			,FROI.CUSTOMER_ALTERNATIVE_CONTACT_ID					AS AlternativeContactId
			,FROI.SHIP_TO_CUSTOMER_ID								AS ShipToCustomerId
			,SC.CUSTOMER_NAME										AS ShipToCustomerName
			,FROI.SHIP_TO_ADDRESS_ID								AS ShipToAddressId
			,FROI.Distance                                          AS Distance
	        ,dbo.fn_money_GET_RECYCLING_INITIAL_AMOUNT(FRO.RECYCLING_ORDER_ID) AS InitialServicesPrice
			,FRO.BOL_NUMBER											AS BolNumber
			,FRO.PO_NUMBER											AS PoNumber
			,CRPD.DURATION											AS Duration
			,FROI.WEIGHT_LBS										AS TotalEstimatedWeight
			,FROI.PALLET_COUNT										AS PalletCount
			,FRO.INTERNAL_COMMENT									AS InternalComments
			,FROI.EQUIPMENT_NOTES									AS EquipmentNotes
			,FROI.[PRIORITY]										AS [PriorityId]
			,RP.RECYCLING_PRIORITY_CD								AS PriorityName
			,FT.TRUCK_ID											AS TruckId
			,FROI.TRUCK_TYPE_ID										AS TruckTypeId
			,FROI.BILLING_TYPE_ID									AS BillingTypeId
			,FROI.SERVICE_TYPE_ID									AS ServiceTypeId
			,ROST.RECYCLING_ORDER_SERVICE_TYPE_CD					AS ServiceTypeName
			,FROI.PACKAGING_TYPE_ID									AS PackagingTypeId
			,DRPT.PACKAGING_TYPE_DESC								AS PackagingTypeName
			,FROI.LOGISTIC_TYPE_ID									AS LogisticTypeId
			,RLT.[RECYCLING_LOGISTIC_TYPE_CD]						AS LogisticTypeName
			,FROI.FREIGHT_CLASS_ID									AS FreightClassId
			,UL.UNLOADING_LOCATION_ID								AS UnloadingLocationId
			,FROI.SHIP_TO_CONTACT_ID								AS ShipToContactId
			,FROI.CUSTOMER_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID	AS CustomerLoadingDockEquipmentTypeId
			,FROI.SHIP_TO_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID	AS ShipToLoadingDockEquipmentTypeId
			,FROI.PACKAGE_LENGTH									AS PackageLength
			,FROI.PACKAGE_WIDTH										AS PackageWidth
			,FROI.PACKAGE_HEIGHT									AS PackageHeight
			,IIF((DRPT.[LENGTH] IS NOT NULL AND DRPT.[WIDTH] IS NOT NULL AND DRPT.[HEIGHT] IS NOT NULL), 0, 1)	AS IsDimensionsEditable
			,ISNULL(FST.STATEMENT_TERM_ID, (
				SELECT TOP(1) STATEMENT_TERM_ID
				FROM F_STATEMENT_TERM	 WITH(NOLOCK)
				WHERE STATEMENT_TERM_TYPE_ID = 2 -- Quote Terms
					AND IS_DEFAULT_FOR_TYPE_ID = 1))				AS TransactionTermId
			,FROI.SLA_DAYS																AS SlaDays
			,FROI.SLA_BEGINNING															AS SlaBeginning
			,[dbo].[fn_datetime_GetSlaDate](FROI.[SLA_BEGINNING], FROI.[INSERTED_DT], FROI.[RECIEVE_DATE],
					FROI.[COMPLETED_DT], FROI.[DELIVERY_DT], FROI.[SLA_DAYS])			AS SlaDate
			,IIF(FROI.[StatusId] = 6, NULL,
				[dbo].[fn_int_GetDaysBetweenInboundOrderDueDateBySla](FROI.[SLA_BEGINNING], FROI.[INSERTED_DT], FROI.[RECIEVE_DATE],
					FROI.[COMPLETED_DT], FROI.[DELIVERY_DT], FROI.[SLA_DAYS]))			AS DueDate
			,FROI.AccountingSla															AS AccountingSlaDays
			,IIF(FROI.[StatusId] = 6 OR FROI.READY_TO_PRICE_DT is null,	NULL,
				dateadd(dd, FROI.AccountingSla, FROI.READY_TO_PRICE_DT))				AS AccountingSlaDate
			,IIF(FROI.[StatusId] = 6 OR FROI.READY_TO_PRICE_DT is null,	NULL,
				FROI.AccountingSla - datediff(dd, FROI.READY_TO_PRICE_DT, @utcNow))		AS AccountingDueDate
			,FROI.IS_COMMODITY_RESTRICTED							AS IsCommodityRestricted
			,COALESCE(FRO.WORK_INSTRUCTIONS, FC.WORK_INSTRUCTIONS)	AS WorkInstructions
			,FRO.[REFERENCE_1]										AS [Reference1]
			,FRO.[REFERENCE_2]										AS [Reference2]
			,FRO.[REFERENCE_3]										AS [Reference3]
			,FRO.[Reference4]										AS [Reference4]
			,FRO.[Reference5]										AS [Reference5]
			,FRO.[Reference6]										AS [Reference6]
			,FRO.[Reference7]										AS [Reference7]
			,FRO.[Reference8]										AS [Reference8]
			,FROI.[ESTIMATED_DELIVERY_DATE]							AS [EstimatedDeliveryDate]
			,FROI.[DELIVERY_POINT]									AS [DeliveryPoint]
			,froi.ActualPickupTime									AS ActualPickupTime
			,froi.ActualArrivalTime									AS ActualArrivalTime
			,FROI.[DELIVERY_DT]										AS DeliveryDate
			,FRO.CurrencyExchangeId
			,cur.Id													AS CurrencyId
			,cur.FullNameShort										AS CurrencyName
			,fro.SMEApprover
			,fro.CBREDisposalRequestWO
			,fro.DisposalRequestIDName
			,fro.DepartmentTurningEquipment
			,fro.CustomerContractNo
			,fro.RECYCLING_SETTLEMENT_STATE_ID						AS SettlementStateId
			,ST.RECYCLING_SETTLEMENT_STATE_CD						AS SettlementStateName
			,FROW.WEIGHT_RECEIVED												AS WeightReceived
			,FROW.WEIGHT_TARE													AS WeightTare
			,ISNULL(FROW.WEIGHT_RECEIVED, 0) - ISNULL(FROW.WEIGHT_TARE, 0)		AS WeightNet
			,FROW.ITEM_COUNT										as ItemCount
			,froi.UPDATED_DT										AS UpdatedDate
			,froi.[INSERTED_DT]										AS CreatedDate
			,FRO.PICKUP_START_DATE									AS PickupStartDateTime
			,FRO.PICKUP_END_DATE									AS PickupEndDateTime
			,FROI.RECIEVE_DATE										AS ReceiveStartDate
			,froi.COMPLETED_DT										AS ReceiveEndDate
			,fro.[SETTLE_DATE]										AS SettledDate
			,fro.RECEIVING_NOTES									AS ReceivingNotes
			,fro.ExternalId			
			,FOS.[OrderScheduledId]									AS PortalQuoteId
			,iif(FROI.[StatusId] = 8, 1, 0)							AS [IsReadyToBePriced]
			,froi.IsFromConsumable									AS IsFromConsumable
			,FROI.SEAL_NUMBER										AS ExpectedSealNumber
			,FROI.ACT_SEAL_NUMBER									AS ActualSealNumber
			,ISNULL(LINKED_PO.[PurchaseOrderNumber], LINKED_SO.[SalesOrderNumber]) AS PurchaseOrderNumber
			,AuditUser.[UserName]									AS [Employee]
			,FROI.[QUOTE_ID]										AS [QuoteId]
			,QUOTE.[AUTO_NAME]										AS [QuoteAutoName]
			,csrRep.FirstName + N'' '' + csrRep.LastName			AS CsrRepUserName
			,ISNULL(spq.QtyStatePrograms, 0)						AS QtyStatePrograms
			,j.Id													as JobId
			,FROI.AutoPriceRuleSetId								as AutoPriceRuleSetId
			,FROI.BusinessUnitId								    as BusinessUnitId
			,CBU.Name												as BusinessUnitName
			,FROI.ProbabilityPercentage								as ProbabilityPercentage
			,crqs.Id												as RecyclingQuoteStatusId
			,iif(FROI.[TakebackOrderRequestId] is null, 0, 1)		as IsRemoteReturn
			,json_query((
				select 
					ffc.FREIGHT_CARRIER_ID				as Id,
					ffc.[CUSTOMER_ID]					as CarrierCustomerId,
					ffc.CUSTOMER_CONTACT_ID				as CarrierContactId,
					ffc.CUSTOMER_ADDRESS_ID				as CarrierAddressId,
					ffc.[IsBlindDropShipment],
					ffc.[Insurance],
					ffc.[ApInvoiceDate],
					ffc.CLIENT_CHARGE					as ClientCharge,
					ffc.QUOTE_RATE						as QuoteRate,
					ffc.FREIGHT_NOTES					as FreightNotes,
					CAR.CUSTOMER_NAME					as CarrierCustomerName,
					po.PURCHASE_ORDER_ID				as POId,
					po.AUTO_NAME						as PoName
				from [dbo].[F_FREIGHT_CARRIER]	ffc with(nolock)
				LEFT JOIN F_CUSTOMER			CAR	WITH(NOLOCK)
					ON FFC.CUSTOMER_ID = CAR.CUSTOMER_ID
				left join dbo.F_PURCHASE_ORDER				po WITH (NOLOCK)
					on po.RECYCLING_ORDER_ID = ffc.[RecyclingOrderId] 
						and ffc.CUSTOMER_ID = po.CUSTOMER_ID	
				where ffc.[RecyclingOrderId] = FRO.RECYCLING_ORDER_ID
				for json path, include_null_values
		)) AS Freights
	        ,iif(FRO.[RECYCLING_SETTLEMENT_STATE_ID] is null, 0.0, isnull(
                [dbo].[fn_money_GET_RECYCLING_ORDER_SUBTOTAL](FRO.[RECYCLING_SETTLEMENT_STATE_ID], FRO.[RECYCLING_ORDER_ID]),
                0.0))                                           as [AmountOwed],
				json_query([recycling].[fn_nvarchar_json_OrderItemTags](FRO.RECYCLING_ORDER_ID	,null))	 AS Tags

		FROM F_RECYCLING_ORDER										FRO	WITH(NOLOCK)
		left join @recyclingOrderIds								rids
			on rids.ID = fro.RECYCLING_ORDER_ID
		inner join F_RECYCLING_ORDER_INBOUND						FROI WITH(NOLOCK)
			ON FRO.RECYCLING_ORDER_ID = FROI.RECYCLING_ORDER_ID
		INNER JOIN tb_User											REP	WITH(NOLOCK)
			ON FRO.[USER_ID] = REP.UserID
		INNER JOIN F_CUSTOMER										FC WITH(NOLOCK)
			ON FRO.CUSTOMER_ID = FC.CUSTOMER_ID ' + 		
		iif(@isUserRestrictSet = 1, 
				N'INNER JOIN @RestrictUserIds AS resrcust
					ON FC.[REP_ID] = resrcust.[Id]
				INNER JOIN @RestrictUserIds AS cuser
					ON fro.[USER_ID] = cuser.[Id]'
				, N''),					
	@q1 nvarchar(max) = N'
		left join @customerIds										cust 
			on FRO.CUSTOMER_ID = cust.ID
		LEFT JOIN F_RECYCLING_ORDER_CONTRACT						ROC	WITH(NOLOCK)
			ON ROC.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
			AND ROC.IsFirstApplied = 1
		LEFT JOIN dbo.F_CONTRACT									C WITH(NOLOCK)
			ON ROC.CONTRACT_ID = C.CONTRACT_ID
		LEFT JOIN dbo.C_RECYCLING_PICKUP_DURATION					CRPD WITH(NOLOCK)
			ON FROI.DURATION_ID = CRPD.DURATION_TYPE_ID
		LEFT JOIN F_CUSTOMER										SC WITH(NOLOCK)
			ON FROI.SHIP_TO_CUSTOMER_ID = SC.CUSTOMER_ID	
		LEFT JOIN C_RECYCLING_ORDER_SERVICE_TYPE					ROST WITH(NOLOCK)
			ON FROI.SERVICE_TYPE_ID = ROST.RECYCLING_ORDER_SERVICE_TYPE_ID		
		LEFT JOIN @ServiceTypeIds									sers 
			ON FROI.SERVICE_TYPE_ID = sers.ID
		LEFT JOIN F_CUSTOMER_ADDRESS								SA WITH(NOLOCK)
			ON FROI.SHIP_TO_ADDRESS_ID = SA.CUSTOMER_ADDRESS_ID
		LEFT JOIN	F_CUSTOMER_CONTACT								SCC	WITH(NOLOCK)
			ON SCC.CUSTOMER_ID = FROI.SHIP_TO_CUSTOMER_ID
			AND SCC.IS_MAIN = 1
		LEFT JOIN	F_TRUCK											FT WITH(NOLOCK)
			ON FT.TRUCK_ID = FROI.TRUCK_ID
		LEFT JOIN	F_UNLOADING_LOCATION							UL WITH(NOLOCK)
			ON UL.UNLOADING_LOCATION_ID = FROI.UNLOADING_LOCATION_ID
		LEFT JOIN	D_RECYCLING_PACKAGING_TYPE						DRPT WITH(NOLOCK)
			ON DRPT.RECYCLING_PACKAGING_TYPE_ID = FROI.PACKAGING_TYPE_ID
		LEFT JOIN F_STATEMENT_TERM									FST WITH(NOLOCK)
			ON FST.STATEMENT_TERM_ID = FRO.STATEMENT_TERM_ID
			AND FST.STATEMENT_TERM_TYPE_ID = 2 -- Quote Terms
		LEFT JOIN [dbo].[D_CurrencyExchange]						ce WITH(NOLOCK)
			ON ce.Id = FRO.CurrencyExchangeId
		LEFT JOIN [dbo].[C_Currency]								cur WITH(NOLOCK)
			ON cur.Id = ce.ForeignCurrencyId
		LEFT JOIN F_CUSTOMER_ADDRESS								CLCA WITH(NOLOCK)
			ON FROI.CUSTOMER_ADDRESS_ID = CLCA.CUSTOMER_ADDRESS_ID
		LEFT JOIN @statusIds										S
			ON S.ID = froi.StatusId
		LEFT JOIN recycling.C_InboundOrderStatus					IOS WITH(NOLOCK)
			ON FROI.StatusId = IOS.Id
		LEFT JOIN dbo.C_CUSTOMER_TRANSACTION_TERM					SOT WITH(NOLOCK)
			ON FRO.SO_TERM_ID = SOT.CUSTOMER_TRANSACTION_TERM_ID
		LEFT JOIN dbo.D_WAREHOUSE									W WITH(NOLOCK)
			ON FRO.WAREHOUSE_ID = W.WAREHOUSE_ID
		LEFT JOIN [dbo].[C_RECYCLING_LOGISTIC_TYPE]					RLT WITH (NOLOCK)
			ON RLT.[RECYCLING_LOGISTIC_TYPE_ID] = FROI.[LOGISTIC_TYPE_ID]
		LEFT JOIN dbo.C_RECYCLING_PRIORITY							RP WITH(NOLOCK)
			ON FROI.[PRIORITY] = RP.RECYCLING_PRIORITY_ID
		LEFT JOIN dbo.C_RECYCLING_SETTLEMENT_STATE					ST WITH(NOLOCK)
			ON fro.RECYCLING_SETTLEMENT_STATE_ID = ST.RECYCLING_SETTLEMENT_STATE_ID
		LEFT JOIN @priorityIds										P
			ON FROI.[PRIORITY] = P.[ID]
		OUTER APPLY (
			SELECT TOP(1) [AuditorUserId]
			FROM [recycling].[F_AuditSession] AS FAS WITH(NOLOCK)
			WHERE FAS.[RecyclingOrderId] = FRO.[RECYCLING_ORDER_ID]
		)															AuditSession
		LEFT JOIN [dbo].[tb_User]									AuditUser WITH(NOLOCK)
			ON [AuditUser].UserId = [AuditSession].AuditorUserId
		LEFT JOIN dbo.tb_User										csrRep WITH (NOLOCK)
			ON csrRep.UserId = isnull(FRO.[CSR_REP_ID], FC.[CSR_REP_ID])
		LEFT JOIN [dbo].[F_RECYCLING_ORDER_INBOUND]					QUOTE WITH(NOLOCK)
			ON FROI.[QUOTE_ID] = QUOTE.[RECYCLING_ORDER_ID]
		LEFT JOIN [cp].[F_ORDER_SCHEDULED]							FOS WITH(NOLOCK)
			ON (FRO.[RECYCLING_ORDER_ID] = FOS.[RecyclingOrderId] OR FROI.[QUOTE_ID] = FOS.[RecyclingOrderId])
		LEFT JOIN @portalQuoteIds									PQ
			ON FOS.[OrderScheduledId] = PQ.[ID]
		LEFT JOIN recycling.C_BusinessUnit							CBU WITH(NOLOCK)
            on FROI.BusinessUnitId = CBU.Id
		LEFT JOIN [dbo].[C_RecyclingQuoteStatus]					crqs WITH(NOLOCK)
			ON FROI.QuoteStatusId = crqs.Id
		LEFT JOIN @RecyclingQuoteStatusIds							rqs
			ON rqs.ID = FROI.QuoteStatusId
		LEFT JOIN (
			select
				count(1) QtyStatePrograms,
				RecyclingOrderId
			from recycling.F_InboundOrderStateProgram with(nolock)
			group by RecyclingOrderId
		)	spq
			on spq.RecyclingOrderId = fro.RECYCLING_ORDER_ID
		left join [recycling].[F_InboundOrderJob] j with (nolock)
			on fro.RECYCLING_ORDER_ID = j.[RecyclingOrderId]
		',
		@q2 nvarchar(max) = N'
		LEFT JOIN (
			SELECT 
				ROIT.INBOUND_ORDER_ID AS RECYCLING_ORDER_ID,
				SUM(ISNULL(I.WEIGHT_RECEIVED, 0)) AS WEIGHT_RECEIVED,
				SUM(ISNULL(I.WEIGHT_TARE, 0)) AS WEIGHT_TARE,
				SUM(ISNULL(I.ITEM_COUNT, 0)) as ITEM_COUNT
			FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
			INNER JOIN F_RECYCLING_ORDER_ITEM_TRANSFER ROIT WITH(NOLOCK)
				ON I.RECYCLING_ORDER_ITEM_ID = ROIT.RECYCLING_ORDER_ITEM_ID
			INNER JOIN F_RECYCLING_ORDER O WITH(NOLOCK)
			ON ROIT.INBOUND_ORDER_ID = O.RECYCLING_ORDER_ID
				AND I.PARENT_ID IS NULL AND I.IS_MERGED = 0
			WHERE O.IS_TRANSFER = 1
			GROUP BY ROIT.INBOUND_ORDER_ID		
			UNION ALL
			SELECT 
				I.RECYCLING_ORDER_ID AS RECYCLING_ORDER_ID,
				SUM(ISNULL(I.WEIGHT_RECEIVED, 0)) AS WEIGHT_RECEIVED,
				SUM(ISNULL(I.WEIGHT_TARE, 0)) AS WEIGHT_TARE,
				SUM(ISNULL(I.ITEM_COUNT, 0)) as ITEM_COUNT
			FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
			INNER JOIN F_RECYCLING_ORDER O WITH(NOLOCK)
			ON I.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID 
				AND I.PARENT_ID IS NULL AND I.IS_MERGED = 0
			WHERE O.IS_TRANSFER = 0
			GROUP BY I.RECYCLING_ORDER_ID
		) FROW ON FROW.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID		
		

		LEFT JOIN (
			SELECT 
				CUSTOMER_ADDRESS_ID as CustomerAddressId,
				CAST(ca.LOCATION_NAME + 
				'' ('' + ca.STREET_1 + '', '' + 
				ca.CITY + 
				ISNULL('' ''+ ca.POSTAL_CODE, '''') + 
				ISNULL('' ''+ ca.COUNTRY, '''') + '')'' AS NVARCHAR(1024)) CustomerAddress
			FROM dbo.F_CUSTOMER_ADDRESS ca WITH(NOLOCK)
		)	CA
			ON CA.CustomerAddressId = FROI.[CUSTOMER_ADDRESS_ID]
		LEFT JOIN (
			SELECT FPO.[RECYCLING_ORDER_ID], MAX(FPO.[AUTO_NAME]) AS [PurchaseOrderNumber]
			FROM [dbo].[F_PURCHASE_ORDER] AS FPO WITH(NOLOCK)
			WHERE FPO.[RECYCLING_ORDER_ID] IS NOT NULL
			GROUP BY FPO.[RECYCLING_ORDER_ID]		 
		) LINKED_PO
			ON FRO.[RECYCLING_ORDER_ID] = LINKED_PO.[RECYCLING_ORDER_ID]
		LEFT JOIN (
			SELECT FSO.[RECYCLING_ORDER_ID], MAX(FSO.[SALES_ORDER_NUMBER]) AS [SalesOrderNumber]
			FROM [dbo].[F_SALES_ORDER] AS FSO WITH(NOLOCK)
			WHERE FSO.[RECYCLING_ORDER_ID] IS NOT NULL
			GROUP BY FSO.[RECYCLING_ORDER_ID]	
		) LINKED_SO
			ON FRO.[RECYCLING_ORDER_ID] = LINKED_SO.[RECYCLING_ORDER_ID]
		LEFT JOIN (
			SELECT U.UserID,
				   U.FirstName + N'' '' + U.LastName + N'' (''+ U.UserName + '')''	AS USER_NAME
			FROM dbo.tb_User U WITH(NOLOCK)
		) TU
			ON TU.UserID = FRO.[USER_ID]
		',

		@q3 nvarchar(max) = N'
		WHERE   FROI.IS_INACTIVE = 0
			AND FROI.IS_DELETED = 0
			AND ((@OrderType = 0) or 
				(@OrderType = 1 and FROI.IS_QUOTE = 0) or 
				(@OrderType = 2 and FROI.IS_QUOTE = 1))

			and (@includingFromConsumable = 1 or @includingFromConsumable = 0 and FROI.[IsFromConsumable] = 0)
			and (@isStatusSet = 0 OR S.ID is not null)
			and (@isPrioritySet = 0 OR p.ID is not null)
			and (@isCustomerSet = 0 OR cust.ID is not null)
			and (@isPortalQuoteSet = 0 OR PQ.[ID] is not null)
			and (@isServiceTypeSet = 0 OR sers.ID is not null)
			and (@isRecyclingQuoteStatusSet = 0 OR rqs.ID is not null)
			and (@fromUpdatedDate is null OR @fromUpdatedDate <= froi.UPDATED_DT)
			and (@toUpdatedDate is null or @toUpdatedDate >= froi.UPDATED_DT)

			and (@fromCreatedDate is null or @fromCreatedDate <= froi.INSERTED_DT)
			and (@toCreatedDate is null or @toCreatedDate >= froi.INSERTED_DT)

			and (@fromReceivedDate is null or @fromReceivedDate <= froi.COMPLETED_DT)
			and (@toReceivedDate is null or @toReceivedDate >= froi.COMPLETED_DT)

			and (@fromReceiveDate is null or @fromReceiveDate <= FROI.RECIEVE_DATE)
			and (@toReceiveDate is null or @toReceiveDate >= FROI.RECIEVE_DATE)

			and (@fromScheduledDate is null or @fromScheduledDate <= FRO.PICKUP_START_DATE)
			and (@toScheduledDate is null or @toScheduledDate >= FRO.PICKUP_START_DATE)

			and (@fromSettledDate is null or @fromSettledDate <= fro.[SETTLE_DATE])
			and (@toSettledDate is null or @toSettledDate >= fro.[SETTLE_DATE])

			and (@fromDeliveryDate is null or @fromDeliveryDate <= FROI.[DELIVERY_DT])
			and (@toDeliveryDate is null or @toDeliveryDate >= FROI.[DELIVERY_DT])

			and (@settlementStateId is null OR @settlementStateId = fro.RECYCLING_SETTLEMENT_STATE_ID)
			and (@filterById = 0 OR rids.ID is not null)
			and (FRO.[WAREHOUSE_ID] IS NULL
				OR EXISTS(
					SELECT TOP(1) 1
					FROM @warehouseIds AS WarehouseIds
					WHERE WarehouseIds.[Id] = FRO.[WAREHOUSE_ID]
				)
			)			
			and (@searchTerm is null
				or FROI.AUTO_NAME like @searchTerm
				or FC.CUSTOMER_NAME like @searchTerm
				or FRO.[REFERENCE_1] like @searchTerm
				or FRO.[REFERENCE_2] like @searchTerm
				or FRO.[REFERENCE_3] like @searchTerm
				or FRO.[BOL_NUMBER] like @searchTerm
				or FRO.[ExternalId] like @searchTerm
				or CA.[CustomerAddress] like @searchTerm
				or QUOTE.[AUTO_NAME] like @searchTerm
				or TU.USER_NAME like @searchTerm
				or AuditUser.[UserName] like @searchTerm
				or C.[CONTRACT_NAME] like @searchTerm
				or ISNULL(LINKED_PO.[PurchaseOrderNumber], LINKED_SO.[SalesOrderNumber]) like @searchTerm
				or csrRep.FirstName + N'' '' + csrRep.LastName like @searchTerm
			)			
	)',

	@q4 nvarchar(max) = N'
	SELECT TOP(1)
		COUNT(1)	AS Id
		,NULL		AS AutoName
		,NULL		AS IsQuote
		,NULL		AS IsRevision
		,NULL		AS CustomerId
		,NULL		AS CustomerCode
		,NULL		AS CustomerName
		,NULL		AS StatusId
		,NULL		AS StatusName
		,NULL		AS RepUserId
		,NULL		AS RepUserName
		,NULL		AS RepUserEmail
		,NULL		AS ReceiveStartDate
		,NULL		AS ContractId
		,NULL		AS ContractName
		,NULL		AS SoTransactionTermId
		,NULL		AS SoTransactionTermName
		,NULL		AS WarehouseId
		,NULL		AS WarehouseName
		,NULL		AS WarehouseTimeZoneId
		,NULL		AS CustomerAddressId
		,NULL		AS CustomerLocationName
		,NULL		AS CustomerAddress
		,NULL		AS OnsiteContactId
		,NULL		AS AlternativeContactId
		,NULL		AS ShipToCustomerId
		,NULL		AS ShipToCustomerName
		,NULL		AS ShipToAddressId
		,NULL		AS Distance
		,NULL		AS InitialServicesPrice
		,NULL		AS BolNumber
		,NULL		AS PoNumber
		,NULL		AS PickupStartDateTime
		,NULL		AS PickupEndDateTime
		,NULL		AS Duration
		,NULL		AS TotalEstimatedWeight
		,NULL		AS PalletCount
		,NULL		AS InternalComments
		,NULL		AS EquipmentNotes
		,NULL		AS [PriorityId]
		,NULL		AS PriorityName
		,NULL		AS TruckId
		,NULL		AS TruckTypeId
		,NULL		AS BillingTypeId
		,NULL		AS ServiceTypeId		
		,NULL		AS ServiceTypeName
		,NULL		AS PackagingTypeId
		,NULL		AS PackagingTypeName
		,NULL		AS LogisticTypeId
		,NULL		AS LogisticTypeName
		,NULL		AS FreightClassId
		,NULL		AS UnloadingLocationId
		,NULL		AS ShipToContactId
		,NULL		AS CustomerLoadingDockEquipmentTypeId
		,NULL		AS ShipToLoadingDockEquipmentTypeId
		,NULL		AS PackageLength
		,NULL		AS PackageWidth
		,NULL		AS PackageHeight
		,NULL		AS IsDimensionsEditable
		,NULL		AS TransactionTermId
		,NULL		AS SlaDays
		,NULL		AS SlaBeginning
		,NULL		AS SlaDate
		,NULL		AS DueDate
		,NULL		AS AccountingSlaDays
		,NULL		AS AccountingSlaDate
		,NULL		AS AccountingDueDate
		,NULL		AS IsCommodityRestricted
		,NULL		AS WorkInstructions
		,NULL		AS [Reference1]
		,NULL		AS [Reference2]
		,NULL		AS [Reference3]
		,NULL		AS [Reference4]
		,NULL		AS [Reference5]
		,NULL		AS [Reference6]
		,NULL		AS [Reference7]
		,NULL		AS [Reference8]
		,NULL		AS [EstimatedDeliveryDate]
		,NULL		AS [DeliveryPoint]
		,NULL		AS ActualPickupTime
		,NULL		AS ActualArrivalTime
		,NULL		AS DeliveryDate
		,NULL		AS CurrencyExchangeId
		,NULL		AS CurrencyId
		,NULL		AS CurrencyName
		,NULL		AS SMEApprover
		,NULL		AS CBREDisposalRequestWO
		,NULL		AS DisposalRequestIDName
		,NULL		AS DepartmentTurningEquipment
		,NULL		AS CustomerContractNo
		,NULL		AS SettlementStateId
		,NULL		AS SettlementStateName
		,NULL		AS WeightReceived
		,NULL		AS WeightTare
		,NULL		AS WeightNet
		,NULL		as ItemCount
		,NULL		AS UpdatedDate
		,NULL		AS CreatedDate
		,NULL		AS ReceiveEndDate
		,NULL		AS SettledDate
		,NULL		AS ExternalId
		,NULL		AS [QuoteId]
		,NULL		AS QuoteAutoName
		,NULL		AS PortalQuoteId
		,NULL		AS [IsReadyToBePriced]
		,NULL		AS [ExpectedSealNumber]
		,NULL		AS [ActualSealNumber]
		,NULL		AS PurchaseOrderNumber
		,NULL		AS Employee
		,NULL		AS CsrRepUserName
		,0			AS QtyStatePrograms
		,NULL		as JobId
		,NULL		as AutoPriceRuleSetId
		,NULL		as BusinessUnitId
		,NULL		as BusinessUnitName
		,NULL		as ProbabilityPercentage
		,NULL		as RecyclingQuoteStatusId
		,NULL		as IsRemoteReturn
		,NULL		as Freights
		,NULL		as [AmountOwed]
		,NULL		as [Tags]
	FROM m_data m
	',
	@q5 nvarchar(max) = N'
	UNION ALL
	SELECT
		t.Id
		,t.AutoName
		,t.IsQuote
		,t.IsRevision
		,t.CustomerId
		,t.CustomerCode
		,t.CustomerName
		,t.StatusId
		,t.StatusName
		,t.RepUserId
		,t.RepUserName
		,t.RepUserEmail
		,t.ReceiveStartDate
		,t.ContractId
		,t.ContractName
		,t.SoTransactionTermId
		,t.SoTransactionTermName
		,t.WarehouseId
		,t.WarehouseName
		,t.WarehouseTimeZoneId
		,t.CustomerAddressId
		,t.CustomerLocationName
		,t.CustomerAddress
		,t.OnsiteContactId
		,t.AlternativeContactId
		,t.ShipToCustomerId
		,t.ShipToCustomerName
		,t.ShipToAddressId
		,t.Distance
		,t.InitialServicesPrice
		,t.BolNumber
		,t.PoNumber
		,t.PickupStartDateTime
		,t.PickupEndDateTime
		,t.Duration
		,t.TotalEstimatedWeight
		,t.PalletCount
		,t.InternalComments
		,t.EquipmentNotes
		,t.[PriorityId]
		,t.PriorityName
		,t.TruckId
		,t.TruckTypeId
		,t.BillingTypeId
		,t.ServiceTypeId
		,t.ServiceTypeName
		,t.PackagingTypeId
		,t.PackagingTypeName
		,t.LogisticTypeId
		,t.LogisticTypeName
		,t.FreightClassId
		,t.UnloadingLocationId
		,t.ShipToContactId
		,t.CustomerLoadingDockEquipmentTypeId
		,t.ShipToLoadingDockEquipmentTypeId
		,t.PackageLength
		,t.PackageWidth
		,t.PackageHeight
		,t.IsDimensionsEditable
		,t.TransactionTermId
		,t.SlaDays
		,t.SlaBeginning
		,t.SlaDate
		,t.DueDate
		,t.AccountingSlaDays
		,t.AccountingSlaDate
		,t.AccountingDueDate
		,t.IsCommodityRestricted
		,t.WorkInstructions
		,t.[Reference1]
		,t.[Reference2]
		,t.[Reference3]
		,t.[Reference4]
		,t.[Reference5]
		,t.[Reference6]
		,t.[Reference7]
		,t.[Reference8]
		,t.[EstimatedDeliveryDate]
		,t.[DeliveryPoint]
		,t.ActualPickupTime
		,t.ActualArrivalTime
		,t.DeliveryDate
		,t.CurrencyExchangeId
		,t.CurrencyId
		,t.CurrencyName
		,t.SMEApprover
		,t.CBREDisposalRequestWO
		,t.DisposalRequestIDName
		,t.DepartmentTurningEquipment
		,t.CustomerContractNo
		,t.SettlementStateId
		,t.SettlementStateName
		,t.WeightReceived
		,t.WeightTare
		,t.WeightNet
		,t.ItemCount
		,t.UpdatedDate
		,t.CreatedDate
		,t.ReceiveEndDate
		,t.SettledDate
		,t.ExternalId
		,t.[QuoteId]
		,t.QuoteAutoName
		,t.PortalQuoteId
		,t.[IsReadyToBePriced]
		,t.[ExpectedSealNumber]
		,t.[ActualSealNumber]
		,t.PurchaseOrderNumber
		,t.Employee
		,t.CsrRepUserName
		,t.QtyStatePrograms
		,t.JobId
		,t.AutoPriceRuleSetId
		,t.BusinessUnitId
		,t.BusinessUnitName
		,t.ProbabilityPercentage
		,t.RecyclingQuoteStatusId
		,t.IsRemoteReturn
		,t.Freights
		,t.[AmountOwed]
		,t.[Tags]
	FROM (
		SELECT M.*
		FROM m_data M
		ORDER BY '+ isnull(@OrderBy, 'Id') +N' ' + @SortOrder +N'
		OFFSET (@Offset) ROWS
		FETCH NEXT (@Limit) ROWS ONLY
	) AS t;
	'
	
	set @q0 = @q0 + @q1 + @q2 + @q3 + @q4 + @q5;
	
	if (@IsDebug = 1)
		print cast(@q0 as ntext)

	exec sp_ExecuteSQL
		@q0, N'
		@searchTerm					NVARCHAR(100),
		@warehouseIds				bigint_ID_ARRAY	READONLY,
		@recyclingOrderIds			bigint_ID_ARRAY	READONLY,
		@customerIds				bigint_ID_ARRAY	READONLY,
		@statusIds					bigint_ID_ARRAY	READONLY,
		@priorityIds				bigint_ID_ARRAY	READONLY,
		@portalQuoteIds				bigint_ID_ARRAY	READONLY,
		@filterById					bit,
		@includingFromConsumable	bit,
		@isStatusSet				bit,
		@isPrioritySet				bit,
		@isCustomerSet				bit,
		@isPortalQuoteSet			bit,
		@isServiceTypeSet			bit,
		@isRecyclingQuoteStatusSet	bit,
		@fromUpdatedDate			DATETIME,
		@toUpdatedDate				DATETIME,
		@fromCreatedDate			DATETIME,
		@toCreatedDate				DATETIME,
		@fromReceivedDate			DATETIME,
		@toReceivedDate				DATETIME,
		@fromReceiveDate			DATETIME,
		@toReceiveDate				DATETIME,
		@fromScheduledDate			DATETIME,
		@toScheduledDate			DATETIME,
		@fromSettledDate			DATETIME,
		@toSettledDate				DATETIME,
		@fromDeliveryDate			DATETIME,
		@toDeliveryDate				DATETIME,
		@settlementStateId			INT,
		@Offset						BIGINT,
		@Limit						INT,
		@RestrictUserIds			bigint_ID_ARRAY	READONLY,
		@OrderType					int,
		@ServiceTypeIds				bigint_ID_ARRAY	READONLY,
		@RecyclingQuoteStatusIds	bigint_ID_ARRAY	READONLY',
		@searchTerm					= @searchTerm				,	
		@warehouseIds				= @warehouseIds				,
		@recyclingOrderIds			= @recyclingOrderIds		,	
		@customerIds				= @customerIds				,
		@statusIds					= @statusIds				,	
		@priorityIds				= @priorityIds				,
		@portalQuoteIds				= @portalQuoteIds			,	
		@filterById					= @filterById				,	
		@includingFromConsumable	= @includingFromConsumable	,
		@isStatusSet				= @isStatusSet				,
		@isPrioritySet				= @isPrioritySet			,	
		@isCustomerSet				= @isCustomerSet			,	
		@isPortalQuoteSet			= @isPortalQuoteSet			,
		@isServiceTypeSet			= @isServiceTypeSet			,
		@isRecyclingQuoteStatusSet	= @isRecyclingQuoteStatusSet,
		@fromUpdatedDate			= @fromUpdatedDate			,
		@toUpdatedDate				= @toUpdatedDate			,	
		@fromCreatedDate			= @fromCreatedDate			,
		@toCreatedDate				= @toCreatedDate			,	
		@fromReceivedDate			= @fromReceivedDate			,
		@toReceivedDate				= @toReceivedDate			,	
		@fromReceiveDate			= @fromReceiveDate			,
		@toReceiveDate				= @toReceiveDate			,	
		@fromScheduledDate			= @fromScheduledDate		,	
		@toScheduledDate			= @toScheduledDate			,
		@fromSettledDate			= @fromSettledDate			,
		@toSettledDate				= @toSettledDate			,	
		@fromDeliveryDate			= @fromDeliveryDate			,
		@toDeliveryDate				= @toDeliveryDate			,
		@settlementStateId			= @settlementStateId		,	
		@Offset						= @Offset					,	
		@Limit						= @Limit					,
		@RestrictUserIds			= @RestrictUserIds			,
		@OrderType					= @OrderType				,
		@ServiceTypeIds				= @ServiceTypeIds			,
		@RecyclingQuoteStatusIds	= @RecyclingQuoteStatusIds
END

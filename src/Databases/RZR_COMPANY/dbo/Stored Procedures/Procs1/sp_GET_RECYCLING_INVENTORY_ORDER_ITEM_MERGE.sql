/*
	exec [dbo].[sp_GET_RECYCLING_INVENTORY_ORDER_ITEM_MERGE] 22155, 1, 1
*/
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INVENTORY_ORDER_ITEM_MERGE]
--declare
    @RECYCLING_ORDER_ITEM_ID BIGINT,
	@USER_ID				 BIGINT,
	@C_IS_DEBUG				 bit = 0
AS
BEGIN
	DECLARE @IsMerged bit = 0
	SELECT
		@IsMerged = IS_MERGED
	FROM [dbo].[F_RECYCLING_ORDER_ITEM] OIM WITH (NOLOCK)		
	WHERE OIM.[RECYCLING_ORDER_ITEM_ID] = @RECYCLING_ORDER_ITEM_ID

    DECLARE @ITEMS_DATA bigint_ID_ARRAY;
    INSERT INTO @ITEMS_DATA 
	   SELECT
		  t.ID
	   FROM (				
		  SELECT
			 OIM.[RECYCLING_ORDER_ITEM_ID]  as ID
		  FROM [dbo].[F_RECYCLING_ORDER_ITEM] OIM WITH (NOLOCK)		
		  WHERE OIM.[RECYCLING_ORDER_ITEM_MERGED_ID] = @RECYCLING_ORDER_ITEM_ID
			OR @IsMerged = 0 AND OIM.[RECYCLING_ORDER_ITEM_ID] = @RECYCLING_ORDER_ITEM_ID
	   ) t	

	DECLARE @WAREHOUSE_IDS bigint_ID_ARRAY;
	INSERT INTO @WAREHOUSE_IDS
		EXEC [dbo].[sp_ListUserWarehousesId] @UserId = @USER_ID

	DECLARE @RECYCLING_ORDER_IDS bigint_ID_ARRAY
	DECLARE @CUSTOMER_IDS bigint_ID_ARRAY
	DECLARE @COMMODITY_IDS bigint_ID_ARRAY
	DECLARE @USER_IDS bigint_ID_ARRAY

	IF (EXISTS (SELECT TOP(1) * FROM  @ITEMS_DATA)) BEGIN
		EXEC [dbo].[sp_GET_RECYCLING_INVENTORY_ORDER_ITEMS] 
			@RECYCLING_ORDER_IDS	= @RECYCLING_ORDER_IDS
			,@CUSTOMER_IDS			= @CUSTOMER_IDS
			,@LOT_IDS				= @ITEMS_DATA
			,@COMMODITY_IDS			= @COMMODITY_IDS
			,@REP_IDS				= @USER_IDS
			,@START_DATE			= NULL
			,@END_DATE				= NULL
			,@WORKFLOW_STEP_ID		= ''
			,@IS_MERGE				= 1
			,@WAREHOUSE_IDS			= @WAREHOUSE_IDS
			,@ORDER_COLUMN_NAME		= N'RECYCLING_ORDER_ITEM_ID'
			,@ORDER_DIRECTION		= N'ASC'
			,@ITEMS_PER_PAGE		= 2000000
			,@PAGE_INDEX			= 0
			,@FILTER_WHERE			= ''
			,@C_IS_DEBUG			= @C_IS_DEBUG
	END
	ELSE BEGIN
		SELECT 0 AS RecyclingOrderItemId
	END
END

CREATE FUNCTION [dbo].[tvf_GET_NOTIFICATION_QUEUE_PARAMS]
(	
	@C_NOTIFICATION_QUEUE_ID	BIGINT
)
RETURNS TABLE 
AS
RETURN 
(
	SELECT * FROM
	(
		SELECT
			 nqp.NOTIFICATION_QUEUE_ID
			,PARAM_TYPE_NAME
			,PARAM_VALUE
		FROM F_NOTIFICATION_QUEUE_PARAMS nqp WITH (NOLOCK)
		INNER JOIN [dbo].[F_NOTIFICATION_QUEUE] nq WITH (NOLOCK)
			on nqp.[NOTIFICATION_QUEUE_ID] = nq.NOTIFICATION_QUEUE_ID and GETUTCDATE() - 2 < nq.INSERTED_DT 
		WHERE nqp.NOTIFICATION_QUEUE_ID = @C_NOTIFICATION_QUEUE_ID
	) as s 
	PIVOT
	(
		min(PARAM_VALUE) FOR PARAM_TYPE_NAME IN 
		(	
				CUSTOMER_ID,
				ORDER_ID,
				PURCHASE_ORDER_ID,
				PURCHASE_ORDER_ITEM_ID,
				OLD_STATUS_ID,
				NEW_STATUS_ID,
				CONTRACT_ID,
				ITEM_MASTER_ID,
				SCHEDULED_ORDER_ID,
				PICK_LIST_ID,
				PICK_LIST_ITEM_ID,
				SALES_ORDER_ID,
				RMA_ID,
				REP_ID,
				INVOICE_ID,
				RECYCLING_ORDER_INBOUND_ID,
				INVENTORY_CAPABILITY_TYPE_ID,
				INVENTORY_CAPABILITY_VALUE_ID,
				RECYCLING_ORDER_ITEM_ID,
				CUSTOMER_CONTACT_ID,
				LOT_ID
		) --add all custom attributes here
	) as P
	
	
)
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE function [dbo].[fn_bit_IsInvoiceCanBeReopen]
(
	@SalesOrderId		BIGINT,
	@InvoiceId			BIGINT = null
)
RETURNS bit
AS
BEGIN
	DECLARE @result bit = 1;

	if (exists(
		select top(1) 1
		from [dbo].[F_CreditMemo] cm with (nolock)	
		left join [dbo].[F_INVOICE]	 i with (nolock)
			on cm.InvoiceId = i.INVOICE_ID
		where cm.InvoiceId = @InvoiceId
			or i.ORDER_ID = @SalesOrderId
	))
	begin
		set @result = 0;
	end
	
	RETURN @result;
END
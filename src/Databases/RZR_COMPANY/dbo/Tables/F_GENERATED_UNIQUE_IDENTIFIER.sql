CREATE TABLE [dbo].[F_GENERATED_UNIQUE_IDENTIFIER] (
    [UNIQUE_IDENTIFIER_ID]    BIGINT        IDENTITY (1, 1) NOT NULL,
    [CUSTOMER_NAME]           VARCHAR (150) NOT NULL,
    [CUSTOMER_ID]             BIGINT        NOT NULL,
    [RECYCLING_ORDER_ID]      BIGINT        NULL,
    [UNIQUE_IDENTIFIER_VALUE] VARCHAR (250) NOT NULL,
    [INSERTED_DT]             DATETIME      CONSTRAINT [DF_F_GENERATED_UNIQUE_IDENTIFIER_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [PURCHASE_ORDER_ID]       BIGINT        NULL,
    CONSTRAINT [PK_F_GENERATED_UNIQUE_IDENTIFIER] PRIMARY KEY CLUSTERED ([UNIQUE_IDENTIFIER_ID] ASC),
    CONSTRAINT [F_GENERATED_UNIQUE_IDENTIFIER_F_PURCHASE_ORDER_PURCHASE_ORDER_ID_fk] FOREIGN KEY ([PURCHASE_ORDER_ID]) REFERENCES [dbo].[F_PURCHASE_ORDER] ([PURCHASE_ORDER_ID]),
    CONSTRAINT [FK_F_GENERATED_UNIQUE_IDENTIFIER_F_INBOUND_ORDER] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE SET NULL ON UPDATE CASCADE
);




GO
CREATE NONCLUSTERED INDEX [IX_UNIQUE_ID]
    ON [dbo].[F_GENERATED_UNIQUE_IDENTIFIER]([UNIQUE_IDENTIFIER_VALUE] ASC)
	INCLUDE(PURCHASE_ORDER_ID, [RECYCLING_ORDER_ID], [CUSTOMER_ID], [CUSTOMER_NAME])

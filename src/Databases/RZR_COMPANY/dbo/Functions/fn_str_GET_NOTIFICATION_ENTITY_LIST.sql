-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 09/17/2014
-- Description: get notification email list
-- =============================================
CREATE FUNCTION [dbo].[fn_str_GET_NOTIFICATION_ENTITY_LIST] 
(
	@NOTIFICATION_ID BIGINT
)
RETURNS  NVARCHAR(MAX)
AS
BEGIN
	
	DECLARE @RESULT NVARCHAR(MAX) = N'';

	DECLARE  @T_EMAILS TABLE(ID BIGINT, EMAIL NVARCHAR(512))

	-- get names for entities
	INSERT INTO @T_EMAILS
	SELECT
		NE.ENTITY_TYPE_ID	AS  ID
		,CNE.NAME			AS	EMAIL
	FROM F_NOTIFICATION_ENTITY NE  WITH(NOLOCK) 
	LEFT JOIN C_NOTIFICATION_ENTITY CNE WITH (NOLOCK)
		ON CNE.ENTITY_TYPE_ID = NE.ENTITY_TYPE_ID						  
	WHERE NE.NOTIFICATION_ID = @NOTIFICATION_ID

	-- get emails for users
	INSERT INTO @T_EMAILS
	SELECT
		NU.[USER_ID]		AS	ID
		,U.Email			AS	EMAIL
	FROM F_NOTIFICATION_USER				NU  WITH(NOLOCK)
	INNER JOIN tb_User						U WITH(NOLOCK)
		ON U.UserID = NU.[USER_ID] 
	WHERE NU.NOTIFICATION_ID = @NOTIFICATION_ID
		AND NU.[USER_ID] IS NOT NULL

	-- get additional emails
	INSERT INTO @T_EMAILS
	SELECT
		NULL				AS ID
		,NU.USER_EMAIL		AS EMAIL
	FROM F_NOTIFICATION_USER				NU  WITH(NOLOCK)
	WHERE NU.NOTIFICATION_ID = @NOTIFICATION_ID
		AND NU.[USER_ID] IS NULL


	SET @RESULT = (
		SELECT
			','  + CAST(ISNULL(T.ID, '') AS VARCHAR(100))  + '|' + T.EMAIL AS "data()"
		FROM @T_EMAILS   AS T
		FOR XML PATH('')
	)

	RETURN @RESULT
END
CREATE PROCEDURE [dbo].[sp_SET_DEFAULT_TRANSFER_LOCATION_FOR_RECYCLING_ORDER] 
	@C_RECYCLING_ORDER_ID BIGINT
AS
BEGIN
	DECLARE @WAREHOUSE_ID			BIGINT,
			@TRANSFER_LOCATION_ID	BIGINT
	
	SELECT @WAREHOUSE_ID = R.WAREHOUSE_ID
	  FROM F_RECYCLING_ORDER	R WITH(NOLOCK)
	 WHERE R.RECYCLING_ORDER_ID = @C_RECYCLING_ORDER_ID

	-- get def transfer location
	SELECT @TRANSFER_LOCATION_ID = L.LOCATION_ID
	  FROM F_LOCATION  L WITH (NOLOCK)
	WHERE L.WAREHOUSE_ID = @WAREHOUSE_ID
	  AND L.IS_TRANSFER_DEFAULT_LOCATION = 1

	 ---- receive complete for transfer order: set transfer default location for all lots
	IF (@TRANSFER_LOCATION_ID IS NOT NULL) 
	BEGIN 
		-- update lots warehouse and default location
		UPDATE ROI 
			SET LOCATION_ID = @TRANSFER_LOCATION_ID
		FROM [dbo].[F_RECYCLING_ORDER_ITEM_TRANSFER]		ROIT WITH(NOLOCK)
		INNER JOIN [dbo].[F_RECYCLING_ORDER_ITEM]			ROI
			ON  ROIT.RECYCLING_ORDER_ITEM_ID = ROI.RECYCLING_ORDER_ITEM_ID
		LEFT JOIN [dbo].[F_LOCATION]						L WITH(NOLOCK)
			ON ROI.LOCATION_ID = L.LOCATION_ID
		WHERE ROIT.INBOUND_ORDER_ID = @C_RECYCLING_ORDER_ID AND (ROI.LOCATION_ID IS NULL OR L.WAREHOUSE_ID != @WAREHOUSE_ID)
	    -- Setting a default transfer location only for lots with a location that does not belong to the receiving warehouse

	END
END
-- EXEC [dbo].[sp_GET_RECYCLING_SALES_ORDER_INVOICES] @ORDER_ID = 414262, @DEBUG = 1
-- EXEC [dbo].[sp_GET_RECYCLING_SALES_ORDER_INVOICES] @DUE_STATE_ID = 1
-- EXEC [dbo].[sp_GET_RECYCLING_SALES_ORDER_INVOICES] @DUE_STATE_ID = 2
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_SALES_ORDER_INVOICES]
    @ORDER_ID			bigint			= NULL,
    @DUE_STATE_ID		int				= 0,
	@WAREHOUSE_ID		BIGINT			= 0,
    @ORDER_COLUMN_NAME	varchar(150)	 = N'SalesOrderInvoiceId',
    @ORDER_DIRECTION	varchar(20)		= N'DESC',
    @ITEMS_PER_PAGE		int				= 20,
    @PAGE_INDEX			int				= 0,
    @FILTER_WHERE		Nvarchar(2000)	= N'',
	@DEBUG				BIT				= 0
AS
BEGIN

    SELECT @ORDER_COLUMN_NAME = ISNULL(NULLIF(@ORDER_COLUMN_NAME,''), 'SalesOrderInvoiceId')
	   
    DECLARE @now		   DATETIME	    = GETUTCDATE();
    DECLARE
	   @startRowNumber 	   BIGINT		    = @PAGE_INDEX * @ITEMS_PER_PAGE + 1,
	   @endRowNumber	   BIGINT		    = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE,
	   @filterCondition	   VARCHAR(2006)   = N'',
	   @SALES_ORDER_ID     NVARCHAR(40)    = N'NULL',
		@warehouseCondition VARCHAR(MAX) =  N'';
	
	IF (@WAREHOUSE_ID > 0)
	BEGIN
		SET @warehouseCondition = 'AND ISNULL(O.WAREHOUSE_ID, RO.WAREHOUSE_ID) = ' + CAST(@WAREHOUSE_ID AS VARCHAR(20))
	END
    IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
    BEGIN
	   SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
    END 
	
    IF (ISNULL(@ORDER_ID, 0) != 0)
    BEGIN
	   SET @SALES_ORDER_ID = CAST(@ORDER_ID AS NVARCHAR(20))
    END			
	
    DECLARE
	   @query NVARCHAR (max) = N'
		  WITH m_data AS
		  (	
			 SELECT 
				I.INVOICE_ID							 AS SalesOrderInvoiceId
				,I.AUTO_NAME							 AS InvoiceAutoName
				,O.SALES_ORDER_ID						 AS SalesOrderId
				,O.SALES_ORDER_NUMBER					 AS SoNo
				,C.CUSTOMER_ID							 AS CustomerId
				,C.CUSTOMER_NAME						 AS Customer
				,I.DATE_CREATED							 AS InsertedDate
				,I.DATE_DUE								 AS DateDue
				,I.STATUS_ID							 AS 	StatusId
				,I.IS_FULLY_PAID						 AS	IsPaid
				,I.IS_RECYCLING  						 AS IsRecycling
				,I.IS_VOIDED							 AS IsVoided
				,I.REP_USER_ID							 AS RepUserId
				,R.UserName								 AS RepUserCd					
				,I.AMOUNT_DUE							 AS AmountDue 
				,I.ORIGINAL_AMOUNT						 AS AMOUNT
				,I.PAID_AMOUNT							 AS PaidAmount
				,O.HANDLING_FEE							 AS HandlingFee
				,O.SHIPPING_COST						 AS ShippingCost
				,O.RECYCLING_ORDER_ID					 AS RecyclingId
				,ISNULL(W.WAREHOUSE_CD, WW.WAREHOUSE_CD) AS Warehouse
				,O.Subtotal
				,I.KIDNumber
				,I.ExternalId
			 FROM [vw_AR_INVOICE] I WITH(NOLOCK)
			 INNER JOIN F_SALES_ORDER				O	WITH(NOLOCK)
				ON I.ORDER_ID = O.SALES_ORDER_ID
			 INNER JOIN F_CUSTOMER					C	WITH(NOLOCK)
				ON O.CUSTOMER_ID = C.CUSTOMER_ID
			 INNER JOIN tb_User						R	WITH(NOLOCK)
				ON I.REP_USER_ID = R.UserID
			LEFT JOIN D_WAREHOUSE W WITH (NOLOCK)
				ON W.WAREHOUSE_ID = O.WAREHOUSE_ID
			
			LEFT JOIN [dbo].[F_RECYCLING_ORDER] RO WITH (NOLOCK)
				ON O.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID			
			LEFT JOIN D_WAREHOUSE WW WITH (NOLOCK)
				ON WW.WAREHOUSE_ID = RO.WAREHOUSE_ID

			 WHERE I.INVOICE_TYPE_ID = 1 AND O.IS_DELETED = 0 
				AND O.IS_INACTIVE = 0
				AND ('+ @SALES_ORDER_ID +N' IS NULL OR O.SALES_ORDER_ID = '+ @SALES_ORDER_ID + N')
			 '+ @warehouseCondition+ N'
		  )		
		  SELECT TOP(1)
			 -1		AS RowID,
			 COUNT(SalesOrderInvoiceId) AS SalesOrderInvoiceId,
			 NULL as InvoiceAutoName,
			 0		AS IsActive,
			 -1		AS SalesOrderId,
			 NULL	AS SoNo,
			 -1		AS CustomerId,
			 NULL	AS Customer,
			 NULL	AS InsertedDate,
			 NULL	AS DateDue,
			 NULL	AS StatusId,
			 0		AS IsPaid,
			 0		AS IsRecycling,
			 0		AS IsVoided,
			 -1		AS RepUserId,
			 NULL	AS RepUserCd,
			 0		AS Amount,
			 0		AS AmountDue,
			 0		AS PaidAmount,
			 ''''   AS Warehouse,
			 NULL	as KIDNumber,
			 null	as ExternalId,
			 NULL   as OrderCurrency,
			 0		AS OrderCurrencyAmount,
			 0		AS OrderCurrencyAmountDue,
			 0		AS OrderCurrencyPaidAmount			 
		  FROM m_data ' + @filterCondition + '
		  UNION ALL
		  SELECT			
			t.*,
			[dbo].[fn_str_GetFormattedCurrencyMark](c.Symbol, c.Abbreviation, ce.PREFER_CURRENCY_ABBREVIATION) as OrderCurrency,
			t.Amount	 / e.ForeignToHome as OrderCurrencyAmount,
			t.AmountDue  / e.ForeignToHome as OrderCurrencyAmountDue,
			t.PaidAmount / e.ForeignToHome as OrderCurrencyPaidAmount			
		  FROM (
			 SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				M.SalesOrderInvoiceId as SalesOrderInvoiceId,
				M.InvoiceAutoName,
				(CASE 
				    WHEN M.SalesOrderInvoiceId = (
					   SELECT 
							 MAX(INVOICE_ID) 
					   FROM F_INVOICE IV WITH(NOLOCK)
					   WHERE 
							 IV.INVOICE_TYPE_ID = 1 AND M.SalesOrderId = IV.ORDER_ID AND ISNULL(IV.IS_VOIDED, 0) = 0) THEN 1
				    ELSE 0
				END)								    AS IsActive,
				M.SalesOrderId,
				M.SoNo,
				M.CustomerId,
				M.Customer,
				M.InsertedDate,
				M.DateDue,
				M.StatusId,
				M.IsPaid,
				M.IsRecycling,
				M.IsVoided,
				M.RepUserId,
				M.RepUserCd,
				CASE
				    WHEN M.StatusId >= 4 OR M.IsVoided = 1 THEN 
						M.AMOUNT
				    WHEN M.RecyclingId IS NULL THEN 
						m.Subtotal	 + dbo.sp_CALCULATE_SALES_ORDER_TAX(M.SalesOrderId) + M.HandlingFee + M.ShippingCost	
				    ELSE 
						m.Subtotal							
				END								    AS Amount,
				M.AmountDue,
				M.PaidAmount,
				M.Warehouse,
				M.KIDNumber,
				M.ExternalId
			 FROM m_data M ' + @filterCondition + N' 
		) t
		INNER JOIN dbo.F_SALES_ORDER			o	WITH(NOLOCK)
			ON o.SALES_ORDER_ID = t.SalesOrderId				
		inner join [dbo].[D_CurrencyExchange]	e	WITH(NOLOCK)
			on o.CurrencyExchangeId = e.Id
		inner join [dbo].[C_Currency]			c	WITH(NOLOCK)
			ON e.[ForeignCurrencyId] = c.Id		
		cross join [dbo].[U_SYSTEM_SETTINGS]	ce	with(nolock)
	    WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))
    IF @DEBUG = 1
		PRINT CAST(@query AS NTEXT)
	ELSE 
		EXEC sp_executeSQL @query
    --SELECT @query as SALES_ORDER_NUMBER
END
GO


CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_COMPETITION_INSIGHT]
	@ITEM_ID BIGINT = -1,
	@MODEL_ID	 varchar(50) = N''
AS
BEGIN
	DECLARE @Temp TABLE( 
		SourceTypeId	int,
		AvgPrice		decimal
	)

	INSERT INTO @Temp (SourceTypeId, AvgPrice) VALUES (1, cast(-100 as decimal))  -- 1 is EBay
	INSERT INTO @Temp (SourceTypeId, AvgPrice) VALUES (2, cast(-100 as decimal))  -- 2 is Google
	INSERT INTO @Temp (SourceTypeId, AvgPrice) VALUES (3, cast(-100 as decimal))  -- 3 is <PERSON><PERSON><PERSON>

	SELECT 
		SourceTypeId, AvgPrice
	FROM
		@Temp
END
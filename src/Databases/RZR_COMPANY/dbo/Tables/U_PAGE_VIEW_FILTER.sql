CREATE TABLE [dbo].[U_PAGE_VIEW_FILTER] (
    [FILTER_ID]           BIGINT         IDENTITY (1, 1) NOT NULL,
    [USER_ID]             BIGINT         NOT NULL,
    [PAGE_ID]             INT            NOT NULL,
    [NAME]                NVARCHAR (150) NOT NULL,
    [FILTER_JSON]         NVARCHAR (MAX) NOT NULL,
    [IS_DEFAULT]          BIT            CONSTRAINT [DF_U_PAGE_VIEW_FILTER_IS_DEFAULT] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]         BIT            CONSTRAINT [DF_U_PAGE_VIEW_FILTER_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]          BIT            CONSTRAINT [DF_U_PAGE_VIEW_FILTER_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY_USER_ID] BIGINT         NOT NULL,
    [INSERTED_DT]         DATETIME       NOT NULL,
    [UPDATED_BY_USER_ID]  BIGINT         NULL,
    [UPDATED_DT]          DATETIME       NULL,
    [DELETED_BY_USER_ID]  BIGINT         NULL,
    [DELETED_DT]          DATETIME       NULL,
    CONSTRAINT [PK_U_TABLE_FILTER] PRIMARY KEY CLUSTERED ([FILTER_ID] ASC),
    CONSTRAINT [FK_U_PAGE_VIEW_FILTER_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_U_PAGE_VIEW_FILTER_U_FILTERED_PAGE] FOREIGN KEY ([PAGE_ID]) REFERENCES [dbo].[U_FILTERED_PAGE] ([PAGE_ID])
);


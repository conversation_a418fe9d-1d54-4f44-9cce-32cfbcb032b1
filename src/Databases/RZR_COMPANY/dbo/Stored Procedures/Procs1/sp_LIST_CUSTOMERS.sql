/* 
EXEC [dbo].[sp_LIST_CUSTOMERS] 
	 @TERM	= '%2 Paul%'
	,@MAGENTO_SELECTION = 0
	,@ACCOUNT_TYPE = 0
	,@ACTIVE_ONLY = 1
*/
CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMERS] (
	@TERM							nvarchar(max)	= N'',
	@COUNT							int				= 20,
	@FILTER_AS_CUSTOMER_BY_REP_IDS	bigint_ID_ARRAY READONLY,
	@FILTER_AS_VENDOR_BY_REP_IDS	bigint_ID_ARRAY READONLY,
	@MAGENTO_SELECTION				int				= 0, -- 0 - all; 1 - magento only; 2 - not from magento only
	@ACCOUNT_TYPE					int				= 0, -- 0 - all (vendor and customer flags doesnt matter); 
												 -- 1 - customer and vendor (customer and vendor flags are set); 
												 -- 2 - customer (customer flag is set, vendor flag doesn't matter); 
												 -- 3 - vendor (customer flag doesn't matter, vendor flag is set); 
												 -- 4 - not customer and not vendor (both flags are unset)
	@ACTIVE_ONLY			bit				= 0,
	@IS_LOCK_INCLUDE		bit				= 0,
	@IS_FREIGHT_QUOTE		bit				= 0,
	@IS_DEBUG				BIT				= 0,
	@IS_DISPLAY_PARENT		BIT				= 1,
	@IsSearchParent			BIT				= 0

)
AS
BEGIN
	SET @TERM = LTRIM(RTRIM(@TERM))
	declare  @C_IS_DISPLAY_PARENT bit = @IS_DISPLAY_PARENT
	declare  @C_IsSearchParent bit = @IsSearchParent
	
	DECLARE @ACTIVE		NVARCHAR(MAX) = N'';
	DECLARE @MAGENTO	NVARCHAR(MAX) = N'';
	DECLARE @ACCOUNT	NVARCHAR(MAX) = N'';
	DECLARE @LOCK		NVARCHAR(MAX) = N'';
	DECLARE @joins		NVARCHAR(MAX) = N'';
	DECLARE @BY_REP		NVARCHAR(MAX) = N'';
	DECLARE @post		NVARCHAR(MAX) = N'';

	-- freight Tag Join
	IF (@IS_FREIGHT_QUOTE = 1 )
	BEGIN
		SELECT @joins = N'
			INNER JOIN F_ENTITY_TAG ET WITH(NOLOCK)
				ON  ET.TAG_TYPE_ID = 3 -- Freight
				AND ET.ENTITY_ID   = C.CUSTOMER_ID'
	END

	-- rep IDS
	IF (EXISTS(SELECT ID FROM @FILTER_AS_CUSTOMER_BY_REP_IDS))
    BEGIN
		SET @joins = @joins + '
		   LEFT JOIN @FILTER_AS_CUSTOMER_BY_REP_IDS  cust
			   ON cust.ID = C.REP_ID'

		SET @BY_REP = @BY_REP + '
			AND (C.IS_CUSTOMER = 0 OR cust.ID IS NOT NULL)'
    END
	IF (EXISTS(SELECT ID FROM @FILTER_AS_VENDOR_BY_REP_IDS))
    BEGIN
		SET @joins = @joins + '
		   LEFT JOIN @FILTER_AS_VENDOR_BY_REP_IDS  vend
			   ON vend.ID = C.REP_ID'

		SET @BY_REP = @BY_REP + '
			AND (C.IS_VENDOR = 0 OR vend.ID IS NOT NULL)'
    END

	
	IF (@ACTIVE_ONLY = 1)
	BEGIN
		SELECT @ACTIVE = N' AND C.IS_INACTIVE = 0 '
	END

	IF (@IS_LOCK_INCLUDE = 0)
	BEGIN
		SELECT @LOCK = N' AND C.IS_LOCK = 0 ';
	END
	
	-- Magento selection
	SET @MAGENTO = (
		CASE
			 WHEN @MAGENTO_SELECTION = 1 THEN N' AND C.IS_FROM_MAGENTO = 1 '
			 WHEN @MAGENTO_SELECTION = 2 THEN N' AND C.IS_FROM_MAGENTO = 0 '
			 ELSE N''
		END
	)
	
	-- Customer/Vendor selection
	SET @ACCOUNT = (
		CASE
			 WHEN @ACCOUNT_TYPE = 1 THEN N' AND C.IS_CUSTOMER = 1 AND C.IS_VENDOR = 1 '
			 WHEN @ACCOUNT_TYPE = 2 THEN N' AND C.IS_CUSTOMER = 1 '
			 WHEN @ACCOUNT_TYPE = 3 THEN N' AND C.IS_VENDOR = 1 '
			 WHEN @ACCOUNT_TYPE = 4 THEN N' AND C.IS_CUSTOMER = 0 AND C.IS_VENDOR = 0 '
			 ELSE N''
		END
	)


	DECLARE @query NVARCHAR (MAX) = 
	N'	SELECT TOP( ' + CAST(@COUNT AS VARCHAR(100))  + ' )
			t.CUSTOMER_ID			as value
			,t.label
			,row_number() over(order by charindex(REPLACE(@TERM, ''%'',''''), t.CUSTOMER_NAME), t.CUSTOMER_NAME, t.CUSTOMER_ID) rnk
			,t.[PrimaryCustomerId]	as parentId
		FROM (
			SELECT DISTINCT
				C.CUSTOMER_ID
				,C.CUSTOMER_NAME
				,iif(@C_IS_DISPLAY_PARENT = 1 and c.[PrimaryCustomerId] is not null, C.CUSTOMER_NAME + '' - ('' + pc.CUSTOMER_NAME + '')'', C.CUSTOMER_NAME) as label
				,c.[PrimaryCustomerId]
			FROM dbo.F_CUSTOMER C WITH(NOLOCK)
			' + @joins + N'
			left join dbo.F_CUSTOMER pc with (nolock)
				on c.[PrimaryCustomerId] = pc.CUSTOMER_ID
			WHERE C.IS_DELETED = 0
			  AND iif(@C_IsSearchParent = 1 and c.[PrimaryCustomerId] is not null, C.CUSTOMER_NAME + '' - ('' + pc.CUSTOMER_NAME + '')'', ltrim(rtrim(C.CUSTOMER_NAME))) like @TERM' + 
				+ @ACTIVE 
				+ @MAGENTO 
				+ @BY_REP
				+ @ACCOUNT
				+ @LOCK +'
		) t
		ORDER BY rnk'
	--select @query

	IF (@IS_DEBUG = 1)
	BEGIN
		PRINT CAST(@QUERY AS NTEXT)
	END
	--select @query
	EXEC sp_executesql @query,
		N'@TERM NVARCHAR(max)
		,@FILTER_AS_CUSTOMER_BY_REP_IDS	dbo.bigint_ID_ARRAY READONLY
		,@FILTER_AS_VENDOR_BY_REP_IDS   dbo.bigint_ID_ARRAY READONLY
		,@C_IS_DISPLAY_PARENT			bit
		,@C_IsSearchParent				bit',
		@TERM = @TERM,
		@FILTER_AS_CUSTOMER_BY_REP_IDS	= @FILTER_AS_CUSTOMER_BY_REP_IDS,
		@FILTER_AS_VENDOR_BY_REP_IDS    = @FILTER_AS_VENDOR_BY_REP_IDS,
		@C_IS_DISPLAY_PARENT			= @C_IS_DISPLAY_PARENT,
		@C_IsSearchParent				= @C_IsSearchParent
END
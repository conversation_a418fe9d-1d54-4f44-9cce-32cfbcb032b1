
CREATE PROCEDURE [dbo].[sp_LIST_E_BAY_CATEGORIES]
(@TERM nvarchar(max) = N''
,@COUNT int = 20
,@MATCH_FULL_PATH bit = 0)
AS
BEGIN
	DECLARE @trimmed NVARCHAR(max) = LTRIM(RTRIM(@TERM))
	
	SELECT TOP(@COUNT)
		H.CATEGORY_ID
		,CAST(H.CATEGORY_ID AS NVARCHAR(20)) + N' - '+ H.CATEGORYNAME				AS CATEGORYNAME
		,CAST(H.CATEGORY_ID AS NVARCHAR(20)) + N' - ' + H.<PERSON>BAY_CATEGORY_FULL_PATH	AS EBAY_CATEGORY_FULL_PATH
	FROM D_EBAY_CATEGORY_HIERARCHY	H	WITH (NOLOCK)
	WHERE (ISNULL(@MATCH_FULL_PATH, 0) = 0 AND H.<PERSON><PERSON><PERSON>OR<PERSON><PERSON><PERSON>				LIKE @trimmed) 
	   OR (ISNULL(@MATCH_FULL_PATH, 0) = 1 AND H.EBAY_CATEGORY_FULL_PATH	LIKE @trimmed)
	   OR (CAST(H.CATEGORY_ID AS NVARCHAR(20))								LIKE @trimmed)
END
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT dbo.fn_str_D_CATEGORY_HIERARCHY_SET('FSP300-60BTV')
CREATE FUNCTION [dbo].[fn_str_ENUMERATE_ITEMS_INVENTORY_LOCATIONS]
(@ORDER_ID			BIGINT
,@ITEM_MASTER_ID	BIGINT
,@ALLOCATED			BIT)
RETURNS NVARCHAR(MAX)
AS
BEGIN
	DECLARE STEP_ID CURSOR LOCAL FORWARD_ONLY FOR (
			SELECT DISTINCT
				-- items with the @ITEM_MASTER_ID in the preselected locations
				LTRIM(RTRIM(C.INVENTORY_CAPABILITY_VALUE)) AS LOCATION
				,(CASE
					-- ALLOCATED
					WHEN ISNULL(@ALLOCATED, 0) = 1 THEN
						(SELECT 
							COUNT(INV.ITEM_INVENTORY_ID) 
						FROM F_ITEM_INVENTORY INV WITH (NOLOCK)
						WHERE INV.LOCATION_ID = C.INVENTORY_CAPABILITY_ID
						  AND INV.ITEM_MASTER_ID = @ITEM_MASTER_ID
						  AND INV.IS_DELETED = 0
						  AND EXISTS(
							SELECT 
								ITEM_INVENTORY_ID 
							FROM F_SALES_ORDER_ITEM OI WITH (NOLOCK)
							WHERE OI.SALES_ORDER_ID != @ORDER_ID -- NOT IN THIS ORDER								
								AND OI.ITEM_INVENTORY_ID = INV.ITEM_INVENTORY_ID)
						 
						)
					-- UNALLOCATED
					ELSE 
						(SELECT 
							COUNT(INV.ITEM_INVENTORY_ID) 
						FROM F_ITEM_INVENTORY INV WITH (NOLOCK)
						WHERE INV.LOCATION_ID = C.INVENTORY_CAPABILITY_ID
						  AND INV.ITEM_MASTER_ID = @ITEM_MASTER_ID
						  AND INV.IS_DELETED = 0
						  AND NOT EXISTS(
							SELECT 
								ITEM_INVENTORY_ID 
							FROM F_SALES_ORDER_ITEM OI WITH (NOLOCK)
							WHERE OI.SALES_ORDER_ID != @ORDER_ID -- NOT IN THIS ORDER								
								AND OI.ITEM_INVENTORY_ID = INV.ITEM_INVENTORY_ID)
						  )
				END) AS QTY

			FROM F_ITEM_MASTER M WITH (NOLOCK)
			-- ALL INVENTORY OF THE MASTER ITEMS
			LEFT JOIN F_ITEM_INVENTORY I WITH(NOLOCK)
			  ON M.ITEM_MASTER_ID = I.ITEM_MASTER_ID
			-- WITH LOCATIONS IF ANY
			LEFT JOIN D_INVENTORY_CAPABILITY C WITH (NOLOCK)
			  ON I.LOCATION_ID = C.INVENTORY_CAPABILITY_ID		
			WHERE M.ITEM_MASTER_ID = @ITEM_MASTER_ID
			  AND M.IS_DELETED = 0
			  AND I.IS_DELETED = 0
		)
		
			  
	OPEN STEP_ID
	DECLARE 
		@RESULT NVARCHAR(MAX)
		,@LOCATION NVARCHAR(MAX)
		,@QTY INT = 0

	FETCH NEXT 
	FROM STEP_ID INTO @LOCATION, @QTY
	
	WHILE @@FETCH_STATUS = 0
	BEGIN
		-- Add the T-SQL statements to compute the return value here
		SELECT 
			@RESULT =
			CASE
				WHEN ISNULL(@RESULT, N'') = N'' THEN 
					CASE 
						WHEN ISNULL(@QTY, 0) = 0 THEN @RESULT 
						ELSE N'('+ CAST(@QTY AS NVARCHAR(20)) +N')'+ @LOCATION
					END
				ELSE
					CASE 
						WHEN ISNULL(@QTY, 0) = 0 THEN @RESULT 
						ELSE @RESULT  +N',' + CHAR(13) + CHAR(20) + N'('+ CAST(@QTY AS NVARCHAR(20)) +N')'+ @LOCATION
					END
			END
			
		FETCH NEXT FROM STEP_ID INTO @LOCATION, @QTY
	END
	CLOSE STEP_ID
	DEALLOCATE STEP_ID

	SET @RESULT = ISNULL(LTRIM(RTRIM(@RESULT)), N'none');
	-- Return the result of the function
	RETURN @RESULT

END
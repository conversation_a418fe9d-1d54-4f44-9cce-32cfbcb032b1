-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_MARKETPLACE]
	@ITEM_ID BIGINT = -1,
	@MODEL_ID	 varchar(50) = N''
AS
BEGIN
	DECLARE @Temp TABLE( 
		MarketPlaceId int NOT NULL,
		[State] varchar(50) NOT NULL
	)

	INSERT INTO @Temp (MarketPlaceId, [State]) VALUES (1, N'!Live') -- 1 is EBay
	INSERT INTO @Temp (MarketPlaceId, [State]) VALUES (2, N'!N/A')  -- 2 is ECommerce
	INSERT INTO @Temp (MarketPlaceId, [State]) VALUES (3, N'!N/A')  -- 3 is Amazon

	SELECT 
		* 
	FROM
		@Temp
END
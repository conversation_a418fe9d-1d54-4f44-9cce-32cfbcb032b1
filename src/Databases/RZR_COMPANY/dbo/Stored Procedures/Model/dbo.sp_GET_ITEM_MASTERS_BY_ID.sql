CREATE PROCEDURE [dbo].[sp_GET_ITEM_MASTERS_BY_ID] 
	@ItemMasterIds dbo.bigint_ID_ARRAY readonly
AS
BEGIN	
	
	select 
		m.[ITEM_MASTER_ID]		as Id
		,m.[ITEM_NUMBER]		as [Name]
		,m.[MANUFACTURER_ID]    as ManufacturerI<PERSON>
		,mfg.[MANUFACTURER_CD]  as Manufacturer
		,m.[ITEM_TYPE_ID]		as It<PERSON><PERSON>ypeId
		,m.[DIMENSION_ID]		as DimensionsId
		,m.[IS_INACTIVE]		as IsI<PERSON><PERSON>
		,m.[IS_DELETED]			as IsDeleted
	FROM @ItemMasterIds				ids
	inner join dbo.F_ITEM_MASTER	m	WITH (NOLOCK)
		on m.[ITEM_MASTER_ID] = ids.ID
	inner join dbo.D_MANUFACTURER	mfg	WITH (NOLOCK)
		on m.MANUFACTURER_ID = mfg.[MANUFACTURER_ID]
	
END

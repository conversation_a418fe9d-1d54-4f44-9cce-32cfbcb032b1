CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_PICK_LIST_ITEM]
  (
    @C_PICK_LIST_ITEM_ID BIGINT
  )
RETURNS @T_RESULT TABLE
(
  PICK_LIST_ID               BIGINT,
  PICK_LIST_ITEM_SERIAL      NVARCHAR(MAX),
  PICK_LIST_ITEM_UNIQUE_ID   NVARCHAR(MAX),
  PICK_LIST_ITEM_STATUS_CD   NVARCHAR(MAX),
  PICK_LIST_ITEM_REASON      NVARCHAR

)
AS
BEGIN
  INSERT INTO @T_RESULT
    (
      PICK_LIST_ID,
      PICK_LIST_ITEM_SERIAL,
      PICK_LIST_ITEM_UNIQUE_ID,
      PICK_LIST_ITEM_STATUS_CD,
      PICK_LIST_ITEM_REASON
    )
  SELECT
    PLI.PickListId,
    II.ITEM_INVENTORY_SERIAL,
    II.ITEM_INVENTORY_UNIQUE_ID,
    IIS.STATUS_CD,
    PLI.FailedNote
  FROM
  F_PickListAllocatedItem PLI WITH (NOLOCK)
  INNER JOIN F_ITEM_INVENTORY II WITH (NOLOCK )
    ON II.ITEM_INVENTORY_ID = PLI.ItemInventoryId
  INNER JOIN D_ITEM_INVENTORY_STATUS IIS WITH (NOLOCK )
    ON IIS.ITEM_INVENTORY_STATUS_ID = II.ITEM_STATUS_ID
  WHERE PLI.Id = @C_PICK_LIST_ITEM_ID
  RETURN
END
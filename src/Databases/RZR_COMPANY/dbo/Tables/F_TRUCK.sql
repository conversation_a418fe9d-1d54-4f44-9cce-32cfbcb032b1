CREATE TABLE [dbo].[F_TRUCK] (
    [TRUCK_ID]              BIGINT         IDENTITY (1, 1) NOT NULL,
    [NAME]                  NVARCHAR (256) NOT NULL,
    [TRUCK_TYPE_ID]         INT            NOT NULL,
    [BODY_TYPE_ID]          INT            NOT NULL,
    [PALLET_COUNT]          INT            CONSTRAINT [DF_F_TRUCK_PALLET_COUNT] DEFAULT ((0)) NOT NULL,
    [WEIGHT_LBS]            FLOAT (53)     CONSTRAINT [DF_F_TRUCK_WEIGHT_LBS] DEFAULT ((0)) NOT NULL,
    [TRUCK_WEIGHT_PER_AXEL] FLOAT (53)     NULL,
    [TRUCK_HEIGHT]          FLOAT (53)     NULL,
    [TRUCK_LENGTH]          FLOAT (53)     NULL,
    [TRUCK_WIDTH]           FLOAT (53)     NULL,
    [TRUCK_HAS_TRAILER]     BIT            NULL,
    [COLOR]                 NVARCHAR (50)  NULL,
    [INSERTED_BY]           VARCHAR (150)  NOT NULL,
    [INSERTED_DT]           DATETIME       NOT NULL,
    [UPDATED_BY]            VARCHAR (150)  NULL,
    [UPDATED_DT]            DATETIME       NULL,
    [WEIGHT_KG]             AS             ([WEIGHT_LBS]*(0.45359237)) PERSISTED,
    CONSTRAINT [PK_F_TRUCK] PRIMARY KEY CLUSTERED ([TRUCK_ID] ASC),
    CONSTRAINT [FK_F_TRUCK_C_TRUCK_BODY_TYPE] FOREIGN KEY ([BODY_TYPE_ID]) REFERENCES [dbo].[C_TRUCK_BODY_TYPE] ([BODY_TYPE_ID]),
    CONSTRAINT [FK_F_TRUCK_C_TRUCK_TYPE] FOREIGN KEY ([TRUCK_TYPE_ID]) REFERENCES [dbo].[C_TRUCK_TYPE] ([TRUCK_TYPE_ID])
);




















































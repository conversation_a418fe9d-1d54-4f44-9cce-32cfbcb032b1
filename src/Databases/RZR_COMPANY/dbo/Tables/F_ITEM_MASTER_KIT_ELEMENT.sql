CREATE TABLE [dbo].[F_ITEM_MASTER_KIT_ELEMENT] (
    [ITEM_MASTER_KIT_ELEMENT_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [KIT_ITEM_MASTER_ID]         BIGINT         NOT NULL,
    [KIT_ELEMENT_ITEM_MASTER_ID] BIGINT         NOT NULL,
    [QTY]                        INT            CONSTRAINT [DF_F_ITEM_MASTER_KIT_ELEMENT_QTY] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]                BIT            CONSTRAINT [DF_F_ITEM_MASTER_KIT_ELEMENT_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                 BIT            CONSTRAINT [DF_F_ITEM_MASTER_KIT_ELEMENT_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                NVARCHAR (250) NOT NULL,
    [INSERTED_DT]                DATETIME       NOT NULL,
    [UPDATED_BY]                 NVARCHAR (250) NULL,
    [UPDATED_DT]                 DATETIME       NULL,
    [DELETED_BY]                 NVARCHAR (250) NULL,
    [DELETED_DT]                 DATETIME       NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_KIT_ELEMENT] PRIMARY KEY CLUSTERED ([ITEM_MASTER_KIT_ELEMENT_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_KIT_ELEMENT_F_ITEM_MASTER] FOREIGN KEY ([KIT_ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_KIT_ELEMENT_F_ITEM_MASTER_ELEMENT] FOREIGN KEY ([KIT_ELEMENT_ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of master items for KIT element and the Qty for each model.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_KIT_ELEMENT';


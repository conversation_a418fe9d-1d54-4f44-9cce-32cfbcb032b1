CREATE TABLE [dbo].[F_TAG] (
    [TAG_ID]       BIGINT         IDENTITY (1, 1) NOT NULL,
    [TAG_GROUP_ID] BIGINT         NULL,
    [TAG_CD]       NVARCHAR (256) NOT NULL,
    [TAG_DESC]     NVARCHAR (512) NULL,
    [INSERTED_BY]  NVARCHAR (150) CONSTRAINT [DF_D_TAG_INSERTED_BY] DEFAULT ((1)) NOT NULL,
    [INSERTED_DT]  DATETIME       CONSTRAINT [DF_D_TAG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]   NVARCHAR (150) NULL,
    [UPDATED_DT]   DATETIME       NULL,
    [TypeId]       INT            NULL,
    CONSTRAINT [PK_D_TAG] PRIMARY KEY CLUSTERED ([TAG_ID] ASC),
    CONSTRAINT [FK_F_TAG_C_TAG_TYPE] FOREIGN KEY ([TypeId]) REFERENCES [dbo].[C_TAG_TYPE] ([TAG_TYPE_ID]),
    CONSTRAINT [FK_F_TAG_F_TAG] FOREIGN KEY ([TAG_ID]) REFERENCES [dbo].[F_TAG] ([TAG_ID]),
    CONSTRAINT [FK_F_TAG_F_TAG_GROUP] FOREIGN KEY ([TAG_GROUP_ID]) REFERENCES [dbo].[F_TAG_GROUP] ([TAG_GROUP_ID]) ON DELETE CASCADE
);






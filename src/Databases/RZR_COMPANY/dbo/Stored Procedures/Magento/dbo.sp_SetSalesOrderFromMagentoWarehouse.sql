CREATE PROCEDURE [dbo].[sp_SetSalesOrderFromMagentoWarehouse]
	@SalesOrderId	BIGINT
AS
BEGIN

	DECLARE @warehouseCapability bigint = [dbo].[fn_str_GetWarehouseCapabilityId]();
	DECLARE @warehouseIds [dbo].[bigint_ID_ARRAY];

	INSERT INTO @warehouseIds
	SELECT DISTINCT
		W.[WAREHOUSE_ID]
	FROM [dbo].[F_SALES_ORDER_ITEM]					AS SOI		WITH (NOLOCK)
	INNER JOIN [dbo].[F_ITEM]						AS FI		WITH (NOLOCK)
		ON SOI.[ITEM_ID] = FI.[ITEM_ID]
	INNER JOIN [dbo].[F_ITEM_MASTER_SKU_ATTRB]		AS IMSA		WITH (NOLOCK)
		ON FI.[ITEM_MASTER_SKU_ATTRB_ID] = IMSA.[ITEM_MASTER_SKU_ATTRB_ID]
	INNER JOIN [dbo].[D_WAREHOUSE]					AS W		WITH (NOLOCK)
		ON W.[WAREHOUSE_CD] = [dbo].[fn_str_GetInventoryCapabilityValueByCapabilityTypeId](imsa.[ITEM_MASTER_SKU_ATTRB_CD], @warehouseCapability)
	WHERE SOI.[SALES_ORDER_ID] = @SalesOrderId;

	IF ((SELECT COUNT(*) FROM @warehouseIds) = 1)
	BEGIN

		DECLARE @warehouseId BIGINT = (SELECT TOP(1) [ID] FROM @warehouseIds);

		UPDATE SO SET
			SO.[WAREHOUSE_ID] = @warehouseId
		FROM [dbo].[F_SALES_ORDER] AS SO WITH (ROWLOCK)
		WHERE SO.[SALES_ORDER_ID] = @SalesOrderId
			AND ISNULL(SO.[WAREHOUSE_ID], 0) != @warehouseId;

	END

END
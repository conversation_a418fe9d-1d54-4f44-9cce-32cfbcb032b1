-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
CREATE function [dbo].[fn_str_GetInventoryCapabilityValueByCapabilityTypeId]
(
	@ItemMasterSkuAttr varchar(max)
	,@CapabilityTypeId bigint
)
RETURNS varchar(200)
AS
BEGIN
	
	DECLARE @C_RETURN_VAL varchar(200) = ''

	DECLARE @C_INVENTORY_CAPABILITY_TYPE_CD varchar(20) = CONVERT(varchar(19), @CapabilityTypeId) + '-'
	DECLARE @C_ITEM_CAPABILITY_TYPE_ID varchar(20)
		
	IF (CHARINDEX('|' + @C_INVENTORY_CAPABILITY_TYPE_CD, @ItemMasterSkuAttr) > 0
		OR CHARINDEX(CONVERT(varchar(19), @CapabilityTypeId) + '-', @ItemMasterSkuAttr) > 0 
			AND CHARINDEX(CONVERT(varchar(19), @CapabilityTypeId) + '-', @ItemMasterSkuAttr) = 1
	)
	BEGIN
	
		SET
			@C_ITEM_CAPABILITY_TYPE_ID 
			=
			--CONVERT(bigint,
			SUBSTRING(@ItemMasterSkuAttr
						, CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @ItemMasterSkuAttr) 
							+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD)
						, LEN(@ItemMasterSkuAttr) - 
							(
								CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @ItemMasterSkuAttr) 
								+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD) - 1
							)
					)			
			--)

		IF (CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) > 0)
			SET
				@C_ITEM_CAPABILITY_TYPE_ID 
				=
				SUBSTRING(@C_ITEM_CAPABILITY_TYPE_ID
							, 1
							, CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) - 1
						)			

		
		SELECT
			@C_RETURN_VAL = dic.INVENTORY_CAPABILITY_VALUE
		FROM
			D_INVENTORY_CAPABILITY dic
		WHERE
			dic.INVENTORY_CAPABILITY_ID = CONVERT(bigint, @C_ITEM_CAPABILITY_TYPE_ID)

	END
	
	RETURN @C_RETURN_VAL

END

-- restored as part of RSW-10961
-- move functionality AS IS (WITHOUT REVIEW)
-- TODO: need review
CREATE PROCEDURE [dbo].[sp_SET_CONTRACT_CONSIGNMENTS_CLONE]
     @CONTRACT_ID			BIGINT
    ,@CLONED_CONTRACT_ID	BIGINT
	,@UserId				bigint
	,@UserIp				bigint
AS
BEGIN

	DECLARE
	   @spName NVARCHAR(128)	= isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid),
	   @utcNow DATETIME			= getUtcDate()

	DECLARE @T_IDS TABLE(TEMP_ID BIGINT, REAL_ID BIGINT)
    DECLARE 
	   --@COMMODITIES	bigint_PARE_ARRAY			 
	    @SPLIT_TIERS	recycling.ConsignmentSplitTier	 
	    ,@CONSIGNMENTS	[recycling].[ConsignmentRule]


    --INSERT INTO @COMMODITIES(ID, VALUE)
    --SELECT
	   ---1 * C.CONSIGNMENT_ID
	   --,CC.RECYCLING_ITEM_MASTER_ID
    --FROM F_CONSIGNMENT_RECYCLING_ITEM_MASTER	CC
    --INNER JOIN F_CONTRACT_CONSIGNMENT	C
    --  ON C.CONSIGNMENT_ID = CC.CONSIGNMENT_ID
    --WHERE C.CONTRACT_ID = @CONTRACT_ID

	insert into F_CONTRACT_CONSIGNMENT(
		CONTRACT_ID			
		,[NAME]						
		,IS_FLAT_SPLIT	
		,IS_WHOLE_ORDER
		,STORE_SPLIT					
		,IS_CONVERT_TO_INV_AFTER_EXPIRED	
		,INCLUDE_EBAY_FEES				
		,INCLUDE_PAYPAL_FEES			
		,INCLUDE_SHIPPING_FEES	
		,IncludeShipping
		,IncludeMiscCharge
		,INCLUDE_CARD_HANDLING_FEES		
		,FEE_PER_INVENTORY_ITEM			
		,FEE_PER_SHIPPED_ITEM			
		,FEE_FOR_PACKAGING
		,INSERTED_DT
		,INSERTED_BY
		,INSERTED_BY_USER_ID
		,INSERTED_BY_USER_IP
	) 
	OUTPUT
		CAST(INSERTED.INSERTED_BY AS BIGINT),
		INSERTED.CONSIGNMENT_ID
	INTO @T_IDS(TEMP_ID, REAL_ID)
	SELECT
		@CLONED_CONTRACT_ID
		,NAME						
		,IS_FLAT_SPLIT	
		,IS_WHOLE_ORDER						
		,STORE_SPLIT					
		,IS_CONVERT_TO_INV_AFTER_EXPIRED	
		,INCLUDE_EBAY_FEES				
		,INCLUDE_PAYPAL_FEES			
		,INCLUDE_SHIPPING_FEES			
		,IncludeShipping
		,IncludeMiscCharge
		,INCLUDE_CARD_HANDLING_FEES		
		,FEE_PER_INVENTORY_ITEM			
		,FEE_PER_SHIPPED_ITEM			
		,FEE_FOR_PACKAGING		
		,@utcNow
		,CAST(c.CONSIGNMENT_ID AS NVARCHAR(50)) -- use to bind the related objects
		,@UserId
		,@UserIp
	FROM F_CONTRACT_CONSIGNMENT	C
	WHERE C.CONTRACT_ID = @CONTRACT_ID
    
	INSERT INTO [dbo].[F_CONSIGNMENT_SPLIT_TIER]
	(
			CONSIGNMENT_ID   
			,FROM_SUM           
			,TO_SUM             
			,[VALUE]   
			,INSERTED_BY_USER_ID 
			,INSERTED_BY_USER_IP
	)
    SELECT
	   c.REAL_ID
	   ,T.FROM_SUM
	   ,T.TO_SUM
	   ,T.VALUE
	   ,@UserId
	   ,@UserIp
    FROM F_CONSIGNMENT_SPLIT_TIER		t
	INNER JOIN @T_IDS   c
	     ON t.CONSIGNMENT_ID = c.TEMP_ID 

	update c set
		[ContractConsignmentId] = t.REAL_ID
	from [recycling].[F_CommodityRule] c
	inner join [recycling].[F_CommodityRule] cc
		on c.CommodityId = cc.CommodityId
	inner join @T_IDS   t
	     ON cc.[ContractConsignmentId] = t.TEMP_ID 
	where c.ContractId = @CLONED_CONTRACT_ID and cc.ContractId = @CONTRACT_ID
    
END
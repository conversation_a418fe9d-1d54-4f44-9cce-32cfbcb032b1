CREATE TABLE [dbo].[C_INVOICE_PAYMENT_TYPE] (
    [INVOICE_PAYMENT_TYPE_ID]    INT           NOT NULL,
    [INVOICE_PAYMENT_TYPE_NAME]  VARCHAR (100) NOT NULL,
    [PAYMENT_TYPE_BY_ACCOUNT_ID] INT           NULL,
    [SELF_ID]                    AS            (isnull(CONVERT([bigint],[INVOICE_PAYMENT_TYPE_ID],0),CONVERT([bigint],(-1),0))),
    [SELF_NAME]                  AS            ([INVOICE_PAYMENT_TYPE_NAME]),
    [SELF_CREATE_DATE]           AS            (isnull(CONVERT([datetime],'2000-01-01',0),CONVERT([datetime],'2000-01-01',0))),
    [PARENT_ID]                  AS            (CONVERT([bigint],isnull(NULL,(-1)),0)),
    CONSTRAINT [PK_C_INVOICE_PAYMENT_TYPE] PRIMARY KEY CLUSTERED ([INVOICE_PAYMENT_TYPE_ID] ASC),
    CONSTRAINT [FK_C_INVOICE_PAYMENT_TYPE_D_ACCOUNT_TYPE] FOREIGN KEY ([PAYMENT_TYPE_BY_ACCOUNT_ID]) REFERENCES [dbo].[D_ACCOUNT_TYPE] ([ACCOUNT_TYPE_ID])
);








GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The invoice type of payment ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_INVOICE_PAYMENT_TYPE';


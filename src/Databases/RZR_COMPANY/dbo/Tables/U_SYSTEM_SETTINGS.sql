CREATE TABLE [dbo].[U_SYSTEM_SETTINGS] (
	[SETTING_ID]                                INT             IDENTITY (1, 1) NOT NULL,
	[COMPANY_ID]                                BIGINT          NOT NULL,
	[MAIN_CUSTOMER_ID]                          BIGINT          NULL,
	[EBAY_API_URL]                              NVARCHAR (1024) NULL,
	[EBAY_TOKEN]                                NVARCHAR (MAX)  NULL,
	[EBAY_DEVELOPER_ID]                         NVARCHAR (1024) NULL,
	[EBAY_APPLICATION_ID]                       NVARCHAR (1024) NULL,
	[EBAY_CERTIFICATE_ID]                       NVARCHAR (1024) NULL,
	[EBAY_CATEGORY_HIERARCHY_VERSION]           NVARCHAR (50)   NULL,
	[PREFIX_SALES_ORDER]                        NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_SALES_ORDER] DEFAULT (N'SO') NOT NULL,
	[PREFIX_SALES_QUOTE]                        NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_SALES_ORDER1] DEFAULT (N'SQ') NOT NULL,
	[PREFIX_INVOICE]                            NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_INVOICE] DEFAULT (N'INV') NOT NULL,
	[PREFIX_PURCHASE_ORDER]                     NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_PURCHASE_ORDER] DEFAULT (N'PO') NOT NULL,
	[PREFIX_INBOUND_ORDER]                      NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_INBOUND_ORDER] DEFAULT (N'O') NOT NULL,
	[PREFIX_INBOUND_QUOTE]                      NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_OUTBOUND_QUOTE] DEFAULT (N'Q') NOT NULL,
	[PREFIX_OUTBOUND_ORDER]                     NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_OUTBOUND_ORDER] DEFAULT (N'OUT') NOT NULL,
	[STARTING_NUMBER_SALES_ORDER]               BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_SALES_ORDER] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_INVOICE]                   BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_INVOICE] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_PURCHASE_ORDER]            BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_PURCHASE_ORDER] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_INBOUND_ORDER]             BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_INBOUND_ORDER] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_INBOUND_QUOTE]             BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_OUTBOUND_QUOTE] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_OUTBOUND_ORDER]            BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_OUTBOUND_ORDER] DEFAULT ((10000)) NOT NULL,
	[STARTING_NUMBER_CONTRACT]                  BIGINT          CONSTRAINT [DF_U_SYSTEM_SETTINGS_STARTING_NUMBER_OUTBOUND_ORDER1] DEFAULT ((10000)) NOT NULL,
	[HEADER_IMAGE_ID]                           INT             NULL,
	[CLIENT_PORTAL_IMAGE_ID]                    INT             NULL,
	[HEADER_PDF_IMAGE_ID]                       INT             NULL,
	[SIGNATURE_IMAGE_ID]                        INT             NULL,
	[HEADER_IMAGE_WIDTH]                        INT             NULL,
	[HEADER_IMAGE_HEIGHT]                       INT             NULL,
	[PDF_HEADER_IMAGE_WIDTH]                    INT             NULL,
	[PDF_HEADER_IMAGE_HEGHT]                    INT             NULL,
	[DB_VERSION]                                INT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_DB_VERSION] DEFAULT ((0)) NOT NULL,
	[BLANCO_WEB_SERVER_URL]                     NVARCHAR (256)  NULL,
	[BLANCO_USERNAME]                           NVARCHAR (256)  NULL,
	[BLANCO_PASSWORD]                           NVARCHAR (256)  NULL,
	[IS_TAG_TYPES_INTERSECTION]                 BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_TAG_TYPES_INTERSECTION] DEFAULT ((1)) NOT NULL,
	[IS_CHECK_DIMENSIONS]                       BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_CHECK_DIMENSIONS] DEFAULT ((1)) NOT NULL,
	[IS_CHECK_LOCATION_CAPACITY]                BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_CHECK_LOCATION_CAPACITY] DEFAULT ((1)) NOT NULL,
	[IS_CHECK_LOCATION_FOR_RECEIVE_INVENTORY]   BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_CHECK_LOCATION_FOR_RECEIVE_INVENTORY] DEFAULT ((1)) NOT NULL,
	[IS_HECI_ENABLED]                           BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_HECI_ENABLED] DEFAULT ((0)) NOT NULL,
	[IS_EXTRA_COLUMNS_FOR_UNALLOCATED]          BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_HECI_ENABLED1] DEFAULT ((0)) NOT NULL,
	[SETTLEMENT_AP_AR_LOGIC_ON]                 INT             NULL, -- NULL = ask, values from "SettleAs" enum
	[SYSTEM_CURRENCY_ID]                        INT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_SYSTEM_CURRENCY_ID] DEFAULT ((130)) NOT NULL,
	[PREFER_CURRENCY_ABBREVIATION]              BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFER_CURRENCY_ABBREVIATION] DEFAULT ((0)) NOT NULL,
	[IS_ALLOW_GENERATION_OF_DUPLICATED_SERIALS] BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_ALLOW_GENERATION_OF_DUPLICATED_SERIALS] DEFAULT ((0)) NOT NULL,
	[IS_DISPLAY_CUSTOMER_CODE]                  BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_DISPLAY_CUSTOMER_CODE] DEFAULT ((0)) NOT NULL,
	[DEFAULT_WARRANTY_ID]                       INT             NULL,
	[IS_SOURCE_TYPE_REQUIRED_FOR_APPROVE]       BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_SOURCE_TYPE_REQUIRED_FOR_APPROVE] DEFAULT ((0)) NOT NULL,
	[IsEuropeanAutoDeclaration]                 BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsDeclarationByData] DEFAULT ((0)) NULL,
	[IsContactEmailPrivate]                     BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsContactEmailPrivate] DEFAULT ((0)) NULL,
	[IsSkuDimensions]                           BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsSkuDimensions] DEFAULT ((0)) NULL,
	[IsSkuSplitByWarehouse]                     BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsSkuByWarehouse] DEFAULT ((0)) NULL,
	[IsAutoApplySalesTax]                       BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsAutoApplySalesTax] DEFAULT ((0)) NULL,
	[IsFullCountryName]                         BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsFullCountryName] DEFAULT ((0)) NULL,
	[PREFIX_CREDIT_MEMO]                        NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_CREDIT_MEMO] DEFAULT (N'CM') NULL,
	[PREFIX_RMA_INVOICE]                        NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_RMA_INVOICE] DEFAULT (N'R') NOT NULL,
	[IsAllowDuplicatedHddSerial]                BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsAllowDuplicatedHddSerial] DEFAULT ((1)) NOT NULL,
	[IsAllowAddConsignmentItemToPoAfterReturn]  BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsAllowAddConsignmentItemToPoAfterReturn] DEFAULT ((1)) NOT NULL,
	[DefaultPurchaseOrderItemCost]              MONEY           NULL,
	[PREFIX_BATCH]                              NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_BATCH] DEFAULT (N'B') NULL,
	[DefaultShreddingCommodityId]               BIGINT          NULL,
	[PREFIX_JOB]                                NVARCHAR (10)   CONSTRAINT [DF_U_SYSTEM_SETTINGS_PREFIX_JOB] DEFAULT (N'J') NULL,
	[IsShowOnlyEcomAvailableItemsToQualify]     BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsShowOnlyEcomAvailableItemsToQualify] DEFAULT ((1)) NOT NULL,
	[DateFormat]                                INT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_DateFormat] DEFAULT ((0)) NOT NULL,
	[TimeFormat]                                INT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_TimeFormat] DEFAULT ((0)) NOT NULL,
	[IsWorkflowRuleEnabled]                     BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsWorkflowRuleEnabled] DEFAULT ((0)) NULL,
	[IS_USER_EMAIL_SETTINGS_ALLOWED]            BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IS_USER_EMAIL_SETTINGS_ALLOWED] DEFAULT ((0)) NOT NULL,
	[IsShowAllowQualifiedItemsWithZeroOnHand]   BIT             CONSTRAINT [DF_U_SYSTEM_SETTINGS_IsShowAllowQualifiedItemsWithZeroOnHand] DEFAULT ((1)) NOT NULL,
	[PrefixCustomerCode]                        NVARCHAR (10)   NULL,
	[PrefixCustomerCodeMode]                    INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_PrefixCustomerCodeMode] DEFAULT ((0)) NOT NULL,
	[IsAutoPriceRulesEnabled]                   BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_IsAutoPriceRulesEnabled] DEFAULT ((0)) NOT NULL,
	[OneTimePasswordGroupsCount]                INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_OneTimePasswordGroupsCount] DEFAULT ((1)) NOT NULL,
	[OneTimePasswordGroupLength]                INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_OneTimePasswordGroupLength] DEFAULT ((6)) NOT NULL,
	[OneTimePasswordLifetimeInSeconds]          INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_OneTimePasswordLifetimeInSeconds] DEFAULT ((120)) NOT NULL,
	[OneTimePasswordMaxAttempts]                INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_OneTimePasswordMaxAttempts] DEFAULT ((3)) NOT NULL,
	[RequireCustomerVendor]                     BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_RequireCustomerVendor]  DEFAULT ((0)) NOT NULL,
	[RequireContactMainPhone]                   BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_RequireContactMainPhone]  DEFAULT ((0)) NOT NULL,
	[RequireContactMainEmail]                   BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_RequireContactMainEmail]  DEFAULT ((0)) NOT NULL,
	[RequirePoNeedByDate]                       BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_RequirePoNeedByDate]  DEFAULT ((0)) NOT NULL,
	[IsIpnMpnEnabled]                           BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_IsIpnMpnEnabled]  DEFAULT ((0)) NOT NULL,
	[RemoveAuditToolReportsAfterDays]           INT             CONSTRAINT [D_U_SYSTEM_SETTINGS_RemoveAuditToolReportsAfterDays] DEFAULT ((366)) NOT NULL,
	[IsShowItemsWhichAreNotInTheWarehouse]      BIT             CONSTRAINT [D_U_SYSTEM_SETTINGS_IsShowItemsWhichAreNotInTheWarehouse] DEFAULT ((0)) NOT NULL,
	[DriveSizeAttributeId]						INT				NULL,
	[DriveSizeReceivedAttributeId]				INT				NULL,
	[DefaultRemovedDriveWorkflowStepId]			BIGINT			NULL,
	[IsHideDeleteDataErasureButton]				BIT				CONSTRAINT [D_U_SYSTEM_SETTINGS_IsHideDeleteDataErasureButton] DEFAULT ((0)) NOT NULL,
	[IsIncludeNewLocationsInSalesChannels]		BIT				CONSTRAINT [D_U_SYSTEM_SETTINGS_IsIncludeNewLocationsInSalesChannels] DEFAULT ((0)) NOT NULL,
	[IsAllowPrimaryAccountsReceiveNotifications]BIT				CONSTRAINT [D_U_SYSTEM_SETTINGS_IsAllowPrimaryAccountsReceiveNotifications] DEFAULT ((0)) NOT NULL,
	CONSTRAINT [PK_U_SYSTEM_SETTINGS] PRIMARY KEY CLUSTERED ([SETTING_ID] ASC),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_C_COUNTRY_CURRENCY] FOREIGN KEY ([SYSTEM_CURRENCY_ID]) REFERENCES [dbo].[C_COUNTRY_CURRENCY] ([CURRENCY_ID]),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_C_WARRANTY] FOREIGN KEY ([DEFAULT_WARRANTY_ID]) REFERENCES [dbo].[C_WARRANTY] ([ID]),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_DefaultShreddingCommodity] FOREIGN KEY ([DefaultShreddingCommodityId]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_C_INVENTORY_CAPABILITY_TYPE] FOREIGN KEY ([DriveSizeAttributeId]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID]),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_C_INVENTORY_CAPABILITY_TYPE_Received] FOREIGN KEY ([DriveSizeReceivedAttributeId]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID]),
	CONSTRAINT [FK_U_SYSTEM_SETTINGS_C_AssetWorkflowStepId] FOREIGN KEY ([DefaultRemovedDriveWorkflowStepId]) REFERENCES [recycling].[C_AssetWorkflowStep] ([Id]) ON DELETE SET NULL
);

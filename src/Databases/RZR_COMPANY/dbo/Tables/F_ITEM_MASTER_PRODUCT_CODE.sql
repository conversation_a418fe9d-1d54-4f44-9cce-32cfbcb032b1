CREATE TABLE [dbo].[F_ITEM_MASTER_PRODUCT_CODE] (
    [ITEM_MASTER_PRODUCT_CODE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]              BIGINT        NOT NULL,
    [PRODUCT_CODE_ID]             BIGINT        NOT NULL,
    [IS_INACTIVE]                 BIT           CONSTRAINT [DF_F_ITEM_MASTER_PRODUCT_CODE_IS_INACTIVE] DEFAULT ((0)) NULL,
    [IS_DELETED]                  BIT           CONSTRAINT [DF_F_ITEM_MASTER_PRODUCT_CODE_IS_DELETED] DEFAULT ((0)) NULL,
    [INSERTED_BY]                 VARCHAR (150) NULL,
    [INSERTED_DT]                 DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_PRODUCT_CODE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                  VARCHAR (150) NULL,
    [UPDATED_DT]                  DATETIME      NULL,
    [DELETED_DT]                  DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_PRODUCT_CODE] PRIMARY KEY CLUSTERED ([ITEM_MASTER_PRODUCT_CODE_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_PRODUCT_CODE_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_MASTER_PRODUCT_CODE_F_PRODUCT_CODE] FOREIGN KEY ([PRODUCT_CODE_ID]) REFERENCES [dbo].[F_PRODUCT_CODE] ([PRODUCT_CODE_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);
GO

CREATE UNIQUE NONCLUSTERED INDEX [UK_F_ITEM_MASTER_PRODUCT_CODE]
    ON [dbo].[F_ITEM_MASTER_PRODUCT_CODE]([ITEM_MASTER_ID] ASC, [PRODUCT_CODE_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PRODUCT_CODE_ID]
    ON [dbo].[F_ITEM_MASTER_PRODUCT_CODE]([PRODUCT_CODE_ID] ASC);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Item master product code', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_PRODUCT_CODE';


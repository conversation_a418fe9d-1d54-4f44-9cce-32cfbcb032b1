-- =============================================
-- Author:		<PERSON>
-- Create date: 12/08/2015
-- Description:	Gets visibility of financial blocks on CP for customer
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS]
	@C_CUSTOMER_ID			BIGINT
AS
BEGIN
	SELECT
		fib.FINANCIAL_INFO_BLOCK_ID				AS FinancialInfoBlockId
		,fib.FINANCIAL_INFO_BLOCK_NAME			AS FinancialInfoBlockName
		,CASE 
			WHEN cfib.CUSTOMER_FINANCIAL_INFO_BLOCK_ID IS NOT NULL
			THEN cfib.IS_ENABLED
			ELSE 0
		END										AS IsEnabled
	FROM [cp].C_FINANCIAL_INFO_BLOCK						fib		WITH(NOLOCK)
	LEFT JOIN [cp].[F_CUSTOMER_FINANCIAL_INFO_BLOCK]		cfib	WITH(NOLOCK)
		ON cfib.CUSTOMER_ID = @C_CUSTOMER_ID
		AND fib.FINANCIAL_INFO_BLOCK_ID = cfib.FINANCIAL_INFO_BLOCK_ID
END
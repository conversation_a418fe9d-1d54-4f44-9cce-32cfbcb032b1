CREATE PROCEDURE [rzrapi].[sp_CreateServiceOption]
(
	@Service [dbo].[AutoPricingService] READONLY,
	@ServiceSubcategories [dbo].[ServiceSubcategory] READONLY,
	@ServiceSubcategoryPositions [dbo].[ServiceSubcategoryPosition] READONLY,
	@UserId BIGINT,
	@UserIp BIGINT
)
AS
BEGIN

	DECLARE
		@spName NVARCHAR(50) = ISNULL(OBJECT_SCHEMA_NAME(@@procid) + N'.', N'') + OBJECT_NAME(@@procid),
		@utcNow DATETIME = GETUTCDATE();

	SET XACT_ABORT ON;
	BEGIN TRANSACTION

		-- Save service subcategories
		DECLARE @subcategoryIds TABLE
		(
			[OriginalId] BIGINT,
			[InsertedId] BIGINT NOT NULL
		);

		MERGE [dbo].[D_ServiceSubcategory] AS t
		USING (
			SELECT
				subCat.[Id]											AS Id,
				ISNULL(servIds.[Id], subCat.[ServiceId])			AS [ServiceId],
				subCat.[Name]										AS Name,
				subCat.[IsRadioButton]								AS IsRadioButton
			FROM @ServiceSubcategories AS subCat
			LEFT JOIN @Service AS servIds
				ON subCat.[ServiceId] = servIds.Id
		) AS s
		ON t.[Id] = s.[Id]
		WHEN NOT MATCHED BY TARGET
			THEN INSERT (
				[Name],
				[ServiceId],
				[IsRadioButton],
				[InsertedBy],
				[InsertedDate],
				[InsertedByUserId],
				[InsertedByUserIp]
			)
			VALUES (
				s.[Name],
				s.[ServiceId],
				s.[IsRadioButton],
				@spName,
				@utcNow,
				@UserId,
				@UserIp
			)
		OUTPUT s.[Id] AS [OriginalId], inserted.[Id] AS [InsertedId] INTO @subcategoryIds;


		-- Save service subcategories positions
		MERGE [dbo].[D_ServiceSubcategoryPosition] AS t
		USING (
			SELECT
				positions.[Id]												AS Id,
				ISNULL(subcatIds.[InsertedId], positions.[SubcategoryId])	AS [SubcategoryId],
				positions.[Name]											AS Name,
				positions.[Pos]												AS Pos
			FROM @ServiceSubcategoryPositions AS positions
			LEFT JOIN @subcategoryIds AS subcatIds
				ON positions.[SubcategoryId] = subcatIds.[OriginalId]
		) AS s
		ON t.[Id] = s.[Id]
		WHEN NOT MATCHED BY TARGET
			THEN INSERT (
				[Name],
				[SubcategoryId],
				[Pos],
				[InsertedBy],
				[InsertedDate],
				[InsertedByUserId],
				[InsertedByUserIp]
			)
			VALUES (
				s.[Name],
				s.[SubcategoryId],
				CASE WHEN (SELECT TOP(1) MAX(POS) + 1 AS Po FROM [dbo].[D_ServiceSubcategoryPosition] t WHERE t.SubcategoryId = s.[SubcategoryId]) IS NOT NULL 
					THEN (SELECT TOP(1) MAX(POS) + 1 AS Po FROM [dbo].[D_ServiceSubcategoryPosition] t WHERE t.SubcategoryId = s.[SubcategoryId]) 
					ELSE 1 END,
				@spName,
				@utcNow,
				@UserId,
				@UserIp
			);

	COMMIT TRANSACTION

	SELECT TOP(1) [Id] FROM @Service

END
-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 14.03.2014
-- Description:	Delete recycling order item image by image id
-- =============================================
CREATE PROCEDURE [dbo].[sp_DELETE_RECYCLING_ORDER_ITEM_IMAGE]
	@imageId bigint
AS
BEGIN
	WITH tree (RECYCLING_ORDER_ITEM_IMAGE_ID, RECYCLING_ORDER_ITEM_IMAGE_PARENT_ID) as 
	(
		SELECT
			RECYCLING_ORDER_ITEM_IMAGE_ID,
			RECYCLING_ORDER_ITEM_IMAGE_PARENT_ID
		FROM dbo.F_RECYCLING_ORDER_ITEM_IMAGE	WITH(ROWLOCK)
		WHERE RECYCLING_ORDER_ITEM_IMAGE_ID = @imageId

		UNION ALL

		SELECT
			c2.RECYCLING_ORDER_ITEM_IMAGE_ID,
			c2.RECYCLING_ORDER_ITEM_IMAGE_PARENT_ID
		FROM dbo.F_RECYCLING_ORDER_ITEM_IMAGE	c2 WITH(ROWLOCK) 
		INNER JOIN tree
			ON tree.RECYCLING_ORDER_ITEM_IMAGE_ID = c2.RECYCLING_ORDER_ITEM_IMAGE_PARENT_ID
	)

	DELETE FROM i
	FROM dbo.F_RECYCLING_ORDER_ITEM_IMAGE i WITH(ROWLOCK)
	INNER JOIN tree
		ON i.RECYCLING_ORDER_ITEM_IMAGE_ID = tree.RECYCLING_ORDER_ITEM_IMAGE_ID
END

-- =============================================
-- Author:		<<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>>
-- Create date: <01/16/2014>
-- Description:	<get item info by sku>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ITEM_BY_SKU]
	@SKU BIGINT
AS
BEGIN
	SELECT
		M.ITEM_NUMBER
		,M.ITEM_MASTER_ID
		,M.MANUFACTURER_ID
		,MFG.MANUFACTURER_CD
		,I.CONDITION_ID
		,C.ITEM_CONDITION_DESC	 
		,IL.ITEM_LOT_VALUE
		,IMSA.ITEM_MASTER_SKU_ATTRB_CD
	FROM F_ITEM					I	WITH(NOLOCK)
	INNER JOIN F_ITEM_MASTER	M	WITH(NOLOCK)
		ON I.ITEM_MASTER_ID = M.ITEM_MASTER_ID	
	INNER JOIN D_MANUFACTURER	MFG WITH(NOLOCK)
		ON M.MANUFACTURER_ID  = MFG.MANUFACTURER_ID	
	INNER JOIN D_ITEM_CONDITION	C	WITH(NOLOCK)
		ON I.CONDITION_ID = C.ITEM_CONDITION_ID		
	LEFT JOIN F_ITEM_LOT IL WITH (NOLOCK)	
		ON I.ITEM_ID = IL.ITEM_ID
	LEFT JOIN dbo.F_ITEM_MASTER_SKU_ATTRB 	IMSA	WITH (NOLOCK)
		ON IMSA.ITEM_MASTER_SKU_ATTRB_ID = I.ITEM_MASTER_SKU_ATTRB_ID
	WHERE 
		I.ITEM_ID = @SKU
END
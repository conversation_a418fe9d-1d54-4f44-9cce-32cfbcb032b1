CREATE TABLE [dbo].[C_RecyclingQuoteStatus]
(
	[Id]				int not null,
	[Name]				nvarchar(100) not null,
    [IsInitial]         bit not null,
    [IsCanceled]        bit not null,
    [IsConvertToOrder]  bit not null,
    [InsertedByUserId]	bigint not null,
    [InsertedBy]        nvarchar(250) null,
    [InsertedDate]      datetime not null
        constraint [DF_C_RecyclingQuoteStatus_InsertedDate] default (getutcdate()), 
    constraint [PK_C_RecyclingQuoteStatus_Id] primary key clustered ([Id] ASC),
    constraint [FK_C_RecyclingQuoteStatus_tb_User] foreign key ([InsertedByUserId]) references [dbo].[tb_User] ([UserID])
);

GO
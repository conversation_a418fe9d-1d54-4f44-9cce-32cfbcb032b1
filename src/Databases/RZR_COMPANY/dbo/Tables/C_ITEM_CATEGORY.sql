CREATE TABLE [dbo].[C_ITEM_CATEGORY] (
    [ITEM_CATEGORY_ID]   BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_CATEGORY_CD]   VARCHAR (50)  NULL,
    [ITEM_CATEGORY_DESC] VARCHAR (250) NULL,
    [IS_AUTOMATIC]       BIT           CONSTRAINT [DF_D_ITEM_CATEGORY_IS_AUTOMATIC] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]        BIT           NOT NULL,
    [IS_DELETED]         BIT           NOT NULL,
    [INSERTED_BY]        VARCHAR (150) NOT NULL,
    [INSERTED_DT]        DATETIME      NOT NULL,
    [UPDATED_BY]         VARCHAR (150) NULL,
    [UPDATED_DT]         DATETIME      NULL,
    [DELETED_BY]         VARCHAR (150) NULL,
    [DELETED_DT]         DATETIME      NULL,
    CONSTRAINT [PK_C_ITEM_CATEGORY] PRIMARY KEY CLUSTERED ([ITEM_CATEGORY_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Imported list of Aida categories', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_ITEM_CATEGORY';


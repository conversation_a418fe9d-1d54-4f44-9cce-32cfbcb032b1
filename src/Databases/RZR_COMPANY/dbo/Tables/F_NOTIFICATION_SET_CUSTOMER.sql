CREATE TABLE [dbo].[F_NOTIFICATION_SET_CUSTOMER] (
	[NOTIFICATION_SET_CUSTOMER_ID]		BIGINT			IDENTITY (1, 1) NOT NULL,
	[NOTIFICATION_SET_ID]				BIGINT			NOT NULL,
	[CUSTOMER_ID]						BIGINT			NOT NULL,
	[INSERTED_BY]						VARCHAR (150)	CONSTRAINT [DF_F_NOTIFICATION_SET_CUSTOMER_INSERTED_BY] DEFAULT ('admin') NOT NULL,
	[INSERTED_DT]						DATETIME		CONSTRAINT [DF_F_NOTIFICATION_SET_CUSTOMER_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
	[UPDATED_BY]						VARCHAR (150)	NULL,
	[UPDATED_DT]						DATETIME		NULL,
	[DELETED_BY]						VARCHAR (150)	NULL,
	[DELETED_DT]						DATETIME		NULL
	CONSTRAINT [PK_F_NOTIFICATION_SET_CUSTOMER] PRIMARY KEY CLUSTERED ([NOTIFICATION_SET_CUSTOMER_ID] ASC),
	CONSTRAINT [FK_F_NOTIFICATION_SET_CUSTOMER_F_NOTIFICATION_SET] FOREIGN KEY ([NOTIFICATION_SET_ID]) REFERENCES [dbo].[F_NOTIFICATION_SET] ([NOTIFICATION_SET_ID]),
	CONSTRAINT [FK_F_NOTIFICATION_SET_CUSTOMER_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
	CONSTRAINT [UNIQ_F_NOTIFICATION_SET_CUSTOMER] UNIQUE([CUSTOMER_ID])
);

GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of notification set allowed for customer', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_NOTIFICATION_SET_CUSTOMER';

GO

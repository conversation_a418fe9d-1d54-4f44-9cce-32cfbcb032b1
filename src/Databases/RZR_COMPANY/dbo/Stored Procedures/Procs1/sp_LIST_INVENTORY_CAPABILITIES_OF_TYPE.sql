-- =============================================
-- Author: <Oleg K. Evseev>
-- Create date: <08/05/2013>
-- Description: <lists inventory capability variants by the provided capability type>
--		!!! The term parameter is expected to come without bordering percent signs
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_INVENTORY_CAPABILITIES_OF_TYPE]
	(@TERM		 varchar(MAX) = N''
	,@ITEM_COUNT int = 20
	,@TYPE_ID	 BIGINT
	,@ExactMatch bit = 0)
AS
BEGIN
	SET @TERM = LTRIM(RTRIM(@TERM))
	
	DECLARE
		@TERM_SHIELDED varchar(MAX) = IIF(@ExactMatch = 1, 
			@TERM,
			REPLACE(REPLACE(REPLACE(REPLACE(@TERM, '\', '\\'), '%', '\%'), '[', '\['), '_', '\_'))

	SELECT TOP(@ITEM_COUNT)
		 t.*
	FROM (
		SELECT
			INVENTORY_CAPABILITY_ID						AS value
			,LTRIM(RTRIM(INVENTORY_CAPABILITY_VALUE))	AS label
			-- do not remove
			,INVENTORY_CAPABILITY_ID					AS Id		
			,INVENTORY_CAPABILITY_VALUE					AS Name
			,INVENTORY_CAPABILITY_TYPE_ID				AS TypeId

			,CASE
				WHEN LTRIM(RTRIM(INVENTORY_CAPABILITY_VALUE)) = @TERM
				THEN 0		-- exact match
				WHEN LTRIM(RTRIM(INVENTORY_CAPABILITY_VALUE)) LIKE @TERM_SHIELDED + '%' escape '\'
				THEN 1		-- at the beginning
				ELSE 2		-- in the middle or at the end
			END											AS Ord

		FROM dbo.D_INVENTORY_CAPABILITY		WITH (NOLOCK)
		WHERE INVENTORY_CAPABILITY_TYPE_ID = @TYPE_ID 
			AND (@ExactMatch = 1 and LTRIM(RTRIM(INVENTORY_CAPABILITY_VALUE)) = @TERM_SHIELDED
				or @ExactMatch = 0 and LTRIM(RTRIM(INVENTORY_CAPABILITY_VALUE)) LIKE '%' + @TERM_SHIELDED + '%' escape '\')
			AND IS_INACTIVE = 0
	) t
	ORDER BY
		t.Ord
		,t.label
END
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_RECIPIENT_EMAIL]  
	@SALES_ORDER_ID BIGINT
AS
BEGIN
	SELECT
		fcc.MAIN_EMAIL AS [EMAIL]
	FROM [F_SHIPPING] fsos
	INNER JOIN dbo.F_CUSTOMER_CONTACT fcc WITH(NOLOCK)
		ON fcc.CUSTOMER_ID = fsos.RECIPIENT_CUSTOMER_ID AND fcc.IS_MAIN = 1
	WHERE fsos.SALES_ORDER_ID = @SALES_ORDER_ID
END
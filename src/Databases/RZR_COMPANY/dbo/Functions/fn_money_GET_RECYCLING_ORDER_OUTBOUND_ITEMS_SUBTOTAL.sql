CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_ORDER_OUTBOUND_ITEMS_SUBTOTAL] (
	@ORDER_ID	BIGINT
)
RETURNS FLOAT
AS
BEGIN

	RETURN ISNULL((
		SELECT SUM(dbo.fn_money_GET_ITEM_PRICE_BY_TYPE_RECYCLING(
			OI.ITEM_PRICE_OUTBOUND, 
			OI.PRICE_TYPE_ID_OUTBOUND, 
			OI.ITEM_COUNT, 
			OI.WEIGHT_RECEIVED, 
			OI.WEIGHT_TARE))
		FROM F_RECYCLING_ORDER_ITEM						AS OI	WITH(NOLOCK)
		LEFT JOIN recycling.F_SortedLotFromBatch		AS FSL	WITH(NOLOCK)
			ON FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
		WHERE OI.OUTBOUND_ORDER_ID = @ORDER_ID
			AND OI.USE_FOR_PRICE_CALCULATION = 1
			AND FSL.InputId IS NULL
	), 0.0);

END
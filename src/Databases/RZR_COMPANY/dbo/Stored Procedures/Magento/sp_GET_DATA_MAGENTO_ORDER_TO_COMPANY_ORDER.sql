-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_DATA_MAGENTO_ORDER_TO_COMPANY_ORDER] 
	@MAGENTO_ORDER_ID BIGINT,
	@MAGENTO_SHOP_ID INT
AS
BEGIN
	
	SELECT 
		mop.QUANTITY_ORDERED			AS [QuantityOrdered],
		mop.PRICE						AS [Price],
		fi.CONDITION_ID					AS [ConditionId],
		fi.ITEM_MASTER_ID				AS [ItemMasterId],
		fi.ITEM_ID						AS [Sku]
	FROM MAGENTO_ORDER_PRODUCTS AS mop
	INNER JOIN MAGENTO_PRODUCTS AS mp 
		ON mp.M_PRODUCT_ID = mop.M_PRODUCT_ID AND 
		   mp.SHOP_ID = @MAGENTO_SHOP_ID
	INNER JOIN F_ITEM AS fi 
		ON CAST (fi.ITEM_ID AS NVARCHAR) = mp.SKU
	WHERE 
		mop.M_ORDER_ID = @MAGENTO_ORDER_ID AND
		mop.SHOP_ID = @MAGENTO_SHOP_ID
END
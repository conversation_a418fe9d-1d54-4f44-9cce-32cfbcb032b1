CREATE TABLE [dbo].[tb_UserRole] (
    [UserRoleID]     INT           IDENTITY (1, 1) NOT NULL,
    [UserRoleCD]     NVARCHAR (50)  NOT NULL,
    [UserRoleDesc]   NVARCHAR (100) NULL,
    [MASTER_ROLE_ID] INT           CONSTRAINT [DF_tb_UserRole_MASTER_ROLE_ID] DEFAULT ((1)) NULL,
    [IS_SYSTEM_ROLE] BIT           CONSTRAINT [DF_tb_UserRole_SystemRole] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_tb_UserRole] PRIMARY KEY CLUSTERED ([UserRoleID] ASC)
);










GO
CREATE NONCLUSTERED INDEX [IX_USER_ROLE_ID_ROLE_CD]
    ON [dbo].[tb_UserRole]([UserRoleID] ASC, [UserRoleCD] ASC);


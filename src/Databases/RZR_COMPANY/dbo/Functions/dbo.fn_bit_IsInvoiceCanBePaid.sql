
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
create function [dbo].[fn_bit_IsInvoiceCanBePaid]
(
	@Id		BIGINT
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @result bit = 0

	SELECT @result = cast(1 as bit) 
	FROM [dbo].[F_INVOICE] i with (nolock)	
	WHERE i.[INVOICE_ID] = @Id and 
		(i.[IS_VOIDED] = 0 and 
			(i.[INVOICE_CONSOLIDATED_ID] is not null or i.[STATUS_ID] = 3 or i.[STATUS_ID] = 4) and I.STATUS_ID != 5 and i.[AMOUNT_DUE] > 0)
	
	RETURN 
		ISNULL(@result, 0)
END
-- =============================================
-- Author:		Oleg K. Evseev
-- Create date: 08/09/2013
-- Description:	Scans all locations to find items with Serial or UniqueId = @TERM
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_SEARCH]
	@TERM				NVARCHAR(max)	= N'',
	@ORDER_COLUMN_NAME	NVARCHAR(150)	= N'ITEM_INVENTORY_ID',
	@ORDER_DIRECTION	NVARCHAR(20)	= N'ASC',
	@ITEMS_PER_PAGE		int				= 20,
	@PAGE_INDEX			int				= 0,
	@FILTER_WHERE		NVARCHAR(2000)	= N'',
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE

	DECLARE @filterCondition varchar(2006) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END

	SET @TERM = RTRIM(LTRIM(@TERM));

	DECLARE @query NVARCHAR (MAX) = N'
		WITH m_data AS	(
			SELECT
				fii.ITEM_INVENTORY_ID,
				fim.ITEM_NUMBER												AS MODEL,
				fii.ITEM_INVENTORY_SERIAL									AS SERIAL,
				fii.ITEM_INVENTORY_UNIQUE_ID								AS UNIQUE_IDENTIFIER,
				dm.MANUFACTURER_CD											AS MANUFACTURER,
				dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(fii.LOCATION_ID) AS LOCATION,
				cond.ITEM_CONDITION_CD										AS CONDITION
			FROM F_ITEM_INVENTORY				fii		WITH (NOLOCK)
			INNER JOIN D_ITEM_CONDITION			cond	WITH(NOLOCK)
				ON cond.ITEM_CONDITION_ID = fii.CONDITION_ID
			LEFT JOIN F_ITEM_MASTER				fim		WITH(NOLOCK)
				ON fim.ITEM_MASTER_ID = fii.ITEM_MASTER_ID
			LEFT JOIN D_MANUFACTURER			dm		WITH(NOLOCK)
				ON dm.MANUFACTURER_ID = fim.MANUFACTURER_ID
			WHERE fii.ITEM_STATUS_ID IN (1, 2, 8, 14) 
				AND fii.IS_DELETED = 0
				AND FII.IS_VIRTUAL = 0
				AND FII.IS_DROP_SHIP_ITEM = 0
				AND (RTRIM(LTRIM(fii.ITEM_INVENTORY_SERIAL)) = ''' + @TERM + ''' 
				OR RTRIM(LTRIM(fii.ITEM_INVENTORY_UNIQUE_ID)) = '''+ @TERM + ''')
		)SELECT TOP(1)
			-1		AS RowID,	
			COUNT(ITEM_INVENTORY_ID) AS ITEM_INVENTORY_ID,
			NULL	AS MODEL,
			NULL	AS SERIAL,
			NULL	AS UNIQUE_IDENTIFIER,
			NULL	AS MANUFACTURER,
			NULL	AS LOCATION,
			NULL	AS CONDITION
		FROM m_data ' + @filterCondition + N'
		UNION
		SELECT
			t.*
		FROM
			(SELECT	ROW_NUMBER() OVER (ORDER BY M.'+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N', M.ITEM_INVENTORY_ID desc) AS RowID,
					M.ITEM_INVENTORY_ID,
					M.MODEL,
					M.SERIAL,
					M.UNIQUE_IDENTIFIER,
					M.MANUFACTURER,
					M.LOCATION,
					M.CONDITION
				FROM m_data M ' + @filterCondition + N'
			) t	WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))
	
	IF (@C_IS_DEBUG = 1)
		SELECT @query AS MODEL
	ELSE
		exec sp_executeSQL @query
END
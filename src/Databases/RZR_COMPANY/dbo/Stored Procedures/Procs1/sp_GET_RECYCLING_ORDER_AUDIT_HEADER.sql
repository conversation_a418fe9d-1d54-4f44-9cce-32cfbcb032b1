-- =============================================
-- Author:		<PERSON>
-- Create date: 05/12/2014
-- Description:	Gets recycling order audit header data
-- =============================================
-- EXEC [sp_GET_RECYCLING_ORDER_AUDIT_HEADER] 1
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ORDER_AUDIT_HEADER]
	@RECYCLING_ORDER_AUDIT_HEADER_ID BIGINT = NULL, -- Get directly by id
	@RECYCLING_ORDER_ID				 BIGINT = NULL  -- Get the firt one by InboundOrderId if @RECYCLING_ORDER_AUDIT_HEADER_ID is not specified
AS
BEGIN
	
	IF (@RECYCLING_ORDER_AUDIT_HEADER_ID IS NULL)
	BEGIN
		SELECT TOP(1)
			@RECYCLING_ORDER_AUDIT_HEADER_ID = [Id]
		FROM [recycling].[F_AuditSession] FROAH WITH(NOLOCK)
		WHERE FROAH.[RecyclingOrderId] = @RECYCLING_ORDER_ID
	END

	SELECT TOP(1)
	   frao.[Id] as RecyclingAuditOrderId
      ,frao.[RecyclingOrderId] as RecyclingOrderId
      ,frao.[AutoName] as AutoName
      ,frao.StatusId as StatusId
	  ,ds.[RECYCLING_ORDER_AUDIT_STATUS_CD] as StatusCode
	  ,frao.[StartDate] as IssueDate
	  ,fro.CUSTOMER_ID as CustomerId
	  ,fc.CUSTOMER_NAME as CustomerName
	  ,frao.[IsAskForPrinting] as IsAskForPrinting
      ,frao.[IsAddNewItemsMasterDatabase] as IsAddNewItemsMasterDatabase
      ,frao.[IsInactive]
      ,frao.[IsDeleted]
      ,frao.[InsertedBy]
      ,frao.[InsertedDate]
      ,frao.[UpdatedBy]
      ,frao.[UpdatedDate]
      ,frao.[DeletedBy]
      ,frao.[DeletedDate]
	FROM [recycling].[F_AuditSession]			frao WITH(NOLOCK)
	INNER JOIN D_RECYCLING_ORDER_AUDIT_STATUS		ds	 WITH(NOLOCK)
		ON frao.StatusId = ds.RECYCLING_ORDER_AUDIT_STATUS_ID
	INNER JOIN [dbo].[F_RECYCLING_ORDER]			fro
		on frao.RecyclingOrderId = fro.RECYCLING_ORDER_ID
	INNER JOIN F_CUSTOMER							fc   WITH(NOLOCK)
		ON fro.CUSTOMER_ID = fc.CUSTOMER_ID
	WHERE [Id] = @RECYCLING_ORDER_AUDIT_HEADER_ID

END
CREATE TABLE [dbo].[F_TAG_GROUP_TYPE] (
    [TAG_GROUP_TYPE_ID] BIGINT IDENTITY (1, 1) NOT NULL,
    [TAG_TYPE_ID]       INT    NOT NULL,
    [TAG_GROUP_ID]      BIGINT NOT NULL,
    CONSTRAINT [PK_F_TAG_GROUP_TYPE] PRIMARY KEY CLUSTERED ([TAG_GROUP_TYPE_ID] ASC),
    CONSTRAINT [FK_F_TAG_GROUP_TYPE_C_TAG_TYPE] FOREIGN KEY ([TAG_TYPE_ID]) REFERENCES [dbo].[C_TAG_TYPE] ([TAG_TYPE_ID]),
    CONSTRAINT [FK_F_TAG_GROUP_TYPE_F_TAG_GROUP] FOREIGN KEY ([TAG_GROUP_ID]) REFERENCES [dbo].[F_TAG_GROUP] ([TAG_GROUP_ID]) ON DELETE CASCADE
);


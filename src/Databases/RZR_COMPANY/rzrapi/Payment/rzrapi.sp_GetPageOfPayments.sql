create procedure [rzrapi].[sp_GetPageOfPayments] @Ids [dbo].[bigint_ID_ARRAY] readonly,
                                                 @InvoiceIds [dbo].[bigint_ID_ARRAY] readonly,
                                                 @ConsolidatedInvoiceIds [dbo].[bigint_ID_ARRAY] readonly,
                                                 @CustomerIds [dbo].[bigint_ID_ARRAY] readonly,
                                                 @FromUtcDateTime datetime = null,
                                                 @ToUtcDateTime datetime = null,
                                                 @IsInactive bit = 0,
                                                 @AccountType tinyint = null,
                                                 @Limit int = 20,
                                                 @Offset int = 0
as
begin

    declare @anyIds bit = isnull((select top (1) 1 from @Ids), 0);
    declare @anyInvoices bit = isnull((select top (1) 1 from @InvoiceIds), 0);
    declare @anyConsolidatedInvoices bit = isnull((select top (1) 1 from @ConsolidatedInvoiceIds), 0);
    declare @anyCustomers bit = isnull((select top (1) 1 from @CustomerIds), 0);

    declare @defaultCurrencyExchangeId int = [dbo].[fn_bigint_CompanyHomeCurrencyExchangeId]();

    with MAIN_QUERY as (select FCP.[CUSTOMER_PAYMENT_ID]                         as Id
                             , FCP.[CUSTOMER_ID]                                 as CustomerId
                             , FCP.[REFERENCE_NUMBER]                            as ReferenceId
                             , FCP.[CHECK_NUMBER]                                as CheckNumber
                             , FCP.[TYPE_BY_ACCOUNT_TYPE_ID]                     as AccountType
                             , FCP.[PAYMENT_TYPE_ID]                             as PaymentMethod
                             , FIP.[AMOUNT]                                      as Amount
                             , FCP.[AMOUNT]                                      as TotalPaymentAmount
                             , FCP.[DISCOUNT]                                    as Discount
                             , FCP.[PAYMENT_DATE]                                as PaymentDate
                             , FCP.[ACCOUNT_ID]                                  as AccountId
                             , FIP.[INVOICE_ID]                                  as InvoiceId
                             , FCP.[IS_DELETED]                                  as IsVoided
                             , FCP.[DELETING_REFERENCE_NUMBER]                   as VoidingReferenceId
                             , iif(FCP.[IS_DELETED] = 1, FCP.[DELETED_DT], null) as VoidedDate
                             , FCP.[CurrencyExchangeId]                          as CurrencyExchangeId
                             , CC.[Abbreviation]                                 as CurrencyCode

-- used credit
                             , FCRP.[CREDIT_PAYMENT_ID]                          as CreditPaymentId
                             , FCM.[Id]                                          as CreditMemoId
                             , FCM.[AutoName]                                    as CreditMemoAutoName
                             , DCE_FSO.[Id]                                      as CreditCurrencyExchangeId
                             , CC_FSO.[Abbreviation]                             as CreditCurrencyCode
                             , FCRP.[CREDIT_USED_FOR_PAYMENT]                    as CreditAppliedAmount

-- paid invoices
                             , FI.[INVOICE_ID]                                   as PaidInvoiceId
                             , FI.[AUTO_NAME]                                    as PaidInvoiceAutoName
                             , FI.[IS_VOIDED]                                    as PaidInvoiceIsVoided
                             , FCP.[CurrencyExchangeId]                          as PaidInvoiceCurrencyExchangeId
                             , CC.[Abbreviation]                                 as PaidInvoiceCurrencyCode
                             , FIP.[AMOUNT]                                      as PaidInvoicePaidAmount
                             , FIC.[INVOICE_CONSOLIDATED_ID]                     as PaidInvoiceConsolidatedId
                             , FIC.[SELF_NAME]                                   as PaidInvoiceConsolidatedName

                        from [dbo].[F_CUSTOMER_PAYMENT] FCP with (nolock)
                                 inner join [dbo].[F_INVOICE_PAYMENT] FIP with (nolock)
                                            on FCP.[CUSTOMER_PAYMENT_ID] = FIP.[PAYMENT_ID]
                                 left join [dbo].[F_INVOICE] FI with (nolock)
                                           on FIP.[INVOICE_ID] = FI.[INVOICE_ID]
                                 left join [dbo].[F_INVOICE_CONSOLIDATED] FIC with (nolock)
                                           on FI.[INVOICE_CONSOLIDATED_ID] = FIC.[INVOICE_CONSOLIDATED_ID]
                                 left join [dbo].[D_CurrencyExchange] DCE with (nolock)
                                           on DCE.[Id] = FCP.[CurrencyExchangeId]
                                 left join [dbo].[C_Currency] CC with (nolock)
                                           on DCE.[ForeignCurrencyId] = CC.[Id]

                                 left join [dbo].[F_CREDIT_PAYMENT] FCRP with (nolock)
                                           on FCP.[CUSTOMER_PAYMENT_ID] = FCRP.[PAYMENT_ID]
                                 left join [dbo].[F_CUSTOMER_CREDIT] FCC
                                           on FCRP.[CREDIT_ID] = FCC.[CUSTOMER_CREDIT_ID]
                                 left join [dbo].[F_CreditMemo] FCM with (nolock)
                                           on FCC.[CUSTOMER_CREDIT_ID] = FCM.[CustomerCreditId]
                                 left join [dbo].[F_SALES_ORDER_RMA] FSOR with (nolock)
                                           on FCM.[RmaId] = FSOR.[RMA_ID]
                                 left join [dbo].[F_INVOICE] FI_FCM with (nolock)
                                           on FCM.[InvoiceId] = FI_FCM.[INVOICE_ID]
                                 left join [dbo].[F_SALES_ORDER] FSO with (nolock)
                                           on FSO.[SALES_ORDER_ID] = isnull(FI_FCM.[ORDER_ID], FSOR.[SALES_ORDER_ID])
                                 left join [dbo].[D_CurrencyExchange] DCE_FSO with (nolock)
                                           on DCE_FSO.[Id] =
                                              isnull(FSO.[CurrencyExchangeId], @defaultCurrencyExchangeId)
                                 left join [dbo].[C_Currency] CC_FSO with (nolock)
                                           on DCE_FSO.[ForeignCurrencyId] = CC_FSO.[Id]

                                 left join @Ids PIDS
                                           on FCP.[CUSTOMER_PAYMENT_ID] = PIDS.ID
                                 left join @InvoiceIds IIDS
                                           on FIP.[INVOICE_ID] = IIDS.ID
                                 left join @ConsolidatedInvoiceIds CIIDS
                                           on FIC.[INVOICE_CONSOLIDATED_ID] = CIIDS.ID
                                 left join @CustomerIds CIDS
                                           on FCP.[CUSTOMER_ID] = CIDS.ID

                        where (@anyIds = 0 or PIDS.ID is not null)
                          and (@anyInvoices = 0 or IIDS.ID is not null)
                          and (@anyConsolidatedInvoices = 0 or CIIDS.ID is not null)
                          and (@anyCustomers = 0 or CIDS.ID is not null)
                          and (@IsInactive is null or FCP.[IS_DELETED] = @IsInactive)
                          and (@FromUtcDateTime is null or FCP.[PAYMENT_DATE] >= @FromUtcDateTime)
                          and (@ToUtcDateTime is null or FCP.[PAYMENT_DATE] <= @ToUtcDateTime)
                          and (@AccountType is null or FCP.[TYPE_BY_ACCOUNT_TYPE_ID] = @AccountType))
    select count(distinct [Id]) as [Id]
         , null                 as [CustomerId]
         , null                 as [ReferenceId]
         , null                 as [CheckNumber]
         , null                 as [AccountType]
         , null                 as [PaymentMethod]
         , null                 as [Amount]
         , null                 as [TotalPaymentAmount]
         , null                 as [Discount]
         , null                 as [PaymentDate]
         , null                 as [AccountId]
         , null                 as [InvoiceId]
         , null                 as [IsVoided]
         , null                 as [VoidingReferenceId]
         , null                 as [VoidedDate]
         , null                 as [CurrencyExchangeId]
         , null                 as [CurrencyCode]
         , null                 as [CreditPaymentId]
         , null                 as [CreditMemoId]
         , null                 as [CreditMemoAutoName]
         , null                 as [CreditCurrencyExchangeId]
         , null                 as [CreditCurrencyCode]
         , null                 as [CreditAppliedAmount]
         , null                 as [PaidInvoiceId]
         , null                 as [PaidInvoiceAutoName]
         , null                 as [PaidInvoiceIsVoided]
         , null                 as [PaidInvoiceCurrencyExchangeId]
         , null                 as [PaidInvoiceCurrencyCode]
         , null                 as [PaidInvoicePaidAmount]
         , null                 as [PaidInvoiceConsolidatedId]
         , null                 as [PaidInvoiceConsolidatedName]
    from MAIN_QUERY C
    union all
    select T.[Id]
         , T.[CustomerId]
         , T.[ReferenceId]
         , T.[CheckNumber]
         , T.[AccountType]
         , T.[PaymentMethod]
         , T.[Amount]
         , T.[TotalPaymentAmount]
         , T.[Discount]
         , T.[PaymentDate]
         , T.[AccountId]
         , T.[InvoiceId]
         , T.[IsVoided]
         , T.[VoidingReferenceId]
         , T.[VoidedDate]
         , T.[CurrencyExchangeId]
         , T.[CurrencyCode]
         , T.[CreditPaymentId]
         , T.[CreditMemoId]
         , T.[CreditMemoAutoName]
         , T.[CreditCurrencyExchangeId]
         , T.[CreditCurrencyCode]
         , T.[CreditAppliedAmount]
         , T.[PaidInvoiceId]
         , T.[PaidInvoiceAutoName]
         , T.[PaidInvoiceIsVoided]
         , T.[PaidInvoiceCurrencyExchangeId]
         , T.[PaidInvoiceCurrencyCode]
         , T.[PaidInvoicePaidAmount]
         , T.[PaidInvoiceConsolidatedId]
         , T.[PaidInvoiceConsolidatedName]
    from (select *
          from MAIN_QUERY MD
          order by MD.[Id] desc
          offset (@Offset) rows fetch next (@Limit) rows only) as T

end
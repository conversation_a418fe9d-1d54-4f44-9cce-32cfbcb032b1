 
-- =============================================
-- Author:		V<PERSON><PERSON><PERSON><PERSON><PERSON>
-- Create date: 01/23/2014
-- Description:	returns workflow types with count
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INVENTORY_WORKFLOW_TYPE_COUNTS]	
	 @RECYCLING_ORDER_IDS	bigint_ID_ARRAY		READONLY
    ,@CUSTOMER_IDS			bigint_ID_ARRAY		READONLY
    ,@LOT_IDS				bigint_ID_ARRAY		READONLY--'<Root><Items ID="363" /><Items ID="362" /></Root>'
    ,@COMMODITY_IDS			bigint_ID_ARRAY		READONLY
	,@WAREHOUSE_ID			BIGINT = 0
	,@USER_ID				BIGINT = 1
	,@REP_IDS				bigint_ID_ARRAY		READONLY
	,@START_DATE			DATETIME			= NULL
    ,@END_DATE				DATETIME			= NULL

AS
BEGIN
	DECLARE @IS_REP_RESTRICTED BIT = CASE WHEN EXISTS(SELECT ID FROM @REP_IDS) THEN 1 ELSE 0 END
	DECLARE @t_user_warehouses_ids [dbo].[bigint_ID_ARRAY]

	IF (@WAREHOUSE_ID = 0) BEGIN
		DECLARE
			@t_user_warehouses table (
				VALUE		bigint,
				LABEL		NVARCHAR(50),
				IS_DEFAULT	BIT
			)

		INSERT INTO  @t_user_warehouses
			EXEC [dbo].[sp_LIST_USER_WAREHOUSES] @USER_ID = @USER_ID
		INSERT INTO @t_user_warehouses_ids(ID)
			SELECT [value] FROM @t_user_warehouses
	END ELSE BEGIN
		INSERT INTO @t_user_warehouses_ids(ID)
			SELECT @WAREHOUSE_ID
	END

	declare 
		@IsRecyclingExists	bit =  CASE WHEN EXISTS(SELECT ID FROM @RECYCLING_ORDER_IDS) THEN 1 ELSE 0 END,
		@IsCustomerExists	bit =  CASE WHEN EXISTS(SELECT ID FROM @CUSTOMER_IDS) THEN 1 ELSE 0 END,
		@IsLotExists		bit =  CASE WHEN EXISTS(SELECT ID FROM @LOT_IDS) THEN 1 ELSE 0 END,
		@IsCommodityExists  bit =  CASE WHEN EXISTS(SELECT ID FROM @COMMODITY_IDS) THEN 1 ELSE 0 END,
		@IsStartDate        bit =  CASE WHEN @START_DATE IS NULL THEN 0 ELSE 1 END,
		@IsEndDate          bit =  CASE WHEN @END_DATE IS NULL THEN 0 ELSE 1 END

	create TABLE #t(WORKFLOW_STEP_ID BIGINT, [COUNT] INT)
	INSERT INTO #t(WORKFLOW_STEP_ID, [COUNT])	
		SELECT
			OI.WORKFLOW_STEP_ID			
			,COUNT(distinct OI.RECYCLING_ORDER_ITEM_ID) as CNT
		FROM F_RECYCLING_ORDER_ITEM							OI WITH (NOLOCK)
		INNER JOIN F_RECYCLING_ORDER						RO WITH (NOLOCK)
			ON OI.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
		INNER JOIN  dbo.F_RECYCLING_ORDER_INBOUND			FROIN WITH (NOLOCK)
			ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID					
			--AND RO.RECYCLING_ORDER_STATUS_ID != 4/*Canceled*/		
		INNER JOIN  [recycling].[C_InboundOrderStatus]		OS   WITH(NOLOCK)
			ON FROIN.StatusId = OS.Id			 
		INNER JOIN 	dbo.F_CUSTOMER							C WITH(NOLOCK)
			ON RO.CUSTOMER_ID = C.CUSTOMER_ID
		INNER JOIN 	F_RECYCLING_ITEM_MASTER		frim	WITH (NOLOCK)	
			ON oi.RECYCLING_ITEM_MASTER_ID = frim.RECYCLING_ITEM_MASTER_ID
		LEFT JOIN F_LOCATION								L WITH (NOLOCK)
			ON L.LOCATION_ID = OI.LOCATION_ID				
		LEFT JOIN @REP_IDS									rep_restr
			ON C.REP_ID = rep_restr.ID
		left join @RECYCLING_ORDER_IDS oi_
			on oi.RECYCLING_ORDER_ID = oi_.ID
		left join  @CUSTOMER_IDS cc
            on c.CUSTOMER_ID = cc.ID    
		left join  @LOT_IDS lot
            on OI.RECYCLING_ORDER_ITEM_ID = lot.ID
		left join  @COMMODITY_IDS cm
            on OI.RECYCLING_ITEM_MASTER_ID = cm.ID
		LEFT JOIN  F_RECYCLING_ORDER						OO WITH(NOLOCK)
			ON OI.OUTBOUND_ORDER_ID = OO.RECYCLING_ORDER_ID
			AND OO.RECYCLING_ORDER_STATUS_ID != 4
		LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER				ROIT WITH(NOLOCK)
			ON  ROIT.RECYCLING_ORDER_ITEM_ID = OI.RECYCLING_ORDER_ITEM_ID
			AND ROIT.OUTBOUND_ORDER_ID		 = OI.OUTBOUND_ORDER_ID -- only the actual one
		LEFT JOIN dbo.F_RECYCLING_ORDER						ROT   WITH(NOLOCK)
			ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
		inner join @t_user_warehouses_ids W
			on COALESCE(ROT.WAREHOUSE_ID, l.WAREHOUSE_ID, RO.WAREHOUSE_ID) = W.ID
				or RO.WAREHOUSE_ID IS NULL AND L.WAREHOUSE_ID IS NULL 				
		WHERE 
			OI.IS_INACTIVE = 0			
			and (@IS_REP_RESTRICTED = 0 or rep_restr.ID is not null)
			and (@IsCustomerExists = 0 or cc.ID is not null)
			and (@IsRecyclingExists = 0 or oi_.ID is not null)
			and (@IsLotExists = 0 or lot.ID is not null)
			and (@IsCommodityExists = 0 or cm.ID is not null)
			and (OO.RECYCLING_ORDER_ID IS NULL OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL)
			and (@IsStartDate = 0 OR DATEDIFF(dd, oi.INSERTED_DT, DATEADD(dd, DATEDIFF(dd, 0, @START_DATE), 0)) <= 0) 
			and (@IsEndDate = 0 OR DATEDIFF(dd, oi.INSERTED_DT, DATEADD(dd, DATEDIFF(dd, 0, @END_DATE), 0)) >= 0)			
		GROUP BY
			OI.WORKFLOW_STEP_ID			
	
	SELECT 
		WT.WORKFLOW_TYPE_ID AS [value],
		ISNULL(SUM(OI.COUNT),0) as label
	FROM dbo.C_RECYCLING_WORKFLOW_TYPE	 WT WITH (NOLOCK)
		LEFT JOIN #t OI  ON (OI.WORKFLOW_STEP_ID = WT.WORKFLOW_TYPE_ID OR WT.WORKFLOW_TYPE_ID = 8 AND OI.WORKFLOW_STEP_ID IN (8, 9)) 
	GROUP BY WT.WORKFLOW_TYPE_ID
	UNION
	SELECT 
		 0		AS [value] 
		,ISNULL(SUM(OI.COUNT),0)	AS [label]	
	FROM #t OI 
	WHERE (OI.WORKFLOW_STEP_ID NOT IN(8,0, 9))

END
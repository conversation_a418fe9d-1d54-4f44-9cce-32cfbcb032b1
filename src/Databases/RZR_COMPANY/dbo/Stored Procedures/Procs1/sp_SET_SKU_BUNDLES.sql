-- Create Procedure sp_SET_SKU_BUNDLES
-- =============================================
-- Author:	 <Author,,Name>
-- Create date: <Create Date,,>
-- Description: <Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_SKU_BUNDLES]
    @SKU_ID			BIGINT
    ,@T_INSERT		SKU_BUNDLE	 READONLY
    ,@T_UPDATE		SKU_BUNDLE	 READONLY
    ,@T_DELETE_IDS	bigint_ID_ARRAY READONLY
	,@C_IS_DEBUG	bit = 0
AS
BEGIN
    DECLARE
		@NOW			DATETIME     = GETUTCDATE(),
		@SP_NAME	    NVARCHAR(50) = N'sp_SET_SKU_BUNDLES',
		@QTY_ON_HAND	INT			= dbo.fn_int_GET_SKU_QTY_ON_HAND(@SKU_ID),
		@INSERTED		INT			= 0,
		@UPDATED	    INT			= 0,
		@DELETED	    INT			= 0;


    BEGIN TRANSACTION	   
    SET XACT_ABORT ON

		INSERT INTO dbo.F_ITEM_LOT (
			ITEM_ID_MASTER,
			ITEM_LOT_VALUE,
			ITEM_LOT_UNIT_PRICE,
			ITEM_LOT_TOTAL_PRICE,
			ITEM_LOT_QTY,
			ITEM_LOT_TITLE,
			ITEM_LOT_DESC,
			INSERTED_BY,
			INSERTED_DT
		) 
		SELECT
			@SKU_ID
			,[LOT_SIZE]
			,[UNIT_PRICE]
			,[LOT_SIZE] * [UNIT_PRICE]		-- ITEM_LOT_TOTAL_PRICE can change
			,FLOOR(@QTY_ON_HAND/[LOT_SIZE]) -- ITEM_LOT_QTY also can change
			,[SKU_TITLE]
			,[SKU_DESCR]
			,@SP_NAME
			,@NOW
		FROM @T_INSERT

		SET @INSERTED = @@ROWCOUNT

		UPDATE fil SET
			fil.ITEM_LOT_VALUE			= T.[LOT_SIZE]
			,fil.ITEM_LOT_UNIT_PRICE	= T.[UNIT_PRICE]
			,fil.ITEM_LOT_TOTAL_PRICE	= T.[LOT_SIZE] * T.[UNIT_PRICE]		
			,fil.ITEM_LOT_QTY			= FLOOR(@QTY_ON_HAND/T.[LOT_SIZE])
			,fil.ITEM_LOT_TITLE			= T.[SKU_TITLE]
			,fil.ITEM_LOT_DESC			= T.[SKU_DESCR]
			,fil.UPDATED_BY				= @SP_NAME
			,fil.UPDATED_DT				= @NOW
		FROM dbo.F_ITEM_LOT		fil
		INNER JOIN @T_UPDATE	T
			ON T.BUNDLE_ID = fil.ITEM_LOT_ID
	   
		SET @UPDATED = @@ROWCOUNT

		UPDATE fil SET
			fil.IS_DELETED		= 1
			,fil.DELETED_BY		= @SP_NAME
			,fil.DELETED_DT		= @NOW
		FROM dbo.F_ITEM_LOT		fil
		INNER JOIN @T_DELETE_IDS T
			ON T.ID = fil.ITEM_LOT_ID
	   
		SET @DELETED = @@ROWCOUNT

		if (@C_IS_DEBUG = 1)
			select
				'To update' as what
				,fi.ITEM_ID
				,fim.ITEM_MASTER_ID
				,fim.ITEM_NUMBER
				,fimsa.ITEM_MASTER_SKU_ATTRB_ID
				,dic.ITEM_CONDITION_ID
				,fil.ITEM_LOT_TITLE
				,fil.ITEM_LOT_DESC
				,fil.ITEM_LOT_TOTAL_PRICE
				,fil.IS_INACTIVE
				,fil.IS_DELETED
				,@SP_NAME
				,@NOW
				,iif(fil.IS_DELETED = 1 and fi_child.IS_DELETED = 0, @SP_NAME, fi_child.DELETED_BY)
				,iif(fil.IS_DELETED = 1 and fi_child.IS_DELETED = 0, @NOW,     fi_child.DELETED_DT)
			FROM F_ITEM										fi		WITH(ROWLOCK)
			INNER JOIN F_ITEM_LOT							fil		WITH (NOLOCK)
				ON fil.ITEM_ID_MASTER = fi.ITEM_ID
			INNER JOIN F_ITEM_MASTER						fim		WITH (NOLOCK)
				ON fi.ITEM_MASTER_ID = fim.ITEM_MASTER_ID
			INNER JOIN D_ITEM_CONDITION						dic		WITH (NOLOCK)
				ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
			LEFT JOIN F_ITEM_MASTER_SKU_ATTRB				fimsa	WITH (NOLOCK)
				ON fi.ITEM_MASTER_ID = fimsa.ITEM_MASTER_ID
				AND fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
			inner join F_ITEM								fi_child with(rowlock)
				on  fi_child.ITEM_TYPE_CHILD_ID = fil.ITEM_LOT_ID 
				and fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
			where fil.ITEM_ID_MASTER = @SKU_ID

		update fi_child set
			 fi_child.ITEM_MASTER_ID			= fim.ITEM_MASTER_ID
			,fi_child.ITEM_NUMBER				= fim.ITEM_NUMBER
			,fi_child.ITEM_MASTER_SKU_ATTRB_ID  = fimsa.ITEM_MASTER_SKU_ATTRB_ID
			,fi_child.CONDITION_ID				= dic.ITEM_CONDITION_ID
			,fi_child.ITEM_DESC					= fil.ITEM_LOT_TITLE
			,fi_child.LONG_DESC					= fil.ITEM_LOT_DESC
			,fi_child.PRICE						= fil.ITEM_LOT_TOTAL_PRICE
			,fi_child.IS_INACTIVE				= fil.IS_INACTIVE
			,fi_child.IS_DELETED				= fil.IS_DELETED
			,fi_child.UPDATED_BY				= @SP_NAME
			,fi_child.UPDATED_DT				= @NOW
			,fi_child.DELETED_BY				= iif(fil.IS_DELETED = 1 and fi_child.IS_DELETED = 0, @SP_NAME, fi_child.DELETED_BY)
			,fi_child.DELETED_DT				= iif(fil.IS_DELETED = 1 and fi_child.IS_DELETED = 0, @NOW,     fi_child.DELETED_DT)
		FROM F_ITEM										fi		WITH(ROWLOCK)
		INNER JOIN F_ITEM_LOT							fil		WITH (NOLOCK)
			ON fil.ITEM_ID_MASTER = fi.ITEM_ID
		INNER JOIN F_ITEM_MASTER						fim		WITH (NOLOCK)
			ON fi.ITEM_MASTER_ID = fim.ITEM_MASTER_ID
		INNER JOIN D_ITEM_CONDITION						dic		WITH (NOLOCK)
			ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
		LEFT JOIN F_ITEM_MASTER_SKU_ATTRB				fimsa	WITH (NOLOCK)
			ON fi.ITEM_MASTER_ID = fimsa.ITEM_MASTER_ID
			AND fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
		inner join F_ITEM								fi_child with(rowlock)
			on  fi_child.ITEM_TYPE_CHILD_ID = fil.ITEM_LOT_ID 
			and fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
		where fil.ITEM_ID_MASTER = @SKU_ID

		if (@C_IS_DEBUG = 1)
			select DISTINCT
				'To insert' as what
				,fil.ITEM_LOT_ID				as ITEM_TYPE_CHILD_ID
				,4								as ITEM_TYPE_ID -- BUNDLE
				,fim.ITEM_MASTER_ID				as ITEM_MASTER_ID
				,fim.ITEM_NUMBER				as ITEM_NUMBER
				,fimsa.ITEM_MASTER_SKU_ATTRB_ID	as ITEM_MASTER_SKU_ATTRB_ID
				,dic.ITEM_CONDITION_ID			as CONDITION_ID
				,fil.ITEM_LOT_TITLE				as ITEM_DESC
				,fil.ITEM_LOT_DESC				as LONG_DESC
				,fil.ITEM_LOT_TOTAL_PRICE		as PRICE
				,fil.IS_INACTIVE				as IS_INACTIVE
				,fil.IS_DELETED					as IS_DELETED
				,@SP_NAME						as INSERTED_BY
				,@NOW							as INSERTED_DT
			FROM F_ITEM_LOT								fil WITH (NOLOCK)
			INNER JOIN F_ITEM							fi  WITH(ROWLOCK)
				ON fil.ITEM_ID_MASTER = fi.ITEM_ID
			INNER JOIN F_ITEM_MASTER					fim WITH (NOLOCK)
				ON fi.ITEM_MASTER_ID = fim.ITEM_MASTER_ID
			INNER JOIN D_ITEM_CONDITION					dic WITH (NOLOCK)
				ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
			LEFT JOIN F_ITEM_MASTER_SKU_ATTRB		    fimsa WITH (NOLOCK)
				ON fi.ITEM_MASTER_ID = fimsa.ITEM_MASTER_ID
				AND fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
			--OUTER APPLY (
			--	SELECT
			--		dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fil.ITEM_ID)		as LOCKED
			--		,dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fil.ITEM_ID)	as ALLOCATED
			--) FII
			left join f_item							fi_child with(nolock)
				on fi_child.ITEM_TYPE_CHILD_ID =  fil.ITEM_LOT_ID 
				AND fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
			where fil.ITEM_ID_MASTER = @SKU_ID
			  and fi_child.ITEM_ID is null

		INSERT INTO F_ITEM /* fi_child */ (
			ITEM_TYPE_CHILD_ID
			,ITEM_TYPE_ID
			,ITEM_MASTER_ID
			,ITEM_NUMBER
			,ITEM_MASTER_SKU_ATTRB_ID
			,CONDITION_ID
			,ITEM_DESC
			,LONG_DESC
			,PRICE
			,IS_INACTIVE
			,IS_DELETED
			,INSERTED_BY
			,INSERTED_DT
		)
		SELECT DISTINCT
			fil.ITEM_LOT_ID					as ITEM_TYPE_CHILD_ID
			,4								as ITEM_TYPE_ID -- BUNDLE
			,fim.ITEM_MASTER_ID				as ITEM_MASTER_ID
			,fim.ITEM_NUMBER				as ITEM_NUMBER
			,fimsa.ITEM_MASTER_SKU_ATTRB_ID	as ITEM_MASTER_SKU_ATTRB_ID
			,dic.ITEM_CONDITION_ID			as CONDITION_ID
			,fil.ITEM_LOT_TITLE				as ITEM_DESC
			,fil.ITEM_LOT_DESC				as LONG_DESC
			,fil.ITEM_LOT_TOTAL_PRICE		as PRICE
			,fil.IS_INACTIVE				as IS_INACTIVE
			,fil.IS_DELETED					as IS_DELETED
			,@SP_NAME						as INSERTED_BY
			,@NOW							as INSERTED_DT
		FROM F_ITEM_LOT								fil WITH (NOLOCK)
		INNER JOIN F_ITEM							fi  WITH(ROWLOCK)
			ON fil.ITEM_ID_MASTER = fi.ITEM_ID
		INNER JOIN F_ITEM_MASTER					fim WITH (NOLOCK)
			ON fi.ITEM_MASTER_ID = fim.ITEM_MASTER_ID
		INNER JOIN D_ITEM_CONDITION					dic WITH (NOLOCK)
			ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
		LEFT JOIN F_ITEM_MASTER_SKU_ATTRB		    fimsa WITH (NOLOCK)
			ON fi.ITEM_MASTER_ID = fimsa.ITEM_MASTER_ID
			AND fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
		--OUTER APPLY (
		--	SELECT
		--		dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fil.ITEM_ID)		as LOCKED
		--		,dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fil.ITEM_ID)	as ALLOCATED
		--) FII
		left join f_item							fi_child with(nolock)
			on fi_child.ITEM_TYPE_CHILD_ID =  fil.ITEM_LOT_ID 
			AND fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
		where fil.ITEM_ID_MASTER = @SKU_ID
		  and fi_child.ITEM_ID is null
		  

		UPDATE fil SET
			fil.ITEM_ID = fi.ITEM_ID
			,UPDATED_BY	= @SP_NAME
			,UPDATED_DT	= @NOW
		FROM  F_ITEM_LOT						fil WITH(ROWLOCK)
		INNER JOIN F_ITEM						fi  WITH(ROWLOCK)
			ON fi.ITEM_TYPE_CHILD_ID = fil.ITEM_LOT_ID
			AND fi.ITEM_TYPE_ID = 4 -- BUNDLE
		WHERE fil.ITEM_ID_MASTER = @SKU_ID
		  and (ISNULL(fi.ITEM_ID, 0) != ISNULL(fil.ITEM_ID, 0))

    COMMIT TRANSACTION

    SELECT 
	   @INSERTED	AS Inserted
	   ,@UPDATED	AS Updated
	   ,@DELETED	AS Deleted
END
CREATE TABLE [dbo].[D_ACCOUNT_TYPE] (
    [ACCOUNT_TYPE_ID]   INT            NOT NULL,
    [ACCOUNT_TYPE_CD]   NVARCHAR (256) NOT NULL,
    [ACCOUNT_TYPE_DESC] NVARCHAR (MAX) NULL,
    [IS_INACTIVE]       BIT            CONSTRAINT [DF_D_ACCOUNT_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]        BIT            CONSTRAINT [DF_D_ACCOUNT_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]       VARCHAR (150)  NOT NULL,
    [INSERTED_DT]       DATETIME       NOT NULL,
    [UPDATED_BY]        VARCHAR (250)  NULL,
    [UPDATED_DT]        DATETIME       NULL,
    [DELETED_BY]        VARCHAR (150)  NULL,
    [DELETED_DT]        DATETIME       NULL,
    [SELF_ID]           AS             (isnull(CONVERT([bigint],[ACCOUNT_TYPE_ID],0),CONVERT([bigint],(-1),0))),
    [SELF_NAME]         AS             ([ACCOUNT_TYPE_CD]),
    [SELF_CREATE_DATE]  AS             (isnull([INSERTED_DT],'2000-01-01')),
    [PARENT_ID]         AS             (CONVERT([bigint],isnull(NULL,(-1)),0)),
    CONSTRAINT [PK_D_ACCOUNT_TYPE] PRIMARY KEY CLUSTERED ([ACCOUNT_TYPE_ID] ASC)
);










GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'All types of  accounts', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ACCOUNT_TYPE';


GO
CREATE NONCLUSTERED INDEX [NonClusteredIndex-********-191635]
    ON [dbo].[D_ACCOUNT_TYPE]([ACCOUNT_TYPE_ID] ASC, [IS_INACTIVE] ASC, [IS_DELETED] ASC);



-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT dbo.fn_str_CONVERT_CAMEL_CASE_REMOVE_UNDERSCORE('testing')
-- SELECT dbo.fn_str_CONVERT_CAMEL_CASE_REMOVE_UNDERSCORE('testing aga')
CREATE FUNCTION [dbo].[fn_str_CONVERT_CAMEL_CASE_REMOVE_UNDERSCORE]
(
	@InputString NVARCHAR(4000) 
) 
RETURNS NVARCHAR(4000)
AS
BEGIN
	DECLARE @Index          INT
	DECLARE @Char           CHAR(1)
	DECLARE @PrevChar       CHAR(1)
	DECLARE @OutputString   NVARCHAR(255)

	SET @InputString = REPLACE(@InputString, '_', ' ')
	SET @OutputString = LOWER(@InputString)
	SET @Index = 1

	WHILE @Index <= LEN(@InputString)
	BEGIN
		SET @Char     = SUBSTRING(@InputString, @Index, 1)
		SET @PrevChar = CASE WHEN @Index = 1 THEN ' '
							 ELSE SUBSTRING(@InputString, @Index - 1, 1)
						END

		IF @PrevChar IN (' ', ';', ':', '!', '?', ',', '.', '_', '-', '/', '&', '''', '(')
		BEGIN
			IF @PrevChar != '''' OR UPPER(@Char) != 'S'
				SET @OutputString = STUFF(@OutputString, @Index, 1, UPPER(@Char))
		END

		SET @Index = @Index + 1
	END

	RETURN @OutputString

END
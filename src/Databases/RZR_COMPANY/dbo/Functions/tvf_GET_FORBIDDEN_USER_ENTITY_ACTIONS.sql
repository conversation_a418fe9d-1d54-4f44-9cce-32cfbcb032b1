-- =============================================
-- Author:		O.Evseev
-- Create date: 10/06/2016
-- Description: Retruns forbiden actions with an entity for the user
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_FORBIDDEN_USER_ENTITY_ACTIONS]
(
	 @C_USER_ID			BIGINT
    ,@C_ENTITY_TYPE_ID	BIGINT
)
RETURNS @T TABLE (
	 ENTITY_ACTION_ID	BIGINT
	,ENTITY_ACTION_CD	NVARCHAR(512)
)
AS
BEGIN
	
	-- MUST NOT CHECK THE PERMISSIONS IF:
	IF @C_USER_ID <= 0 -- Shadow admin
	OR EXISTS(
		-- System admin
		SELECT TOP(1)
			1
		FROM F_USER_ROLE		FUR WITH(NOLOCK)
		INNER JOIN tb_UserRole	TUR WITH(NOLOCK)
		  ON TUR.UserRoleID = FUR.ROLE_ID		  
		WHERE FUR.USER_ID = @C_USER_ID
		  AND TUR.IS_SYSTEM_ROLE = 1)
	RETURN;


	-- LISTING THE FORBIDDEN ACTIONS:
	INSERT INTO @T (
		ENTITY_ACTION_ID,
		ENTITY_ACTION_CD
	)-- The actions having no permission in all the user's roles
	SELECT
		CEA.ENTITY_ACTION_ID,
		'The user has no permission to '+ CEA.ENTITY_ACTION_CD		
	FROM C_ENTITY_ACTION		CEA	WITH(NOLOCK)
	WHERE CEA.ENTITY_TYPE_ID = @C_ENTITY_TYPE_ID
	  AND CEA.IS_MAIN_ENTITY_ACTION = 0
	  AND CEA.ENTITY_ACTION_ID NOT IN (
		SELECT DISTINCT
			CEA.ENTITY_ACTION_ID
		FROM F_USER_ROLE							FUR		WITH(NOLOCK)
		INNER JOIN F_PERMISSION_ROLE_ENTITY_ACTIONS	FPREA	WITH(NOLOCK)
			ON FPREA.ROLE_ID		= FUR.ROLE_ID
		INNER JOIN C_ENTITY_ACTION					CEA		WITH(NOLOCK)
			ON CEA.ENTITY_ACTION_ID = FPREA.ENTITY_ACTION_ID
		WHERE FUR.[USER_ID]		 = @C_USER_ID
		  AND CEA.ENTITY_TYPE_ID = @C_ENTITY_TYPE_ID
	)
	
	RETURN;  
END
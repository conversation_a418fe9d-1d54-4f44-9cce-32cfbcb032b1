

CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ACTIVE_INVOICE_STATUS]
	@SALES_ORDER_ID	BIGINT = NULL
AS
BEGIN
	DECLARE @INVOICE_ID BIGINT = (
		SELECT
			MAX(INVOICE_ID)
		FROM F_INVOICE	WITH (NOLOCK)
		WHERE INVOICE_TYPE_ID = 1 AND ORDER_ID = @SALES_ORDER_ID AND IS_VOIDED = 0
	)

	SELECT
		INVOICE_ID AS SALES_ORDER_INVOICE_ID,
		STATUS_ID
	FROM F_INVOICE	WITH(NOLOCK)
	WHERE INVOICE_ID = @INVOICE_ID
END
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_RECYCLING_INVOICE]
	@INVOICE_ID bigint = -1,
	@IS_SUMMARY bit = 0
AS
BEGIN
	IF (@IS_SUMMARY = 0)
	BEGIN

		SELECT
			RI.RECYCLING_ORDER_ITEM_ID							as OrderItemId,
			SI.SALES_ORDER_ITEM_ID								as SalesOrderItemId,
			iif(SI.RECYCLING_ORDER_ITEM_ID IS NOT NULL, 1, 0)	as ItemType,
			CASE
				WHEN SI.RECYCLING_ORDER_ITEM_ID IS NULL THEN
					CASE
						WHEN PM.SERVICE_MASTER_ID IS NULL THEN
							CASE
								WHEN ROIM.[CommodityId] IS NULL THEN IMR.ITEM_NUMBER
								ELSE [recycling].[fn_nvarchar_GetCommodityName](ROIM.[CommodityId], ROIM.[RecyclingOrderId])
							END
						ELSE
							(SELECT st.SERVICE_TYPE_CD FROM dbo.C_RECYCLING_ITEM_SERVICE_TYPE st WITH (NOLOCK) WHERE st.SERVICE_TYPE_ID = PM.SERVICE_MASTER_ID)
					END
				ELSE
					[recycling].[fn_nvarchar_GetCommodityName](RI.[RECYCLING_ITEM_MASTER_ID], RI.[RECYCLING_ORDER_ID])
			END													AS ItemNumber,
			dbo.fn_str_GET_SALES_ORDER_ITEM_TITLE(SI.SALES_ORDER_ITEM_ID) AS [Description],
			SI.ITEM_QUANTITY									AS QtyInUnit,
			SI.RECEIVED_PRICE									AS UnitPrice,
			SI.IS_FLAT_FEE										as IsFlatFee,
			pt.PRICE_TYPE_ID									as PriceTypeId,
			pt.PRICE_TYPE_DESC									as PriceTypeDesc,
			ISNULL(RI.[AlternativeName], [recycling].[fn_nvarchar_GetCommodityName](RI.[RECYCLING_ITEM_MASTER_ID], RI.RECYCLING_ORDER_ID)) AS AlternativeLotName,
			ISNULL(vw.CategoryName, ISNULL(NULLIF(DCH.[FRIENDLY_NAME], ''), DCH.[CATEGORYNAME])) AS Category
		FROM F_INVOICE									INV WITH(NOLOCK)
		INNER JOIN F_SALES_ORDER_ITEM					SI	WITH(NOLOCK)
			ON INV.ORDER_ID = SI.SALES_ORDER_ID
		LEFT JOIN F_RECYCLING_ORDER_ITEM				RI	WITH(NOLOCK)
			ON SI.RECYCLING_ORDER_ITEM_ID = RI.RECYCLING_ORDER_ITEM_ID
		LEFT JOIN F_RECYCLING_ITEM_MASTER				MI	WITH(NOLOCK)
			ON RI.RECYCLING_ITEM_MASTER_ID = MI.RECYCLING_ITEM_MASTER_ID
		LEFT JOIN [dbo].[F_PRODUCT_MASTER]				PM WITH (NOLOCK)
			ON SI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
		LEFT JOIN F_ITEM_MASTER							IMR WITH (NOLOCK)
			ON PM.[PRODUCT_MASTER_TYPE_ID] = 1 AND PM.ITEM_MASTER_ID = IMR.ITEM_MASTER_ID
		LEFT JOIN C_RECYCLING_ITEM_SERVICE_TYPE			ST WITH(NOLOCK)
			ON PM.SERVICE_MASTER_ID = ST.SERVICE_TYPE_ID
		LEFT JOIN [recycling].[F_CommodityRule]			ROIM WITH (NOLOCK)
			ON SI.[CommodityRuleId] = ROIM.[Id]
		left join [dbo].[C_RECYCLING_PRICE_TYPE]		pt with (nolock)
			on ISNULL(SI.PRICE_TYPE_ID, 2) = pt.PRICE_TYPE_ID
		LEFT JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY] vw WITH(NOLOCK)
			ON IMR.ITEM_MASTER_ID = vw.ItemMasterId
			AND vw.CategoryIsPrimary = 1
		LEFT JOIN [dbo].[D_CATEGORY_HIERARCHY]			DCH WITH(NOLOCK)
			ON MI.CATEGORY_ID = DCH.[CATEGORY_ID]
			AND DCH.[IS_INACTIVE] = 0
		WHERE INV.INVOICE_ID = @INVOICE_ID
		ORDER BY ItemType, ItemNumber;

	END
	ELSE
	BEGIN

		SELECT
			T_INVOICE_ITEMS.PRODUCT_NAME			AS ItemNumber,
			SUM(
				IIF(T_INVOICE_ITEMS.IS_FLAT_FEE = 1,
					1,				--RSW-12230: for summary we ignore qty for flat fee items...
					T_INVOICE_ITEMS.ITEM_QUANTITY)
			)										AS QtyInUnit,
			SUM(
				IIF(T_INVOICE_ITEMS.IS_FLAT_FEE = 1,
					T_INVOICE_ITEMS.RECEIVED_PRICE,
					T_INVOICE_ITEMS.RECEIVED_PRICE * T_INVOICE_ITEMS.ITEM_QUANTITY)
				)
			/SUM(
				IIF(T_INVOICE_ITEMS.IS_FLAT_FEE = 1,
					1,
					NULLIF(T_INVOICE_ITEMS.ITEM_QUANTITY, 0.0)
				)
			)										AS UnitPrice, -- NO CASTS AND ROUNDINGS HERE!
			T_INVOICE_ITEMS.IS_FLAT_FEE				AS IsFlatFee,
			PT.PRICE_TYPE_ID						AS PriceTypeId,
			PT.PRICE_TYPE_DESC						AS PriceTypeDesc,
			T_INVOICE_ITEMS.CategoryName			AS Category
		FROM (
			SELECT
				COALESCE(FIM.ITEM_NUMBER, ST.SERVICE_TYPE_CD, [recycling].[fn_nvarchar_GetCommodityName](RMI.[RECYCLING_ITEM_MASTER_ID], FROI.[RECYCLING_ORDER_ID])) AS PRODUCT_NAME,
				SI.ITEM_QUANTITY,
				SI.RECEIVED_PRICE,
				SI.IS_FLAT_FEE,
				SI.PRICE_TYPE_ID,
				SI.SALES_ORDER_ID,
				ST.SERVICE_TYPE_ID,
				ISNULL(vw.CategoryName, ISNULL(NULLIF(DCH.[FRIENDLY_NAME], ''), DCH.[CATEGORYNAME])) AS CategoryName
			FROM F_INVOICE								INV WITH(NOLOCK)
			INNER JOIN F_SALES_ORDER_ITEM				SI	WITH(NOLOCK)
				ON INV.ORDER_ID = SI.SALES_ORDER_ID
			INNER JOIN dbo.F_PRODUCT_MASTER				PM	WITH(NOLOCK)
				ON  SI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
			LEFT JOIN F_ITEM_MASTER						FIM	WITH(NOLOCK)
				ON  PM.ITEM_MASTER_ID = FIM.ITEM_MASTER_ID
				AND PM.PRODUCT_MASTER_TYPE_ID = 1
			LEFT JOIN C_RECYCLING_ITEM_SERVICE_TYPE		ST	WITH(NOLOCK)
				ON ST.SERVICE_TYPE_ID = PM.SERVICE_MASTER_ID
				AND PM.PRODUCT_MASTER_TYPE_ID = 2
			LEFT JOIN F_RECYCLING_ITEM_MASTER			RMI	WITH(NOLOCK)
				ON  PM.MODULE_MASTER_ID = RMI.RECYCLING_ITEM_MASTER_ID
				AND PM.PRODUCT_MASTER_TYPE_ID = 3
			LEFT JOIN [dbo].[F_RECYCLING_ORDER_ITEM]	FROI WITH(NOLOCK)
				ON FROI.[RECYCLING_ORDER_ITEM_ID] = SI.[RECYCLING_ORDER_ITEM_ID]
			LEFT JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY] vw WITH(NOLOCK)
				ON FIM.ITEM_MASTER_ID = vw.ItemMasterId
				AND vw.CategoryIsPrimary = 1
			LEFT JOIN [dbo].[D_CATEGORY_HIERARCHY]		DCH WITH(NOLOCK)
				ON RMI.CATEGORY_ID = DCH.[CATEGORY_ID]
				AND DCH.[IS_INACTIVE] = 0
			WHERE INV.INVOICE_ID = @INVOICE_ID
		) T_INVOICE_ITEMS
		left join [dbo].[C_RECYCLING_PRICE_TYPE]		PT with(nolock)
			on ISNULL(T_INVOICE_ITEMS.PRICE_TYPE_ID, 2) = PT.PRICE_TYPE_ID
		GROUP BY T_INVOICE_ITEMS.PRODUCT_NAME, T_INVOICE_ITEMS.IS_FLAT_FEE, PT.PRICE_TYPE_ID, PT.PRICE_TYPE_DESC, T_INVOICE_ITEMS.CategoryName
		ORDER BY T_INVOICE_ITEMS.PRODUCT_NAME;

	END
END
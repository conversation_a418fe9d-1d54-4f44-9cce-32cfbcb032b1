-- =============================================
-- Author:		<Oleg Evseev>
-- Create date: <2013.07.19>
-- Description:	<Lists customer transaction terms available for the selected customer>
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMER_TERMS]
(	@CUSTOMER_ID bigint = -1
	,@TERM nvarchar(max) = N''
	,@COUNT int = 20)
AS
BEGIN
	DECLARE @trimmed nvarchar(max) = LTRIM(RTRIM(@TERM))
		
	SELECT TOP(@COUNT)
		T.CUSTOMER_TRANSACTION_TERM_ID
		,T.TRANSACTION_TERM_VALUE
	FROM C_CUSTOMER_TRANSACTION_TERM T
	WHERE T.TRANSACTION_TERM_TYPE_ID = 1 AND 
		T.TRANSACTION_TERM_VALUE like @trimmed
END
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_MAGENTO_ORDER_PROCESSED] 
	@M_ORDER_ID BIGINT,
	@SALES_ORDER_ID BIGINT,
	@MAGENTO_SHOP_ID INT
AS
BEGIN
	UPDATE MAGENTO_ORDERS SET 
		SALES_ORDER_ID = @SALES_ORDER_ID
	WHERE 
		M_ORDER_ID = @M_ORDER_ID AND
		SHOP_ID = @MAGENTO_SHOP_ID	AND SALES_ORDER_ID IS NULL

	UPDATE SO SET
		SO.NOTES		= MO.BUYER_MESSAGE,
		SO.PO_NUMBER	= MO.INCREMENТ_ID
	FROM F_SALES_ORDER SO 
	INNER JOIN [MAGENTO_ORDERS]					 MO WITH (NOLOCK)
		ON SO.SALES_ORDER_ID = MO.SALES_ORDER_ID
	WHERE SO.SALES_ORDER_ID = @SALES_ORDER_ID	

END
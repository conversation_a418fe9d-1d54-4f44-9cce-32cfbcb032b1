-- SELECT * FROM F_ITEM_EXPORT
-- EXEC [sp_REBUILD_F_ITEM_EXPORT]
CREATE PROCEDURE [dbo].[sp_REBUILD_F_ITEM_EXPORT]
AS
BEGIN
	DECLARE 
		@alias_gallery			  NVARCHAR(50) = N'media_gallery'
		,@alias_position_gallerey NVARCHAR(50) = N'media_gallery_position'
		,@alias_image_base		  NVARCHAR(50) = N'base_image'
		,@alias_image_small		  NVARCHAR(50) = N'small_image'
		,@alias_image_thumbnail	  NVARCHAR(50) = N'thumbnail'
	
		,@alias_label_gallerey	  NVARCHAR(50) = N'media_gallery_label'
		,@alias_label_base		  NVARCHAR(50) = N'base_image_label'
		,@alias_label_small		  NVARCHAR(50) = N'small_image_label'
		,@alias_label_thumbnail   NVARCHAR(50) = N'thumbnail_image_label'
		
		,@alias_is_featured		  NVARCHAR(50) = N'item_is_featured'
		,@alias_is_deal_off       NVARCHAR(50) = N'item_is_deal_10_perc_off_ebay'
		
		,@alias_category_ids	  NVARCHAR(50) = N'category_ids'
		,@alias_eBay_category_ids NVARCHAR(50) = N'eBay_category_id'
		
	DECLARE @C_SQL_DECLARE_HEADER_00 NVARCHAR(max) = 
	N'DECLARE @ItemImage table(
		  ITEM_ID bigint NOT NULL
		, '+ @alias_gallery			  +N' NVARCHAR(MAX)
		, '+ @alias_position_gallerey +N' NVARCHAR(MAX)
		, '+ @alias_image_base  	  +N' NVARCHAR(MAX)
		, '+ @alias_image_small 	  +N' NVARCHAR(MAX)
		, '+ @alias_image_thumbnail   +N' NVARCHAR(MAX)
		, '+ @alias_label_gallerey	  +N' NVARCHAR(MAX)
		, '+ @alias_label_base		  +N' NVARCHAR(MAX)
		, '+ @alias_label_small		  +N' NVARCHAR(MAX)
		, '+ @alias_label_thumbnail   +N' NVARCHAR(MAX));
	INSERT INTO @ItemImage (
		  ITEM_ID 
		, '+ @alias_gallery	          +N'
		, '+ @alias_position_gallerey +N'
		, '+ @alias_image_base		  +N'
		, '+ @alias_image_small		  +N'
		, '+ @alias_image_thumbnail	  +N'
		, '+ @alias_label_gallerey	  +N'
		, '+ @alias_label_base		  +N'
		, '+ @alias_label_small		  +N'
		, '+ @alias_label_thumbnail   +N')
	SELECT 	
		fi.ITEM_ID
		, STUFF(
			(	SELECT
					'', '' + MEDIA_GALLERY AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.MEDIA_GALLERY,
						II.POSITION_INDEX
					FROM 
						F_ITEM_IMAGE II 	WITH(NOLOCK)						
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                         @alias_gallery +N'
		, STUFF(
			(	SELECT
					'', '' + CAST((POSITION_INDEX + 1) AS NVARCHAR(10)) AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.POSITION_INDEX
					FROM 
						F_ITEM_IMAGE II WITH(NOLOCK)						
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                          @alias_position_gallerey + N''

	DECLARE @C_SQL_DECLARE_HEADER_01 NVARCHAR(max) = '
		, (SELECT TOP(1)
					BASE_IMAGE AS "data()" 
				FROM F_ITEM_IMAGE WITH(NOLOCK)						
				WHERE ITEM_ID = fi.ITEM_ID 
					AND BASE_SELECTED = 1) AS '+  @alias_image_base  +N'
		, (SELECT TOP(1)
					SMALL_IMAGE AS "data()" 
			FROM F_ITEM_IMAGE WITH(NOLOCK)						
			WHERE ITEM_ID = fi.ITEM_ID 
				AND SMALL_SELECTED = 1) AS '+     @alias_image_small +N'
		, (SELECT TOP(1)
				THUMBNAIL_IMAGE AS "data()" 
			FROM F_ITEM_IMAGE WITH(NOLOCK)						
			WHERE ITEM_ID = fi.ITEM_ID 
				AND THUMBNAIL_SELECTED = 1) AS '+ @alias_image_thumbnail   +N'
		, STUFF(
			(	SELECT
					'', '' + MEDIA_GALLERY_LABEL AS "data()" 
				FROM 
					(SELECT DISTINCT TOP (10000) -- TO ALLOW "ORDER BY"
						II.IMAGE_ID, 
						II.POSITION_INDEX,
						II.MEDIA_GALLERY_LABEL
					FROM 
						F_ITEM_IMAGE II WITH(NOLOCK)					
					WHERE II.ITEM_ID = fi.ITEM_ID
					ORDER BY II.POSITION_INDEX ASC) AS INTERNAL
					FOR XML PATH(''''), root(''MyString''), type 
				).value(''/MyString[1]'',''NVARCHAR(max)'') 
			,1
			,2
			,'''') AS '+                        @alias_label_gallerey	+N'
		, (SELECT TOP(1)
				BASE_IMAGE_LABEL AS "data()"
			FROM F_ITEM_IMAGE WITH(NOLOCK)						
			WHERE ITEM_ID = fi.ITEM_ID 
				AND BASE_SELECTED = 1) AS '+	@alias_label_base		+N'
		, (SELECT TOP(1)
				SMALL_IMAGE_LABEL AS "data()"
			FROM F_ITEM_IMAGE WITH(NOLOCK)						
			WHERE ITEM_ID = fi.ITEM_ID 
				AND SMALL_SELECTED = 1) AS '+   @alias_label_small		+N'
		, (SELECT TOP(1)
				THUMBNAIL_IMAGE_LABEL AS "data()" 
			FROM F_ITEM_IMAGE WITH(NOLOCK)						
			WHERE ITEM_ID = fi.ITEM_ID 
				AND THUMBNAIL_SELECTED = 1) AS '+@alias_label_thumbnail +N'
	FROM 
	(
		SELECT DISTINCT ITEM_ID, ITEM_NUMBER
		FROM F_ITEM WITH (NOLOCK)
	)	fi'

	DECLARE @C_SQL_HEADER_SELECT NVARCHAR(max) = 
	N' SELECT ' +
	N'  CONVERT(NVARCHAR(MAX), ITEM_EXPORT_SYS_CD)					ITEM_EXPORT_SYS_CD'			+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_category_ids +N')			'+ @alias_category_ids		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_eBay_category_ids +N')		'+ @alias_eBay_category_ids +
	N', CONVERT(NVARCHAR(MAX), item_model_b)						item_model_b'				+
	N', CONVERT(NVARCHAR(MAX), name)								name'						+
	N', CONVERT(NVARCHAR(MAX), meta_title)							meta_title'					+
	N', CONVERT(NVARCHAR(MAX), meta_description)					meta_description'			+
	N', CONVERT(NVARCHAR(MAX), condition)							condition'					+
	N', CONVERT(NVARCHAR(MAX), ebay_condition_id)                   ebay_condition_id'			+ 
	N', CONVERT(NVARCHAR(MAX), amazon_condition_id)                 amazon_condition_id'		+ 
	N', CONVERT(NVARCHAR(MAX), price)								price'						+
	N', CONVERT(NVARCHAR(MAX), item_dimension_length) 				item_dimension_length'		+
	N', CONVERT(NVARCHAR(MAX), item_dimension_width)				item_dimension_width'		+
	N', CONVERT(NVARCHAR(MAX), item_dimension_height) 				item_dimension_height'		+
	N', CONVERT(NVARCHAR(MAX), weight) 								weight '					+
	N', CONVERT(NVARCHAR(MAX), manufacturer)						manufacturer'				+
	N', CONVERT(NVARCHAR(MAX), description)							description'				+
	N', CONVERT(NVARCHAR(MAX), short_description) 					short_description'			+
	N', CONVERT(NVARCHAR(MAX), status)								status'						+
	N', CONVERT(NVARCHAR(MAX), tax_class_id)						tax_class_id'				+
	N', CONVERT(NVARCHAR(MAX), qty )								qty'						+
	N', CONVERT(NVARCHAR(MAX), is_in_stock)							is_in_stock'				+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_is_featured		+N')	'+ @alias_is_featured		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_is_deal_off		+N')	'+ @alias_is_deal_off		+
	N', CONVERT(NVARCHAR(MAX), item_is_deal_10_perc_off_ebay_price)	item_is_deal_10_perc_off_ebay_price ' +
	N', CONVERT(NVARCHAR(MAX), SKU)									SKU '						+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_gallery			+N')	'+ @alias_gallery           +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_position_gallerey	+N')	'+ @alias_position_gallerey +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_gallerey		+N')	'+ @alias_label_gallerey    +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_base			+N')	'+ @alias_image_base        +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_base			+N')	'+ @alias_label_base		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_small		+N')	'+ @alias_image_small		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_small		+N')	'+ @alias_label_small		+
	N', CONVERT(NVARCHAR(MAX), '+ @alias_image_thumbnail	+N')	'+ @alias_image_thumbnail   +
	N', CONVERT(NVARCHAR(MAX), '+ @alias_label_thumbnail	+N')	'+ @alias_label_thumbnail   +
	N', CONVERT(NVARCHAR(MAX),  visibility)							 visibility'			+N'
	'

	DECLARE @C_SQL_HEADER NVARCHAR(max) = '' +
	' SELECT ' +
	'	0										as ORDER_BY_KEY'						+
	', ''ALL'' as ITEM_EXPORT_SYS_CD ' + -- *** DO NOT CHANGE THIS ROW it is used by REPLACE() later ***
	', '''+ @alias_category_ids +N'''			as '+ @alias_category_ids				+
	', '''+ @alias_eBay_category_ids +N'''		as '+ @alias_eBay_category_ids			+
	', ''item_model_b''							as item_model_b'						+
	', ''name''									as name'								+
	', ''meta_title''							as meta_title'							+
	', ''meta_description''						as meta_description'					+
	', ''condition''							as condition'							+
	', ''ebay_condition_id''					as ebay_condition_id'					+
	', ''amazon_condition_id''					as amazon_condition_id'					+
	', ''price''								as price'								+
	', ''item_dimension_length''				as item_dimension_length'				+
	', ''item_dimension_width''					as item_dimension_width'				+
	', ''item_dimension_height''				as item_dimension_height'				+
	', ''weight''								as weight'								+
	', ''manufacturer''							as manufacturer'						+
	', ''description''							as [description]'						+
	', ''short_description''					as [short_description]' 				+
	', ''status''								as [status]'							+
	', ''tax_class_id''							as tax_class_id'						+
	', ''qty''									as qty'									+
	', ''is_in_stock''							as is_in_stock'							+
	', '''+ @alias_is_featured			 + N'''	as '+ @alias_is_featured				+
	', '''+ @alias_is_deal_off			 + N'''	as '+ @alias_is_deal_off				+
	', ''item_is_deal_10_perc_off_ebay_price''	as item_is_deal_10_perc_off_ebay_price' +
	', ''SKU''									as SKU'									+
	', '''+ @alias_gallery				 + N''' as '+ @alias_gallery					+
	', '''+ @alias_position_gallerey	 + N''' as '+ @alias_position_gallerey			+
	', '''+ @alias_label_gallerey		 + N''' as '+ @alias_label_gallerey				+
	', '''+ @alias_image_base			 + N''' as '+ @alias_image_base					+
	', '''+ @alias_label_base			 + N''' as '+ @alias_label_base					+
	', '''+ @alias_image_small			 + N''' as '+ @alias_image_small				+
	', '''+ @alias_label_small			 + N''' as '+ @alias_label_small				+
	', '''+ @alias_image_thumbnail		 + N''' as '+ @alias_image_thumbnail			+
	', '''+ @alias_label_thumbnail		 + N''' as '+ @alias_label_thumbnail			+ 
	', '' visibility''							as  visibility'					+N'
	'
	
	DECLARE @C_SQL_BODY NVARCHAR(max) = 
	' 			
	SELECT ' +
	'  1 ORDER_BY_KEY ' +
	', ''ALL'' as ITEM_EXPORT_SYS_CD ' + -- *** DO NOT CHANGE THIS ROW it is used by REPLACE() later ***
	', dbo.fn_str_D_CATEGORY_HIERARCHY_SET(fim.ITEM_NUMBER)							as '+ @alias_category_ids				+
	', dbo.fn_str_D_EBAY_CATEGORY_HIERARCHY_SET(fi.ITEM_ID)							as '+ @alias_eBay_category_ids			+
	', fim.ITEM_NUMBER																as item_model_b'						+
	', fi.ITEM_DESC																	as name'								+
	', fi.ITEM_DESC																	as meta_title'							+
	', fimt.ITEM_TITLE																as meta_description'					+
	', dic.ITEM_CONDITION_CD														as condition'							+
	', CONVERT(NVARCHAR(10), diec.EXTERNAL_CONDITION_ID)							as ebay_condition_id'					+
	', CONVERT(NVARCHAR(10), diec_amazon.EXTERNAL_CONDITION_CD)						as amazon_condition_id'					+
	', CONVERT(NVARCHAR(50), fi.PRICE)												as price'								+
	', CONVERT(NVARCHAR(20), fimd.DIMENSION_LENGTH)									as item_dimension_length'				+
	', CONVERT(NVARCHAR(20), fimd.DIMENSION_WIDTH)									as item_dimension_width' 				+
	', CONVERT(NVARCHAR(20), fimd.DIMENSION_HEIGHT)									as item_dimension_height'				+
	', CONVERT(NVARCHAR(20), fimd.DIMENSION_WEIGHT)									as weight'								+
	', CASE 
		WHEN (ISNULL(dm.MANUFACTURER_DESC, '''') != '''') THEN dm.MANUFACTURER_DESC 
		ELSE dm.MANUFACTURER_CD 
	   END																			as manufacturer'						+
	', REPLACE(REPLACE(fi.LONG_DESC, CHAR(13), '' ''), CHAR(10), '''')				as [description]'						+
	', fimt.ITEM_TITLE																as [short_description]'					+
	', ''1''																		as [status]'							+
	', ''None''																		as tax_class_id'						+
	', CONVERT(NVARCHAR(50), fi.QUANTITY)											as qty'									+
	', CASE WHEN ISNULL(fi.QUANTITY, 0) > 0 THEN ''1'' ELSE ''0'' END				as is_in_stock'							+
	', CONVERT(char(1), ISNULL(fi.IS_FEATURED, 0))									as '+ @alias_is_featured				+
	', CONVERT(char(1), ISNULL(fi.IS_DEAL_10_PERC_OFF_EBAY, 0))						as '+ @alias_is_deal_off				+
	', CONVERT(NVARCHAR(20), ISNULL(fif.ITEM_IS_DEAL_10_PERC_OFF_EBAY_PRICE, 0))	as item_is_deal_10_perc_off_ebay_price' +
	', CONVERT(NVARCHAR(20), fi.ITEM_ID)											as SKU'									+
	', fii.'+ @alias_gallery										+
	', fii.'+ @alias_position_gallerey								+
	', fii.'+ @alias_label_gallerey									+
	', fii.'+ @alias_image_base										+
	', fii.'+ @alias_label_base										+
	', fii.'+ @alias_image_small									+
	', fii.'+ @alias_label_small									+
	', fii.'+ @alias_image_thumbnail								+
	', fii.'+ @alias_label_thumbnail								+
	', ''4'' as  visibility'	+N'
	'
	
	DECLARE ItemCapabilityTypeId CURSOR LOCAL FORWARD_ONLY FOR
		SELECT
			INVENTORY_CAPABILITY_TYPE_ID
		FROM 
			dbo.C_INVENTORY_CAPABILITY_TYPE
		WHERE
			IS_INACTIVE = 0
			AND IS_DELETED = 0
		ORDER BY
			INVENTORY_CAPABILITY_TYPE_ID
			
	OPEN ItemCapabilityTypeId
	DECLARE @ITEM_CAPABILITY_TYPE_ID NVARCHAR(150)
	FETCH NEXT FROM ItemCapabilityTypeId INTO @ITEM_CAPABILITY_TYPE_ID
	WHILE @@FETCH_STATUS = 0
	BEGIN
		DECLARE @ITEM_CAPABILITY_TYPE_NAME NVARCHAR(150) = ''
		SELECT
			@ITEM_CAPABILITY_TYPE_NAME = LOWER('ITEM_' + REPLACE(REPLACE(REPLACE(REPLACE(INVENTORY_CAPABILITY_VALUE, ' ', '_'), '.', ''), '(', ''), ')', ''))		
		FROM 
			dbo.C_INVENTORY_CAPABILITY_TYPE
		WHERE
			INVENTORY_CAPABILITY_TYPE_ID = @ITEM_CAPABILITY_TYPE_ID
		
		SET @C_SQL_HEADER_SELECT = @C_SQL_HEADER_SELECT + ', CONVERT(NVARCHAR(MAX), ' + @ITEM_CAPABILITY_TYPE_NAME + ') ' + @ITEM_CAPABILITY_TYPE_NAME
		SET @C_SQL_HEADER = @C_SQL_HEADER + ', ''' + @ITEM_CAPABILITY_TYPE_NAME + ''' AS ' + @ITEM_CAPABILITY_TYPE_NAME
		--SET @C_SQL_BODY = @C_SQL_BODY + ', ''' + @ITEM_CAPABILITY_TYPE_ID + ''' ' + @ITEM_CAPABILITY_TYPE_NAME
		SET @C_SQL_BODY = @C_SQL_BODY + 
			', dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE(fi.ITEM_ID, fimsa.ITEM_MASTER_SKU_ATTRB_CD, ' + @ITEM_CAPABILITY_TYPE_ID + ') ' + @ITEM_CAPABILITY_TYPE_NAME
		
		FETCH NEXT FROM ItemCapabilityTypeId INTO @ITEM_CAPABILITY_TYPE_ID
	END
	CLOSE ItemCapabilityTypeId
	DEALLOCATE ItemCapabilityTypeId

	IF (OBJECT_ID('dbo.F_ITEM_EXPORT', 'U') IS NOT NULL)
	BEGIN
		DROP TABLE F_ITEM_EXPORT
	END
	
	EXEC
	(	@C_SQL_DECLARE_HEADER_00 + 
		@C_SQL_DECLARE_HEADER_01 + 
		@C_SQL_HEADER_SELECT +
		N' INTO F_ITEM_EXPORT 
		   FROM 
		   ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM 
				F_ITEM_MASTER							fim		WITH(NOLOCK)		
				LEFT JOIN F_ITEM_MASTER_TITLE			fimt	WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fimt.ITEM_MASTER_ID
				INNER JOIN D_MANUFACTURER				dm		WITH(NOLOCK)
					ON fim.MANUFACTURER_ID = dm.MANUFACTURER_ID
				INNER JOIN F_ITEM						fi		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fi.ITEM_MASTER_ID
					AND fi.IS_QUALIFIED = 1 
				LEFT JOIN [dbo].[F_DIMENSION]			fimd	WITH(NOLOCK)
					ON fimd.DIMENSION_ID = FI.DIMENSION_ID
				LEFT JOIN D_ITEM_CONDITION				dic		WITH(NOLOCK)
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
					AND dic.IS_ECOMMERCE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec	WITH(NOLOCK)
					ON diec.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec.EXETERNAL_CONDITION_TYPE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec_amazon WITH(NOLOCK)
					ON diec_amazon.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec_amazon.EXETERNAL_CONDITION_TYPE = 2
				LEFT OUTER JOIN F_ITEM_PRICING			fip		WITH(NOLOCK)
					ON  fim.ITEM_MASTER_ID = fip.ITEM_MASTER_ID 
					AND fip.SOURCE_SYS_ID = 3
				LEFT OUTER JOIN F_ITEM_FEATURED			fif		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fif.ITEM_MASTER_ID
				LEFT OUTER JOIN F_ITEM_MASTER_SKU_ATTRB fimsa	WITH(NOLOCK)
					ON fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
				LEFT JOIN @ItemImage					fii
					ON fi.ITEM_ID = fii.ITEM_ID			
		 ) t 
		ORDER BY ORDER_BY_KEY 
		'
	)

	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''ALL'' as ITEM_EXPORT_SYS_CD', '''EBAY'' as ITEM_EXPORT_SYS_CD')
	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''4'' as  visibility', '''1'' as  visibility')
	SET @C_SQL_HEADER = REPLACE(@C_SQL_HEADER, '''ALL'' as ITEM_EXPORT_SYS_CD', '''EBAY'' as ITEM_EXPORT_SYS_CD')
	--SELECT @C_SQL_BODY

	EXEC
	(
		@C_SQL_DECLARE_HEADER_00 +
		@C_SQL_DECLARE_HEADER_01 +
		N' INSERT INTO F_ITEM_EXPORT ' +
		@C_SQL_HEADER_SELECT + 
		--' INTO F_ITEM_EXPORT ' +
		N' FROM 
		 ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM
				F_ITEM_MASTER							fim		WITH(NOLOCK)				
				LEFT JOIN F_ITEM_MASTER_TITLE			fimt	WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fimt.ITEM_MASTER_ID
				INNER JOIN D_MANUFACTURER				dm		WITH(NOLOCK)
					ON fim.MANUFACTURER_ID = dm.MANUFACTURER_ID
				INNER JOIN F_ITEM						fi		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fi.ITEM_MASTER_ID
					AND fi.IS_QUALIFIED = 1 
				LEFT JOIN [dbo].[F_DIMENSION]			fimd	WITH(NOLOCK)
					ON fimd.DIMENSION_ID = FI.DIMENSION_ID
				INNER JOIN D_ITEM_CONDITION				dic		WITH(NOLOCK)
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
					AND dic.IS_ECOMMERCE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec	WITH(NOLOCK)
					ON diec.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec.EXETERNAL_CONDITION_TYPE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec_amazon	WITH(NOLOCK)
					ON diec_amazon.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec_amazon.EXETERNAL_CONDITION_TYPE = 2
				LEFT OUTER JOIN F_ITEM_PRICING			fip		WITH(NOLOCK)
					ON  fim.ITEM_MASTER_ID = fip.ITEM_MASTER_ID 
					AND fip.SOURCE_SYS_ID = 3
				LEFT OUTER JOIN F_ITEM_FEATURED			fif		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fif.ITEM_MASTER_ID
				LEFT OUTER JOIN F_ITEM_MASTER_SKU_ATTRB fimsa	WITH(NOLOCK)
					ON fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
				LEFT JOIN @ItemImage					fii		WITH(NOLOCK)
					ON fi.ITEM_ID = fii.ITEM_ID	
		 ) t 
		 ORDER BY ORDER_BY_KEY 
		 '
	)

	SET @C_SQL_BODY = REPLACE(@C_SQL_BODY, '''EBAY'' as ITEM_EXPORT_SYS_CD', '''AMAZON'' as ITEM_EXPORT_SYS_CD')
	SET @C_SQL_HEADER = REPLACE(@C_SQL_HEADER, '''EBAY'' as ITEM_EXPORT_SYS_CD', '''AMAZON'' as ITEM_EXPORT_SYS_CD')
	--SELECT @C_SQL_BODY

	EXEC
	(
		@C_SQL_DECLARE_HEADER_00 +
		@C_SQL_DECLARE_HEADER_01 +
		N' INSERT INTO F_ITEM_EXPORT ' +
		@C_SQL_HEADER_SELECT + 
		--' INTO F_ITEM_EXPORT ' +
		N' FROM 
		 ( ' +
			@C_SQL_HEADER +
			N' UNION ' + 
			@C_SQL_BODY +
			N' FROM
				F_ITEM_MASTER							fim		WITH(NOLOCK)		
				LEFT JOIN F_ITEM_MASTER_TITLE			fimt	WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fimt.ITEM_MASTER_ID
				INNER JOIN D_MANUFACTURER				dm		WITH(NOLOCK)
					ON fim.MANUFACTURER_ID = dm.MANUFACTURER_ID
				INNER JOIN F_ITEM						fi		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fi.ITEM_MASTER_ID
					AND fi.IS_QUALIFIED = 1
				LEFT JOIN [dbo].[F_DIMENSION]			fimd	WITH(NOLOCK)
					ON fimd.DIMENSION_ID = FI.DIMENSION_ID
				LEFT JOIN D_ITEM_CONDITION				dic		WITH(NOLOCK)
					ON fi.CONDITION_ID = dic.ITEM_CONDITION_ID
					AND dic.IS_ECOMMERCE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec	WITH(NOLOCK)
					ON diec.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec.EXETERNAL_CONDITION_TYPE = 1
				LEFT JOIN dbo.D_ITEM_EXTERNAL_CONDITION diec_amazon WITH(NOLOCK)
					ON diec_amazon.TBS_CONDITION_ID = fi.CONDITION_ID
					AND diec_amazon.EXETERNAL_CONDITION_TYPE = 2
				LEFT OUTER JOIN F_ITEM_PRICING			fip		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fip.ITEM_MASTER_ID
					AND fip.SOURCE_SYS_ID = 3
				LEFT OUTER JOIN F_ITEM_FEATURED			fif		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fif.ITEM_MASTER_ID
				LEFT OUTER JOIN F_ITEM_MASTER_SKU_ATTRB fimsa	WITH(NOLOCK)
					ON fi.ITEM_MASTER_SKU_ATTRB_ID = fimsa.ITEM_MASTER_SKU_ATTRB_ID
				LEFT JOIN @ItemImage					fii
					ON fi.ITEM_ID = fii.ITEM_ID 
			' +
			N'
		 ) t 
		ORDER BY ORDER_BY_KEY 
		'
	)	
END

-- =============================================
-- Author:		
-- Create date: 7/10/2014
-- Description:	deletes substitute reference
-- =============================================
CREATE PROCEDURE [dbo].[sp_DELETE_ITEM_MASTER_SUBSTITUTES] 
	@C_MASTER_ITEM_ID			BIGINT,
	@C_SUBSTITUTE_IDS			bigint_Bit_Array READONLY,
	@C_USE_INTERNAL_TRANSACTION	BIT	= 1
AS
BEGIN

	IF (@C_USE_INTERNAL_TRANSACTION = 1)
	BEGIN
		SET XACT_ABORT ON
		BEGIN TRAN
	END
		
		-- normal (group) substitutes ----------------------------
		DELETE FIMS -- delete from the group
		FROM @C_SUBSTITUTE_IDS						SI
		INNER JOIN dbo.F_ITEM_MASTER_SUBSTITUTE		FIMS WITH(ROWLOCK)
			ON SI.ID = FIMS.ITEM_MASTER_ID
			AND SI.[VALUE] = 0 -- group sub

		-- delete the lonely record
		IF (SELECT
				COUNT(FIMS_GR.ITEM_MASTER_SUBSTITUTE_ID)
			FROM dbo.F_ITEM_MASTER_SUBSTITUTE			FIMS	WITH(NOLOCK)
			INNER JOIN dbo.F_ITEM_MASTER_SUBSTITUTE		FIMS_GR	WITH(NOLOCK)
				ON  FIMS_GR.ITEM_MASTER_GROUP_ID	= FIMS.ITEM_MASTER_GROUP_ID
			WHERE FIMS.ITEM_MASTER_ID = @C_MASTER_ITEM_ID) = 1
		BEGIN
			DELETE FROM dbo.F_ITEM_MASTER_SUBSTITUTE
			WHERE ITEM_MASTER_ID = @C_MASTER_ITEM_ID
		END

		-- one way substitutes -----------------------------------
		DELETE FIMS1
		FROM @C_SUBSTITUTE_IDS							SI
		INNER JOIN dbo.F_ITEM_MASTER_SUBSTITUTE_1_WAY	FIMS1 WITH(ROWLOCK)
			ON SI.ID = FIMS1.SUBSTITUTE_ITEM_MASTER_ID
			AND SI.[VALUE] = 1 -- 1-way sub
		WHERE FIMS1.ITEM_MASTER_ID = @C_MASTER_ITEM_ID


	IF (@C_USE_INTERNAL_TRANSACTION = 1)
	BEGIN
		COMMIT TRAN
	END

END
CREATE TABLE [dbo].[F_SYSTEM_LOG] (
    [ENTRY_ID]    BIGINT          IDENTITY (1, 1) NOT NULL,
    [LOGGER_NAME] NVARCHAR (256)  NOT NULL,
    [URL]         NCHAR (1024)    NULL,
    [THREAD]      NVARCHAR (256)  NULL,
    [EVENT_LEVEL] NVARCHAR (64)   NULL,
    [CLASS]       NVARCHAR (128)  NULL,
    [METHOD]      NVARCHAR (128)  NULL,
    [MESSAGE]     NVARCHAR (4000) NOT NULL,
    [EXCEPTION]   NVARCHAR (2048) NULL,
    [STACK_TRACE] NVARCHAR (MAX)  NULL,
    [USER_ID]     BIGINT          NULL,
    [USER_IP]     BIGINT          NULL,
    [INSERTED_DT] DATETIME        CONSTRAINT [DF_F_SYSTEM_LOG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [INSERTED_BY] NVARCHAR (64)   NOT NULL,
    CONSTRAINT [PK_F_SYSTEM_LOG] PRIMARY KEY CLUSTERED ([ENTRY_ID] ASC),
    CONSTRAINT [FK_F_SYSTEM_LOG_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);




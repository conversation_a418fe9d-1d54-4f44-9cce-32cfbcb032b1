CREATE TABLE [dbo].[C_RecyclingQuoteTransition]
(
	[Id]				int not null,
	[FromStatus]		int not null,
    [ToStatus]	        int not null,
    [Name]              nvarchar(100),
    [IsAllowed]         bit not null constraint [DF_C_RecyclingQuoteTransition_IsAllowed] default (1),
    [InsertedDate]      datetime not null
        constraint [DF_C_RecyclingQuoteTransition_InsertedDate] default (getutcdate()),
    [InsertedByUserId]	bigint not null,
	[InsertedBy]        nvarchar(250) null, 
    constraint [PK_C_RecyclingQuoteTransition_Id] primary key clustered ([Id] ASC),
    constraint [FK_C_RecyclingQuoteTransition_tb_User] foreign key ([InsertedByUserId]) references [dbo].[tb_User] ([UserID]),
    constraint [FK_C_RecyclingQuoteTransition_С_RecyclingQuoteStatusFrom] foreign key (FromStatus) references [dbo].C_RecyclingQuoteStatus ([Id]),
    constraint [FK_C_RecyclingQuoteTransition_С_RecyclingQuoteStatusTo] foreign key (ToStatus) references [dbo].C_RecyclingQuoteStatus ([Id])
);

GO
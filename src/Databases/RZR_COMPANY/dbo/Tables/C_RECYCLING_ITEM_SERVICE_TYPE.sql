CREATE TABLE [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] (
    [SERVICE_TYPE_ID]          INT            NOT NULL,
    [SERVICE_TYPE_CD]          NVARCHAR (256) NOT NULL,
    [SERVICE_TYPE_DESCR]       NVARCHAR (MAX) NULL,
    [IS_SYSTEM]                BIT            CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_IS_SYSTEM] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]              BIGINT         CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_INSERTED_BY] DEFAULT ((1)) NOT NULL,
    [INSERTED_DT]              DATETIME       CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]               BIGINT         NULL,
    [UPDATED_DT]               DATETIME       NULL,
    [DELETED_BY]               BIGINT         NULL,
    [DELETED_DT]               DATETIME       NULL,
    [IS_ACTIVE]                BIT            CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_IS_ACTIVE] DEFAULT ((1)) NOT NULL,
    [ApplyType]                TINYINT        CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_ApplyType] DEFAULT ((0)) NOT NULL,
    [IsForServiceRequirements] BIT            CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_IsDisplayOnClientPortal] DEFAULT ((0)) NULL,
    [ExternalId]               NVARCHAR (68)  NULL,
    [OnsiteTypeId]             INT            NULL,
    [PricingType]              TINYINT        CONSTRAINT [DF_C_RECYCLING_ITEM_SERVICE_TYPE_PricingType] DEFAULT ((0)) NOT NULL,
    [SinglePrice]              FLOAT          NULL,
    CONSTRAINT [PK_C_RECYCLING_ITEM_SERVICE_TYPE] PRIMARY KEY CLUSTERED ([SERVICE_TYPE_ID] ASC),
    CONSTRAINT [FK_C_RECYCLING_ITEM_SERVICE_TYPE_D_ServiceOnsiteOptionHierarchy] FOREIGN KEY ([OnsiteTypeId]) REFERENCES [dbo].[D_ServiceOnsiteOptionHierarchy]([Id]),
);


GO
EXECUTE sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = 'Contains all allowed services', 
    @level0type = N'SCHEMA', 
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'C_RECYCLING_ITEM_SERVICE_TYPE';
    
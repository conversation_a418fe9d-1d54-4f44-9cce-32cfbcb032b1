-- =============================================
-- Author:		<PERSON><PERSON><PERSON><PERSON><PERSON>
-- Create date: 09/16/2015
-- Description:	Gets master item primary category for autocomplete
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ITEM_MASTER_PRIMARY_CATEGORY_FOR_AUTOCOMPLETE] 
	@ITEM_MASTER_ID		BIGINT
AS
BEGIN
	SELECT DISTINCT TOP(1)
		vw.CategoryId							AS [value]
		,vw.CategoryFullPathToDisplay			AS label
		,vw.CategoryName						AS DisplayName
		,Ct.INVENTORY_ATTRIBUTE_TYPE_ID			AS InventoryAttributeId
		,CT.INVENTORY_ATTRIBUTE_NAME			AS InventoryAttributeCd
	FROM dbo.F_ITEM_MASTER											FIM WITH(NOLOCK)
	LEFT JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY]	vw	WITH(NOLOCK)	
		ON vw.ItemMasterId = FIM.ITEM_MASTER_ID
		AND vw.CategoryIsPrimary = 1
	LEFT JOIN C_INVENTORY_ATTRIBUTE_TYPE							CT	WITH (NOLOCK)
		ON CT.INVENTORY_ATTRIBUTE_TYPE_ID = ISNULL(vw.CategoryAttributeSetId, FIM.INVENTORY_ATTRIBUTE_TYPE_ID)
	WHERE FIM.ITEM_MASTER_ID	= @ITEM_MASTER_ID
END
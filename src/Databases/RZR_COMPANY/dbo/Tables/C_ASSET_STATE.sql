--TODO: get rid of this table
CREATE TABLE [dbo].[C_ASSET_STATE] (
    [ASSET_STATE_ID]                BIGINT        NOT NULL,
    [ASSET_STATE_CD]                VARCHAR (256) NOT NULL,
    [ASSET_STATE_DESC]              VARCHAR (256) NOT NULL,
    [ASSET_STATE_NEXT_STEP_DEFAULT] BIGINT        CONSTRAINT [DF_C_ASSET_STATE_ASSET_STATE_NEXT_STEP_DEFAULT] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]                   BIT           CONSTRAINT [DF_C_ASSET_STATE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT           CONSTRAINT [DF_C_ASSET_STATE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   VARCHAR (256) CONSTRAINT [DF_C_ASSET_STATE_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                   DATETIME      CONSTRAINT [DF_C_ASSET_STATE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                    VARCHAR (256) NULL,
    [UPDATED_DT]                    DATETIME      NULL,
    [DELETED_BY]                    VARCHAR (256) NULL,
    [DELETED_DT]                    DATETIME      NULL,
    CONSTRAINT [PK_C_ASSET_STATE] PRIMARY KEY CLUSTERED ([ASSET_STATE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The ITAD tabs/steps', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_ASSET_STATE';


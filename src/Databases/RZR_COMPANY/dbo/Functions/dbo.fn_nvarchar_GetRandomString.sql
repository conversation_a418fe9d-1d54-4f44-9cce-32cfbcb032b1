CREATE FUNCTION [dbo].[fn_nvarchar_GetRandomString]
(
	@len int
)
RETURNS NVARCHAR(MAX)
AS
BEGIN
	declare
		@password nvarchar(max),
		@type int,
		@rnd float

	set @password = ''

	while (@len > 0)
	begin
		select 
			@rnd = val
		from [dbo].[vw_RandValue]
		
		set @type = round(1 + (@rnd * (3)), 0)

		select 
			@rnd = val
		from [dbo].[vw_RandValue]

		set @password = @password + 
			case @type
				when 1 
				then char(round(97 + (@rnd * (25)), 0))
				when 2 
				then char(round(65 + (@rnd * (25)), 0))
				when 3
				then char(round(48 + (@rnd * (9)), 0))
				when 4
				then char(round(33 + (@rnd * (13)), 0))
			end
	
		set @len = @len - 1
	end

	return @password
END
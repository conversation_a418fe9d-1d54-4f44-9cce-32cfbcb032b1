CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_PICK_LIST_CLOSED_FROM_QUEUE]
	@C_COUNT INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID		BIGINT = 18
	DECLARE @IS_ALLOWED					BIT = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID)
	
	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY  = N'sp_GET_NOTIFICATIONS_PICK_LIST_CLOSED_FROM_QUEUE',
			UPDATED_DT  = GETUTCDATE()
		WHERE IS_INACTIVE		   = 0
		  AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID		
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP (@C_COUNT)

			 NQ.NotificationQueueId		AS [NOTIFICATION_QUEUE_ID]
			,DU.*
			,PL.*
			,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, QP.REP_ID, NULL, NULL, NQ.NotificationSetId,NQ.CustomerId) AS EMAILS
		
		FROM @tNotifyItemWithNotifySet													NQ
		CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId)		QP
		CROSS APPLY [dbo].tvf_GET_SNIPPET_DATA_PICK_LIST        (QP.PICK_LIST_ID)   PL
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER]			(QP.REP_ID)			DU
		WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		  AND QP.PICK_LIST_ID IS NOT NULL
	
END

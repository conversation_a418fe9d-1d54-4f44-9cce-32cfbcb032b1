CREATE TABLE [dbo].[F_ITEM_COMBO] (
    [ITEM_COMBO_ID]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_COMBO_HEADER_ID] BIGINT        NOT NULL,
    [ITEM_ID_MASTER]       BIGINT        NOT NULL,
    [ITEM_ID]              BIGINT        NULL,
    [QUANTITY]             FLOAT (53)    CONSTRAINT [DF_F_ITEM_COMBO_QUANTITY] DEFAULT ((1)) NULL,
    [IS_INACTIVE]          BIT           CONSTRAINT [DF_F_ITEM_COMBO_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]           BIT           CONSTRAINT [DF_F_ITEM_COMBO_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]          VARCHAR (250) NOT NULL,
    [INSERTED_DT]          DATETIME      NOT NULL,
    [UPDATED_BY]           VARCHAR (250) NULL,
    [UPDATED_DT]           DATETIME      NULL,
    [DELETED_BY]           VARCHAR (250) NULL,
    [DELETED_DT]           DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_COMBO] PRIMARY KEY CLUSTERED ([ITEM_COMBO_ID] ASC),
    CONSTRAINT [FK_F_ITEM_COMBO_F_ITEM] FOREIGN KEY ([ITEM_ID_MASTER]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]),
    CONSTRAINT [FK_F_ITEM_COMBO_F_ITEM_COMBO_HEADER] FOREIGN KEY ([ITEM_COMBO_HEADER_ID]) REFERENCES [dbo].[F_ITEM_COMBO_HEADER] ([ITEM_COMBO_HEADER_ID]),
    CONSTRAINT [FK_F_ITEM_COMBO_F_ITEM1] FOREIGN KEY ([ITEM_ID]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID])
);
GO
CREATE INDEX IDX_F_ITEM_COMBO_ITEM_ID_MASTER ON dbo.F_ITEM_COMBO(ITEM_ID_MASTER)
INCLUDE([ITEM_COMBO_ID], [ITEM_COMBO_HEADER_ID])
GO

CREATE INDEX IDX_F_ITEM_COMBO_ITEM_COMBO_HEADER_ID ON dbo.F_ITEM_COMBO(ITEM_COMBO_HEADER_ID)
INCLUDE([ITEM_COMBO_ID], ITEM_ID_MASTER)
GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'it stores the set of items (SKUs)  with quantity for each', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_COMBO';


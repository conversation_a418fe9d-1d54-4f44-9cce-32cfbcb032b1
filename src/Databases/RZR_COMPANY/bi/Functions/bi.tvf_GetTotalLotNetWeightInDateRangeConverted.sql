CREATE FUNCTION [bi].[tvf_GetTotalLotNetWeightInDateRangeConverted]
(  
	@from			datetime,
	@to				datetime,
	@utc_offset		int,
	@convertToKgs	bit
)
RETURNS @T_RESULT TABLE
(
	TotalLotNetWeight	decimal(13,2),
	[time]				datetime
)
AS
BEGIN

	DECLARE
		@UTC_NOW      DATETIME = dateadd(d, datediff(d,0, GETUTCDATE()), 0),
		@poundToKilos float = 0.45359237
	DECLARE @conversionRate  float = IIF(@convertToKgs = 1, @PoundToKilos, 1)
	
	INSERT INTO @T_RESULT
	SELECT 
		ROUND(SUM(ISNULL(FROI.[WEIGHT_RECEIVED], 0) - ISNULL(FROI.[WEIGHT_TARE], 0)) * @conversionRate, 2) AS TotalLotNetWeight,
		@UTC_NOW	AS [time]		
	FROM dbo.F_RECYCLING_ORDER_ITEM										FROI	WITH(NOLOCK)
	INNER JOIN dbo.F_RECYCLING_ORDER									FRO		WITH(NOLOCK)
		ON FROI.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
	INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND							FROIN	WITH(NOLOCK)
		ON FRO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID					
	INNER JOIN [recycling].[C_InboundOrderStatus]						OS		WITH(NOLOCK)
		ON FROIN.StatusId = OS.Id			 
	INNER JOIN dbo.F_CUSTOMER											C		WITH(NOLOCK)
		ON FRO.CUSTOMER_ID = C.CUSTOMER_ID
	INNER JOIN dbo.F_RECYCLING_ITEM_MASTER								FRIM	WITH(NOLOCK)	
		ON FROI.RECYCLING_ITEM_MASTER_ID = FRIM.RECYCLING_ITEM_MASTER_ID
	INNER JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]						CRWT	WITH(NOLOCK)
		ON CRWT.[WORKFLOW_TYPE_ID] = FROI.[WORKFLOW_STEP_ID]
	INNER JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]						DRPT	WITH(NOLOCK)
		ON DRPT.[RECYCLING_PACKAGING_TYPE_ID] = FROI.[PACKAGING_TYPE_ID]
	LEFT JOIN dbo.F_LOCATION											L		WITH(NOLOCK)
		ON L.LOCATION_ID = FROI.LOCATION_ID				
	LEFT JOIN dbo.F_RECYCLING_ORDER										OO		WITH(NOLOCK)
		ON FROI.OUTBOUND_ORDER_ID = OO.RECYCLING_ORDER_ID
		AND OO.RECYCLING_ORDER_STATUS_ID != 4
	LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM_TRANSFER						ROIT	WITH(NOLOCK)
		ON ROIT.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
		AND ROIT.OUTBOUND_ORDER_ID = FROI.OUTBOUND_ORDER_ID -- only the actual one
	LEFT JOIN dbo.F_RECYCLING_ORDER										ROT		WITH(NOLOCK)
		ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
	LEFT JOIN dbo.D_WAREHOUSE											W		WITH(NOLOCK)
		ON COALESCE(ROT.[WAREHOUSE_ID], L.[WAREHOUSE_ID], FRO.[WAREHOUSE_ID]) = W.WAREHOUSE_ID
	WHERE FROI.IS_INACTIVE = 0
		AND (ROT.[WAREHOUSE_ID] IS NULL AND L.[WAREHOUSE_ID] IS NULL AND FRO.[WAREHOUSE_ID] IS NULL OR W.[WAREHOUSE_ID] IS NOT NULL)
		AND (OO.RECYCLING_ORDER_ID IS NULL OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL)
		AND FROI.IS_CONSUMED_OR_PROCESSED = 0
		AND FROI.[INSERTED_DT] BETWEEN @from AND @to

	RETURN

END
CREATE TABLE [dbo].[C_RECYCLING_ORDER_INBOUND_BILLING_TYPE] (
    [RECYCLING_ORDER_INBOUND_BILLING_TYPE_ID] INT            NOT NULL,
    [RECYCLING_ORDER_INBOUND_BILLING_TYPE_CD] NVARCHAR (256) NOT NULL,
    CONSTRAINT [PK_C_RECYCLING_ORDER_INBOUND_BILLING_TYPE] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_INBOUND_BILLING_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The types of billing, is used for RMA and Inbound order modal or example', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_ORDER_INBOUND_BILLING_TYPE';


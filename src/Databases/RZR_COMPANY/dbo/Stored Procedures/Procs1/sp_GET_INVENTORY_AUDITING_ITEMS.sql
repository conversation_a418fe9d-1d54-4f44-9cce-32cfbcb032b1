-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_AUDITING_ITEMS]
	@LOCATION_ID		BIGINT,
	@SESSION_ID			BIGINT,
	@ORDER_COLUMN_NAME	varchar(150)	= N'ITEM_INVENTORY_ID',
	@ORDER_DIRECTION	varchar(20)		= N'ASC',
	@ITEMS_PER_PAGE		int				= 20,
	@PAGE_INDEX			int				= 0,
	@FILTER_WHERE		varchar(2000)	= N''
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE

	DECLARE @filterCondition varchar(2006) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END

	DECLARE @query NVARCHAR (MAX) = N'
			;WITH m_data AS	(
				SELECT
					fii.ITEM_INVENTORY_ID,
					fim.ITEM_NUMBER													AS MODEL,
					fim.ITEM_IPN													AS IPN,
					fim.ITEM_MPN													AS MPN,
					fii.ITEM_INVENTORY_SERIAL										AS SERIAL,
					fii.ITEM_INVENTORY_UNIQUE_ID									AS UNIQUE_IDENTIFIER,
					dm.MANUFACTURER_CD												AS MANUFACTURER,
					fi.ITEM_DESC													AS DESCRIPTION,
					dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(fii.LOCATION_ID)		AS LOCATION,
					cond.ITEM_CONDITION_CD											AS CONDITION,
					fii.AUDIT_DATE,
					u.UserName														AS AUDIT_BY,
					dias.STATUS_NAME												AS AUDIT_STATUS,
					u.UserID														as AuthorID
				FROM F_ITEM_INVENTORY						fii		WITH(NOLOCK)				
				left JOIN F_ITEM_MASTER					fim		WITH(NOLOCK)
					ON fim.ITEM_MASTER_ID = fii.ITEM_MASTER_ID
				left JOIN dbo.D_MANUFACTURER				dm		WITH(NOLOCK)
					ON dm.MANUFACTURER_ID = fim.MANUFACTURER_ID
				LEFT JOIN dbo.F_ITEM		fi	WITH(NOLOCK)
					ON fii.ITEM_ID = fi.ITEM_ID
				LEFT JOIN dbo.D_ITEM_CONDITION				cond	WITH(NOLOCK)
					ON cond.ITEM_CONDITION_ID = fii.CONDITION_ID
				LEFT JOIN dbo.D_INVENTORY_AUDIT_STATUS		dias	WITH(NOLOCK)
					ON dias.STATUS_ID = fii.AUDIT_STATUS_ID
				LEFT JOIN tb_User							u		WITH(NOLOCK)
					ON u.UserID = fii.AUDIT_USER_ID
				LEFT JOIN F_PURCHASE_ORDER_ITEM						FPOI		WITH (NOLOCK)
					ON fii.ITEM_INVENTORY_ID = FPOI.INVENTORY_ITEM_ID
				LEFT JOIN F_PURCHASE_ORDER							FPO			WITH (NOLOCK)
					ON FPOI.PURCHASE_ORDER_ID = FPO.PURCHASE_ORDER_ID
				LEFT JOIN F_ORDER_ORDER_SUBJECT_TYPE				POT			WITH (NOLOCK)
					ON POT.ORDER_ID = FPO.PURCHASE_ORDER_ID
						AND POT.ENTITY_TYPE_ID = 3
				LEFT JOIN F_PURCHASE_ORDER							FPOR		WITH (NOLOCK)
					ON fii.PurchaseOrderRepairId = FPOR.PURCHASE_ORDER_ID
				LEFT JOIN F_ORDER_ORDER_SUBJECT_TYPE				PORT		WITH (NOLOCK)
					ON PORT.ORDER_ID = FPOR.PURCHASE_ORDER_ID
					AND PORT.ENTITY_TYPE_ID = 3				
				WHERE fii.ITEM_STATUS_ID IN (1, 2, 8, 14)
				  AND fii.IS_DELETED = 0
				  AND (PORT.[ENTITY_SUBJECT_TYPE_ID] IS NULL and POT.[ENTITY_SUBJECT_TYPE_ID] IS NULL OR ISNULL(PORT.[ENTITY_SUBJECT_TYPE_ID], POT.[ENTITY_SUBJECT_TYPE_ID]) != 8)		-- 8 - Drop Ship		
				  AND fii.LOCATION_ID = ' + CAST(@LOCATION_ID AS VARCHAR(100)) + '
				  AND fii.AUDIT_SESSION_ID = ' + CAST(@SESSION_ID AS VARCHAR(100)) + '
				  AND fii.AUDIT_STATUS_ID != 0
			)SELECT TOP(1)
				-1		AS RowID,	COUNT(ITEM_INVENTORY_ID) AS ITEM_INVENTORY_ID,
				''''	AS MODEL,
				''''	AS IPN,
				''''	AS MPN,
				''''	AS SERIAL,
				''''	AS UNIQUE_IDENTIFIER,
				''''	AS MANUFACTURER,
				''''	AS DESCRIPTION,
				''''	AS LOCATION,
				''''	AS CONDITION,
				NULL	AS AUDIT_DATE,
				''''	AS AUDIT_BY,
				''''	AS AUDIT_STATUS
			FROM m_data ' + @filterCondition + N'
			UNION
			SELECT
				t.*
			FROM
				(SELECT	ROW_NUMBER() OVER (ORDER BY M.'+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N', M.ITEM_INVENTORY_ID desc) AS RowID,
						M.ITEM_INVENTORY_ID,
						M.MODEL,
						M.IPN,
						M.MPN,
						M.SERIAL,
						M.UNIQUE_IDENTIFIER,
						M.MANUFACTURER,
						M.DESCRIPTION,
						M.LOCATION,
						M.CONDITION,
						M.AUDIT_DATE,
						M.AUDIT_BY,
						M.AUDIT_STATUS
					FROM m_data M ' + @filterCondition + N'
				) t	WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))
	exec sp_executeSQL @query
END
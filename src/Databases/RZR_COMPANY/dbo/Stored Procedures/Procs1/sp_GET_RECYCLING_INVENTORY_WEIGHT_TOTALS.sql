-- =============================================
-- Author:		<O. Evseev>
-- Create date: <02/17/2014>
-- Description:	<Returns total weights for the order items>
-- =============================================
-- EXEC [dbo].[sp_GET_RECYCLING_INVENTORY_WEIGHT_TOTALS] 1
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INVENTORY_WEIGHT_TOTALS] 
	@C_WAREHOUSE_ID	BIGINT
AS
BEGIN

	DECLARE @UTC_NOW DATETIME = GETUTCDATE()

	DECLARE @TOTAL_CONSUMED_WEIGHT FLOAT = ISNULL([dbo].[fn_float_GET_RECYCLING_INVENTORY_CONSUMED_WEIGHT](@C_WAREHOUSE_ID, 1, NULL),0)
	DECLARE @TODAY_CONSUMED_WEIGHT FLOAT = ISNULL([dbo].[fn_float_GET_RECYCLING_INVENTORY_CONSUMED_WEIGHT](@C_WAREHOUSE_ID, 0, @UTC_NOW),0)

	SELECT 
		@TOTAL_CONSUMED_WEIGHT + (
			SELECT
				SUM(FROI.WEIGHT_REMAIN) as onHand
			FROM dbo.F_RECYCLING_ORDER_ITEM				FROI	WITH(NOLOCK)
			LEFT JOIN F_LOCATION						L		WITH(NOLOCK)
				ON FROI.LOCATION_ID = L.LOCATION_ID
			LEFT JOIN F_RECYCLING_ORDER					FRO		WITH(NOLOCK)
				ON FROI.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
			LEFT JOIN F_RECYCLING_ORDER					FROO	WITH(NOLOCK)
				ON  FROO.RECYCLING_ORDER_ID			= FROI.OUTBOUND_ORDER_ID
				AND FROO.RECYCLING_ORDER_STATUS_ID NOT IN (4, 8)
			LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER	ROIT	WITH(NOLOCK)
				ON  ROIT.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
				AND ROIT.OUTBOUND_ORDER_ID		 = FROI.OUTBOUND_ORDER_ID -- only the actual one
			LEFT JOIN dbo.F_RECYCLING_ORDER				ROT		WITH(NOLOCK)
				ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
				-- Not outbound shipped
			WHERE (@C_WAREHOUSE_ID IS NULL OR  ISNULL(L.WAREHOUSE_ID, FRO.WAREHOUSE_ID) = @C_WAREHOUSE_ID)
			  AND (FROO.RECYCLING_ORDER_ID IS NULL 
				-- or transfered and arrived to another warehouse
				OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL) -- AND ROO.IS_TRANSFER = 1 AND ROO.RECYCLING_ORDER_STATUS_ID = 7) -- 7 - Loading Complete
			  AND FROI.IS_CONSUMED_OR_PROCESSED = 0
			  AND FROI.IS_INACTIVE = 0					
		) 							AS Total,
			
		ISNULL((SELECT
			SUM(FROI.WEIGHT_RECEIVED - FROI.WEIGHT_TARE)
		FROM F_RECYCLING_ORDER_ITEM					FROI	WITH(NOLOCK)
		INNER JOIN dbo.F_RECYCLING_ORDER			FRO		WITH(NOLOCK)
			-- order ------------------------------------------
			ON  FRO.RECYCLING_ORDER_ID = ISNULL(FROI.RECYCLING_ORDER_INITIAL_ID, FROI.RECYCLING_ORDER_ID)
			AND FRO.IS_DELETED	 = 0
			AND FRO.IS_INACTIVE = 0 
				
		INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND	FROIN   WITH(NOLOCK)
			ON  FRO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID
			AND FROIN.StatusId NOT IN (7)/*Canceled*/
			AND FROIN.IsFromConsumable =0  /* Is not From Consumable*/
			--AND FRO.IS_TRANSFER = 0
			
		LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER	ROIT	WITH(NOLOCK)
			ON  ROIT.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
			AND ROIT.OUTBOUND_ORDER_ID		 = FROI.OUTBOUND_ORDER_ID -- only the actual one
		LEFT JOIN F_LOCATION						L	WITH (NOLOCK)
			ON L.LOCATION_ID = FROI.LOCATION_ID		
		-- FROI --------------------------------------------
		WHERE DATEDIFF(day, FROI.INSERTED_DT, @UTC_NOW) = 0
			AND (@C_WAREHOUSE_ID IS NULL OR ISNULL(L.WAREHOUSE_ID, FRO.WAREHOUSE_ID) = @C_WAREHOUSE_ID)
			-- not a sorted out one
			AND FROI.PARENT_ID IS NULL 
			-- not received from consumed
			AND NOT EXISTS (
			  SELECT TOP(1) 1
			  FROM F_RECYCLING_ORDER_ITEM_CONSUMED	FROC WITH(ROWLOCK)
			  WHERE FROC.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
			)
			-- not a sorted out one
			AND (FROI.PARENT_ID IS NULL 
				-- not created as a merge (but could be merged several times)
				and not exists(
					select top(1) 1
					from F_RECYCLING_ORDER_ITEM_MERGE M1
					where M1.RECYCLING_ORDER_ITEM_PRIMARY_ID = FROI.RECYCLING_ORDER_ITEM_ID
				)
				--OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL -- will dupe the weight if run against all warehouses
			)--AND FROI.IS_INACTIVE = 0 -- was set inactive if was merged	
			AND FROI.IS_DELETED  = 0), 0.0)	AS Received,
											
		ISNULL((SELECT
			SUM(IIF(ISNULL(FROI.WEIGHT_REMAIN, 0.0) = 0.0, FROI.WEIGHT_RECEIVED - FROI.WEIGHT_TARE, FROI.WEIGHT_REMAIN))-- - ISNULL(FROI.WEIGHT_LOOSE_LOAD, 0.0)))
		FROM F_RECYCLING_ORDER_ITEM				FROI	WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER			FRO_OUT	WITH(NOLOCK)
			ON  FRO_OUT.RECYCLING_ORDER_ID = FROI.OUTBOUND_ORDER_ID
			AND FRO_OUT.IS_TRANSFER = 0
			AND FRO_OUT.IS_DELETED	= 0
			AND FRO_OUT.IS_INACTIVE = 0
			AND FRO_OUT.RECYCLING_ORDER_STATUS_ID IN (4,5,7) --select * from C_RECYCLING_ORDER_STATUS
		INNER JOIN F_RECYCLING_ORDER_OUTBOUND	FROO	WITH(NOLOCK)
			ON  FROO.RECYCLING_ORDER_ID = FROI.OUTBOUND_ORDER_ID
			AND FROO.IS_DELETED = 0	
		LEFT JOIN F_LOCATION					L		WITH (NOLOCK)
			ON L.LOCATION_ID = FROI.LOCATION_ID
		WHERE DATEDIFF(day, FROI.EXPORTED_DT, @UTC_NOW) = 0--DATEDIFF(day, FROO.LOADING_DT, @UTC_NOW) = 0)
		  AND (ISNULL(L.WAREHOUSE_ID, FRO_OUT.WAREHOUSE_ID) = @C_WAREHOUSE_ID OR @C_WAREHOUSE_ID IS NULL)
		  AND FROI.IS_DELETED  = 0
		  AND FROI.IS_INACTIVE = 0	-- Internal lots are set inactive
		), 0.0)								AS Exported,

		@TODAY_CONSUMED_WEIGHT				AS Consumed	

END
GO
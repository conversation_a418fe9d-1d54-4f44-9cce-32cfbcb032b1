CREATE TABLE [dbo].[F_USER_WAREHOUSE] (
    [USER_WAREHOUSE_ID] BIGINT IDENTITY (1, 1) NOT NULL,
    [USER_ID]           BIGINT NOT NULL,
    [WAREHOUSE_ID]      BIGINT NOT NULL,
    CONSTRAINT [PK_F_USER_WAREHOUSE] PRIMARY KEY CLUSTERED ([USER_WAREHOUSE_ID] ASC),
    CONSTRAINT [FK_F_USER_WAREHOUSE_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_USER_WAREHOUSE_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE ON UPDATE CASCADE
);


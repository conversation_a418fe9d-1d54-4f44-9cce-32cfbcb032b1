
CREATE procedure [dbo].[sp_SetContractResalePricingRuleImport]
	 @ResalePricingId				BIGINT
	,@Data							dbo.[ContractResalePricingRuleImport] readonly
	,@UserId						BIGINT
AS
BEGIN
	DECLARE 
		@SP_NAME	NVARCHAR(150) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
		,@UTC_NOW	DATETIME = GETUTCDATE()
		
	set xact_abort on
	begin tran

		declare 
			@profitMargin float = 0,
			@isGlobal	  bit = 0
		declare @ResalePricingAssetIds dbo.bigint_ID_ARRAY	

		select 
			@profitMargin = isnull(c.ProfitMargin, 0),
			@isGlobal     = crp.IsGlobal
		from [dbo].[F_CONTRACT_RESALE_PRICING] crp with (nolock)
		left join [dbo].[F_CUSTOMER] c with (nolock)
			on c.CUSTOMER_ID = crp.CUSTOMER_ID
		where crp.CONTRACT_RESALE_PRICING_ID = @ResalePricingId

		update crp set
			[QUERY_FILTER] = d.QueryFilt<PERSON>,
		    [FAIR_MARKET] = isnull(d.[FairMarket], d.[ExpectedSellingPrice] - d.[ExpectedSellingPrice] * @profitMargin / 100),
			ExpectedSellingPrice = d.[ExpectedSellingPrice]
		output inserted.CONTRACT_RESALE_PRICING_OTHER_ASSET_ID INTO @ResalePricingAssetIds
		from @Data d	
		inner join dbo.F_CONTRACT_RESALE_PRICING_OTHER_ASSET crp with (rowlock)
			on d.[Description] = crp.[DESCRIPTION] and crp.ParentResalePricingRuleId is null
		where crp.[CONTRACT_RESALE_PRICING_ID] = @ResalePricingId

		insert into F_CONTRACT_RESALE_PRICING_OTHER_ASSET (
				[CONTRACT_RESALE_PRICING_ID]				
				,[QUERY_FILTER]
				,[DESCRIPTION]
				,[FAIR_MARKET]
				,ExpectedSellingPrice
				,[INSERTED_BY]
				,[INSERTED_DT]
			) 
		output inserted.CONTRACT_RESALE_PRICING_OTHER_ASSET_ID INTO @ResalePricingAssetIds
		select
			@ResalePricingId,
			d.[QueryFilterJson],
			d.[Description],
			isnull(d.[FairMarket], d.[ExpectedSellingPrice] - d.[ExpectedSellingPrice] * @profitMargin / 100),
			d.[ExpectedSellingPrice],			
			@SP_NAME,
			@UTC_NOW
		from @Data d		
		left join dbo.F_CONTRACT_RESALE_PRICING_OTHER_ASSET crp with (nolock)
			on crp.[CONTRACT_RESALE_PRICING_ID] = @ResalePricingId and d.[Description] = crp.[DESCRIPTION]				
		where crp.[CONTRACT_RESALE_PRICING_ID] is null

	commit tran

	if (@isGlobal = 1)
	begin
							 
		exec [dbo].[sp_SetResalePricingRulesByIds] @ResalePricingId, @ResalePricingAssetIds, @UserId

	end

	
END
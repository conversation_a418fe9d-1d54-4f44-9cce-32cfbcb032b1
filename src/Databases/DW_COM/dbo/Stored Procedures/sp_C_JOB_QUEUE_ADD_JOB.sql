-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
-- EXEC DW_COM.dbo.sp_C_JOB_QUEUE_ADD_JOB 'LITE', 'LOCAL', 'DW_GAMES', 1, 'sp_REBUILD_F_GAME_SPREAD', -2, '''LITE'', ''DEFAULT'', ''N'', ''N'', ''1900-01-01'', ''1900-01-01'', 0, 0, 0, 0', 0, 'processCd'
-- EXEC DW_COM.dbo.sp_C_JOB_QUEUE_ADD_JOB 'LITE', 'DEFAULT', 'DW_COM', 0, 'sp_C_BUILD_JOB_HANDLER', -2, '''LITE'', ''DEFAULT'', ''N'', ''N'', ''1900-01-01'', ''1900-01-01'', 0, 0, 0, 0', 0, 'ws_DW_GAMES.sql'
CREATE PROCEDURE [dbo].[sp_C_JOB_QUEUE_ADD_JOB]
	@JOB_QUEUE_BUILD_TYPE_CD			nvarchar(100) = 'DEFAULT'
	,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD	nvarchar(50) = 'DEFAULT'
	,@JOB_QUEUE_BUILD_DB				nvarchar(100) = 'DW_COM'
	,@JOB_QUEUE_BUILD_ID				bigint = 0
	,@JOB_QUEUE_BUILD_STEP_CD			nvarchar(250) = ''
	,@JOB_QUEUE_BUILD_STEP_STATUS		int = 0
	,@JOB_QUEUE_BUILD_STEP_ARGS			nvarchar(max) = ''
	,@IS_WAITING						bit = 0
	,@C_PROCESS_CD						nvarchar(150) = ''
	,@C_JOB_IS_ADDED_TO_QUEUE			bit = 0 OUTPUT
	,@JOB_QUEUE_PARENT_ID				bigint = NULL
	,@JOB_BUILD_ID						INT = NULL
	,@C_IS_DEBUG						BIT = 0
	--WITH RECOMPILE
AS
BEGIN

	SET XACT_ABORT ON

	DECLARE
		 @SP_NAME						nvarchar(128)		= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
		,@Who							nvarchar(100)		= @C_PROCESS_CD +'_sp_C_JOB_QUEUE_ADD_JOB'
		,@C_JOB_QUEUE_RUNNING_STATUS	int					= -1
		,@C_JOB_QUEUE_OPEN_STATUS		int					= -2
		,@BUILD_JOB_HANDLER				nvarchar(32)		= 'sp_C_BUILD_JOB_HANDLER'
		,@DW_COM_DB_NAME				nvarchar(50)		= 'DW_COM'
		,@JOB_QUEUE_GUID				uniqueidentifier	= NEWID()
		,@BUILD_DB						nvarchar(255)		= ISNULL(
			(SELECT TOP(1)
				BUILD_TYPE_DEFAULT_DATABASE FROM vw_C_BUILD_TYPE 
			WHERE BUILD_TYPE_CD = @JOB_QUEUE_BUILD_TYPE_CD)
			, @JOB_QUEUE_BUILD_DB)


	SET @C_JOB_IS_ADDED_TO_QUEUE		= 0 
	SET @IS_WAITING						= ISNULL(@IS_WAITING, 0)
	SET @JOB_QUEUE_BUILD_STEP_ARGS		= RTRIM(LTRIM(@JOB_QUEUE_BUILD_STEP_ARGS))
	SET @JOB_QUEUE_BUILD_ID				= IIF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER AND @JOB_QUEUE_BUILD_ID != 0, 0, @JOB_QUEUE_BUILD_ID)
	SET @JOB_QUEUE_BUILD_STEP_STATUS	= IIF(@JOB_QUEUE_BUILD_STEP_STATUS = 0, @C_JOB_QUEUE_OPEN_STATUS, @JOB_QUEUE_BUILD_STEP_STATUS)
	
	SET @JOB_BUILD_ID				= ISNULL(
		IIF(@JOB_BUILD_ID IS NULL OR @JOB_BUILD_ID = -1
			, (SELECT TOP(1)
					BUILD_ID
				FROM C_BUILD t WITH(NOLOCK) 
				WHERE (t.BUILD_STEP_DB = @BUILD_DB
				   OR (t.BUILD_STEP_DB  = @DW_COM_DB_NAME
				     AND @JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER))
				     AND t.BUILD_STEP_CD = @JOB_QUEUE_BUILD_STEP_CD
				     AND t.BUILD_TYPE_CD = @JOB_QUEUE_BUILD_TYPE_CD
				)
			, @JOB_BUILD_ID)
		, 0)

	DECLARE
		 @ERROR_NUMBER								INT					= 0
		,@ERROR_CD									nvarchar(max)		= ''
		,@ERROR_DESC								nvarchar(max)		= ''
		,@ERROR_FULL_DESC							nvarchar(max)		= ''
	

	
	-- VALIDATION: @JOB_QUEUE_BUILD_STEP_ARGS
	IF @JOB_QUEUE_BUILD_STEP_ARGS IS NULL AND CHARINDEX('Ext.', @JOB_QUEUE_BUILD_STEP_CD, 0) = 0
	BEGIN
		SET @ERROR_DESC = 'The arguments are not set for
			JOB_QUEUE_BUILD_TYPE_CD = '		+ @JOB_QUEUE_BUILD_TYPE_CD + ' 
			JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD + '
			JOB_QUEUE_BUILD_DB = '			+ @JOB_QUEUE_BUILD_DB + '
			JOB_QUEUE_BUILD_STEP_ARGS = '	+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL')

		BEGIN TRY
			EXEC dbo.[sp_C_MESSAGE] 
				@QUERY_TYPE			= 'SET'
				,@MESSAGE_TYPE_CD	= 'DB_BUILD_GENERAL_FAILURE'
				,@MESSAGE_CD		= NULL
				,@MESSAGE_DESC		= @ERROR_DESC
				,@MESSAGE_CMD		= 'VALIDATION: @JOB_QUEUE_BUILD_STEP_ARGS'
				,@C_BUILD_ID		= @JOB_QUEUE_BUILD_ID
				,@INSERTED_BY		= @Who
				,@IS_RETURN_RESULT	= 0
					
		END TRY
		BEGIN CATCH
			PRINT @SP_NAME + ': ' +ERROR_MESSAGE()
		END CATCH
		RAISERROR(@ERROR_DESC, 16,1)
		RETURN
	END

	-- VALIDATION: C_BUILD
	IF NOT (@JOB_QUEUE_BUILD_STEP_CD IN (
				@BUILD_JOB_HANDLER
				,'sp_C_SYS_DATABASE_BACKUP_ALL_MACHINE'
				,'sp_C_SYS_MAINTAINING_INDEXES_ALL_MACHINE'
			)
			AND EXISTS (SELECT 1 FROM C_BUILD WHERE (BUILD_TYPE_CD = @JOB_QUEUE_BUILD_TYPE_CD))
		)
		AND NOT EXISTS (SELECT 1 FROM dbo.fn_tbl_JOB_QUEUE_EXECUTE_PLAN(@JOB_QUEUE_BUILD_TYPE_CD) WHERE (BUILD_STEP_CD = @JOB_QUEUE_BUILD_STEP_CD))
	BEGIN

		SET @ERROR_DESC = 'Missing C_BUILD record for 
			JOB_QUEUE_BUILD_TYPE_CD = '		+ @JOB_QUEUE_BUILD_TYPE_CD + ' 
			JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD + '
			JOB_QUEUE_BUILD_DB = '			+ @JOB_QUEUE_BUILD_DB + '
			JOB_QUEUE_BUILD_STEP_ARGS = '	+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL')

		BEGIN TRY
			EXEC dbo.[sp_C_MESSAGE] 
				@QUERY_TYPE			= 'SET'
				,@MESSAGE_TYPE_CD	= 'DB_BUILD_GENERAL_FAILURE'
				,@MESSAGE_CD		= NULL
				,@MESSAGE_DESC		= @ERROR_DESC
				,@MESSAGE_CMD		= 'VALIDATION: C_BUILD'
				,@C_BUILD_ID		= @JOB_QUEUE_BUILD_ID
				,@INSERTED_BY		= @Who
				,@IS_RETURN_RESULT	= 0

		END TRY
		BEGIN CATCH
			PRINT @SP_NAME + ': ' +ERROR_MESSAGE()
		END CATCH

		RAISERROR(@ERROR_DESC, 16,1)
		RETURN
	END

	
	-- Assign BUILD parameters
	DECLARE
		@JOB_QUEUE_BUILD_STEP_ID					bigint				= 0
		,@JOB_QUEUE_BUILD_STEP_DESC					nvarchar(250)		= ''
		,@JOB_QUEUE_BUILD_STEP_TABLE_NAME			nvarchar(100)		= ''
		,@JOB_QUEUE_BUILD_STEP_CMD					nvarchar(250)		= ''
		,@C_BUILD_STEP_INTERVAL_DAYS				int					= 30
		,@C_BUILD_STEP_IS_WAITING_DEFAULT			int			= ISNULL(
			NULLIF((SELECT 1 where @JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER), @IS_WAITING)
			, 0)
		,@JOB_QUEUE_BUILD_PRIORITY					VARCHAR(13)	 = dbo.fn_str_GET_QUEUE_BUILD_PRIORITY(@JOB_QUEUE_BUILD_TYPE_CD, @JOB_QUEUE_BUILD_STEP_CD)
		,@JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD		nchar(1)	 = 'N'
		,@JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD	nchar(1)	 = 'N'
		,@JOB_QUEUE_BUILD_STEP_START_DATE			datetime	 = '1900-01-01' 
		,@JOB_QUEUE_BUILD_STEP_END_DATE				datetime	 = '2100-01-01'
		,@JOB_QUEUE_BUILD_IS_DIVIDE_BUILD			bit			 = 0
		,@BUILD_TYPE								varchar(100) = dbo.fn_str_GET_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @JOB_QUEUE_BUILD_DB,  'C_BUILD_TYPE_CD')
		
	IF @JOB_QUEUE_BUILD_STEP_ARGS IS NOT NULL
	BEGIN
		/*data use on from local table*/
		SET @JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD		= CONVERT(char(1),  dbo.fn_str_GET_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @JOB_QUEUE_BUILD_DB, 'C_IS_FULL_REBUILD'))
		SET @JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD	= CONVERT(char(1),  dbo.fn_str_GET_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @JOB_QUEUE_BUILD_DB, 'C_IS_PARTIAL_REBUILD'))
		SET @JOB_QUEUE_BUILD_STEP_START_DATE			= CONVERT(datetime, dbo.fn_str_GET_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @JOB_QUEUE_BUILD_DB, 'C_START_DATE'))
		SET @JOB_QUEUE_BUILD_STEP_END_DATE				= CONVERT(datetime, dbo.fn_str_GET_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @JOB_QUEUE_BUILD_DB, 'C_END_DATE'))
	END

	
	DECLARE @BUILD_STEP_ID_CHILD int = (
		SELECT TOP(1)
			BUILD_STEP_ID 
		FROM C_BUILD	WITH (NOLOCK)
		WHERE BUILD_TYPE_CD			= @JOB_QUEUE_BUILD_TYPE_CD 
		  AND BUILD_STEP_CD			= @JOB_QUEUE_BUILD_STEP_CD 
		  AND BUILD_TYPE_CHILD_CD	= @BUILD_TYPE
	) 

	SELECT 
		@JOB_QUEUE_BUILD_STEP_ID			= IIF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER, ISNULL(@BUILD_STEP_ID_CHILD, 0), vcb.BUILD_STEP_ID)
		,@JOB_QUEUE_BUILD_STEP_DESC			= IIF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER, 'Build Job Handler', vcb.BUILD_STEP_DESC)
		,@JOB_QUEUE_BUILD_STEP_TABLE_NAME	= IIF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER, '', vcb.BUILD_TABLE_NAME)
		,@JOB_QUEUE_BUILD_STEP_CMD			= @JOB_QUEUE_BUILD_STEP_CD + ' ' + @JOB_QUEUE_BUILD_STEP_ARGS
		,@JOB_QUEUE_BUILD_IS_DIVIDE_BUILD	= ISNULL(vcb.IS_DIVIDE_BUILD, 0)
		,@C_BUILD_STEP_INTERVAL_DAYS		= ISNULL(vcb.BUILD_STEP_INTERVAL_DAYS, 0)
		,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD  = IIF(@JOB_QUEUE_BUILD_LOCATION_TYPE_CD = 'DEFAULT', ISNULL(vcb.BUILD_LOCATION_TYPE_CD, 'DEFAULT'), @JOB_QUEUE_BUILD_LOCATION_TYPE_CD)
	FROM C_BUILD_TYPE vcbt WITH (NOLOCK)
	LEFT JOIN C_BUILD vcb  WITH (NOLOCK)
		ON vcbt.BUILD_TYPE_CD = vcb.BUILD_TYPE_CD
	WHERE vcb.BUILD_TYPE_CD = @JOB_QUEUE_BUILD_TYPE_CD 
	  AND (@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER
	    OR vcb.BUILD_STEP_CD = @JOB_QUEUE_BUILD_STEP_CD)
		

	DECLARE @MACHINE_CD VARCHAR(50) = 
		IIF(@JOB_QUEUE_BUILD_LOCATION_TYPE_CD NOT IN( 'LOCAL', 'DEFAULT', 'REMOTE')
		AND @JOB_QUEUE_BUILD_LOCATION_TYPE_CD IN (SELECT ISNULL(MACHINE_COM_CD, MACHINE_CD) FROM C_MACHINE)
			,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD
			,dbo.[fn_str_GET_MACHINE_CD_BY_ENVIRONMENT_DATABASE_MACHINE] (
				NULL
				,IIF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER
					,@DW_COM_DB_NAME
					,@JOB_QUEUE_BUILD_DB) 
				,CASE @JOB_QUEUE_BUILD_LOCATION_TYPE_CD
					WHEN 'LOCAL'   THEN 'MASTER_DB' 
					WHEN 'DEFAULT' THEN 'MASTER_DB'
					WHEN 'REMOTE'  THEN 'REMOTE_DB'
				END)
			)
	
	-- For dynamic sql
	DECLARE
		@C_MASTER_LINK_SERVER		nvarchar(50) = dbo.fn_str_GET_MACHINE_CD_BY_ENVIRONMENT_DATABASE_MACHINE(NULL, @DW_COM_DB_NAME, 'MASTER_DB')	

	DECLARE
		@C_SP_CMD			 nvarchar(250)  =  QUOTENAME(@C_MASTER_LINK_SERVER) + N'.'+@DW_COM_DB_NAME+N'.sys.sp_executesql'
		,@C_PARAM_DEFINITION nvarchar(4000) = '' --Is one string that contains the definitions of all parameters that have been embedded
		,@C_SQL_CMD			 nvarchar(max)  = '' 

	-- Insert build task
	IF (@JOB_QUEUE_BUILD_IS_DIVIDE_BUILD = 0)
	BEGIN 
		-- Try if such job is already executing in the queue
		/*Uses data from master server - F_JOB_QUEUE_ARG*/
		DECLARE 
			@IS_IN_SUCH_JOB_IN_QUEUE	bit = 0 --At now such job is not executing
			,@CHECKSUM_BUILD_ARG		int = dbo.fn_int_GET_CHECKSUM_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @BUILD_DB)	

		SET @C_PARAM_DEFINITION  = '
			@JOB_QUEUE_BUILD_TYPE_CD		varchar(50) 
			,@JOB_QUEUE_BUILD_STEP_CD		varchar(MAX)
			,@C_JOB_QUEUE_RUNNING_STATUS	int
			,@C_JOB_QUEUE_OPEN_STATUS		int
			,@CHECKSUM_BUILD_ARG			int
			,@IS_IN_SUCH_JOB_IN_QUEUE		bit OUTPUT
		'

		SET @C_SQL_CMD = '
			DECLARE @T_BUILD_TYPE_IS_MULTIPLE TABLE([BUILD_TYPE_CD] varchar(50))
			INSERT INTO @T_BUILD_TYPE_IS_MULTIPLE   
			SELECT
				[BUILD_TYPE_CD]
			FROM [C_BUILD_TYPE] with(nolock)
			WHERE [IS_QUEUEABLE] = 1

			SET @IS_IN_SUCH_JOB_IN_QUEUE = isnull((
					SELECT top(1) 1
					FROM (
						SELECT
							1 as yes
						FROM DW_COM.dbo.F_JOB_QUEUE WITH (NOLOCK)
						WHERE JOB_QUEUE_BUILD_TYPE_CD		= @JOB_QUEUE_BUILD_TYPE_CD 
						  AND JOB_QUEUE_STEP_CD				= @JOB_QUEUE_BUILD_STEP_CD
						  AND JOB_QUEUE_STEP_ARGS_CHECKSUM	= @CHECKSUM_BUILD_ARG
						  AND JOB_QUEUE_STEP_STATUS			= @C_JOB_QUEUE_RUNNING_STATUS -- running
						  AND JOB_QUEUE_BUILD_TYPE_CD NOT IN (SELECT [BUILD_TYPE_CD] FROM @T_BUILD_TYPE_IS_MULTIPLE)
						UNION all
						SELECT
							1 as yes
						FROM DW_COM.dbo.F_JOB_QUEUE WITH (NOLOCK)
						WHERE JOB_QUEUE_BUILD_TYPE_CD		= @JOB_QUEUE_BUILD_TYPE_CD 
						  AND JOB_QUEUE_STEP_CD				= @JOB_QUEUE_BUILD_STEP_CD
						  AND JOB_QUEUE_STEP_ARGS_CHECKSUM	= @CHECKSUM_BUILD_ARG
						  AND JOB_QUEUE_STEP_STATUS			= @C_JOB_QUEUE_OPEN_STATUS -- open
					) t
				)	
				, 0)
			'
			BEGIN TRY
				IF @C_IS_DEBUG = 1
					PRINT @SP_NAME + ': ' +'-- Executing: 
						DECLARE ' + @C_PARAM_DEFINITION + '
						set @JOB_QUEUE_BUILD_TYPE_CD	= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_TYPE_CD	) +'''
						set @JOB_QUEUE_BUILD_STEP_CD	= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_CD	) +'''
						set @C_JOB_QUEUE_RUNNING_STATUS	= '''+ convert(nvarchar(64), @C_JOB_QUEUE_RUNNING_STATUS) +'''
						set @C_JOB_QUEUE_OPEN_STATUS	= '''+ convert(nvarchar(64), @C_JOB_QUEUE_OPEN_STATUS 	) +'''
						set @CHECKSUM_BUILD_ARG			= '''+ convert(nvarchar(64), @CHECKSUM_BUILD_ARG		) +'''
						' + @C_SP_CMD + '
						' + @C_SQL_CMD + '
						----
						';


				EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
					,@JOB_QUEUE_BUILD_TYPE_CD		= @JOB_QUEUE_BUILD_TYPE_CD
					,@JOB_QUEUE_BUILD_STEP_CD		= @JOB_QUEUE_BUILD_STEP_CD
					,@C_JOB_QUEUE_RUNNING_STATUS	= @C_JOB_QUEUE_RUNNING_STATUS
					,@C_JOB_QUEUE_OPEN_STATUS		= @C_JOB_QUEUE_OPEN_STATUS
					,@CHECKSUM_BUILD_ARG			= @CHECKSUM_BUILD_ARG
					,@IS_IN_SUCH_JOB_IN_QUEUE		= @IS_IN_SUCH_JOB_IN_QUEUE OUTPUT

				SET @C_JOB_IS_ADDED_TO_QUEUE = ISNULL(@IS_IN_SUCH_JOB_IN_QUEUE, 0)
			END TRY
			BEGIN CATCH
				PRINT @SP_NAME + ': ' +ERROR_MESSAGE()
			END CATCH


			-- No such job in the queue
			IF @C_JOB_IS_ADDED_TO_QUEUE = 0
			BEGIN
				BEGIN TRY
					SET @C_SQL_CMD =
						'INSERT INTO dbo.F_JOB_QUEUE (
							[JOB_QUEUE_BUILD_ID]
							,[JOB_QUEUE_BUILD_TYPE_CD]
							,[JOB_QUEUE_BUILD_LOCATION_TYPE_CD]
							,[JOB_QUEUE_BUILD_DB]
							,[JOB_QUEUE_STEP_CD]
							,[JOB_QUEUE_STEP_STATUS]
							,[JOB_QUEUE_STEP_ARGS]
							,[JOB_QUEUE_STEP_DESC]
							,[JOB_QUEUE_STEP_TABLE_NAME]
							,[JOB_QUEUE_STEP_ARGS_CHECKSUM]
							,[MACHINE_CD]
							,[IS_WAITING]
							,[INSERTED_BY]
							,[JOB_QUEUE_GUID]
							,[JOB_QUEUE_PARENT_ID]
							,[JOB_BUILD_ID]
							,[JOB_QUEUE_BUILD_PRIORITY]
							,[JOB_QUEUE_BUILD_SESSION_ID]
							,[INSERTED_DT]
						) VALUES (
							@JOB_QUEUE_BUILD_ID
							,@JOB_QUEUE_BUILD_TYPE_CD
							,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD 
							,@JOB_QUEUE_BUILD_DB
							,@JOB_QUEUE_BUILD_STEP_CD
							,@JOB_QUEUE_BUILD_STEP_STATUS
							,@JOB_QUEUE_BUILD_STEP_ARGS 
							,@JOB_QUEUE_BUILD_STEP_DESC
							,@JOB_QUEUE_BUILD_STEP_TABLE_NAME
							,@JOB_QUEUE_STEP_ARGS_CHECKSUM
							,@MACHINE_CD
							,@C_BUILD_STEP_IS_WAITING_DEFAULT
							,@C_PROCESS_CD
							,@JOB_QUEUE_GUID
							,@JOB_QUEUE_PARENT_ID
							,@JOB_BUILD_ID
							,@JOB_QUEUE_BUILD_PRIORITY
							,0
							,GETDATE()
						)
					'
					
					SET @C_PARAM_DEFINITION  = '
						@JOB_QUEUE_BUILD_ID					bigint
						,@JOB_QUEUE_BUILD_TYPE_CD			varchar(50)
						,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD	varchar(50)
						,@JOB_QUEUE_BUILD_DB				nvarchar(100)
						,@JOB_QUEUE_BUILD_STEP_CD			nvarchar(250)
						,@JOB_QUEUE_BUILD_STEP_STATUS		int 
						,@JOB_QUEUE_BUILD_STEP_DESC			nvarchar(250)
						,@JOB_QUEUE_BUILD_STEP_TABLE_NAME	nvarchar(100)
						,@JOB_QUEUE_BUILD_STEP_ARGS			nvarchar(max)
						,@C_BUILD_STEP_IS_WAITING_DEFAULT	bit 
						,@C_PROCESS_CD						nvarchar(150)
						,@JOB_QUEUE_GUID					uniqueidentifier
						,@JOB_QUEUE_PARENT_ID				bigint
						,@JOB_BUILD_ID						bigint
						,@JOB_QUEUE_BUILD_PRIORITY			VARCHAR(13)
						,@JOB_QUEUE_STEP_ARGS_CHECKSUM		INT
						,@MACHINE_CD						VARCHAR(50)
					'
					SET @JOB_QUEUE_BUILD_STEP_ARGS = REPLACE(@JOB_QUEUE_BUILD_STEP_ARGS, '''', '''')
					
					
					IF @C_IS_DEBUG = 1
						PRINT @SP_NAME + ': ' +'-- Executing: 
							DECLARE ' + @C_PARAM_DEFINITION + '
							set @JOB_QUEUE_BUILD_ID					= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_ID								) +'''
							set @JOB_QUEUE_BUILD_LOCATION_TYPE_CD	= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_LOCATION_TYPE_CD					) +'''
							set @JOB_QUEUE_BUILD_DB					= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_DB								) +'''
							set @JOB_QUEUE_BUILD_STEP_CD			= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_CD 							) +'''
							set @JOB_QUEUE_BUILD_STEP_STATUS		= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_STATUS						) +'''
							set @JOB_QUEUE_BUILD_STEP_DESC			= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_DESC							) +'''
							set @JOB_QUEUE_BUILD_STEP_TABLE_NAME	= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_TABLE_NAME					) +'''
							set @JOB_QUEUE_BUILD_STEP_ARGS			= '''+ replace(convert(nvarchar(max), @JOB_QUEUE_BUILD_STEP_ARGS), '''', ''''''	) +'''
							set @C_BUILD_STEP_IS_WAITING_DEFAULT	= '''+ convert(nvarchar(64), @C_BUILD_STEP_IS_WAITING_DEFAULT					) +'''
							set @C_PROCESS_CD						= '''+ convert(nvarchar(64), @C_PROCESS_CD										) +'''
							set @JOB_QUEUE_GUID						= '''+ convert(nvarchar(64), @JOB_QUEUE_GUID									) +'''
							set @JOB_QUEUE_PARENT_ID				= '''+ convert(nvarchar(64), @JOB_QUEUE_PARENT_ID								) +'''
							set @JOB_BUILD_ID						= '''+ convert(nvarchar(64), @JOB_BUILD_ID										) +'''
							set @JOB_QUEUE_BUILD_PRIORITY			= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_PRIORITY							) +'''
							set @JOB_QUEUE_STEP_ARGS_CHECKSUM		= '''+ convert(nvarchar(64), @CHECKSUM_BUILD_ARG								) +'''
							set @MACHINE_CD							= '''+ convert(nvarchar(64), @MACHINE_CD										) +'''
							' + @C_SP_CMD + '
							' + @C_SQL_CMD + '
							----
							'

					EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
						,@JOB_QUEUE_BUILD_ID				= @JOB_QUEUE_BUILD_ID 
						,@JOB_QUEUE_BUILD_TYPE_CD			= @JOB_QUEUE_BUILD_TYPE_CD
						,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD	= @JOB_QUEUE_BUILD_LOCATION_TYPE_CD
						,@JOB_QUEUE_BUILD_DB				= @JOB_QUEUE_BUILD_DB
						,@JOB_QUEUE_BUILD_STEP_CD			= @JOB_QUEUE_BUILD_STEP_CD
						,@JOB_QUEUE_BUILD_STEP_STATUS		= @JOB_QUEUE_BUILD_STEP_STATUS
						,@JOB_QUEUE_BUILD_STEP_DESC			= @JOB_QUEUE_BUILD_STEP_DESC
						,@JOB_QUEUE_BUILD_STEP_TABLE_NAME	= @JOB_QUEUE_BUILD_STEP_TABLE_NAME 
						,@JOB_QUEUE_BUILD_STEP_ARGS			= @JOB_QUEUE_BUILD_STEP_ARGS  
						,@C_BUILD_STEP_IS_WAITING_DEFAULT	= @C_BUILD_STEP_IS_WAITING_DEFAULT
						,@C_PROCESS_CD						= @C_PROCESS_CD
						,@JOB_QUEUE_GUID					= @JOB_QUEUE_GUID
						,@JOB_QUEUE_PARENT_ID				= @JOB_QUEUE_PARENT_ID
						,@JOB_BUILD_ID						= @JOB_BUILD_ID
						,@JOB_QUEUE_BUILD_PRIORITY			= @JOB_QUEUE_BUILD_PRIORITY
						,@JOB_QUEUE_STEP_ARGS_CHECKSUM		= @CHECKSUM_BUILD_ARG
						,@MACHINE_CD						= @MACHINE_CD
					
					SET @C_JOB_IS_ADDED_TO_QUEUE = 1

				END TRY
				BEGIN CATCH
					SET @ERROR_NUMBER	 = ERROR_NUMBER()
					SET @ERROR_CD		 = CONVERT(varchar(20), @ERROR_NUMBER)
					set @ERROR_DESC		 = ERROR_MESSAGE()
					SET @ERROR_FULL_DESC = 
						'ERROR_NUMBER() = '					+ CONVERT(varchar(20), @ERROR_NUMBER) + 
						'; BUILD_SESSION_ID = '				+ CONVERT(varchar(20), 0) + 
						'; ERROR_MESSAGE() = '				+ @ERROR_DESC + 
						'; BUILD_ID = '						+ CONVERT(varchar(20), @JOB_QUEUE_BUILD_ID) +
						'; JOB_QUEUE_BUILD_TYPE_CD = '		+ @JOB_QUEUE_BUILD_TYPE_CD + 
						'; JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD + 
						'; JOB_QUEUE_BUILD_DB = '			+ @JOB_QUEUE_BUILD_DB + 
						'; JOB_QUEUE_BUILD_STEP_ARGS = '	+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL') +
						'; Insert values into vw_C_JOB_QUEUE from varibles'

					SET @C_PARAM_DEFINITION  = '
						@MESSAGE_CD		nvarchar(MAX)
						,@MESSAGE_DESC	varchar(MAX) 
						,@MESSAGE_CMD	varchar(MAX)
						,@C_BUILD_ID	bigint 
						,@INSERTED_BY	varchar(150)
					'

					SET @C_SQL_CMD = ' dbo.[sp_C_MESSAGE] 
						@QUERY_TYPE			= ''SET''
						,@MESSAGE_TYPE_CD	= ''DB_BUILD_GENERAL_FAILURE''
						,@MESSAGE_CD		= @MESSAGE_CD
						,@MESSAGE_DESC		= @MESSAGE_DESC
						,@MESSAGE_CMD		= @MESSAGE_CMD
						,@C_BUILD_ID		= @C_BUILD_ID
						,@INSERTED_BY		= @INSERTED_BY
					'

					BEGIN TRY

						--IF @C_IS_DEBUG = 1
						--	PRINT @SP_NAME + ': ' +'-- Executing: 
						--		' + @C_PARAM_DEFINITION + '
						--		' + @C_SP_CMD + '
						--		' + @C_SQL_CMD + '
						--		----
						--		'

						EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
							,@MESSAGE_CD	= @ERROR_CD
							,@MESSAGE_DESC	= @ERROR_FULL_DESC
							,@MESSAGE_CMD	= 'Insert into F_JOB_QUEUE'
							,@C_BUILD_ID	= @JOB_QUEUE_BUILD_ID
							,@INSERTED_BY	= @C_PROCESS_CD
					END TRY
					BEGIN CATCH
						PRINT @SP_NAME + ': ' +ERROR_MESSAGE() 
					END CATCH
				RAISERROR(@ERROR_FULL_DESC, 16, 1)
				RETURN

			END CATCH
		END		
	END

	/*We should change this code to use sp_C_JOB_SHREDDER SP
	ELSE 
	BEGIN
		BEGIN TRY	
				
		DECLARE @C_JOB_QUEUE_STEP_START_DT datetime = @JOB_QUEUE_BUILD_STEP_START_DATE
		DECLARE @C_JOB_QUEUE_STEP_END_DT datetime = DATEADD(d, @C_BUILD_STEP_INTERVAL_DAYS, @JOB_QUEUE_BUILD_STEP_START_DATE)
		DECLARE @C_JOB_SHREDDER_ITERATION_CNT int = 0
		-- DROP TABLE [dbo].[#temp_C_JOB_QUEUE]
		CREATE TABLE [dbo].[#temp_C_JOB_QUEUE] (
			[JOB_QUEUE_BUILD_ID] [bigint] NULL,
			[JOB_QUEUE_BUILD_TYPE_CD] [varchar](50) NULL,
			[JOB_QUEUE_BUILD_LOCATION_TYPE_CD] [varchar](50) NOT NULL,
			[JOB_QUEUE_BUILD_DB] [varchar](50) NOT NULL,
			[JOB_QUEUE_STEP_CD] [varchar](100) NOT NULL,
			[JOB_QUEUE_STEP_STATUS] [int] NOT NULL,
			[JOB_QUEUE_STEP_ARGS] [varchar](max) NOT NULL,
			[JOB_QUEUE_STEP_DESC] [varchar](250) NULL,
			[JOB_QUEUE_STEP_TABLE_NAME] [varchar](100) NULL,
			[IS_WAITING] [bit] NOT NULL,
			[INSERTED_BY] [varchar](150) NOT NULL,
			[INSERTED_DT] [datetime] NOT NULL,
			[JOB_QUEUE_GUID] uniqueidentifier NULL,
			[JOB_QUEUE_PARENT_ID] BIGINT NULL,
			[JOB_BUILD_ID] INT NULL,
			JOB_QUEUE_BUILD_PRIORITY VARCHAR(13) NULL,
			JOB_QUEUE_BUILD_SESSION_ID INT,
		)	
				
		--WHILE (@JOB_QUEUE_STEP_START_DT <= '2002-08-01')
		WHILE (@C_JOB_QUEUE_STEP_START_DT < @JOB_QUEUE_BUILD_STEP_END_DATE OR @C_JOB_SHREDDER_ITERATION_CNT = 0)
		BEGIN
			IF (@C_JOB_QUEUE_STEP_END_DT > @JOB_QUEUE_BUILD_STEP_END_DATE)
			BEGIN
				SET @C_JOB_QUEUE_STEP_END_DT = @JOB_QUEUE_BUILD_STEP_END_DATE
			END
			--ELSE SET @C_JOB_QUEUE_STEP_END_DT = DATEADD(ms, -1, @C_END_DATE)
					
			IF NOT EXISTS (
				SELECT 1 
					FROM 
						F_JOB_QUEUE WITH(NOLOCK)
					WHERE 
						JOB_QUEUE_BUILD_TYPE_CD = @JOB_QUEUE_BUILD_TYPE_CD
						AND JOB_QUEUE_STEP_CD = @JOB_QUEUE_BUILD_STEP_CD
						AND JOB_QUEUE_BUILD_TYPE_CD NOT IN (SELECT [BUILD_TYPE_CD] FROM @T_BUILD_TYPE_IS_MULTIPLE)
						AND JOB_QUEUE_STEP_STATUS IN (@C_JOB_QUEUE_RUNNING_STATUS, @C_JOB_QUEUE_OPEN_STATUS)
						AND [JOB_QUEUE_STEP_ARGS_CHECKSUM] = @CHECKSUM_BUILD_ARG
						--AND dbo.fn_int_GET_CHECKSUM_BUILD_ARG(JOB_QUEUE_STEP_ARGS, @BUILD_DB) = dbo.fn_int_GET_CHECKSUM_BUILD_ARG(@JOB_QUEUE_BUILD_STEP_ARGS, @BUILD_DB)
			)
			BEGIN
			
				INSERT INTO [dbo].[#temp_C_JOB_QUEUE] 
				SELECT
					@JOB_QUEUE_BUILD_ID
					,@JOB_QUEUE_BUILD_TYPE_CD
					,@JOB_QUEUE_BUILD_LOCATION_TYPE_CD
					,@JOB_QUEUE_BUILD_DB
					,@JOB_QUEUE_BUILD_STEP_CD
					,@C_JOB_QUEUE_OPEN_STATUS
					,@JOB_QUEUE_BUILD_STEP_ARGS
					,@JOB_QUEUE_BUILD_STEP_DESC
					,@JOB_QUEUE_BUILD_STEP_TABLE_NAME
					,@C_BUILD_STEP_IS_WAITING_DEFAULT
					,@C_PROCESS_CD
					,GETDATE() as INSERTED_DT
					,@JOB_QUEUE_GUID
					,@JOB_QUEUE_PARENT_ID
					,@JOB_BUILD_ID
					,@JOB_QUEUE_BUILD_PRIORITY
					,0 as JOB_QUEUE_BUILD_SESSION_ID
							
															
				SET @C_JOB_QUEUE_STEP_START_DT = DATEADD(d, 0, @C_JOB_QUEUE_STEP_END_DT)
				SET @C_JOB_QUEUE_STEP_END_DT = DATEADD(d, @C_BUILD_STEP_INTERVAL_DAYS, @C_JOB_QUEUE_STEP_START_DT)
				SET @C_JOB_SHREDDER_ITERATION_CNT = @C_JOB_SHREDDER_ITERATION_CNT + 1
			END


		END
				
		INSERT INTO dbo.F_JOB_QUEUE
			(
				[JOB_QUEUE_BUILD_ID]
				,[JOB_QUEUE_BUILD_TYPE_CD]
				,[JOB_QUEUE_BUILD_LOCATION_TYPE_CD]
				,[JOB_QUEUE_BUILD_DB]
				,[JOB_QUEUE_STEP_CD]
				,[JOB_QUEUE_STEP_STATUS]
				,[JOB_QUEUE_STEP_ARGS]
				,[JOB_QUEUE_STEP_DESC]
				,[JOB_QUEUE_STEP_TABLE_NAME]
				,[IS_WAITING]
				,[INSERTED_BY]
				,[INSERTED_DT]
				,[JOB_QUEUE_GUID]
				,[JOB_QUEUE_PARENT_ID]
				,[JOB_BUILD_ID]
				,[JOB_QUEUE_BUILD_PRIORITY]
			)
		SELECT * FROM [dbo].[#temp_C_JOB_QUEUE]

		SET @C_JOB_IS_ADDED_TO_QUEUE = 1
				
				
				
		IF OBJECT_ID('tempdb..#temp_C_JOB_QUEUE') IS NOT NULL
			DROP TABLE dbo.#temp_C_JOB_QUEUE
		
		END TRY 
		BEGIN CATCH 
			SET @ERROR_NUMBER = ERROR_NUMBER()
			SET @ERROR_CD = CONVERT(varchar(20), @ERROR_NUMBER)
			set @ERROR_DESC = ERROR_MESSAGE()
			SET @ERROR_FULL_DESC = 
								'ERROR_NUMBER() = ' + CONVERT(varchar(20), @ERROR_NUMBER) + 
								'; BUILD_SESSION_ID = ' + CONVERT(varchar(20), 0) + 
								'; ERROR_MESSAGE() = ' + @ERROR_DESC + 
								'; BUILD_ID = ' + CONVERT(varchar(20), @JOB_QUEUE_BUILD_ID) +
								'; JOB_QUEUE_BUILD_TYPE_CD = ' + @JOB_QUEUE_BUILD_TYPE_CD + 
								'; JOB_QUEUE_BUILD_STEP_CD = ' + @JOB_QUEUE_BUILD_STEP_CD + 
								'; JOB_QUEUE_BUILD_DB = ' + @JOB_QUEUE_BUILD_DB + 
								'; JOB_QUEUE_BUILD_STEP_ARGS = ' + ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL') +
								'; Insert values into vw_C_JOB_QUEUE from #temp_C_JOB_QUEUE'

			EXEC [sp_C_MESSAGE] @QUERY_TYPE = 'SET'
								, @MESSAGE_TYPE_CD = 'DB_BUILD_GENERAL_FAILURE'
								, @MESSAGE_ID = 0
								, @MESSAGE_CD = @ERROR_CD
								, @MESSAGE_DESC = @ERROR_FULL_DESC
								, @MESSAGE_CMD = 'Insert values into vw_C_JOB_QUEUE from #temp_C_JOB_QUEUE'
								, @C_BUILD_ID = @JOB_QUEUE_BUILD_ID
								, @IS_INACTIVE = 0
								, @INSERTED_BY = @C_PROCESS_CD
								, @IS_RETURN_RESULT = 0
		END CATCH

				
	END
	*/

	/*Uses data from linked server*/

	-- Insert build arguments
	if (@C_IS_DEBUG = 1)
		select
			 @SP_NAME					as [SP]
			,@C_JOB_IS_ADDED_TO_QUEUE	as [@C_JOB_IS_ADDED_TO_QUEUE]
			,@JOB_QUEUE_BUILD_STEP_CD	as [@JOB_QUEUE_BUILD_STEP_CD] 
			,@BUILD_JOB_HANDLER			as [@BUILD_JOB_HANDLER]

	IF(@C_JOB_IS_ADDED_TO_QUEUE = 1 AND @JOB_QUEUE_BUILD_STEP_CD  = @BUILD_JOB_HANDLER)
	BEGIN
		BEGIN TRY
			
			DECLARE @delimiter char(1)  = ','

			SET @C_PARAM_DEFINITION  = '
				@JOB_QUEUE_GUID				uniqueidentifier
				,@delimiter					char(1) 
				,@JOB_QUEUE_BUILD_TYPE_CD	nvarchar(150)
				,@JOB_QUEUE_BUILD_STEP_ARGS nvarchar(MAX)
				,@C_PROCESS_CD				nvarchar(150) 
				,@JOB_QUEUE_BUILD_ID		bigint
				,@BUILD_DB					nvarchar(255)
			'

			SET @C_SQL_CMD ='
				CREATE TABLE #t_C_JOB_QUEUEID (JOB_QUEUE_ID bigint primary key)	-- DROP TABLE #t_C_JOB_QUEUEID
				INSERT INTO #t_C_JOB_QUEUEID
				SELECT distinct
					JOB_QUEUE_ID
				FROM F_JOB_QUEUE	jq WITH(NOLOCK)
				WHERE JOB_QUEUE_GUID = @JOB_QUEUE_GUID
				  AND NOT EXISTS(
					SELECT TOP(1)
						1
					FROM #t_C_JOB_QUEUEID t
					where t.JOB_QUEUE_ID = jq.JOB_QUEUE_ID
				)
				
				IF EXISTS (SELECT TOP(1) 1 FROM #t_C_JOB_QUEUEID)
				BEGIN
					
					DECLARE @tJOB_QUEUE_BUILD_STEP_ARGS TABLE (
						listpos bigint primary key
						,nstr	nvarchar(max)
					)

					IF NULLIF(@JOB_QUEUE_BUILD_STEP_ARGS, '''') IS NOT NULL
					BEGIN
						INSERT INTO @tJOB_QUEUE_BUILD_STEP_ARGS(listpos, nstr)
						SELECT 
							listpos
							,REPLACE(nstr, '''''''', '''')  
						FROM dbo.[iter_charlist_to_table] (@JOB_QUEUE_BUILD_STEP_ARGS, @delimiter)
					END
					ELSE
					BEGIN
						INSERT INTO @tJOB_QUEUE_BUILD_STEP_ARGS (
							listpos
							,nstr
						)
						SELECT
							BUILD_ARG_PARAM_ORDER_BY
							,CASE BUILD_ARG_PARAM_NAME
								WHEN ''C_BUILD_TYPE_CD'' THEN @JOB_QUEUE_BUILD_TYPE_CD 
								WHEN ''C_START_DATE''	 THEN ''1900-01-01''
								WHEN ''C_END_DATE''		 THEN ''2100-01-01''
								ELSE BUILD_ARG_PARAM_DEFAULT_VALUE
							END
						FROM dbo.vw_C_BUILD_ARG_LIST WITH(NOLOCK)
						WHERE BUILD_DATABASE_NAME = @BUILD_DB
					END

					INSERT INTO F_JOB_QUEUE_ARG (
						[JOB_QUEUE_BUILD_ID], 
						[JOB_QUEUE_ID], 
						[BUILD_ARG_ID], 
						[BUILD_ARG_PARAM_NAME], 
						[BUILD_ARG_PARAM_DATA_TYPE], 
						[BUILD_ARG_PARAM_VALUE], 
						[BUILD_ARG_PARAM_ORDER_BY],
						INSERTED_BY,
						INSERTED_DT
					)
					SELECT
						[JOB_QUEUE_BUILD_ID], 
						[JOB_QUEUE_ID], 
						[BUILD_ARG_ID], 
						[BUILD_ARG_PARAM_NAME], 
						[BUILD_ARG_PARAM_DATA_TYPE], 
						[BUILD_ARG_PARAM_VALUE], 
						[BUILD_ARG_PARAM_ORDER_BY],
						@C_PROCESS_CD,
						GETDATE()
					FROM (
						SELECT distinct
							@JOB_QUEUE_BUILD_ID			as JOB_QUEUE_BUILD_ID, 
							t.JOB_QUEUE_ID				as JOB_QUEUE_ID,  
							cba.BUILD_ARG_ID			as BUILD_ARG_ID,
							BUILD_ARG_PARAM_NAME		as BUILD_ARG_PARAM_NAME,
							BUILD_ARG_PARAM_DATA_TYPE	as BUILD_ARG_PARAM_DATA_TYPE,
							sa.nstr						as BUILD_ARG_PARAM_VALUE,
							BUILD_ARG_PARAM_ORDER_BY	as BUILD_ARG_PARAM_ORDER_BY
						FROM @tJOB_QUEUE_BUILD_STEP_ARGS	sa
						INNER JOIN vw_C_BUILD_ARG_LIST		cba WITH (NOLOCK)
							ON sa.listpos = cba.BUILD_ARG_PARAM_ORDER_BY
							AND cba.BUILD_DATABASE_NAME = @BUILD_DB
							AND cba.IS_INACTIVE = 0
							AND cba.IS_DELETED  = 0
						CROSS JOIN #t_C_JOB_QUEUEID			t
					) S
					WHERE NOT EXISTS(
						SELECT TOP(1) 1
						FROM F_JOB_QUEUE_ARG fjqa WITH(NOLOCK) 
						WHERE fjqa.JOB_QUEUE_ID = s.JOB_QUEUE_ID
						  AND fjqa.BUILD_ARG_ID = s.BUILD_ARG_ID
					)
				END
			'
			
			IF @C_IS_DEBUG = 1
				PRINT @SP_NAME + ': ' +'-- Executing: 
					DECLARE ' + @C_PARAM_DEFINITION + '
					set @JOB_QUEUE_GUID				= '''+ convert(nvarchar(64), @JOB_QUEUE_GUID 									) +'''
					set @delimiter					= '''+ convert(nvarchar(64), @delimiter											) +'''
					set @JOB_QUEUE_BUILD_TYPE_CD	= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_TYPE_CD							) +'''
					set @JOB_QUEUE_BUILD_STEP_ARGS	= '''+ replace(convert(nvarchar(max), @JOB_QUEUE_BUILD_STEP_ARGS), '''', ''''''	) +'''
					set @C_PROCESS_CD				= '''+ convert(nvarchar(64), @C_PROCESS_CD										) +'''
					set @JOB_QUEUE_BUILD_ID			= '  + convert(nvarchar(64), @JOB_QUEUE_BUILD_ID								) +'
					set @BUILD_DB					= '''+ convert(nvarchar(64), @BUILD_DB											) +'''
					' + @C_SP_CMD + '
					' + @C_SQL_CMD + '
					----
					'

			EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
				,@JOB_QUEUE_GUID			= @JOB_QUEUE_GUID 
				,@delimiter					= @delimiter
				,@JOB_QUEUE_BUILD_TYPE_CD	= @JOB_QUEUE_BUILD_TYPE_CD
				,@JOB_QUEUE_BUILD_STEP_ARGS = @JOB_QUEUE_BUILD_STEP_ARGS
				,@C_PROCESS_CD				= @C_PROCESS_CD
				,@JOB_QUEUE_BUILD_ID		= @JOB_QUEUE_BUILD_ID
				,@BUILD_DB					= @BUILD_DB

		END TRY
		BEGIN CATCH
			SET @ERROR_NUMBER		= ERROR_NUMBER()
			SET @ERROR_CD			= CONVERT(varchar(20), @ERROR_NUMBER)
			set @ERROR_DESC			= ERROR_MESSAGE()
			SET @ERROR_FULL_DESC	= 
				'ERROR_NUMBER() = '					+ @ERROR_CD + 
				'; BUILD_SESSION_ID = '				+ CONVERT(varchar(20), 0) + 
				'; ERROR_MESSAGE() = '				+ @ERROR_DESC + 
				'; BUILD_ID = '						+ CONVERT(varchar(20), @JOB_QUEUE_BUILD_ID) +
				'; JOB_QUEUE_BUILD_TYPE_CD = '		+ @JOB_QUEUE_BUILD_TYPE_CD + 
				'; JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD + 
				'; JOB_QUEUE_BUILD_DB = '			+ @JOB_QUEUE_BUILD_DB + 
				'; JOB_QUEUE_BUILD_STEP_ARGS = '	+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL') +
				'; JOB_QUEUE_GUID = '				+ CAST(@JOB_QUEUE_GUID as varchar(36)) + 
				'; C_JOB_IS_ADDED_TO_QUEUE = '		+ cast(@C_JOB_IS_ADDED_TO_QUEUE as varchar(10)) + 
				'; Insert values into F_JOB_QUEUE_ARG'

				
			BEGIN TRY
				EXEC dbo.[sp_C_MESSAGE] 
					@QUERY_TYPE			= 'SET'
					, @MESSAGE_TYPE_CD	= 'DB_BUILD_GENERAL_FAILURE'
					, @MESSAGE_CD		= @ERROR_CD
					, @MESSAGE_DESC		= @ERROR_FULL_DESC
					, @MESSAGE_CMD		= 'Add value arg in F_JOB_QUEUE_ARG'
					, @C_BUILD_ID		= @JOB_QUEUE_BUILD_ID
					, @INSERTED_BY		= @C_PROCESS_CD
					, @IS_RETURN_RESULT = 0
			END TRY
			BEGIN CATCH
				PRINT @SP_NAME + ': ' +ERROR_MESSAGE() 
			END CATCH
			 
		END CATCH
	END
		
	-- Insert build audit record
	IF (@C_JOB_IS_ADDED_TO_QUEUE = 1 AND @JOB_QUEUE_BUILD_STEP_CD != @BUILD_JOB_HANDLER)
	BEGIN
		BEGIN TRY
					
			SET @C_PARAM_DEFINITION  = '
				@JOB_QUEUE_BUILD_ID							bigint
				,@JOB_QUEUE_BUILD_STEP_ID					int
				,@JOB_QUEUE_BUILD_DB						nvarchar(255)
				,@JOB_QUEUE_BUILD_STEP_CD					nvarchar(200) 
				,@JOB_QUEUE_BUILD_STEP_TABLE_NAME			nvarchar(200)
				,@JOB_QUEUE_BUILD_STEP_CMD					nvarchar(250)
				,@JOB_QUEUE_BUILD_STEP_STATUS				int 
				,@JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD		nchar(1)
				,@JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD	nchar(1)
				,@JOB_QUEUE_BUILD_STEP_START_DATE			datetime
				,@JOB_QUEUE_BUILD_STEP_END_DATE				datetime
				,@C_PROCESS_CD								varchar(150)
			'
					
			SET @C_SQL_CMD = ' dbo.[sp_C_BUILD_AUDIT]
				@AUDIT_TYPE_CD				= ''INSERTED''
				,@BUILD_AUDIT_ID			= 0
				,@BUILD_ID					= @JOB_QUEUE_BUILD_ID
				,@BUILD_STEP_ID				= @JOB_QUEUE_BUILD_STEP_ID
				,@BUILD_STEP_DB				= @JOB_QUEUE_BUILD_DB
				,@BUILD_STEP_CD				= @JOB_QUEUE_BUILD_STEP_CD
				,@BUILD_TABLE_NAME			= @JOB_QUEUE_BUILD_STEP_TABLE_NAME
				,@BUILD_STEP_CMD			= @JOB_QUEUE_BUILD_STEP_CMD
				,@BUILD_STATUS				= @JOB_QUEUE_BUILD_STEP_STATUS
				,@BUILD_IS_FULL_REBUILD		= @JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD
				,@BUILD_IS_PARTIAL_REBUILD	= @JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD
				,@BUILD_START_DT			= @JOB_QUEUE_BUILD_STEP_START_DATE
				,@BUILD_END_DT				= @JOB_QUEUE_BUILD_STEP_END_DATE
				,@C_PROCESS_CD				= @C_PROCESS_CD'	
			
			IF @C_IS_DEBUG = 1
				PRINT @SP_NAME + ': ' +'-- Executing: 
					DECLARE ' + @C_PARAM_DEFINITION + '
					set @JOB_QUEUE_BUILD_ID							= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_ID												) +'''
					set @JOB_QUEUE_BUILD_STEP_ID					= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_ID											) +'''
					set @JOB_QUEUE_BUILD_DB							= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_DB												) +'''
					set @JOB_QUEUE_BUILD_STEP_CD					= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_CD 											) +'''
					set @JOB_QUEUE_BUILD_STEP_TABLE_NAME			= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_TABLE_NAME									) +'''
					set @JOB_QUEUE_BUILD_STEP_CMD					= '''+ replace(convert(nvarchar(max), @JOB_QUEUE_BUILD_STEP_CMD), '''', ''''''					) +'''
					set @JOB_QUEUE_BUILD_STEP_STATUS				= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_STATUS										) +'''
					set @JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD		= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD								) +'''
					set @JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD	= '''+ replace(convert(nvarchar(max), @JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD), '''', ''''''	) +'''
					set @JOB_QUEUE_BUILD_STEP_START_DATE			= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_START_DATE									) +'''
					set @JOB_QUEUE_BUILD_STEP_END_DATE				= '''+ convert(nvarchar(64), @JOB_QUEUE_BUILD_STEP_END_DATE										) +'''
					set @C_PROCESS_CD								= '''+ convert(nvarchar(64), @C_PROCESS_CD														) +'''
					' + @C_SP_CMD + '
					' + @C_SQL_CMD + '
					----
					'

			EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
				,@JOB_QUEUE_BUILD_ID						= @JOB_QUEUE_BUILD_ID
				,@JOB_QUEUE_BUILD_STEP_ID					= @JOB_QUEUE_BUILD_STEP_ID
				,@JOB_QUEUE_BUILD_DB						= @JOB_QUEUE_BUILD_DB
				,@JOB_QUEUE_BUILD_STEP_CD					= @JOB_QUEUE_BUILD_STEP_CD
				,@JOB_QUEUE_BUILD_STEP_TABLE_NAME			= @JOB_QUEUE_BUILD_STEP_TABLE_NAME
				,@JOB_QUEUE_BUILD_STEP_CMD					= @JOB_QUEUE_BUILD_STEP_CMD
				,@JOB_QUEUE_BUILD_STEP_STATUS				= @JOB_QUEUE_BUILD_STEP_STATUS
				,@JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD		= @JOB_QUEUE_BUILD_STEP_IS_FULL_REBUILD
				,@JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD	= @JOB_QUEUE_BUILD_STEP_IS_PARTIAL_REBUILD
				,@JOB_QUEUE_BUILD_STEP_START_DATE			= @JOB_QUEUE_BUILD_STEP_START_DATE
				,@JOB_QUEUE_BUILD_STEP_END_DATE				= @JOB_QUEUE_BUILD_STEP_END_DATE
				,@C_PROCESS_CD								= @C_PROCESS_CD

		END TRY
		BEGIN CATCH
			SET @ERROR_NUMBER		= ERROR_NUMBER()
			SET @ERROR_CD			= CONVERT(varchar(20), @ERROR_NUMBER)
			set @ERROR_DESC			= ERROR_MESSAGE()
			SET @ERROR_FULL_DESC	= 
				'ERROR_NUMBER() = '					+ @ERROR_CD + 
				'; BUILD_SESSION_ID = '				+ CONVERT(varchar(20), 0) + 
				'; ERROR_MESSAGE() = '				+ @ERROR_DESC + 
				'; BUILD_ID = '						+ CONVERT(varchar(20), @JOB_QUEUE_BUILD_ID) +
				'; JOB_QUEUE_BUILD_TYPE_CD = '		+ @JOB_QUEUE_BUILD_TYPE_CD + 
				'; JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD + 
				'; JOB_QUEUE_BUILD_DB = '			+ @JOB_QUEUE_BUILD_DB + 
				'; JOB_QUEUE_BUILD_STEP_CD = '		+ @JOB_QUEUE_BUILD_STEP_CD +
				'; JOB_QUEUE_BUILD_STEP_ARGS = '	+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL') +
				'; EXEC sp_C_BUILD_AUDIT from sp_C_JOB_QUEUE_ADD_JOB'
			
			BEGIN TRY
				EXEC dbo.[sp_C_MESSAGE] 
					@QUERY_TYPE			='SET'
					,@MESSAGE_TYPE_CD	= 'DB_BUILD_AUDIT_WARNING'
					,@MESSAGE_CD		= @ERROR_CD
					,@MESSAGE_DESC		= @ERROR_FULL_DESC
					,@MESSAGE_CMD		= 'EXEC  sp_C_BUILD_AUDIT from sp_C_JOB_QUEUE_ADD_JOB'
					,@C_BUILD_ID		= @JOB_QUEUE_BUILD_ID
					,@INSERTED_BY		= @C_PROCESS_CD
					,@IS_RETURN_RESULT	= 0
			END TRY
			BEGIN CATCH
				PRINT @SP_NAME + ': ' +ERROR_MESSAGE() 
			END CATCH
					
					
		END CATCH
	END
	
	-- Resume waiting job by@JOB_QUEUE_GUID
	IF(@JOB_QUEUE_BUILD_STEP_CD = @BUILD_JOB_HANDLER)
	BEGIN
		BEGIN TRY

			SET @C_PARAM_DEFINITION  = '
				@JOB_QUEUE_GUID uniqueidentifier
				,@C_PROCESS_CD	nvarchar(150) 
			'

			SET @C_SQL_CMD = '
				UPDATE t set 
					[IS_WAITING] = 0,
					UPDATED_DT	 = GETDATE(),
					UPDATED_BY	 = @C_PROCESS_CD
				FROM F_JOB_QUEUE	t WITH(ROWLOCK)
				WHERE t.JOB_QUEUE_GUID = @JOB_QUEUE_GUID
			'

			IF @C_IS_DEBUG = 1
				PRINT @SP_NAME + ': ' +'-- Executing: 
					DECLARE ' + @C_PARAM_DEFINITION + '
					set @JOB_QUEUE_GUID	= '''+ convert(nvarchar(64), @JOB_QUEUE_GUID ) +'''
					set @C_PROCESS_CD	= '''+ convert(nvarchar(64), @C_PROCESS_CD	 ) +'''
					' + @C_SP_CMD + '
					' + @C_SQL_CMD + '
					----
					'

			EXEC @C_SP_CMD @C_SQL_CMD, @C_PARAM_DEFINITION
				,@JOB_QUEUE_GUID	= @JOB_QUEUE_GUID
				,@C_PROCESS_CD		= @C_PROCESS_CD
					
		END TRY
		BEGIN CATCH	
			SET @ERROR_NUMBER	 = ERROR_NUMBER()
			SET @ERROR_CD		 = CONVERT(varchar(20), @ERROR_NUMBER)
			SET @ERROR_DESC		 = ERROR_MESSAGE()
			SET @ERROR_FULL_DESC = 
				'ERROR_NUMBER() = '							+ CONVERT(varchar(20), @ERROR_NUMBER) + 
				'; XACT_STATE() = '							+ CONVERT(nvarchar(20), XACT_STATE()) + 
				'; TRANCOUNT= '								+ CONVERT(nvarchar(20), @@TRANCOUNT) +
				'; BUILD_SESSION_ID = '						+ CONVERT(varchar(20), 0) + 
				'; ERROR_MESSAGE() = '						+ @ERROR_DESC + 
				'; BUILD_ID = '								+ CONVERT(varchar(20), @JOB_QUEUE_BUILD_ID) +
				'; JOB_QUEUE_BUILD_TYPE_CD = '				+ @JOB_QUEUE_BUILD_TYPE_CD + 
				'; JOB_QUEUE_BUILD_STEP_CD = '				+ @JOB_QUEUE_BUILD_STEP_CD + 
				'; JOB_QUEUE_BUILD_DB = '					+ @JOB_QUEUE_BUILD_DB + 
				'; JOB_QUEUE_BUILD_STEP_CD = '				+ @JOB_QUEUE_BUILD_STEP_CD +
				'; JOB_QUEUE_BUILD_STEP_ARGS = '			+ ISNULL(@JOB_QUEUE_BUILD_STEP_ARGS, ' IS NULL') +
				'; Update vw_C_JOB_QUEUE JOB_QUEUE_GUID = ' + ISNULL( CONVERT(varchar(40), @JOB_QUEUE_GUID), '')

			BEGIN TRY
				EXEC dbo.[sp_C_MESSAGE] 
					@QUERY_TYPE			= 'SET'
					,@MESSAGE_TYPE_CD	= 'DB_BUILD_JOB_QUEUE_UPDATE_WARNING'
					,@MESSAGE_CD		= @ERROR_CD
					,@MESSAGE_DESC		= @ERROR_FULL_DESC
					,@MESSAGE_CMD		= 'Update F_JOB_QUEUE'
					,@C_BUILD_ID		= @JOB_QUEUE_BUILD_ID
					,@INSERTED_BY		= @C_PROCESS_CD
					,@IS_RETURN_RESULT	= 0
			END TRY
			BEGIN CATCH
				PRINT @SP_NAME + ': ' +ERROR_MESSAGE() 
			END CATCH
				
		END CATCH
	END

END

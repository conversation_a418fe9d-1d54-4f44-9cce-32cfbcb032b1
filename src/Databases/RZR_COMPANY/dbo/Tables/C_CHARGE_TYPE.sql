CREATE TABLE [dbo].[C_CHARGE_TYPE] (
    [CHARGE_TYPE_ID]   BIGINT        NOT NULL,
    [CHARGE_TYPE_CD]   VARCHAR (250) NOT NULL,
    [CHARGE_TYPE_DESC] VARCHAR (250) NOT NULL,
    [IS_INACTIVE]      BIT           CONSTRAINT [DF_C_CHARGE_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]       BIT           CONSTRAINT [DF_C_CHARGE_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]      VARCHAR (250) NOT NULL,
    [INSERTED_DT]      DATETIME      NOT NULL,
    [UPDATED_BY]       VARCHAR (250) NULL,
    [UPDATED_DT]       DATETIME      NULL,
    [DELETED_BY]       VARCHAR (250) NULL,
    [DELETED_DT]       DATETIME      NULL,
    CONSTRAINT [PK_C_CHARGE_TYPE] PRIMARY KEY CLUSTERED ([CHARGE_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is for recycling inbound order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_CHARGE_TYPE';


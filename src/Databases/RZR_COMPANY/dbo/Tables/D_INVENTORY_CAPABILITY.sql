CREATE TABLE [dbo].[D_INVENTORY_CAPABILITY] (
    [INVENTORY_CAPABILITY_ID]            BIGINT        IDENTITY (1, 1) NOT NULL,
    [INVENTORY_CAPABILITY_VALUE]         VARCHAR (1000) NOT NULL,
    [INVENTORY_CAPABILITY_TYPE_ID]       INT           NOT NULL,
    [INVENTORY_CAPABILITY_IS_DEFAULT]    BIT           CONSTRAINT [DF_D_INVENTORY_CAPABILITY_INVENTORY_CAPABILITY_IS_DEFAULT] DEFAULT ((0)) NOT NULL,
    [INVENTORY_PARENT_CAPABILITY_ID]     INT           NULL,
    [INVENTORY_CAPABILITY_VALUE_NUMERIC] FLOAT (53)    NULL,
    [INVENTORY_CAPABILITY_VALUE_SIZE_ID] INT           NULL,
    [IS_INACTIVE]                        BIT           CONSTRAINT [DF_D_INVENTORY_CAPABILITY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                         BIT           CONSTRAINT [DF_D_INVENTORY_CAPABILITY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                        VARCHAR (150) CONSTRAINT [DF_D_INVENTORY_CAPABILITY_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                        DATETIME      CONSTRAINT [DF_D_INVENTORY_CAPABILITY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                         VARCHAR (150) NULL,
    [UPDATED_DT]                         DATETIME      NULL,
    [DELETED_BY]                         VARCHAR (150) NULL,
    [DELETED_DT]                         DATETIME      NULL,
    CONSTRAINT [PK_D_INVENTORY_CAPABILITY] PRIMARY KEY CLUSTERED ([INVENTORY_CAPABILITY_ID] ASC),
    CONSTRAINT [FK_D_INVENTORY_CAPABILITY_C_INVENTORY_CAPABILITY_SIZE] FOREIGN KEY ([INVENTORY_CAPABILITY_VALUE_SIZE_ID]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_SIZE] ([INVENTORY_CAPABILITY_SIZE_ID]),
    CONSTRAINT [FK_D_INVENTORY_CAPABILITY_C_INVENTORY_CAPABILITY_TYPE] FOREIGN KEY ([INVENTORY_CAPABILITY_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID])
);
GO

CREATE NONCLUSTERED INDEX [IX__dbo_D_INVENTORY_CAPABILITY__INVENTORY_CAPABILITY_TYPE_ID__IS_INACTIVE__INCLUDE]
  ON [dbo].[D_INVENTORY_CAPABILITY] (
    [INVENTORY_CAPABILITY_TYPE_ID], [IS_INACTIVE]
  )
  INCLUDE (
    [INVENTORY_CAPABILITY_ID], [INVENTORY_CAPABILITY_VALUE]
  )

GO
CREATE NONCLUSTERED INDEX [IX__dbo_D_INVENTORY_CAPABILITY__IS_INACTIVE__IS_DELETED__INCLUDE]
  ON [dbo].[D_INVENTORY_CAPABILITY] ([IS_INACTIVE], [IS_DELETED])
  INCLUDE (
    [INVENTORY_CAPABILITY_VALUE], [INVENTORY_CAPABILITY_ID]
  )

GO
CREATE TRIGGER trg_INVENTORY_CAPABILITY_VALUE_AFTER_NOTIFICATION
    ON  dbo.D_INVENTORY_CAPABILITY
    AFTER INSERT, UPDATE 
AS 
BEGIN
	DECLARE @params dbo.NOTIFICATION_QUEUE_PARAMS
	
	IF NOT EXISTS(SELECT TOP(1) 1 FROM DELETED)
	BEGIN
		INSERT INTO @params (NAME, VALUE) 
		SELECT
			'INVENTORY_CAPABILITY_VALUE_ID'
			,INVENTORY_CAPABILITY_ID
		FROM INSERTED

		----add notifications about item create
	EXEC sp_ADD_NOTIFICATION_TO_QUEUE
		@C_NOTIFICATION_TYPE_ID = 30 -- Attribute Value Is Created
		,@C_PARAMS = @params
	END	

END
GO



GO
CREATE NONCLUSTERED INDEX [IX_NVENTORY_CAPABILITY_TYPE_VALUE]
    ON [dbo].[D_INVENTORY_CAPABILITY]([INVENTORY_CAPABILITY_TYPE_ID] ASC, [INVENTORY_CAPABILITY_VALUE] ASC)
	INCLUDE([INVENTORY_CAPABILITY_ID])

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Stores values of capabilities for all capabilities types. It is changeable', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_INVENTORY_CAPABILITY';


GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ATTRIBUTE_VALUE_PUT_CHANGE]
    ON  [dbo].[D_INVENTORY_CAPABILITY]
    AFTER INSERT, UPDATE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select distinct
		fiic.ITEM_INVENTORY_ID
	from (
		select
			isnull(i.INVENTORY_CAPABILITY_ID, d.INVENTORY_CAPABILITY_ID) INVENTORY_CAPABILITY_ID
		from inserted		i
		full join deleted	d
			on i.INVENTORY_CAPABILITY_ID = d.INVENTORY_CAPABILITY_ID
		where isnull(i.INVENTORY_CAPABILITY_VALUE, '') <> isnull(d.INVENTORY_CAPABILITY_VALUE, '')
			or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
			or isnull(i.IS_DELETED, 0) <> isnull(d.IS_DELETED, 0)
			or isnull(i.INVENTORY_CAPABILITY_TYPE_ID, 0) <> isnull(d.INVENTORY_CAPABILITY_TYPE_ID, 0)
	) t
	inner join F_ITEM_INVENTORY_CAPABILITY	fiic with(nolock)
		on t.INVENTORY_CAPABILITY_ID = fiic.INVENTORY_CAPABILITY_ID
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 1 --Inventory
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

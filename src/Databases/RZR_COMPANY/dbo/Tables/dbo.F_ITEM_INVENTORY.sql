CREATE TABLE [dbo].[F_ITEM_INVENTORY] (
    [ITEM_INVENTORY_ID]                    BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_ID]                              BIGINT         NULL,
    [ITEM_STATUS_ID]                       INT            CONSTRAINT [DF_F_ITEM_INVENTORY_ITEM_STATUS_ID] DEFAULT ((1)) NOT NULL,
    [ITEM_INVENTORY_UNIQUE_ID]             VARCHAR (250)  NOT NULL,
    [AssetId]                              BIGINT         NULL,
    [RECYCLING_ORDER_ITEM_ID]              BIGINT         NULL,
    [CUSTOMER_ID]                          BIGINT         NOT NULL,
    [ITEM_INVENTORY_SERIAL]                VARCHAR (200)  NOT NULL,
    [ITEM_MASTER_ID]                       BIGINT         NULL,
    [COSMETICS_ID]                         BIGINT         NULL,
    [LOCATION_ID]                          BIGINT         NULL,
    [LOCATION_PREV_ID]                     BIGINT         NULL,
    [CONDITION_ID]                         BIGINT         NULL,
    [DEFECT_UNIQUE_ID]                     BIGINT         NULL,
    [IS_VIRTUAL]                           BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_VIRTUAL] DEFAULT ((0)) NULL,
    [WEIGHT]                               FLOAT (53)     NULL,
    [IS_FAIL]                              BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_PASS] DEFAULT ((0)) NOT NULL,
    [AUTHOR_USER_ID]                       BIGINT         NOT NULL,
    [MODIFIER_USER_ID]                     BIGINT         NULL,
    [ITEM_INVENTORY_INVENTORY_ID]          INT            NULL,
    [ITEM_INVENTORY_PT_ID]                 INT            NULL,
    [ITEM_INVENTORY_PT_LINE]               INT            NULL,
    [ITEM_INVENTORY_UNIT_COST]             FLOAT (53)     NULL,
    [ITEM_INVENTORY_UNIT_COST_ORIGINAL]    FLOAT (53)     CONSTRAINT [DF_F_ITEM_INVENTORY_ITEM_INVENTORY_UNIT_COST_ORIGINAL] DEFAULT ((3)) NULL,
    [ITEM_INVENTORY_UNIT_COST_FAIR_MARKET] FLOAT (53)     NULL,
    [ITEM_INVENTORY_STATUS_CD]             CHAR (11)      NULL,
    [ITEM_INVENTORY_RECEIVE_STATUS_CD]     CHAR (18)      NULL,
    [ITEM_INVENTORY_DATE_RECEIVED]         DATE           NULL,
    [ITEM_INVENTORY_SKU_ATTRB]             VARCHAR (MAX)  NULL,
    [ITEM_INVENTORY_NOTES]                 VARCHAR (MAX)  NULL,
    [ITEM_INVENTORY_CHILD_QTY_UNALLOCATED] BIGINT         CONSTRAINT [DF_F_ITEM_INVENTORY_ITEM_INVENTORY_CHILD_QTY_UNALLOCATED] DEFAULT ((1)) NULL,
    [ITEM_INVENTORY_CHILD_QTY_ALLOCATED]   BIGINT         CONSTRAINT [DF_F_ITEM_INVENTORY_ITEM_INVENTORY_CHILD_QTY_ALLOCATED] DEFAULT ((0)) NULL,
    [ITEM_INVENTORY_COMMENTS]              TEXT           NULL,
    [PRICE_TYPE_ID]                        INT            CONSTRAINT [DF_F_ITEM_INVENTORY_PRICE_TYPE_ID] DEFAULT ((2)) NULL,
    [INVENTORY_GRADE_LEVEL_ID]             BIGINT         NULL,
    [INVENTORY_GRADE_LEVEL_POINT]          INT            NULL,
    [INVENTORY_GRADE_LEVEL_MARK]           NVARCHAR (128) NULL,
    [AUDIT_SESSION_ID]                     BIGINT         NULL,
    [AUDIT_STATUS_ID]                      INT            CONSTRAINT [DF_F_ITEM_INVENTORY_AUDIT_STATUS_ID] DEFAULT ((1)) NULL,
    [AUDIT_DATE]                           DATETIME       NULL,
    [AUDIT_USER_ID]                        BIGINT         NULL,
    [IS_SOLD]                              BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_SOLD] DEFAULT ((0)) NOT NULL,
    [IS_VOIDED]                            BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_VOIDED] DEFAULT ((0)) NOT NULL,
    [IS_PART_REMOVED]                      BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_PART_REMOVED] DEFAULT ((0)) NOT NULL,
    [IS_PART_ADDED]                        BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_PART_ADDED] DEFAULT ((0)) NOT NULL,
    [PARENT_ITEM_INVENTORY_ID]             BIGINT         NULL,
    [NOT_INCLUDE_IN_RESALE_CHANNELS]       AS             ([dbo].[fn_bit_LOCATION_DETAIL_NOT_INCLUDED]([LOCATION_ID])),
    [IS_CONSIGNMENT]                       BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_CONSIGNMENT] DEFAULT ((0)) NOT NULL,
    [IS_DROP_SHIP_ITEM]                    BIT            CONSTRAINT [DF_F_ITEM_INVENTORY_IS_DROP_SHIP_ITEM] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                           BIT            CONSTRAINT [DF_F_INVENTORY_RECV_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                          VARCHAR (150)  CONSTRAINT [DF_F_INVENTORY_RECV_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_BY_IP]                       BIGINT         CONSTRAINT [DF_F_ITEM_INVENTORY_USER_IP] DEFAULT ((0)) NOT NULL,
    [INSERTED_DT]                          DATETIME       CONSTRAINT [DF_F_INVENTORY_RECV_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                           VARCHAR (150)  NULL,
    [UPDATED_BY_IP]                        BIGINT         NULL,
    [UPDATED_DT]                           DATETIME       NULL,
    [DELETED_BY]                           VARCHAR (150)  NULL,
    [DELETED_DT]                           DATETIME       NULL,
    [WEIGHT_KG]                            AS             ([WEIGHT]*(0.45359237)) PERSISTED,
    [SalesOrderItemId]                     BIGINT         NULL,
    [PurchaseOrderRepairId]                BIGINT         NULL,
    [ProductCodeIdHeci]                    BIGINT         NULL,
    [RevisionId]                           BIGINT         NULL,
    [SplitFromInventoryItemId]             BIGINT         NULL,
    [Tag]                                  NVARCHAR (512) NULL,
    [IS_NEEDS_VERIFICATION]                BIT            CONSTRAINT [DF_F_INVENTORY_IS_NEEDS_VERIFICATION] DEFAULT ((0)) NULL,
    [VERIFIED_BY_USER_ID]                  BIGINT         NULL,
    [VERIFIED_DT]                          DATETIME       NULL,
    CONSTRAINT [PK_F_ITEM_INVENTORY] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_ID] ASC),
    CONSTRAINT [FK_F_ITEM_INVENTORY_C_RECYCLING_PRICE_TYPE] FOREIGN KEY ([PRICE_TYPE_ID]) REFERENCES [dbo].[C_RECYCLING_PRICE_TYPE] ([PRICE_TYPE_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_D_INVENTORY_AUDIT_STATUS] FOREIGN KEY ([AUDIT_STATUS_ID]) REFERENCES [dbo].[D_INVENTORY_AUDIT_STATUS] ([STATUS_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_INVENTORY_D_INVENTORY_CAPABILITY] FOREIGN KEY ([COSMETICS_ID]) REFERENCES [dbo].[D_INVENTORY_CAPABILITY] ([INVENTORY_CAPABILITY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_D_ITEM_CONDITION] FOREIGN KEY ([CONDITION_ID]) REFERENCES [dbo].[D_ITEM_CONDITION] ([ITEM_CONDITION_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_D_ITEM_INVENTORY_STATUS] FOREIGN KEY ([ITEM_STATUS_ID]) REFERENCES [dbo].[D_ITEM_INVENTORY_STATUS] ([ITEM_INVENTORY_STATUS_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_Asset] FOREIGN KEY ([AssetId]) REFERENCES [recycling].[F_Asset] ([Id]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_INVENTORY_AUDIT_SESSION] FOREIGN KEY ([AUDIT_SESSION_ID]) REFERENCES [dbo].[F_INVENTORY_AUDIT_SESSION] ([SESSION_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_ITEM] FOREIGN KEY ([ITEM_ID]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_LOCATION] FOREIGN KEY ([LOCATION_ID]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_LOCATION_PREV_ID] FOREIGN KEY ([LOCATION_PREV_ID]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_RECYCLING_ORDER_ITEM] FOREIGN KEY ([RECYCLING_ORDER_ITEM_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_F_SALES_ORDER_ITEM] FOREIGN KEY ([SalesOrderItemId]) REFERENCES [dbo].[F_SALES_ORDER_ITEM] ([SALES_ORDER_ITEM_ID]) ON DELETE SET NULL,
    CONSTRAINT [FK_F_ITEM_INVENTORY_tb_User_AUDIT_USER_ID] FOREIGN KEY ([AUDIT_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_tb_User_AUTHOR_USER_ID] FOREIGN KEY ([AUTHOR_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_tb_User_MODIFIER_USER_ID] FOREIGN KEY ([MODIFIER_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);


GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ASSET_ID]
	ON [dbo].[F_ITEM_INVENTORY] (AssetId, [ITEM_INVENTORY_ID], ITEM_STATUS_ID)
	INCLUDE (ITEM_ID)
	WHERE ([AssetId] IS NOT NULL)
GO


CREATE unique NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ASSET_STATUS]
	ON [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID], AssetId, [ITEM_STATUS_ID])
	INCLUDE ([AUDIT_STATUS_ID])
GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_UNIQUE_ID]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_INVENTORY_UNIQUE_ID] ASC, [ITEM_STATUS_ID] ASC)
    INCLUDE([ITEM_INVENTORY_ID]) WHERE ([IS_DELETED]<>(1) AND [ITEM_STATUS_ID]<>(3) AND [ITEM_STATUS_ID]<>(8));
GO

CREATE NONCLUSTERED INDEX [IX__dbo_F_ITEM_INVENTORY__LOCATION_ID__INCLUDE__ITEM_ID]
	ON [dbo].[F_ITEM_INVENTORY] (
		[LOCATION_ID]
	)
	INCLUDE (
		[ITEM_ID]
	)

GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__IS_SOLD__IS_INACTIVE__IS_DELETED__INCLUDE__ASSET__INVENTORY__MODEL__IDS]
	ON [dbo].[F_ITEM_INVENTORY] ([IS_SOLD],[IS_DELETED])
	INCLUDE ([ITEM_INVENTORY_ID],[AssetId],[ITEM_MASTER_ID])
GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__PARENT__IS_DELETED]
	ON [dbo].[F_ITEM_INVENTORY] (
		[IS_DELETED],
		[PARENT_ITEM_INVENTORY_ID] 
	) INCLUDE (
		[ITEM_INVENTORY_ID],
		[INSERTED_DT],
		[ITEM_MASTER_ID]
	)
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_AUTHOR_USER_ID]
    ON [dbo].[F_ITEM_INVENTORY] (
		[AUTHOR_USER_ID] ASC, 
		[ITEM_STATUS_ID] ASC, 
		[IS_DELETED] ASC
	) INCLUDE (
		[ITEM_INVENTORY_ID], 
		[ITEM_ID], 
		[ITEM_INVENTORY_UNIQUE_ID], 
		[CUSTOMER_ID], 
		[ITEM_INVENTORY_SERIAL], 
		[COSMETICS_ID], 
		[CONDITION_ID], 
		[DEFECT_UNIQUE_ID], 
		[ITEM_INVENTORY_UNIT_COST_ORIGINAL], 
		[ITEM_INVENTORY_NOTES], 
		[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], 
		[AUDIT_STATUS_ID], 
		[AUDIT_DATE], 
		[AUDIT_USER_ID], 
		[IS_SOLD], 
		[INSERTED_DT], 
		[ITEM_MASTER_ID], 
		[LOCATION_ID]
	) WHERE ([IS_DELETED]=(0))
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_CONDITION_ID]
    ON [dbo].[F_ITEM_INVENTORY](
		[CONDITION_ID] ASC
	) INCLUDE (
		[ITEM_INVENTORY_ID],
		[ITEM_INVENTORY_UNIQUE_ID],
		[CUSTOMER_ID],
		[ITEM_INVENTORY_SERIAL],
		[ITEM_MASTER_ID],
		[LOCATION_ID],
		[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED],
		[ITEM_INVENTORY_NOTES],
		[INSERTED_DT],
		[ITEM_ID]
	)
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_CUSTOMER_ID_IS_INACTIVE_IS_DELETED]
    ON [dbo].[F_ITEM_INVENTORY](
		[CUSTOMER_ID] ASC,
		[ITEM_STATUS_ID] ASC,
		[IS_DELETED] ASC
	) INCLUDE (
		[ITEM_INVENTORY_ID],
		[ITEM_ID], 
		[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], 
		[AUDIT_STATUS_ID], 
		[AUDIT_DATE], 
		[AUDIT_USER_ID], 
		[IS_SOLD], 
		[INSERTED_DT], 
		[LOCATION_ID], 
		[CONDITION_ID], 
		[DEFECT_UNIQUE_ID], 
		[AUTHOR_USER_ID], 
		[ITEM_INVENTORY_UNIT_COST_ORIGINAL], 
		[ITEM_INVENTORY_NOTES], 
		[ITEM_INVENTORY_UNIQUE_ID], 
		[ITEM_INVENTORY_SERIAL], 
		[ITEM_MASTER_ID], 
		[COSMETICS_ID]
	) WHERE ([IS_DELETED]=(0))
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_IS_SOLD_IS_INACTIVE_IS_DELETED_ITEM_STATUS_ID]
    ON [dbo].[F_ITEM_INVENTORY](
		[IS_SOLD] ASC, 
		[IS_DELETED] ASC, 
		[ITEM_STATUS_ID] ASC
	) INCLUDE (
		[ITEM_INVENTORY_ID],
		[ITEM_ID]
	)
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_MASTER_CONDITION_SKU]
    ON [dbo].[F_ITEM_INVENTORY](
		[ITEM_MASTER_ID] ASC, 
		[CONDITION_ID] ASC, 
		[IS_SOLD] ASC, 
		[IS_DELETED] ASC,
		[ITEM_ID] ASC
	)
    INCLUDE(
		[ITEM_INVENTORY_SKU_ATTRB], 
		[ITEM_INVENTORY_ID], 
		[ITEM_STATUS_ID], 
		[ITEM_INVENTORY_UNIQUE_ID], 
		[AssetId], 
		[LOCATION_ID], 
		[LOCATION_PREV_ID], 
		[DEFECT_UNIQUE_ID], 
		[CUSTOMER_ID], 
		[ITEM_INVENTORY_SERIAL], 
		[COSMETICS_ID], 
		[IS_VIRTUAL], 
		[AUTHOR_USER_ID], 
		[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], 
		[ITEM_INVENTORY_UNIT_COST_ORIGINAL], 
		[ITEM_INVENTORY_NOTES], 
		[AUDIT_STATUS_ID], 
		[AUDIT_DATE], 
		[AUDIT_USER_ID], 
		[IS_PART_REMOVED], 
		[IS_PART_ADDED], 
		[INSERTED_DT]
	)
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_STATUS_ID_IS_DELETED]
    ON [dbo].[F_ITEM_INVENTORY](
		[ITEM_STATUS_ID] ASC,
		[IS_DELETED] ASC
	) INCLUDE (
		[ITEM_ID],
		[ITEM_MASTER_ID], 
		[CONDITION_ID], 
		[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], 
		[AUDIT_STATUS_ID]
	)
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_LOCATION_ID]
    ON [dbo].[F_ITEM_INVENTORY](
		[LOCATION_ID] ASC, 
		[IS_DELETED] ASC
	)
GO

CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ID_ITEM_STATUS_ID_ITEM_MASTER_ID_LOCATION_ID]
    ON [dbo].[F_ITEM_INVENTORY](
		[ITEM_INVENTORY_ID] ASC, 
		[AUTHOR_USER_ID] ASC, 
		[IS_DELETED] ASC, 
		[ITEM_STATUS_ID] ASC
	) INCLUDE (
		[ITEM_MASTER_ID],
		[LOCATION_ID]
	) WHERE ([IS_DELETED]=(0))
GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__ITEM_INVENTORY_UNIQUE_ID__IS_INACTIVE__IS_DELETED_for_uid_generation]
	ON [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_UNIQUE_ID],[IS_DELETED])
	INCLUDE ([ITEM_INVENTORY_ID])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_STATUS_ID_INVENTORY_SERIAL_UNIQUE_ID]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_STATUS_ID] ASC, [ITEM_INVENTORY_SERIAL] ASC, [ITEM_INVENTORY_UNIQUE_ID] ASC)
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_MASTER_ID_CONDITION_ID_QTY_UNALLOCATED]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_ID] ASC, [ITEM_MASTER_ID] ASC, [CONDITION_ID] ASC, [ITEM_INVENTORY_CHILD_QTY_UNALLOCATED] ASC, [IS_DELETED] ASC)
    INCLUDE([ITEM_STATUS_ID], [ITEM_INVENTORY_UNIQUE_ID], [CUSTOMER_ID], [ITEM_INVENTORY_SERIAL], [COSMETICS_ID], [IS_VIRTUAL], [AUTHOR_USER_ID], [ITEM_INVENTORY_UNIT_COST_ORIGINAL], [ITEM_INVENTORY_NOTES], [AUDIT_STATUS_ID], [AUDIT_DATE], [AUDIT_USER_ID], [IS_PART_REMOVED], [IS_PART_ADDED], [INSERTED_DT], [LOCATION_ID], [ITEM_INVENTORY_ID])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_ID]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_ID] ASC)
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_LOCATION_ID_ITEM_MASTER_ID]
    ON [dbo].[F_ITEM_INVENTORY]([LOCATION_ID] ASC, [ITEM_MASTER_ID] ASC)
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTOR_IS_INACTIVE_IS_DELETED]
    ON [dbo].[F_ITEM_INVENTORY]([IS_DELETED] ASC, [ITEM_STATUS_ID] ASC)
    INCLUDE(
		[ITEM_INVENTORY_ID], 
		[ITEM_ID], 
		[ITEM_INVENTORY_UNIQUE_ID], 
		[ITEM_INVENTORY_UNIT_COST_ORIGINAL], 
		[ITEM_INVENTORY_NOTES], 
		[AUDIT_STATUS_ID], 
		[AUDIT_DATE], 
		[AUDIT_USER_ID], 
		[IS_SOLD], 
		[INSERTED_DT], 
		[ITEM_INVENTORY_SERIAL], 
		[ITEM_MASTER_ID], 
		[COSMETICS_ID], 
		[LOCATION_ID], 
		[RECYCLING_ORDER_ITEM_ID]
	) WHERE ([IS_DELETED]=(0))
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTOR_SALES_ORDER_QUOTED]
    ON [dbo].[F_ITEM_INVENTORY]([IS_DELETED] ASC, [ITEM_STATUS_ID] ASC, [AUDIT_STATUS_ID] ASC)
    INCLUDE([ITEM_INVENTORY_ID], [ITEM_ID], [RECYCLING_ORDER_ITEM_ID], [ITEM_MASTER_ID], [LOCATION_ID], [CONDITION_ID], [SalesOrderItemId])
GO
CREATE NONCLUSTERED INDEX [ix__F_ITEM_INVENTORY__consignment]
	ON [dbo].[F_ITEM_INVENTORY] ([IS_DELETED], [ITEM_STATUS_ID],[AUDIT_STATUS_ID])
	INCLUDE ([ITEM_INVENTORY_ID],[ITEM_ID],[ITEM_INVENTORY_UNIQUE_ID],[AssetId],[RECYCLING_ORDER_ITEM_ID],[ITEM_INVENTORY_SERIAL],[ITEM_MASTER_ID],[CONDITION_ID])
	WHERE([IS_DELETED] = (0) AND ([AUDIT_STATUS_ID] IN ((1), (2))) AND [ITEM_STATUS_ID] <> (8) AND [ITEM_STATUS_ID] <> (9))
GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ACTIVE]
    ON [dbo].[F_ITEM_INVENTORY]([RECYCLING_ORDER_ITEM_ID] ASC, [IS_DELETED] ASC)
    INCLUDE([ITEM_INVENTORY_ID], [ITEM_INVENTORY_CHILD_QTY_UNALLOCATED])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_MASTER_ID]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_MASTER_ID] ASC)
INCLUDE (ITEM_ID, ITEM_STATUS_ID, ITEM_INVENTORY_UNIQUE_ID, AssetId, CUSTOMER_ID, ITEM_INVENTORY_SERIAL, COSMETICS_ID, LOCATION_ID, LOCATION_PREV_ID, CONDITION_ID, IS_VIRTUAL, AUTHOR_USER_ID, ITEM_INVENTORY_UNIT_COST_ORIGINAL, ITEM_INVENTORY_SKU_ATTRB, ITEM_INVENTORY_NOTES, ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, AUDIT_STATUS_ID, AUDIT_DATE, AUDIT_USER_ID, IS_SOLD, IS_PART_REMOVED, IS_PART_ADDED, IS_DELETED, INSERTED_DT, IS_CONSIGNMENT, WEIGHT)
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_ITEM_STATUS_ID_IS_INACTIVE_IS_DELETED]
	ON [dbo].[F_ITEM_INVENTORY] ([ITEM_STATUS_ID],[IS_DELETED])
	INCLUDE ([ITEM_INVENTORY_ID],[AUDIT_STATUS_ID])
GO
CREATE NONCLUSTERED INDEX [ix__dbo_F_ITEM_INVENTORY__for_raw_allocate]
	ON [dbo].[F_ITEM_INVENTORY] ([ITEM_STATUS_ID],[IS_VIRTUAL],[IS_DELETED],[AUDIT_STATUS_ID])
	INCLUDE ([ITEM_INVENTORY_ID],[SalesOrderItemId])
GO
CREATE NONCLUSTERED INDEX [IX_AUDIT_ITEM_MASTER_ID_VIRTUAL_AUDIT_STATUS_ID]
    ON [dbo].[F_ITEM_INVENTORY](AssetId ASC, [ITEM_MASTER_ID] ASC, [IS_VIRTUAL] ASC, [AUDIT_STATUS_ID] ASC, [IS_DELETED])
	INCLUDE(ITEM_ID, ITEM_INVENTORY_SERIAL, IS_SOLD, ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, IS_CONSIGNMENT, ITEM_STATUS_ID, ITEM_INVENTORY_ID, ITEM_INVENTORY_UNIT_COST_ORIGINAL)
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY__UNIQUE_CHECK_UID]
	ON [dbo].[F_ITEM_INVENTORY] ([IS_DELETED],[ITEM_INVENTORY_ID])
	INCLUDE ([ITEM_INVENTORY_UNIQUE_ID])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY__UNIQUE_CHECK_SERIAL]
	ON [dbo].[F_ITEM_INVENTORY] ([IS_VIRTUAL],[IS_DROP_SHIP_ITEM],[IS_DELETED],[ITEM_INVENTORY_ID])
	INCLUDE ([ITEM_INVENTORY_SERIAL])
GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_SERIAL]
    ON [dbo].[F_ITEM_INVENTORY]([ITEM_INVENTORY_SERIAL] ASC)
    INCLUDE([ITEM_INVENTORY_ID])
GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__RawResaleOnHandSummary]
	ON [dbo].[F_ITEM_INVENTORY] ([IS_DELETED])
	INCLUDE ([ITEM_STATUS_ID],[AssetId],[ITEM_MASTER_ID],[LOCATION_ID],[CONDITION_ID],[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED],[AUDIT_STATUS_ID],[SalesOrderItemId])
GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__COSMETICS_ID]  
	ON [dbo].[F_ITEM_INVENTORY] ([COSMETICS_ID])
	INCLUDE ([ITEM_INVENTORY_ID])
GO

CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY__LOCATION_ID__INCLUDE__ITEM_INVENTORY_ID__ITEM_STATUS_ID]
	ON [dbo].[F_ITEM_INVENTORY] ([LOCATION_ID])
	INCLUDE ([ITEM_INVENTORY_ID],[ITEM_STATUS_ID])
GO
CREATE NONCLUSTERED INDEX [ix_IS_DROP_SHIP_ITEM_ITEM_STATUS_ID]
    ON [dbo].[F_ITEM_INVENTORY]([IS_DROP_SHIP_ITEM] ASC, [ITEM_STATUS_ID] ASC)
    INCLUDE([ITEM_INVENTORY_ID], [ITEM_ID], [ITEM_MASTER_ID], [LOCATION_ID], [CONDITION_ID], [DEFECT_UNIQUE_ID], [IS_VIRTUAL], [ITEM_INVENTORY_SKU_ATTRB], [ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], [AUDIT_STATUS_ID])
GO
CREATE NONCLUSTERED INDEX [IX_SalesOrderItemId]
    ON [dbo].[F_ITEM_INVENTORY]([SalesOrderItemId] ASC)
GO
CREATE NONCLUSTERED INDEX [IX_PurchaseOrderRepairId]
    ON [dbo].[F_ITEM_INVENTORY]([PurchaseOrderRepairId] ASC)
GO
CREATE NONCLUSTERED INDEX [IX_ProductCodeIdHeci]
    ON [dbo].[F_ITEM_INVENTORY]([ProductCodeIdHeci] ASC)
GO
-- =============================================
-- Author:             A. Semenov
-- Author update:      S. Pyatko
-- Create date:        10/21/2015
-- Update date:        11/09/2015
-- Reason for update:  RSW-9113
-- Description:	       Occures after update item inventory entry and logs changed data
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_INVENTORY_AFTER_UPDATE]
    ON  [dbo].[F_ITEM_INVENTORY]
    AFTER UPDATE
AS 
BEGIN

	DECLARE
		 @PROCESS_CD	NVARCHAR(128) =	 'trg_ITEM_INVENTORY_AFTER_UPDATE',
		 @TAIL			NVARCHAR(4)		= N'"',
		 @DEF			NVARCHAR(4)		= N'"-"';

	-- SKU Id update - always a single action
	INSERT INTO [dbo].[F_LOG_ACTION_DATA]
		([ACTION_ID]
		,[ENTITY_TYPE_ID]
		,[ENTITY_KEY_VALUE]
		,[ENTITY_AUTO_NAME]	   
		,[CHANED_PROPERTY_PREV_VALUE]
		,[CHANED_PROPERTY_CURRENT_VALUE]
		,[USER_ID]     
		,[USER_IP]
		,[SOURCE])
	SELECT
		23													AS [ACTION_ID]		-- SKU change
		,9													AS [ENTITY_TYPE_ID] -- SKU
		,I.ITEM_INVENTORY_ID								AS [ENTITY_KEY_VALUE]
		,I.ITEM_INVENTORY_UNIQUE_ID							AS [ENTITY_AUTO_NAME]
		,D.ITEM_ID											AS [CHANED_PROPERTY_PREV_VALUE]
		,I.ITEM_ID											AS [CHANED_PROPERTY_CURRENT_VALUE]
		,ISNULL(I.[MODIFIER_USER_ID], I.[AUTHOR_USER_ID])	AS [USER_ID]	
		,ISNULL(I.[UPDATED_BY_IP], I.[INSERTED_BY_IP])		AS [USER_IP]	
		,@PROCESS_CD										AS [SOURCE]
		FROM INSERTED                        AS I
		INNER JOIN DELETED                   AS D                   
			ON I.[ITEM_INVENTORY_ID] = D.[ITEM_INVENTORY_ID]					
		WHERE ( D.ITEM_ID is not null and D.ITEM_ID != isnull(I.ITEM_ID, 0) )
	
	if (@@ROWCOUNT > 0)
		return;


	select  @PROCESS_CD = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
	INSERT INTO F_LOG_DATA 	WITH(ROWLOCK)
	(	
                [SOURCE]
               ,[USER_ID]
               ,[USER_IP]
               ,[OPERATION_NAME]
               ,[ENTITY_TYPE_ID]
               ,[ENTITY_KEY_VALUE]
               ,[ENTITY_AUTO_NAME]
               ,[CHANGES])
	SELECT @PROCESS_CD
               ,NEW.[USER_ID]
               ,NEW.[USER_IP]
               ,NEW.[OPERATION_NAME]
               ,9 --N'Receive Inventory'
               ,NEW.[ENTITY_KEY_VALUE]
               ,NEW.[ENTITY_AUTO_NAME]
	           ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT NEW.[LOCATION_NAME]
                                                     ,NEW.[STATUS_NAME]
                                                     ,NEW.[UNIQUE_NAME]	
													 ,NEW.[SERIAL_NAME]
													 ,NEW.[Quantity]
													 ,NEW.[ITEM_INVENTORY_UNIT_COST_ORIGINAL]											
	                                          FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT))		AS [CHANGES]
           FROM (
                     SELECT -- Fixed columns		  
                            N'Updated'															AS [OPERATION_NAME]
                           ,ISNULL(I.[MODIFIER_USER_ID], I.[AUTHOR_USER_ID])					AS [USER_ID]
                           ,ISNULL(I.[UPDATED_BY_IP], I.[INSERTED_BY_IP])						AS [USER_IP]
                           ,ISNULL(I.[ITEM_INVENTORY_ID], D.[ITEM_INVENTORY_ID])				AS [ENTITY_KEY_VALUE]
                           ,ISNULL(I.[ITEM_INVENTORY_UNIQUE_ID], D.[ITEM_INVENTORY_UNIQUE_ID])	AS [ENTITY_AUTO_NAME]
                           -- XML columns														AS [WORKFLOW_TYPE_DESC]
                           ,CASE
                               WHEN ISNULL(D.LOCATION_ID, 0) <> ISNULL(I.LOCATION_ID, 0) AND I.IS_DELETED = 0
                               THEN N'Location was ' + 
                                   ISNULL(N'"' + L_D.[LOCATION_NAME] + N'"', @DEF) + N' is ' +
                                   ISNULL(N'"' + L.[LOCATION_NAME] + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [LOCATION_NAME]
                           ,CASE
                               WHEN ISNULL(D.ITEM_STATUS_ID, 0) <> ISNULL(I.ITEM_STATUS_ID, 0)
                               THEN N'Status was ' + 
                                   ISNULL(N'"' + IIS_D.[STATUS_CD] + N'"', @DEF) + N' is ' +
                                   ISNULL(N'"' + IIS.[STATUS_CD] + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [STATUS_NAME]
                           ,CASE
                               WHEN ISNULL(D.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0) <> ISNULL(I.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0)
                               THEN N'Recv price was ' + 
                                   ISNULL(N'"' + CAST(D.ITEM_INVENTORY_UNIT_COST_ORIGINAL AS NVARCHAR(40)) + N'"',  @DEF) + N' is ' +
                                   ISNULL(N'"' + CAST(I.ITEM_INVENTORY_UNIT_COST_ORIGINAL AS NVARCHAR(40)) + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [ITEM_INVENTORY_UNIT_COST_ORIGINAL]
                           ,CASE WHEN D.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED <> I.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED
								THEN N'Quantity was changed. Old value:'
									+  ISNULL(N'"' + CAST(D.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED as nvarchar(19))+ @TAIL, @DEF) + N', new value: ' +	 
									 ISNULL(N'"' + CAST(I.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED as nvarchar(19)) + @TAIL, @DEF) 
								ELSE NULL 
							END																		as [Quantity]
						   ,CASE																					  
                               WHEN (D.[ITEM_INVENTORY_UNIQUE_ID] <> I.[ITEM_INVENTORY_UNIQUE_ID])
                               THEN N'The UID was changed. Old value: ' + 										  
                                   ISNULL(N'"' + D.[ITEM_INVENTORY_UNIQUE_ID] + N'"', @DEF) + N', new value: ' +	  
                                   ISNULL(N'"' + I.[ITEM_INVENTORY_UNIQUE_ID] + @TAIL, @DEF)						  
                               ELSE NULL																			  
                            END																	AS [UNIQUE_NAME]
						   ,CASE
                               WHEN (D.[ITEM_INVENTORY_SERIAL] <> I.[ITEM_INVENTORY_SERIAL])
                               THEN N'The Serial Number was changed. Old value: ' +
                                   ISNULL(N'"' + D.[ITEM_INVENTORY_SERIAL] + N'"', @DEF) + N', new value: ' +
                                   ISNULL(N'"' + I.[ITEM_INVENTORY_SERIAL] + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [SERIAL_NAME]																				
                    FROM INSERTED                        AS I
					INNER JOIN DELETED                         AS D                   ON I.[ITEM_INVENTORY_ID] = D.[ITEM_INVENTORY_ID]
					LEFT JOIN [F_LOCATION]                    AS L     WITH (NOLOCK) ON I.[LOCATION_ID] = l.[LOCATION_ID]
					--LEFT JOIN [dbo].[F_PURCHASE_ORDER_ITEM]   AS POI   WITH (NOLOCK) ON POI.[INVENTORY_ITEM_ID] = I.[ITEM_INVENTORY_ID]
					--LEFT JOIN [dbo].[F_PURCHASE_ORDER]        AS PO    WITH (NOLOCK) ON POI.[PURCHASE_ORDER_ID] = PO.[PURCHASE_ORDER_ID]
					LEFT JOIN [dbo].[D_ITEM_INVENTORY_STATUS] AS IIS   WITH (NOLOCK) ON I.[ITEM_STATUS_ID] = IIS.[ITEM_INVENTORY_STATUS_ID]
    	   
					LEFT JOIN [F_LOCATION]                    AS L_D   WITH (NOLOCK) ON D.[LOCATION_ID] = L_D.[LOCATION_ID]
					--LEFT JOIN [dbo].[F_PURCHASE_ORDER_ITEM]   AS POI_D WITH (NOLOCK) ON POI_D.[INVENTORY_ITEM_ID] = I.[ITEM_INVENTORY_ID]
					--LEFT JOIN [dbo].[F_PURCHASE_ORDER]        AS PO_D  WITH (NOLOCK) ON POI_D.[PURCHASE_ORDER_ID] = PO_D.[PURCHASE_ORDER_ID]
					LEFT JOIN [dbo].[D_ITEM_INVENTORY_STATUS] AS IIS_D WITH (NOLOCK) ON D.[ITEM_STATUS_ID] = IIS_D.[ITEM_INVENTORY_STATUS_ID]
    	   
					WHERE D.[ITEM_STATUS_ID]						!= I.[ITEM_STATUS_ID]
						OR (I.IS_DELETED = 0 AND ISNULL(D.[LOCATION_ID], 0)	!= ISNULL(I.[LOCATION_ID], 0))
						OR D.[ITEM_INVENTORY_UNIQUE_ID]				!= I.[ITEM_INVENTORY_UNIQUE_ID]
						OR D.[ITEM_INVENTORY_SERIAL]				!= I.[ITEM_INVENTORY_SERIAL]
						OR D.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED	!= I.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED
						OR D.ITEM_INVENTORY_UNIT_COST_ORIGINAL		!= I.ITEM_INVENTORY_UNIT_COST_ORIGINAL						
                ) NEW
    


	DECLARE @logActionData TABLE
	( 
		[ACTION_ID]                      INT           
		,[ENTITY_TYPE_ID]                INT           
		,[ENTITY_KEY_VALUE]              BIGINT        
		,[ENTITY_AUTO_NAME]              NVARCHAR (256)
		,[CHANED_PROPERTY_PREV_ID]       BIGINT        
		,[CHANED_PROPERTY_PREV_VALUE]    NVARCHAR (150)
		,[CHANED_PROPERTY_CURRENT_ID]    BIGINT        
		,[CHANED_PROPERTY_CURRENT_VALUE] NVARCHAR (150)
		,[USER_ID]                       BIGINT        
		,[USER_IP]                       BIGINT         
		,[SOURCE]                        NVARCHAR (256)
	)


	INSERT INTO @logActionData (
	    [ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_PREV_ID]	   
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_PREV_VALUE]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE]
	)
	SELECT  
		CASE
			--Cannot log DELETED here bc the location is already discarded
			WHEN (IS_STOCK_MOVE = 1)	 THEN 1	 -- Stock Move
			WHEN (IS_EQUIPMENT_SALE = 1) THEN 3  -- Equipment Sale
			WHEN (IS_ALLOCATED = 1)		 THEN 5	 -- Allocated
			WHEN (IS_RECYCLED = 1)		 THEN 7	 -- Recycled
			WHEN (IS_AUDITED = 1)		 THEN 9	 -- Audited
			WHEN (IS_SCRAP = 1)			 THEN 10 -- Scrap
			WHEN (IS_NOT_FOUND = 1)		 THEN 11 -- NotFound
			WHEN (IS_BROKEN_DOWN = 1)	 THEN 12 -- Broken Down
			WHEN (IS_PART_ADDED = 1)	 THEN 13 -- Build UP
			WHEN (IS_UNALLOCATED = 1)	 THEN 16 -- Unallocated
		END									AS [ACTION_ID]
		,9									AS [ENTITY_TYPE_ID]					-- Receive Inventory
		,NEW.ENTITY_KEY_VALUE				AS [ENTITY_KEY_VALUE]
		,NEW.ENTITY_AUTO_NAME				AS [ENTITY_AUTO_NAME]
		,NEW.LOCATION_PREV_ID				AS [CHANED_PROPERTY_PREV_ID]	   
		,NEW.LOCATION_ID					AS [CHANED_PROPERTY_CURRENT_ID]
		,NEW.LOCATION_PREV_NAME				AS [CHANED_PROPERTY_PREV_VALUE]	
		,NEW.LOCATION_NAME					AS [CHANED_PROPERTY_CURRENT_VALUE]
		,NEW.[USER_ID]						AS [USER_ID]
		,NEW.[USER_IP]						AS [USER_IP]
		,@PROCESS_CD						AS [SOURCE]
	FROM
	(
		SELECT
			-- Fixed columns
			 ISNULL(I.MODIFIER_USER_ID, I.AUTHOR_USER_ID)					AS [USER_ID]
			,ISNULL(I.UPDATED_BY_IP, I.INSERTED_BY_IP)						AS [USER_IP]
			,ISNULL(I.ITEM_INVENTORY_ID, D.ITEM_INVENTORY_ID)				AS ENTITY_KEY_VALUE
			,ISNULL(I.ITEM_INVENTORY_UNIQUE_ID, D.ITEM_INVENTORY_UNIQUE_ID)	AS ENTITY_AUTO_NAME
			,I.LOCATION_ID													AS LOCATION_ID
			,FLI.LOCATION_NAME												AS LOCATION_NAME
			,D.LOCATION_ID													AS LOCATION_PREV_ID
			,FLD.LOCATION_NAME												AS LOCATION_PREV_NAME
			,CASE
				WHEN (ISNULL(D.LOCATION_ID, 0) <> ISNULL(I.LOCATION_ID, 0) AND I.IS_DELETED = 0)				
				THEN 1
				ELSE 0 
			END																AS IS_STOCK_MOVE
			,CASE
				WHEN (I.ITEM_STATUS_ID = 1 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Unallocated
				THEN 1
				ELSE 0 
			END																AS IS_UNALLOCATED
			,CASE
				WHEN (I.ITEM_STATUS_ID = 2 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Allocated
				THEN 1
				ELSE 0 
			END																AS IS_ALLOCATED
			,CASE
				WHEN (I.ITEM_STATUS_ID = 3 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Sold
				THEN 1
				ELSE 0 
			END																AS IS_EQUIPMENT_SALE
			,CASE
				WHEN (I.ITEM_STATUS_ID = 9 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Consumed
				THEN 1
				ELSE 0 
			END																AS IS_RECYCLED
			,CASE
				WHEN (I.ITEM_STATUS_ID = 10 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Breakdown
				THEN 1
				ELSE 0 
			END																AS IS_BROKEN_DOWN
			,CASE
				WHEN (I.ITEM_STATUS_ID = 12 AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID) -- Part Added
				THEN 1
				ELSE 0 
			END																AS IS_PART_ADDED
			,CASE
				WHEN (I.AUDIT_STATUS_ID = 2 AND I.AUDIT_STATUS_ID != D.AUDIT_STATUS_ID) -- Audited
				THEN 1
				ELSE 0 
			END																AS IS_AUDITED
			,CASE
				WHEN (I.AUDIT_STATUS_ID = 3 AND I.AUDIT_STATUS_ID != D.AUDIT_STATUS_ID 
				   OR I.ITEM_STATUS_ID IN (8, 9) AND D.ITEM_STATUS_ID NOT IN (8, 9)) -- Scrap
				THEN 1
				ELSE 0 
			END																AS IS_SCRAP
			,CASE
				WHEN (I.AUDIT_STATUS_ID = 4 AND I.AUDIT_STATUS_ID != D.AUDIT_STATUS_ID) -- NotFound
				THEN 1
				ELSE 0 
			END																AS IS_NOT_FOUND
		FROM		INSERTED							I
		INNER JOIN	DELETED								D
			ON I.ITEM_INVENTORY_ID = D.ITEM_INVENTORY_ID
		LEFT JOIN	F_LOCATION							FLI WITH(NOLOCK)
			ON FLI.LOCATION_ID = I.LOCATION_ID
		LEFT JOIN	F_LOCATION							FLD WITH(NOLOCK)
			ON FLD.LOCATION_ID = D.LOCATION_ID
		WHERE(I.IS_DELETED = 0 AND ISNULL(D.LOCATION_ID, 0) != ISNULL(I.LOCATION_ID, 0))
		   OR I.ITEM_STATUS_ID  != D.ITEM_STATUS_ID  AND I.ITEM_STATUS_ID  IN (1,2,3,8,9,10,12)
		   OR I.AUDIT_STATUS_ID != D.AUDIT_STATUS_ID AND I.AUDIT_STATUS_ID IN (2,3,4)
	) NEW

	-- Logging Sales Order items that is invoiced
	INSERT INTO @logActionData
       ([ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]	
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE])
    SELECT
		14												AS [ACTION_ID]		-- Invoiced
	    ,9												AS [ENTITY_TYPE_ID]	-- Receive Inventory
		,I.ITEM_INVENTORY_ID							AS [ENTITY_KEY_VALUE]
		,I.ITEM_INVENTORY_UNIQUE_ID						AS [ENTITY_AUTO_NAME]
		,SOI.SALES_ORDER_ITEM_ID						AS [CHANED_PROPERTY_CURRENT_ID]
		,SOI.ITEM_PRICE									AS [CHANED_PROPERTY_CURRENT_VALUE]
		,ISNULL(I.MODIFIER_USER_ID, I.AUTHOR_USER_ID)	AS [USER_ID]
		,ISNULL(I.UPDATED_BY_IP, I.INSERTED_BY_IP)		AS [USER_IP]
		,@PROCESS_CD									AS [SOURCE]
		
	FROM INSERTED					I
	INNER JOIN	DELETED				D
		ON I.ITEM_INVENTORY_ID = D.ITEM_INVENTORY_ID
	INNER JOIN F_ITEM_INVENTORY	IID	WITH (NOLOCK)
		ON IID.ITEM_INVENTORY_ID = I.ITEM_INVENTORY_ID
	inner join [dbo].[F_SALES_ORDER_ITEM] soi with (nolock)
		on iid.SalesOrderItemId = soi.SALES_ORDER_ITEM_ID
	INNER JOIN F_INVOICE				INV	WITH (NOLOCK)
		ON soi.SALES_ORDER_ID = INV.ORDER_ID
		AND INV.INVOICE_TYPE_ID = 1  -- SALES	
	
	WHERE INV.IS_VOIDED = 0
	AND I.ITEM_STATUS_ID = 3 -- Sold
	AND D.ITEM_STATUS_ID != I.ITEM_STATUS_ID


	-- Logging Audit Status in parallel with Item Inventory's creation
    INSERT INTO @logActionData
    ( [ACTION_ID]
    , [ENTITY_TYPE_ID]
    , [ENTITY_KEY_VALUE]
    , [ENTITY_AUTO_NAME]
    , [CHANED_PROPERTY_PREV_ID]
    , [CHANED_PROPERTY_CURRENT_ID]
    , [CHANED_PROPERTY_PREV_VALUE]
    , [CHANED_PROPERTY_CURRENT_VALUE]
    , [USER_ID]
    , [USER_IP]
    , [SOURCE])
    SELECT CASE
               WHEN (I.AUDIT_STATUS_ID = 1) THEN 2 -- Pending -> Scanned In
               WHEN (I.AUDIT_STATUS_ID = 2) THEN 9 -- Audited -> Audited
               WHEN (I.AUDIT_STATUS_ID = 3) THEN 10 -- Scrap -> Scrap
               WHEN (I.AUDIT_STATUS_ID = 4) THEN 11 -- NotFound -> NotFound
        END                                             AS [ACTION_ID]
         , 9                                            AS [ENTITY_TYPE_ID] -- Receive Inventory
         , I.ITEM_INVENTORY_ID                          AS [ENTITY_KEY_VALUE]
         , I.ITEM_INVENTORY_UNIQUE_ID                   AS [ENTITY_AUTO_NAME]

         , D.AUDIT_STATUS_ID                            AS [CHANED_PROPERTY_PREV_ID]
         , I.AUDIT_STATUS_ID                            AS [CHANED_PROPERTY_CURRENT_ID]
         , IASD.STATUS_NAME                             AS [CHANED_PROPERTY_PREV_VALUE]
         , IASI.STATUS_NAME                             AS [CHANED_PROPERTY_CURRENT_VALUE]

         , isnull(I.MODIFIER_USER_ID, I.AUTHOR_USER_ID) AS [USER_ID]
         , isnull(I.UPDATED_BY_IP, I.INSERTED_BY_IP)    AS [USER_IP]
         , @PROCESS_CD                                  AS [SOURCE]
    FROM INSERTED I
             INNER JOIN DELETED D
                        ON I.ITEM_INVENTORY_ID = D.ITEM_INVENTORY_ID
             LEFT JOIN D_INVENTORY_AUDIT_STATUS IASI WITH (NOLOCK)
                       ON I.AUDIT_STATUS_ID = IASI.STATUS_ID
             LEFT JOIN D_INVENTORY_AUDIT_STATUS IASD WITH (NOLOCK)
                       ON D.AUDIT_STATUS_ID = IASD.STATUS_ID
    WHERE D.AUDIT_STATUS_ID != I.AUDIT_STATUS_ID
      AND I.AUDIT_STATUS_ID IN (1, 2, 3, 4) -- Pending, Audited, Scrap, NotFound

	INSERT INTO [dbo].[F_LOG_ACTION_DATA]	WITH(ROWLOCK)
	(
		[ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_PREV_ID]	   
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_PREV_VALUE]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE]
	)
	SELECT
		[ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_PREV_ID]	   
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_PREV_VALUE]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE]
	FROM @logActionData
	
	-- Logging other Actions had been done in SPs:
	-- trg_ITEM_INVENTORY_AFTER_INSERT				2-Scanned In
	-- trg_SALES_ORDER_SHIPPING_AFTER_UPDATE		4-Shipped
	-- trg_ITEM_AFTER_UPDATE						8-Qualified
	-- trg_PURCHASE_ORDER_ITEM_INVOICE_AFTER_INSERT	14-Invoiced
	-- trg_INVOICE_AFTER_UPDATE						15-Invoiced Voided

END
GO
-- =============================================
-- Author:		Anton Semenov
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_INVENTORY_AFTER_INSERT]
    ON  [dbo].[F_ITEM_INVENTORY]
    AFTER INSERT
AS 
BEGIN									

    DECLARE @TAIL NVARCHAR(4) = N'"';
    DECLARE @DEF  NVARCHAR(4) = N'"-"';

	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	)
    
    INSERT INTO @logData(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
    SELECT
	   'trg_ITEM_INVENTORY_AFTER_INSERT',	   
	   NEW.[USER_ID],
	   NEW.USER_IP,
	   NEW.OPERATION_NAME,
	   9, --N'Receive Inventory',
	   NEW.ENTITY_KEY_VALUE,
	   NEW.ENTITY_AUTO_NAME,	   
	   [dbo].[fn_str_STRIP_XML_TAGS]((SELECT
			NEW.WAS_CREATED
			,NEW.SERIAL
			,NEW.LOCATION_NAME
			,NEW.STATUS_NAME
			,NEW.QTY
			,NEW.ITEM_INVENTORY_UNIT_COST_ORIGINAL
	   FOR XML
	   PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
    FROM
    (
		SELECT
			-- Fixed columns		  
			N'Inserted'														AS OPERATION_NAME
			,I.AUTHOR_USER_ID												AS [USER_ID]
			,I.INSERTED_BY_IP												AS [USER_IP]
			,I.ITEM_INVENTORY_ID											AS ENTITY_KEY_VALUE
			,I.ITEM_INVENTORY_UNIQUE_ID										AS ENTITY_AUTO_NAME
			,N'Was created'													AS WAS_CREATED
		  -- XML columns	
			,N'Serial Number is "' + ISNULL(I.ITEM_INVENTORY_SERIAL, @DEF) + @TAIL			AS SERIAL
			,CASE
				WHEN l.LOCATION_ID IS NOT NULL
				THEN N'Location is "'+ l.LOCATION_NAME + @TAIL
				ELSE N''
			END																									AS LOCATION_NAME
			,N'Qty is "'  + ISNULL(CAST(ITEM_INVENTORY_CHILD_QTY_UNALLOCATED as nvarchar(20)), @DEF) + @TAIL	AS QTY		
			,N'Status is "' + IIS.STATUS_CD	+ @TAIL																AS STATUS_NAME
			,N'Recv price is "' + cast(I.ITEM_INVENTORY_UNIT_COST_ORIGINAL as nvarchar(40))	+ @TAIL				AS ITEM_INVENTORY_UNIT_COST_ORIGINAL
		
		FROM		INSERTED								I
		LEFT JOIN F_LOCATION								L	WITH (NOLOCK)
			ON I.LOCATION_ID = l.LOCATION_ID
		LEFT JOIN [dbo].[F_PURCHASE_ORDER_ITEM]				POI	WITH (NOLOCK)
			ON POI.INVENTORY_ITEM_ID = I.ITEM_INVENTORY_ID
		LEFT JOIN [dbo].[F_PURCHASE_ORDER]					PO	WITH (NOLOCK)
			ON POI.PURCHASE_ORDER_ID = PO.PURCHASE_ORDER_ID
		LEFT JOIN [dbo].[D_ITEM_INVENTORY_STATUS]			IIS	WITH (NOLOCK)
			ON I.ITEM_STATUS_ID	= IIS.ITEM_INVENTORY_STATUS_ID
    ) NEW


	INSERT INTO [dbo].[F_LOG_DATA] WITH(ROWLOCK)
	(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
	SELECT
		[SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]
	FROM @logData


	INSERT INTO [dbo].[F_LOG_ACTION_DATA] WITH(ROWLOCK)
       ([ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE])
    SELECT
		 2									AS [ACTION_ID]		-- Scanned In
	    ,9									AS [ENTITY_TYPE_ID]	-- Receive Inventory
		,I.ITEM_INVENTORY_ID				AS [ENTITY_KEY_VALUE]
		,I.ITEM_INVENTORY_UNIQUE_ID			AS [ENTITY_AUTO_NAME]
		,I.LOCATION_ID						AS [CHANED_PROPERTY_CURRENT_ID]
		,FL.LOCATION_NAME					AS [CHANED_PROPERTY_CURRENT_VALUE]
		,I.AUTHOR_USER_ID					AS [USER_ID]
		,I.INSERTED_BY_IP					AS [USER_IP]
		,'trg_ITEM_INVENTORY_AFTER_INSERT'	AS [SOURCE]
	FROM INSERTED	I
	LEFT JOIN F_LOCATION	FL WITH(NOLOCK)
		ON I.LOCATION_ID = FL.LOCATION_ID
END
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'the item inventory is stores here with information. The inventory is the separate device which you receive in the system. Then these inventory are grouped and inventory with the same model, condition, SKU effecting capabilities are built into SKU (F_ITEM) table. So e.g. all good iphone 7 in good condition will have SKU = 100.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY';
GO

-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_INVENTORY_PUT_CHANGE]
    ON  [dbo].[F_ITEM_INVENTORY]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_INVENTORY_ID, d.ITEM_INVENTORY_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_INVENTORY_ID = d.ITEM_INVENTORY_ID	
	where isnull(i.ITEM_INVENTORY_SERIAL, '')					<> isnull(d.ITEM_INVENTORY_SERIAL, '')
		or isnull(i.ITEM_INVENTORY_UNIQUE_ID, '')				<> isnull(d.ITEM_INVENTORY_UNIQUE_ID, '')
		or isnull(i.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0)		<> isnull(d.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0)
		or isnull(i.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 0)	<> isnull(d.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 0)
		or isnull(i.ITEM_INVENTORY_NOTES, '')					<> isnull(d.ITEM_INVENTORY_NOTES, '')
		or isnull(i.[WEIGHT], 0)								<> isnull(d.[WEIGHT], 0)
		or isnull(i.IS_CONSIGNMENT, 0)							<> isnull(d.IS_CONSIGNMENT, 0)
		or isnull(i.INSERTED_DT, '1/1/1900')					<> isnull(d.INSERTED_DT, '1/1/1900')
		or isnull(i.IS_DELETED, 0)								<> isnull(d.IS_DELETED, 0)
		or isnull(i.ITEM_MASTER_ID, 0)							<> isnull(d.ITEM_MASTER_ID, 0)
		or isnull(i.ITEM_STATUS_ID, 0)							<> isnull(d.ITEM_STATUS_ID, 0)
		or isnull(i.CONDITION_ID, 0)							<> isnull(d.CONDITION_ID, 0)
		or isnull(i.ITEM_ID, 0)									<> isnull(d.ITEM_ID, 0)
		or isnull(i.LOCATION_ID, 0)								<> isnull(d.LOCATION_ID, 0)
		or isnull(i.CUSTOMER_ID, 0)								<> isnull(d.CUSTOMER_ID, 0)
		or isnull(i.AUDIT_STATUS_ID, 0)							<> isnull(d.AUDIT_STATUS_ID, 0)
		or isnull(i.SalesOrderItemId, 0)						<> isnull(d.SalesOrderItemId, 0)
		or isnull(i.AssetId, 0)									<> isnull(d.AssetId, 0)
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 1 -- Inventory
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO

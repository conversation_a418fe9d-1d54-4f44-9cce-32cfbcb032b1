create procedure [dbo].[sp_AddBoxProgramAssetTransaction]
	@AssetId					bigint,
	@AssetWorkflowStepId		bigint			= null,
	@PrevAssetWorkflowStepId	bigint			= null,
	@UserId						bigint			= null,
	@UserIp						bigint			= 0,
	@OuterSpName				nvarchar(200)	= null
as
begin

	if (@AssetWorkflowStepId is null)
	begin

		select top(1) @AssetWorkflowStepId = A.AssetWorkflowStepId
		from [recycling].[F_Asset] as A with(nolock)
		where A.[Id] = @AssetId;

	end

	declare @isRedeployment bit = recycling.fn_bit_IsAssetWorkflowStepRedeployment(@AssetWorkflowStepId);
	declare @wasRedeployment bit = recycling.fn_bit_IsAssetWorkflowStepRedeployment(@PrevAssetWorkflowStepId);

	if (@isRedeployment = 0 and @wasRedeployment = 0 and @AssetWorkflowStepId != 102 and @AssetWorkflowStepId != 103) -- Not Resale or Scrap
		return;

	declare @spName		nvarchar(200)	= isnull(object_schema_name(@@procid) + N'.', N'') + object_name(@@procid) + isnull(N' (' + @OuterSpName + N')', N'');
	declare @utcNow		datetime		= getutcdate();

	declare @customerId bigint;
	declare @recyclingOrderId bigint;
	declare @uniqueId varchar(250);
	declare @categoryName varchar(250);
	declare @conditionId bigint;
	declare @qty int;
	declare @weight float;

	select top(1)
		@uniqueId = A.UniqueId,
		@categoryName = coalesce(DCH.CATEGORYNAME, A.CategoryName, ''),
		@conditionId = A.[ConditionId],
		@qty = A.[Quantity],
		@weight = A.[Weight],
		@recyclingOrderId = LOT.[RECYCLING_ORDER_ID],
		@customerId = RO.[CUSTOMER_ID]
	from [recycling].[F_Asset]					as A with(nolock)
	left join [dbo].[F_RECYCLING_ORDER_ITEM]	as LOT with(nolock)
		on A.[RecyclingOrderItemId] = LOT.[RECYCLING_ORDER_ITEM_ID]
	left join [dbo].[F_RECYCLING_ORDER]			as RO with(nolock)
		on LOT.[RECYCLING_ORDER_ID] = RO.[RECYCLING_ORDER_ID]
	left join [dbo].[D_CATEGORY_HIERARCHY]		as DCH with(nolock)
		on A.CategoryId = DCH.CATEGORY_ID
	where A.[Id] = @AssetId
		and A.[IsInactive] = 0
		and A.[IsDeleted] = 0;

	if (@customerId is null)
		return;

	declare @isBoxProgramActive bit;

	select top(1) @isBoxProgramActive = [IsActive]
	from [dbo].[D_BoxProgramCustomerSettings] with(nolock)
	where [CustomerId] = @customerId;

	if (isnull(@isBoxProgramActive, 0) = 0)
		return; -- The Box Program is not enabled for the customer

	declare @requestId bigint;
	declare @remarketingRequestId bigint;

	select top(1) @requestId = [TakebackOrderRequestId]
	from [dbo].[F_RECYCLING_ORDER_INBOUND] with(nolock)
	where [RECYCLING_ORDER_ID] = @RecyclingOrderId;

	declare @auditFee float = 0;
	declare @erasureFee float = 0;
	declare @destructionFee float = 0;
	declare @remarketingFee float = 0;
	declare @scrapFee float = 0;
	declare @auditScrapFee float = 0;
	declare @resaleFee float = 0;

	declare @scrapBillingName nvarchar(1024);
	declare @resaleBillingName nvarchar(1024);

	select top(1)
		@auditFee = [AuditFee],
		@erasureFee = [ErasureFee],
		@destructionFee = [DestructionFee],
		@remarketingFee = [RemarketingFee],
		@scrapFee = [ScrapFee] * (
			case [ScrapPriceTypeId]
			when 1 then isnull(@weight, 0)
			when 2 then isnull(@qty, 1)
			else 1
			end
		),
		@auditScrapFee = [AuditScrapFee],
		@scrapBillingName = [ScrapBillingName],
		@resaleFee = [ResaleFee],
		@resaleBillingName = [ResaleBillingName]
	from [dbo].[D_BoxProgramFees] with(nolock)
	where [CustomerId] = @customerId;

	if (@isRedeployment = 0)
	begin

		if (@AssetWorkflowStepId = 103) -- Scrap
		begin

			set @remarketingRequestId = [recycling].[fn_bigint_GetAssetRemarketingRequestId](@AssetId);
			declare @chargeTypeId bigint = iif(@remarketingRequestId is not null,
				16,	-- Scrap Fee
				18); -- Audit Scrap fee

			if (not exists(
				select top(1) 1
				from [dbo].[F_BoxProgramTransactionLog] as BPT with(nolock)
				where BPT.[AssetId] = @AssetId
				and BPT.[ChargeTypeId] = @chargeTypeId
			))
			begin

				insert into [dbo].[F_BoxProgramTransactionLog] with(rowlock) (
					[CustomerId],
					[ChargeTypeId],
					[Amount],
					[Description],
					[RequestId],
					[AssetId],
					[InsertedBy],
					[InsertedDate],
					[InsertedByUserId],
					[InsertedByUserIp]
				) values (
					@customerId,
					@chargeTypeId,
					iif(@remarketingRequestId is not null, @scrapFee, @auditScrapFee),
					isnull(@scrapBillingName, N'Obsolesence Fee - Scrap') + N' for ' + @uniqueId + N' ' + @categoryName,
					isnull(@remarketingRequestId, @requestId),
					@AssetId,
					@spName,
					@utcNow,
					@UserId,
					@UserIp
				);

			end

			return;

		end

		if (@AssetWorkflowStepId = 102) -- Resale
		begin

			if (not exists(
				select top(1) 1
				from [dbo].[F_BoxProgramTransactionLog] as BPT with(nolock)
				where BPT.[AssetId] = @AssetId
				and BPT.[ChargeTypeId] = 17	-- Resale Fee
			))
			begin

				set @remarketingRequestId = [recycling].[fn_bigint_GetAssetRemarketingRequestId](@AssetId);

				if (@remarketingRequestId is not null)
				begin

					insert into [dbo].[F_BoxProgramTransactionLog] with(rowlock) (
						[CustomerId],
						[ChargeTypeId],
						[Amount],
						[Description],
						[RequestId],
						[AssetId],
						[InsertedBy],
						[InsertedDate],
						[InsertedByUserId],
						[InsertedByUserIp]
					) values (
						@customerId,
						17,	-- Resale Fee
						@resaleFee,
						isnull(@resaleBillingName, N'Obsolesence Fee - Resale') + N' for ' + @uniqueId + N' ' + @categoryName,
						@remarketingRequestId,
						@AssetId,
						@spName,
						@utcNow,
						@UserId,
						@UserIp
					);

				end
				-- else skip Resale Fee if there were no Remarketing Request

			end

			return;

		end

		if (@wasRedeployment = 1)
		begin

			if (not exists(
				select top(1) 1
				from [dbo].[F_BoxProgramTransactionLog] as BPT with(nolock)
				where BPT.[AssetId] = @AssetId
				and BPT.[ChargeTypeId] = 14	-- Remarketing Fee
			))
			begin

				insert into [dbo].[F_BoxProgramTransactionLog] with(rowlock) (
					[CustomerId],
					[ChargeTypeId],
					[Amount],
					[Description],
					[RequestId],
					[AssetId],
					[InsertedBy],
					[InsertedDate],
					[InsertedByUserId],
					[InsertedByUserIp]
				) values (
					@customerId,
					14,	-- Remarketing Fee
					@remarketingFee,
					'Remarketing fee for ' + @uniqueId + N' ' + @categoryName,
					isnull([recycling].[fn_bigint_GetAssetRemarketingRequestId](@AssetId), @requestId),
					@AssetId,
					@spName,
					@utcNow,
					@UserId,
					@UserIp
				);

			end

		end

		return;

	end

	-- Use audit fee per condition if any
	select @AuditFee = BPRF.[Fee]
	from [dbo].[D_BoxProgramRangeFees] as BPRF with(nolock)
	where BPRF.[CustomerId] = @customerId
		and BPRF.[FeeType] = 3 -- Audit Fee
		and BPRF.[ConditionId] = @conditionId;

	declare @conditionName nvarchar(50) = N'';
	select top(1) @conditionName = [ITEM_CONDITION_CD]
	from [dbo].[D_ITEM_CONDITION] with(nolock)
	where [ITEM_CONDITION_ID] = @conditionId;

	;with m_target as (
		select BPTL.*
		from [dbo].[F_BoxProgramTransactionLog] as BPTL with(rowlock)
		where BPTL.[AssetId] = @AssetId
			and BPTL.[ChargeTypeId] in (11, 12, 13) -- Audit Fee, Erasure Fee, Destruction Fee
	)
	merge m_target as T
	using (
		select
			11						as [ChargeTypeId], -- Audit Fee
			@AuditFee				as [Amount],
			N'Audit fee for ' + @uniqueId + N' ' + @categoryName + N' ' + @conditionName as [Description],
			null					as [DataDestructionId]
		union all
		select
			12						as [ChargeTypeId], -- Erasure Fee
			isnull(DEF.[Fee], @erasureFee)	as [Amount],
			N'Erasure fee' + isnull(N' for ' + nullif(rtrim(ltrim(isnull(ST.[SERVICE_TYPE_CD], DE.[ErasureMethodName]))), N''), N'')
				+ ' HDD' + isnull(N' Serial ' + nullif(DE.[HddSerial], N''), N'') + N' of ' + @uniqueId as [Description],
			DE.[Id]					as [DataDestructionId]
		from [recycling].[F_AssetDataDestruction]	DE with(nolock)
		left join [dbo].[D_BoxProgramRangeFees]		DEF with(nolock)
			on DEF.[CustomerId] = @customerId
			and DEF.[FeeType] = 4 --  Data Erasure Fee
			and DEF.[ServiceId] = DE.[ErasureMethodId]
		left join [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] as ST
			on DE.[ErasureMethodId] = ST.[SERVICE_TYPE_ID]
		where DE.[AssetId] = @AssetId
			--and DE.[ErasureMethodId] is not null
			and isnull(DE.[ErasureApplyResult], 0) > 0
		union all
		select
			13						as [ChargeTypeId], -- Destruction Fee
			isnull(DDF.[Fee], @destructionFee)	as [Amount],
			N'Destruction fee' + isnull(N' for ' + nullif(rtrim(ltrim(isnull(ST.[SERVICE_TYPE_CD], DD.[DestructionMethodName]))), N''), N'')
				+ ' HDD' + isnull(N' Serial ' + nullif(DD.[HddSerial], N''), N'') + N' of ' + @uniqueId as [Description],
			DD.[Id]					as [DataDestructionId]
		from [recycling].[F_AssetDataDestruction]	DD with(nolock)
		left join [dbo].[D_BoxProgramRangeFees]		DDF with(nolock)
			on DDF.[CustomerId] = @customerId
			and DDF.[FeeType] = 5 --  Data Destruction Fee
			and DDF.[ServiceId] = DD.[DestructionMethodId]
		left join [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] as ST
			on DD.[DestructionMethodId] = ST.[SERVICE_TYPE_ID]
		where DD.[AssetId] = @AssetId
			and DD.[DestructionMethodId] is not null
	) as S
		on T.[ChargeTypeId] = S.[ChargeTypeId]
		and isnull(T.[DataDestructionId], 0) = isnull(S.[DataDestructionId], 0)
	when not matched by target then insert (
		[CustomerId],
		[ChargeTypeId],
		[Amount],
		[Description],
		[RequestId],
		[AssetId],
		[DataDestructionId],
		[InsertedBy],
		[InsertedDate],
		[InsertedByUserId],
		[InsertedByUserIp]
	) values (
		@customerId,
		S.[ChargeTypeId],
		S.[Amount],
		S.[Description],
		@requestId,
		@AssetId,
		S.[DataDestructionId],
		@spName,
		@utcNow,
		@UserId,
		@UserIp
	);


	;with m_target as (
		select BPTL.*
		from [dbo].[F_BoxProgramTransactionLog] as BPTL with(rowlock)
		where BPTL.[AssetId] = @AssetId
			and BPTL.[ChargeTypeId] = 15 -- Service Fee
	)
	merge m_target as T
	using (
		select
			ASVC.[Id],
			IOSVC.[ChargeValue],
			N'Fee for ' + SVC.[Name] + N' service for ' + @uniqueId as [Description]
		from [recycling].[F_AssetPricingServicePosition]		as ASVC with(nolock)
		left join [dbo].[F_OrderServiceTemplatePositionValue]	as IOSVC with(nolock)
			on ASVC.[SubcategoryPositionValueId] = IOSVC.[Id]
		left join [dbo].[D_ServiceSubcategoryPosition]			as SVC with(nolock)
			on IOSVC.[ServiceSubcategoryPositionId] = SVC.[Id]
		where ASVC.[AssetId] = @AssetId
			and ASVC.IsDeleted = 0
	) as S
		on T.[ServiceId] = S.[Id]
	when not matched by target then insert (
		[CustomerId],
		[ChargeTypeId],
		[Amount],
		[Description],
		[RequestId],
		[AssetId],
		[ServiceId],
		[InsertedBy],
		[InsertedDate],
		[InsertedByUserId],
		[InsertedByUserIp]
	) values (
		@customerId,
		15, -- Service Fee
		S.[ChargeValue],
		S.[Description],
		@requestId,
		@AssetId,
		S.[Id],
		@spName,
		@utcNow,
		@UserId,
		@UserIp
	);

end

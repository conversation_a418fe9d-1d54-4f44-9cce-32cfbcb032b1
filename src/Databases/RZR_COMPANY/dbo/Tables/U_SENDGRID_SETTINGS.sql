CREATE TABLE [dbo].[U_SENDGRID_SETTINGS] (
    [SENDGRID_SETTING_ID]        BIGINT         IDENTITY (1, 1) NOT NULL,
    [USER_ID]                    BIGINT         NULL,
    [SENDGRID_APIKEY]            NVARCHAR (256) COLLATE Latin1_General_CS_AS NULL,
    [SENDGRID_DEFAULT_SEND_FROM] NVARCHAR (256) COLLATE Latin1_General_CS_AS NULL,
    [IS_INACTIVE]                BIT            CONSTRAINT [DF_U_SENDGRID_SETTINGS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                NVARCHAR (128) NOT NULL,
    [INSERTED_DT]                DATETIME       NOT NULL,
    [UPDATED_BY]                 NVARCHAR (128) NULL,
    [UPDATED_DT]                 DATETIME       NULL,
    CONSTRAINT [PK_U_SENDGRID_SETTINGS] PRIMARY KEY CLUSTERED ([SENDGRID_SETTING_ID] ASC),
    CONSTRAINT [FK_U_SENDGRID_SETTINGS_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE ON UPDATE CASCADE
);

GO
CREATE NONCLUSTERED INDEX IX__U_SENDGRID_SETTINGS__ID__USER_ID__INCLUDE ON U_SENDGRID_SETTINGS ([USER_ID] ASC, IS_INACTIVE ASC)
INCLUDE (SENDGRID_DEFAULT_SEND_FROM, SENDGRID_APIKEY)

GO
CREATE UNIQUE NONCLUSTERED INDEX [IX__U_SENDGRID_SETTINGS_IS_INACTIVE]
    ON [dbo].[U_SENDGRID_SETTINGS]([SENDGRID_SETTING_ID] ASC)
    WHERE [USER_ID] IS NULL AND [IS_INACTIVE] = 0;
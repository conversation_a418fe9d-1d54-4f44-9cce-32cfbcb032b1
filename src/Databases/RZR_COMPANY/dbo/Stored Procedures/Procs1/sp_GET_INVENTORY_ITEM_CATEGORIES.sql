 
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_CATEGORIES] 
	@ITEM_INVENTORY_ID  BIGINT
AS
BEGIN
	SELECT 
		vw.CategoryId					AS CATEGORY_ID,
		vw.CategoryFullPathToDisplay	AS CATEGORY_NAME,
		vw.CategoryIsPrimary			AS IS_PRIMARY
	FROM F_ITEM_INVENTORY											fi	WITH (NOLOCK)
	INNER JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY]	vw	WITH(NOLOCK)
		ON vw.ItemMasterId		= FI.ITEM_MASTER_ID
	WHERE fi.ITEM_INVENTORY_ID = @ITEM_INVENTORY_ID 	  
END
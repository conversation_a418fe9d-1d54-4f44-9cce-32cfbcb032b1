CREATE TABLE [dbo].[F_ITEM_MASTER_BILL_OF_MATERIAL] (
    [ITEM_MASTER_BILL_OF_MATERIAL_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_PARENT_ID]           BIGINT        NOT NULL,
    [ITEM_MASTER_CHILD_ID]            BIGINT        NOT NULL,
    [QTY]                             INT           CONSTRAINT [DF_F_ITEM_MASTER_BILL_OF_MATERIAL_QTY] DEFAULT ((0)) NOT NULL,
    [COST_PERCENTAGE]                 FLOAT (53)    CONSTRAINT [DF_F_ITEM_MASTER_BILL_OF_MATERIAL_COST_PERCENTAGE] DEFAULT ((0)) NULL,
    [INSERTED_BY]                     VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_BILL_OF_MATERIAL_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                     DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_BILL_OF_MATERIAL_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                      VARCHAR (150) NULL,
    [UPDATED_DT]                      DATETIME      NULL,
    [DELETED_BY]                      VARCHAR (150) NULL,
    [DELETED_DT]                      DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_BILL_OF_MATERIAL] PRIMARY KEY CLUSTERED ([ITEM_MASTER_BILL_OF_MATERIAL_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_BILL_OF_MATERIAL_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_PARENT_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_BILL_OF_MATERIAL_F_ITEM_MASTER1] FOREIGN KEY ([ITEM_MASTER_CHILD_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID])
);








GO
CREATE NONCLUSTERED INDEX [IX_MASTER_PARENT_ID]
    ON [dbo].[F_ITEM_MASTER_BILL_OF_MATERIAL]([ITEM_MASTER_PARENT_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MASTER_CHILD_ID]
    ON [dbo].[F_ITEM_MASTER_BILL_OF_MATERIAL]([ITEM_MASTER_CHILD_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'For parent model and the list of child models with qty for each cild and percent which it takes from the parent model price. ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_BILL_OF_MATERIAL';


-- Create Procedure sp_LOG_ACCOUNT_SALE
-- Create Procedure sp_LOG_ACCOUNT_SALE
-- Alter Procedure sp_LOG_ACCOUNT_SALE
-- =============================================
-- Author:		Oleg K. Evseev
-- Create date: 06/04/2013
-- Description:	Logs a Sale transaction
-- =============================================
CREATE PROCEDURE [dbo].[sp_LOG_ACCOUNT_SALE]
	@SALES_ORDER_ID BIGINT
AS
BEGIN
	DECLARE @ERROR NVARCHAR(500) = N''
	IF (@SALES_ORDER_ID IS NULL OR @SALES_ORDER_ID <= 0)
	BEGIN
		SET @ERROR = N'Sales order Id is undefined'
		SELECT @ERROR AS [ERROR]
		RETURN;
	END

	IF (NOT EXISTS(SELECT SALES_ORDER_ID FROM F_SALES_ORDER WHERE SALES_ORDER_ID = @SALES_ORDER_ID))
	BEGIN
		SET @ERROR = N'Sales order does not exist'
		SELECT @ERROR AS [ERROR]
		RETURN;
	END

	IF (NOT EXISTS(SELECT INVOICE_ID FROM F_INVOICE WHERE INVOICE_TYPE_ID = 1 AND ORDER_ID = @SALES_ORDER_ID))
	BEGIN
		SET @ERROR = N'Sales order does not have an invoice'
		SELECT @ERROR AS [ERROR]
		RETURN;
	END

	DECLARE
		@DATE					DATETIME = GETUTCDATE()
		,@spName				VARCHAR (150)= 'sp_LOG_ACCOUNT_SALE'
		,@CUSTOMER_ID			BIGINT = NULL
		,@INVOICE_ID			BIGINT = NULL

		-- SALE transaction
		,@TRANSACTION_TYPE_ID	INT		= 3	
		,@TRANSACTION_ID		BIGINT	= NULL
		,@TRANSACTION_NAME		NVARCHAR(1024) = N''
				
		,@ASSET_TYPE_ID			INT		= 3 -- INVENTORY ASSET = Other Current Asset
		,@RECIEVABLE_TYPE_ID	INT		= 2 -- RECIEVABLE = Accounts Receivable
		,@COGS_TYPE_ID			INT		= 9 -- COGS = Cost of Goods Sold
		,@INCOME_TYPE_ID		INT		= 8; -- INCOME


	DECLARE
		@ASSET_ACCOUNT_ID		BIGINT	= (
			SELECT TOP(1)
				ACCOUNT_ID
			FROM F_ACCOUNT WITH (NOLOCK)
			WHERE ACCOUNT_TYPE_ID = @ASSET_TYPE_ID
				AND IS_DEFAULT_FOR_TYPE = 1)

		,@COGS_ACCOUNT_ID		BIGINT	= (
			SELECT TOP(1)
				ACCOUNT_ID
			FROM F_ACCOUNT WITH (NOLOCK)
			WHERE ACCOUNT_TYPE_ID = @COGS_TYPE_ID
				AND IS_DEFAULT_FOR_TYPE = 1)

		,@RECIEVABLE_ACCOUNT_ID BIGINT	= (
			SELECT TOP(1)
				ACCOUNT_ID
			FROM F_ACCOUNT WITH (NOLOCK)
			WHERE ACCOUNT_TYPE_ID = @RECIEVABLE_TYPE_ID
				AND IS_DEFAULT_FOR_TYPE = 1)

		,@INCOME_ACCOUNT_ID		BIGINT	= (
			SELECT TOP(1)
				ACCOUNT_ID
			FROM F_ACCOUNT WITH (NOLOCK)
			WHERE ACCOUNT_TYPE_ID = @INCOME_TYPE_ID
				AND IS_DEFAULT_FOR_TYPE = 1
		);

	-- Get sales order name
	SELECT
		@TRANSACTION_NAME = N'Sale '+ O.SALES_ORDER_NUMBER + N' ' + C.CUSTOMER_NAME
		,@CUSTOMER_ID	  = O.CUSTOMER_ID
		,@INVOICE_ID	  = I.INVOICE_ID
	FROM F_SALES_ORDER		O	WITH (NOLOCK)
	LEFT JOIN F_CUSTOMER	C	WITH(NOLOCK)
		ON O.CUSTOMER_ID = C.CUSTOMER_ID
	LEFT JOIN F_INVOICE		I	WITH(NOLOCK)
		ON I.INVOICE_TYPE_ID = 1
		AND O.SALES_ORDER_ID = I.ORDER_ID
		AND I.IS_VOIDED		 = 0
	WHERE O.SALES_ORDER_ID = @SALES_ORDER_ID;

	BEGIN TRY
		-- Collect actual inventory ids of items sold ---------
		DECLARE @T_INVENTORY_IDS TABLE(ID BIGINT, SALES_ORDER_ITEM_ID BIGINT NULL);
		INSERT INTO @T_INVENTORY_IDS(ID, SALES_ORDER_ITEM_ID)
		SELECT
			 SI.ITEM_INVENTORY_ID
			,SI.SALES_ORDER_ITEM_ID
		FROM F_SALES_ORDER_ITEM SI
		WHERE SI.SALES_ORDER_ID = @SALES_ORDER_ID
			AND SI.ITEM_INVENTORY_ID IS NOT NULL
			AND SI.ITEM_QUANTITY = 1;

		INSERT INTO @T_INVENTORY_IDS(ID)
		SELECT
			FIIAP.PART_ITEM_INVENTORY_ID
		FROM F_ITEM_INVENTORY_ADDED_PART	FIIAP
		INNER JOIN @T_INVENTORY_IDS			TII
			ON TII.ID = FIIAP.PARENT_ITEM_INVENTORY_ID;
		-------------------------------------------------------

		-- Add Transaction
		INSERT INTO F_ACCOUNT_TRANSACTION (
			ACCOUNT_TRANSACTION_TYPE_ID
			,ACCOUNT_TRANSACTION_NAME
			,ACCOUNT_TRANSACTION_DATE
			,CUSTOMER_ID
			,INVOICE_ID
			,INSERTED_DT
			,INSERTED_BY)
		VALUES (
			@TRANSACTION_TYPE_ID
			,@TRANSACTION_NAME
			,@DATE
			,@CUSTOMER_ID
			,@INVOICE_ID
			,@DATE
			,@spName);
		SET @TRANSACTION_ID = SCOPE_IDENTITY();
		
		-- ADD TRANSACTION ITEMS
		-- 1: inventory Asset DECREASES
		INSERT INTO F_ACCOUNT_TRANSACTION_ITEM (
			ACCOUNT_TRANSACTION_ID
			,ITEM_INVENTORY_ID
			,ITEM_MASTER_ID
			,COST
			,ACCOUNT_ID
			,ACCOUNT_TYPE_ID
			,INSERTED_DT
			,INSERTED_BY
		)
		SELECT DISTINCT
			@TRANSACTION_ID
			,I.ITEM_INVENTORY_ID
			,I.ITEM_MASTER_ID
			,CASE
				WHEN I.ITEM_INVENTORY_UNIT_COST_ORIGINAL IS NULL THEN 0
				ELSE (-1 * I.ITEM_INVENTORY_UNIT_COST_ORIGINAL)
				END
			,CASE 
				WHEN A.ACCOUNT_ID IS NULL THEN @ASSET_ACCOUNT_ID
				ELSE A.ACCOUNT_ID
			END
			,@COGS_TYPE_ID
			,@DATE
			,@spName
		FROM @T_INVENTORY_IDS					TII
		INNER JOIN F_ITEM_INVENTORY				I	WITH(NOLOCK)
			ON TII.ID = I.ITEM_INVENTORY_ID
		INNER JOIN [dbo].[F_PRODUCT_MASTER]		PM	WITH (NOLOCK)
			ON PM.PRODUCT_MASTER_TYPE_ID = 1
			AND I.ITEM_MASTER_ID = PM.ITEM_MASTER_ID
		LEFT JOIN dbo.F_PRODUCT_MASTER_ACCOUNT	L	WITH(NOLOCK)
			ON PM.PRODUCT_MASTER_ID = L.PRODUCT_MASTER_ID
			AND L.PRODUCT_MASTER_ACCOUNT_TYPE_ID = @ASSET_TYPE_ID
		LEFT JOIN F_ACCOUNT						A	WITH(NOLOCK)
			ON L.ACCOUNT_ID = A.ACCOUNT_ID
			AND A.ACCOUNT_TYPE_ID = @ASSET_TYPE_ID -- ASSET account of inventory


		-- 2: inventory COGS INCREASES
		INSERT INTO F_ACCOUNT_TRANSACTION_ITEM (
			ACCOUNT_TRANSACTION_ID
			,ITEM_INVENTORY_ID
			,ITEM_MASTER_ID
			,COST
			,ACCOUNT_ID
			,ACCOUNT_TYPE_ID
			,INSERTED_DT
			,INSERTED_BY
		)
		SELECT DISTINCT
			@TRANSACTION_ID
			,I.ITEM_INVENTORY_ID
			,I.ITEM_MASTER_ID
			,ISNULL(I.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0)
			,ISNULL(A.ACCOUNT_ID, @COGS_ACCOUNT_ID)
			,@COGS_TYPE_ID
			,@DATE
			,@spName
		FROM @T_INVENTORY_IDS						TII
		INNER JOIN F_ITEM_INVENTORY					I		WITH(NOLOCK)
			ON TII.ID = I.ITEM_INVENTORY_ID
		INNER JOIN [dbo].[F_PRODUCT_MASTER]			PM		WITH(NOLOCK)
			ON PM.PRODUCT_MASTER_TYPE_ID = 1
			AND I.ITEM_MASTER_ID = PM.ITEM_MASTER_ID
		LEFT JOIN dbo.F_PRODUCT_MASTER_ACCOUNT		L		WITH(NOLOCK)
			ON PM.PRODUCT_MASTER_ID = L.PRODUCT_MASTER_ID
			AND L.PRODUCT_MASTER_ACCOUNT_TYPE_ID = @COGS_TYPE_ID
		LEFT JOIN [dbo].[F_ITEM_MASTER_CATEGORY]	FIMC	WITH(NOLOCK)
			ON FIMC.[ITEM_MASTER_ID] = I.[ITEM_MASTER_ID]
			AND FIMC.[IS_PRIMARY] = 1
			AND FIMC.[IS_INACTIVE] = 0
			AND FIMC.[IS_DELETED] = 0
		LEFT JOIN [dbo].[F_CategoryAccount]			FCA		WITH(NOLOCK)
			ON FCA.[CategoryId] = FIMC.[CATEGORY_ID]
			AND FCA.[AccountTypeId] = @COGS_TYPE_ID
		LEFT JOIN F_ACCOUNT							A		WITH(NOLOCK)
			ON A.ACCOUNT_ID = ISNULL(L.ACCOUNT_ID, FCA.[AccountId])
			AND A.ACCOUNT_TYPE_ID = @COGS_TYPE_ID; -- COGS account of inventory

		-- 3: general Recievable INCREASES
		INSERT INTO F_ACCOUNT_TRANSACTION_ITEM (
			ACCOUNT_TRANSACTION_ID
			,ITEM_INVENTORY_ID
			,ITEM_MASTER_ID
			,COST
			,ACCOUNT_ID
			,ACCOUNT_TYPE_ID
			,INSERTED_DT
			,INSERTED_BY
		)
		SELECT DISTINCT
			@TRANSACTION_ID
			,I.ITEM_INVENTORY_ID
			,I.ITEM_MASTER_ID
			,CASE 
				WHEN SI.ITEM_PRICE IS NULL THEN 0
				ELSE SI.ITEM_PRICE
			END
			,@RECIEVABLE_ACCOUNT_ID
			,@RECIEVABLE_TYPE_ID
			,@DATE
			,@spName
		FROM @T_INVENTORY_IDS					TII
		INNER JOIN F_ITEM_INVENTORY				I	WITH(NOLOCK)
			ON TII.ID = I.ITEM_INVENTORY_ID
		LEFT JOIN F_SALES_ORDER_ITEM			SI	WITH(NOLOCK)
			ON SI.SALES_ORDER_ITEM_ID = TII.SALES_ORDER_ITEM_ID
		
		-- 4: general Income INCREASES
		INSERT INTO F_ACCOUNT_TRANSACTION_ITEM (
			ACCOUNT_TRANSACTION_ID
			,ITEM_INVENTORY_ID
			,ITEM_MASTER_ID
			,COST
			,ACCOUNT_ID
			,ACCOUNT_TYPE_ID
			,INSERTED_DT
			,INSERTED_BY
		)
		SELECT DISTINCT
			@TRANSACTION_ID
			,I.ITEM_INVENTORY_ID
			,I.ITEM_MASTER_ID
			,ISNULL(SI.ITEM_PRICE, 0)
			,ISNULL(A.ACCOUNT_ID, @INCOME_ACCOUNT_ID)
			,@INCOME_TYPE_ID
			,@DATE
			,@spName
		FROM @T_INVENTORY_IDS						TII
		INNER JOIN F_ITEM_INVENTORY					I	WITH(NOLOCK)
			ON TII.ID = I.ITEM_INVENTORY_ID
		INNER JOIN [dbo].[F_PRODUCT_MASTER]			PM	WITH (NOLOCK)
			ON PM.PRODUCT_MASTER_TYPE_ID = 1
			AND I.ITEM_MASTER_ID = PM.ITEM_MASTER_ID
		LEFT JOIN dbo.F_PRODUCT_MASTER_ACCOUNT		L	WITH(NOLOCK)
			ON PM.PRODUCT_MASTER_ID = L.PRODUCT_MASTER_ID
			AND L.PRODUCT_MASTER_ACCOUNT_TYPE_ID = @INCOME_TYPE_ID
		LEFT JOIN [dbo].[F_ITEM_MASTER_CATEGORY]	FIMC	WITH(NOLOCK)
			ON FIMC.[ITEM_MASTER_ID] = I.[ITEM_MASTER_ID]
			AND FIMC.[IS_PRIMARY] = 1
			AND FIMC.[IS_INACTIVE] = 0
			AND FIMC.[IS_DELETED] = 0
		LEFT JOIN [dbo].[F_CategoryAccount]			FCA		WITH(NOLOCK)
			ON FCA.[CategoryId] = FIMC.[CATEGORY_ID]
			AND FCA.[AccountTypeId] = @INCOME_TYPE_ID
		LEFT JOIN F_ACCOUNT							A	WITH(NOLOCK)
			ON A.ACCOUNT_ID = ISNULL(L.ACCOUNT_ID, FCA.[AccountId])
			AND A.ACCOUNT_TYPE_ID = @INCOME_TYPE_ID -- Income account of inventory
		LEFT JOIN F_SALES_ORDER_ITEM			SI	WITH(NOLOCK)
			ON SI.SALES_ORDER_ITEM_ID = TII.SALES_ORDER_ITEM_ID;

	END TRY
	BEGIN CATCH
		DECLARE @code INT
		SELECT 
			@code = ERROR_NUMBER(),
			@ERROR = ERROR_MESSAGE();
		SET @ERROR = N'Exception '+ convert(varchar(20), @code) + N': '+ @ERROR
		SELECT @ERROR as [ERROR]
	END CATCH
END
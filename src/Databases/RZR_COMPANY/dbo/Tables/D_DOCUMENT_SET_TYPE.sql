CREATE TABLE [dbo].[D_DOCUMENT_SET_TYPE] (
    [DOCUMENT_SET_TYPE_ID] BIGINT          IDENTITY (1, 1) NOT NULL,
    [DOCUMENT_SET_TYPE_CD] NVARCHAR (1024) NOT NULL,
    [INSERTED_BY]          VARCHAR (150)   CONSTRAINT [DF_D_DOCUMENT_TYPE_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]          DATETIME        CONSTRAINT [DF_D_DOCUMENT_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]           VARCHAR (250)   NULL,
    [UPDATED_DT]           DATETIME        NULL,
    [DELETED_BY]           VARCHAR (150)   NULL,
    [DELETED_DT]           DATETIME        NULL,
    CONSTRAINT [PK_D_DOCUMENT_TYPE] PRIMARY KEY CLUSTERED ([DOCUMENT_SET_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Seems is not used or is used like lookup', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_DOCUMENT_SET_TYPE';


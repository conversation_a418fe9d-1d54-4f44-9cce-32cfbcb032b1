CREATE TABLE [dbo].[F_SALES_ORDER_ADDRESS] (
    [SALES_ORDER_ADDRESS_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [SALES_ORDER_ID]         BIGINT         NOT NULL,
    [BILL_TO_CUSTOMER_ID]    BIGINT         NULL,
    [BILL_TO_ADDRESS_ID]     BIGINT         NULL,
    [FIRST_NAME]             NVARCHAR (256) NULL,
    [MIDDLE_NAME]            NVARCHAR (100) NULL,
    [LAST_NAME]              NVARCHAR (256) NULL,
    [COMPANY]                NVARCHAR (512) NULL,
    [PHONE]                  NVARCHAR (100) NULL,
    [POSTAL_CODE]            NVARCHAR (100) NULL,
    [COUNTRY_CODE]           NVARCHAR (10)  NULL,
    [STATE]                  NVARCHAR (100) NULL,
    [CITY]                   NVARCHAR (100) NULL,
    [STREET_1]               NVARCHAR (256) NULL,
    [STREET_2]               NVARCHAR (256) NULL,
    [IS_INACTIVE]            BIT            CONSTRAINT [DF_F_SALES_ORDER_ADDRESS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]             BIT            CONSTRAINT [DF_F_SALES_ORDER_ADDRESS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]            NVARCHAR (150) CONSTRAINT [DF_F_SALES_ORDER_ADDRESS_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]            DATETIME       CONSTRAINT [DF_F_SALES_ORDER_ADDRESS_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]             NVARCHAR (150) NULL,
    [UPDATED_DT]             DATETIME       NULL,
    [DELETED_BY]             NVARCHAR (150) NULL,
    [DELETED_DT]             DATETIME       NULL,
    CONSTRAINT [PK_F_SALES_ORDER_ADDRESS] PRIMARY KEY CLUSTERED ([SALES_ORDER_ADDRESS_ID] ASC),
    CONSTRAINT [FK_F_SALES_ORDER_ADDRESS_F_CUSTOMER] FOREIGN KEY ([BILL_TO_CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_SALES_ORDER_ADDRESS_F_CUSTOMER_ADDRESS] FOREIGN KEY ([BILL_TO_ADDRESS_ID]) REFERENCES [dbo].[F_CUSTOMER_ADDRESS] ([CUSTOMER_ADDRESS_ID]),
    CONSTRAINT [FK_F_SALES_ORDER_ADDRESS_F_SALES_ORDER] FOREIGN KEY ([SALES_ORDER_ID]) REFERENCES [dbo].[F_SALES_ORDER] ([SALES_ORDER_ID])
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UK__F_SALES_ORDER_ADDRESS__ORDER]
    ON [dbo].[F_SALES_ORDER_ADDRESS]([SALES_ORDER_ID] ASC) WHERE ([IS_INACTIVE]=(0) AND [IS_DELETED]=(0));

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'sales order addresses ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_SALES_ORDER_ADDRESS';


CREATE TABLE [dbo].[F_ITEM_INVENTORY_CAPABILITY] (
    [ITEM_INVENTORY_CAPABILITY_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_INVENTORY_ID]            BIGINT        NOT NULL,
    [INVENTORY_<PERSON>PABILITY_ID]      BIGINT        NOT NULL,
    [INVENTORY_CAPABILITY_TYPE_ID] INT           NOT NULL,
    [IS_INACTIVE]                  BIT           CONSTRAINT [DF_F_INVENTORY_RECV_CAPABILITY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                   BIT           CONSTRAINT [DF_F_INVENTORY_RECV_CAPABILITY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                  VARCHAR (150) CONSTRAINT [DF_F_INVENTORY_RECV_CAPABILITY_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                  DATETIME      CONSTRAINT [DF_F_INVENTORY_RECV_CAPABILITY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                   VARCHAR (150) NULL,
    [UPDATED_DT]                   DATETIME      NULL,
    [DELETED_BY]                   VARCHAR (150) NULL,
    [DELETED_DT]                   DATETIME      NULL,
    [INSERTED_BY_USER_ID] BIGINT NOT NULL DEFAULT ((1)), 
    [INSERTED_BY_USER_IP] BIGINT NOT NULL DEFAULT ((1)), 
    [UPDATED_BY_USER_ID] BIGINT NULL, 
    [UPDATED_BY_USER_IP] BIGINT NULL, 
    [DELETED_BY_USER_ID] BIGINT NULL, 
    [DELETED_BY_USER_IP] BIGINT NULL, 
    CONSTRAINT [PK_F_INVENTORY_RECV_CAPABILITY] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_CAPABILITY_ID] ASC),
    CONSTRAINT [FK_F_INVENTORY_RECV_CAPABILITY_C_INVENTORY_CAPABILITY_TYPE] FOREIGN KEY ([INVENTORY_CAPABILITY_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID]),
    CONSTRAINT [FK_F_INVENTORY_RECV_CAPABILITY_D_INVENTORY_CAPABILITY] FOREIGN KEY ([INVENTORY_CAPABILITY_ID]) REFERENCES [dbo].[D_INVENTORY_CAPABILITY] ([INVENTORY_CAPABILITY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_CAPABILITY_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
	CONSTRAINT [FK_F_ITEM_INVENTORY_CAPABILITY_tb_User_INSERTED_BY_USER] FOREIGN KEY ([INSERTED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_ITEM_INVENTORY_CAPABILITY_tb_User_UPDATED_BY_USER] FOREIGN KEY ([UPDATED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_ITEM_INVENTORY_CAPABILITY_tb_User_DELETED_BY_USER] FOREIGN KEY ([DELETED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);


GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ID_CAPABILITY_ID]
    ON [dbo].[F_ITEM_INVENTORY_CAPABILITY]([ITEM_INVENTORY_ID] ASC, [INVENTORY_CAPABILITY_ID] ASC);
GO

CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY_CAPABILITY__IS_DELETED__INCLUDE]
    ON [dbo].[F_ITEM_INVENTORY_CAPABILITY]([IS_DELETED], [ITEM_INVENTORY_ID])
	INCLUDE (INVENTORY_CAPABILITY_ID, INVENTORY_CAPABILITY_TYPE_ID);
GO


CREATE NONCLUSTERED INDEX [IX__F_ITEM_INVENTORY_CAPABILITY__ITEM_INVENTORY_ID__INCLUDE]
    ON [dbo].[F_ITEM_INVENTORY_CAPABILITY]([ITEM_INVENTORY_ID] ASC) -- inactive, deleted needed for search
	include(ITEM_INVENTORY_CAPABILITY_ID, INVENTORY_CAPABILITY_TYPE_ID, [INVENTORY_CAPABILITY_ID], [IS_INACTIVE], [IS_DELETED]);


GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_CAPABILITY_TYPE_ID]
    ON [dbo].[F_ITEM_INVENTORY_CAPABILITY]([INVENTORY_CAPABILITY_TYPE_ID] ASC, [ITEM_INVENTORY_ID] ASC)
    INCLUDE([INVENTORY_CAPABILITY_ID], [ITEM_INVENTORY_CAPABILITY_ID],[IS_INACTIVE],[IS_DELETED],[INSERTED_BY],[INSERTED_DT],[UPDATED_BY],[UPDATED_DT],[DELETED_BY],[DELETED_DT])
GO



GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_INVENTORY_CAPABILITY_INVENTORY_CAPABILITY_ID_inc_ITEM_INVENTORY_ID]
    ON [dbo].[F_ITEM_INVENTORY_CAPABILITY]([INVENTORY_CAPABILITY_ID] ASC)
    INCLUDE([ITEM_INVENTORY_ID]);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Item inventory capabilities, values are in (D_INVENTORY_CAPABILITY)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_CAPABILITY';


GO
-- =============================================
-- Author:		Anton semenov
-- Create date: 04/19/2018
-- Description:	This trigger will phycically delete capabilities which were already logically deleted and logged
-- =============================================
create trigger [dbo].[trg_PhysicalDeleteInventoryCapabilities_AfterUpdate] 
   on [dbo].[F_ITEM_INVENTORY_CAPABILITY]
   after update
as 
BEGIN
	--we check what values from INSERTED table were set with IsDeleted = 1
	--and then delete them physically
	delete		c
	from		[dbo].[F_ITEM_INVENTORY_CAPABILITY]	as c
	inner join	inserted							as i_c
		on c.[ITEM_INVENTORY_CAPABILITY_ID] = i_c.[ITEM_INVENTORY_CAPABILITY_ID] 
		and i_c.[IS_DELETED] = 1

END
GO
EXECUTE sp_settriggerorder @triggername = N'[dbo].[trg_PhysicalDeleteInventoryCapabilities_AfterUpdate]', @order = N'last', @stmttype = N'update';


GO
-- =============================================
-- Author:		Anton Semenov
-- Create date: 04/18/2018
-- Description:	<Description,,>
-- =============================================
CREATE trigger [dbo].[trg_LogInventoryCapabilities_AfterInsertUpdate]
	on [dbo].[F_ITEM_INVENTORY_CAPABILITY]
	after insert, update
as
begin
	declare @capabilities table (
		[Id] bigint
		,[ItemInventoryId]				bigint
		,[CapabilityTypeId]				bigint
		,[CapabilityTypeName]			nvarchar(250)
		,[CapabilityIdOld]				bigint
		,[CapabilityValueOld]			nvarchar(1000)
		,[CapabilityIdNew]				bigint
		,[CapabilityValueNew]			nvarchar(1000)
		,[CapabilityValueChangeText]	nvarchar(max)
		,[Action]						nvarchar(50)
		,[UserId]						bigint
		,[UserIp]						bigint
		,[SpName]						nvarchar(250)
	);

	declare @logs table (
		[ItemInventoryId]		bigint
		,[ItemInventoryName]	nvarchar(250)
		,[Text]					nvarchar(max)
		,[UserId]				bigint
		,[UserIp]				bigint
		,[SpName]				nvarchar(250)
	);

	DECLARE @warehouseCapabilityId bigint = [dbo].[fn_str_GetWarehouseCapabilityId]();

	--get new values
	insert into @capabilities (
		[Id]
		,[ItemInventoryId]
		,[CapabilityTypeId]
		,[CapabilityIdOld]
		,[CapabilityIdNew]
		,[Action]
		,[UserId]
		,[UserIp]
		,[SpName]
	)
	select
		[ITEM_INVENTORY_CAPABILITY_ID]
		,[ITEM_INVENTORY_ID]
		,[INVENTORY_CAPABILITY_TYPE_ID]
		,case when [IS_DELETED] = 1	then [INVENTORY_CAPABILITY_ID] else null end --if caability is deleted we push into OLD
		,case when [IS_DELETED] = 0	then [INVENTORY_CAPABILITY_ID] else null end
		,case		
			when [IS_DELETED] = 1			then 'delete' --if [IsDeleted] = 1, we can say that value is deleting
			when [UPDATED_BY] is not null	then 'update' --if UpdateBy* fields are populated, we can say that value is updating
			else 'add'
		end
		,coalesce([DELETED_BY_USER_ID], [UPDATED_BY_USER_ID], [INSERTED_BY_USER_ID])
		,coalesce([DELETED_BY_USER_IP], [UPDATED_BY_USER_IP], [INSERTED_BY_USER_IP])
		,coalesce([DELETED_BY], [UPDATED_BY], [INSERTED_BY])
	from inserted i
	where i.INVENTORY_CAPABILITY_TYPE_ID != @warehouseCapabilityId;

	--get information about deleted and updated values
	;merge @capabilities	as t
	using deleted			as s
		on s.INVENTORY_CAPABILITY_TYPE_ID != @warehouseCapabilityId and t.[Id] = s.[ITEM_INVENTORY_CAPABILITY_ID]
	when matched and t.[CapabilityIdNew] <> s.[INVENTORY_CAPABILITY_ID]
		--get old values for updated capabilities
		then update
			set t.[CapabilityIdOld] = s.[INVENTORY_CAPABILITY_ID];


	--get another information that should be used in log forming
	update c set
		c.[CapabilityTypeName]			= ict.[INVENTORY_CAPABILITY_TYPE_NAME]
		,c.[CapabilityValueOld]			= ic_old.[INVENTORY_CAPABILITY_VALUE]
		,c.[CapabilityValueNew]			= ic_new.[INVENTORY_CAPABILITY_VALUE]
		,c.[CapabilityValueChangeText]	= N'"' + ict.[INVENTORY_CAPABILITY_TYPE_NAME] +  N'"'
			+ case when c.[CapabilityIdOld] is not null then N' was "' + ic_old.[INVENTORY_CAPABILITY_VALUE] + N'"' else N'' end
			+ case when c.[CapabilityIdNew] is not null then N' is "' + ic_new.[INVENTORY_CAPABILITY_VALUE] + N'"' else N'' end
	from		@capabilities						as c
	inner join	[dbo].[C_INVENTORY_CAPABILITY_TYPE]	as ict with(nolock)
		on c.[CapabilityTypeId] = ict.[INVENTORY_CAPABILITY_TYPE_ID]
	left join	[dbo].[D_INVENTORY_CAPABILITY]		as ic_old with(nolock)
		on c.[CapabilityIdOld] = ic_old.[INVENTORY_CAPABILITY_ID]
	left join	[dbo].[D_INVENTORY_CAPABILITY]		as ic_new with(nolock)
		on c.[CapabilityIdNew] = ic_new.[INVENTORY_CAPABILITY_ID];

	--form logs
	;with m_data as (
		select
			c.[ItemInventoryId]
			,c.[UserId]
			,c.[UserIp]
			,c.[SpName]
			,[dbo].[fn_str_STRIP_XML_TAGS]((
				select
					[CapabilityValueChangeText]
				from @capabilities			as i_c
				where i_c.[ItemInventoryId] = c.[ItemInventoryId]
				for xml path('ROOT'),type,elements absent
			))	as [Text]
		from @capabilities				as c
		group by [ItemInventoryId], [UserId], [UserIp], [SpName]
	)
	insert into @logs (
		[ItemInventoryId]
		,[UserId]
		,[UserIp]
		,[SpName]
		,[Text]
	)
	select
		[ItemInventoryId]
		,[UserId]
		,[UserIp]
		,[SpName]
		,[Text]
	from m_data;

	--append asset auto name (its unique)
	update l
		set l.[ItemInventoryName] = a.[ITEM_INVENTORY_UNIQUE_ID]
	from @logs							as l
	inner join [dbo].[F_ITEM_INVENTORY]	as a
		on l.[ItemInventoryId] = a.[ITEM_INVENTORY_ID];

	--insert logs into permanent log table
	insert into [dbo].[F_LOG_DATA] with(rowlock) (
		[SOURCE],
		[USER_ID],
		USER_IP,
		OPERATION_NAME,
		ENTITY_TYPE_ID,
		ENTITY_KEY_VALUE,
		ENTITY_AUTO_NAME,
		[CHANGES]
	)
	select
		'[dbo].[trg_LogInventoryCapabilities_AfterInsertUpdate]'
		,[UserId]
		,[UserIp]
		,'Capabilities changing'
		,9 --inventory
		,[ItemInventoryId]
		,[ItemInventoryName]
		,[Text]
	from @logs;
end
GO
EXECUTE sp_settriggerorder @triggername = N'[dbo].[trg_LogInventoryCapabilities_AfterInsertUpdate]', @order = N'first', @stmttype = N'update';
GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_INVENTORY_CAPABILITY_PUT_CHANGE]
    ON  [dbo].[F_ITEM_INVENTORY_CAPABILITY]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_INVENTORY_ID, d.ITEM_INVENTORY_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_INVENTORY_ID = d.ITEM_INVENTORY_ID
	where isnull(i.[INVENTORY_CAPABILITY_TYPE_ID], 0) <> isnull(d.[INVENTORY_CAPABILITY_TYPE_ID], 0)
		or isnull(i.INVENTORY_CAPABILITY_ID, 0) <> isnull(d.INVENTORY_CAPABILITY_ID, 0)
		or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
		or isnull(i.IS_DELETED, 0) <> isnull(d.IS_DELETED, 0)
	
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 1 --Inventory
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

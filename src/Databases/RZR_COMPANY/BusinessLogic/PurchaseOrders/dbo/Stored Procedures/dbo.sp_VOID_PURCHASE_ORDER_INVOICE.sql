-- =============================================
-- Author:		<V.<PERSON><PERSON><PERSON><PERSON>>
-- Create date: <03/11/2014>
-- Description:	<>
-- =============================================
CREATE PROCEDURE [dbo].[sp_VOID_PURCHASE_ORDER_INVOICE]
    --PO Invoices are supposed to come here within the same PO
	@PURCHASE_ORDER_INVOICE_IDS bigint_ID_ARRAY READONLY,
    @IS_DEBUG                   BIT     = 0
AS
BEGIN

	DECLARE
		 @UTC_NOW   DATETIME		= GETUTCDATE()
		,@SP_NAME   NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
        ,@msg	    nvarchar(256)   = null

    /*
    take amounts & items & charges                 of normal     invoices to be included into 1 normal     invoice
    take amounts & items & charges & tied invoices of additional invoices to be included into 1 additional invoice
        
    cannot void paid invoices
    additionals are rebound to the new normal invoice when their normal invoice is voided
    */

    declare @tToVoid table (
        INVOICE_ID          bigint
        ,PURCHASE_ORDER_ID  bigint
        ,AUTO_NAME          nvarchar(256)
        ,TIED_INVOICE_ID    bigint
        ,MISC_CHARGE        float
        ,ORIGINAL_AMOUNT    float
        ,PAID_AMOUNT        float
    )
    insert into @tToVoid
    select
        FI.INVOICE_ID                   --  INVOICE_ID      
        ,FI.ORDER_ID                    --  ,PURCHASE_ORDER_ID
        ,FI.AUTO_NAME                   --  ,AUTO_NAME      
        ,FI.TIED_INVOICE_ID             --  ,TIED_INVOICE_ID
        ,FI.MISC_CHARGE                 --  ,MISC_CHARGE    
        ,FI.ORIGINAL_AMOUNT             --  ,ORIGINAL_AMOUNT
        ,FI.PAID_AMOUNT                 --  ,PAID_AMOUNT
    from @PURCHASE_ORDER_INVOICE_IDS    ids
    inner join dbo.F_INVOICE            fi  with(nolock)
        on  fi.INVOICE_ID       = ids.ID
        and fi.INVOICE_TYPE_ID  = 2
    where fi.IS_VOIDED  = 0
        and fi.IS_DELETED = 0
    union all -- with their unpaid additional invoices
    select
        FI.INVOICE_ID                   --  INVOICE_ID      
        ,FI.ORDER_ID                    --  ,PURCHASE_ORDER_ID
        ,FI.AUTO_NAME                   --  ,AUTO_NAME      
        ,FI.TIED_INVOICE_ID             --  ,TIED_INVOICE_ID
        ,FI.MISC_CHARGE                 --  ,MISC_CHARGE    
        ,FI.ORIGINAL_AMOUNT             --  ,ORIGINAL_AMOUNT
        ,FI.PAID_AMOUNT                 --  ,PAID_AMOUNT
    from @PURCHASE_ORDER_INVOICE_IDS    ids
    inner join dbo.F_INVOICE            fi  with(nolock)
        on  fi.TIED_INVOICE_ID  = ids.ID
        and fi.INVOICE_TYPE_ID  = 2
    where fi.IS_VOIDED  = 0
        and fi.IS_DELETED = 0
        and fi.PAID_AMOUNT = 0.0

    if not exists(select top(1) 1 from @tToVoid)
    begin
        return;
    end

    if (select count(distinct PURCHASE_ORDER_ID) from @tToVoid) > 1
    begin
        set @msg = 'Invoices should belong to one Purchase order';
        THROW 50003, @msg, 1;
    end

    declare @autoNames nvarchar(max) 
    select
        @autoNames = isnull(@autoNames + ', ', '') + AUTO_NAME
    from @tToVoid
    where PAID_AMOUNT != 0
    if @autoNames is not null
    begin
        set @msg = 'All payments against PO invoice(s) '+ @autoNames +' should be undone first';
        THROW 50003, @msg, 1;
    end


    begin tran
	set xact_abort on

        declare
            @poId       bigint  = (
                select top(1)
                    PURCHASE_ORDER_ID
                from @tToVoid
            )
            ,@charges   float   = null
            ,@amt       float   = null
            ,@isVoided  bit     = null
            ,@itemIds   dbo.bigint_ID_ARRAY


        if @IS_DEBUG = 1
        begin
            select *
            from dbo.vw_PoItemInvoices fi
            where fi.PURCHASE_ORDER_ID = @poId
            order by AUTO_NAME desc
            
            select
                *
            from @tToVoid
            order by AUTO_NAME desc
        end

        if exists(
            select top(1) 1
            from @tToVoid these
            where these.TIED_INVOICE_ID is null
                and these.PAID_AMOUNT = 0
        )
        begin
            /*
            Void normal together with their additionals
            Voiding the normal one:
                - void normal, unbind items and store item
                - void their additionals, unbind items
                - create a new normal invoice
                - rebind all items and charges
            */
            update fi set
                fi.is_voided    = 1
                ,fi.UPDATED_BY	= @SP_NAME
                ,fi.UPDATED_DT	= @UTC_NOW	
            from @tToVoid               normal
            inner join dbo.F_INVOICE    fi    with(rowlock)
                on fi.INVOICE_ID = normal.INVOICE_ID
            where normal.TIED_INVOICE_ID is null

            delete fpoii
            output deleted.PURCHASE_ORDER_ITEM_ID into @itemIds
            from @tToVoid                                   normal
            inner join dbo.F_PURCHASE_ORDER_ITEM_INVOICE    fpoii   with(rowlock)
                on fpoii.PURCHASE_ORDER_INVOICE_ID = normal.INVOICE_ID
            where normal.TIED_INVOICE_ID is null


            update fi set
                fi.is_voided    = 1
                ,fi.UPDATED_BY	= @SP_NAME
                ,fi.UPDATED_DT	= @UTC_NOW	
            from @tToVoid               normal
            inner join @tToVoid         additional
                on additional.TIED_INVOICE_ID = normal.INVOICE_ID
            inner join dbo.F_INVOICE    fi    with(rowlock)
                on fi.INVOICE_ID = additional.INVOICE_ID

            delete fpoii
            from @tToVoid                                   normal
            inner join @tToVoid                             additional
                on additional.TIED_INVOICE_ID = normal.INVOICE_ID
            inner join dbo.F_PURCHASE_ORDER_ITEM_INVOICE    fpoii   with(rowlock)
                on fpoii.PURCHASE_ORDER_INVOICE_ID = additional.INVOICE_ID

            select
                @charges = sum(normal.MISC_CHARGE)
            from @tToVoid               normal
            where normal.TIED_INVOICE_ID is null

            --There may be another invoice whose payments have been undone, use it
            declare @normalToAppendId bigint
            select top(1)
                @normalToAppendId   = fi.INVOICE_ID
                ,@charges           = @charges + fi.MISC_CHARGE
            from dbo.vw_PoItemInvoices  fi  with(nolock)
            where fi.PURCHASE_ORDER_ID  = @poId
                and fi.IS_VOIDED        = 0
                and fi.PAID_AMOUNT      = 0
                and fi.TIED_INVOICE_ID is null
                and not exists(
                    select top(1)
                        1
                    from @tToVoid these
                    where these.INVOICE_ID = fi.INVOICE_ID)
            order by fi.INVOICE_ID asc

            exec dbo.sp_SetPoInvoice
                @PoId			= @poId
                ,@InvoiceId		= @normalToAppendId
                ,@TiedInvoiceId	= null
                ,@Charges		= @charges
                ,@OutInvoiceId	= @normalToAppendId out
                ,@PoItemIds     = @itemIds
                ,@IsUpdateAmt	= 1
                ,@IsDebug       = @IS_DEBUG
        end


        -- there could remain additionals of other non-voided normal
        delete from @tToVoid
        insert into @tToVoid
        select
            FI.INVOICE_ID                   --  INVOICE_ID      
            ,FI.ORDER_ID                    --  ,PURCHASE_ORDER_ID
            ,FI.AUTO_NAME                   --  ,AUTO_NAME      
            ,FI.TIED_INVOICE_ID             --  ,TIED_INVOICE_ID
            ,FI.MISC_CHARGE                 --  ,MISC_CHARGE    
            ,FI.ORIGINAL_AMOUNT             --  ,ORIGINAL_AMOUNT
            ,FI.PAID_AMOUNT                 --  ,PAID_AMOUNT
        from @PURCHASE_ORDER_INVOICE_IDS    ids
        inner join dbo.F_INVOICE            fi  with(nolock)
            on  fi.INVOICE_ID       = ids.ID
            and fi.INVOICE_TYPE_ID  = 2
        where fi.IS_VOIDED  = 0
            and fi.IS_DELETED = 0
            and fi.TIED_INVOICE_ID is not null

        if @IS_DEBUG = 1
            select
                *
            from @tToVoid
            order by AUTO_NAME desc

        /*
        Items present in additionals (additionals being voided here have paid normal)                
            - void additionals, unbind items
            - get undone items and charges, if any:
                - find/create an additional invoice
                - rebind items and charges, update amount
        */
        -- additionals may be created for different normals
        declare 
            @paidNormalId           bigint
            ,@additionalToAppendId  bigint
        declare curEachNormalPaid cursor local forward_only for
            select distinct
                TIED_INVOICE_ID
            from @tToVoid
            open curEachNormalPaid
                fetch next from curEachNormalPaid INTO @paidNormalId
                while (@@FETCH_STATUS = 0)
                begin

                    if @IS_DEBUG = 1
                        select
                            *
                        from @tToVoid additional
                        where additional.TIED_INVOICE_ID = @paidNormalId
                        order by AUTO_NAME desc

                    update fi set
                        fi.is_voided    = 1
                        ,fi.UPDATED_BY	= @SP_NAME
                        ,fi.UPDATED_DT	= @UTC_NOW	
                    from dbo.F_INVOICE  fi    with(rowlock)
                    inner join @tToVoid additional
                        on additional.INVOICE_ID = fi.INVOICE_ID
                    where additional.TIED_INVOICE_ID = @paidNormalId

                    delete from @itemIds
                    delete fpoii
                    output deleted.PURCHASE_ORDER_ITEM_ID into @itemIds
                    from @tToVoid                                   additional
                    inner join dbo.F_PURCHASE_ORDER_ITEM_INVOICE    fpoii   with(rowlock)
                        on additional.INVOICE_ID = fpoii.PURCHASE_ORDER_INVOICE_ID
                    where additional.TIED_INVOICE_ID = @paidNormalId

                    set @charges = 0.0
                    select
                        @charges    = sum(additional.MISC_CHARGE)
                    from @tToVoid   additional
                    where additional.TIED_INVOICE_ID = @paidNormalId

                    if @charges != 0
                    or exists(select top(1) 1 from @itemIds)
                    begin

                        --There may be another additional invoice for the same normal invoice, use it
                        set @additionalToAppendId = null
                        select top(1)
                            @additionalToAppendId   = fi.INVOICE_ID
                            ,@charges               = @charges + fi.MISC_CHARGE
                        from dbo.vw_PoItemInvoices  fi  with(nolock)
                        where fi.PURCHASE_ORDER_ID  = @poId
                            and fi.IS_VOIDED        = 0
                            and fi.PAID_AMOUNT      = 0
                            and fi.TIED_INVOICE_ID  = @paidNormalId
                            and not exists(
                                select top(1)
                                    1
                                from @tToVoid these
                                where these.INVOICE_ID = fi.INVOICE_ID)
                            order by fi.INVOICE_ID asc

                        exec dbo.sp_SetPoInvoice
                            @PoId			= @poId
                            ,@InvoiceId		= @additionalToAppendId
                            ,@TiedInvoiceId	= @paidNormalId
                            ,@Charges		= @charges
                            ,@OutInvoiceId	= @additionalToAppendId out
                            ,@PoItemIds     = @itemIds
                            ,@IsUpdateAmt	= 1
                            ,@IsDebug       = @IS_DEBUG
                    end

                    fetch next from curEachNormalPaid INTO @paidNormalId
                end
            close curEachNormalPaid
        deallocate curEachNormalPaid

        if @IS_DEBUG = 1
        begin
            select *
            from dbo.vw_PoItemInvoices fi
            where fi.PURCHASE_ORDER_ID = @poId
            order by AUTO_NAME desc
        end

        if @IS_DEBUG = 1
        BEGIN
            rollback tran;
            return;
        end

	commit tran
	
END
GO

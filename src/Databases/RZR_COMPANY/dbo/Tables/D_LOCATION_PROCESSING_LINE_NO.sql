CREATE TABLE [dbo].[D_LOCATION_PROCESSING_LINE_NO] (
	[LOCATION_PROCESSING_LINE_NO_ID]   BIGINT         IDENTITY (1, 1) NOT NULL,
	[WAREHOUSE_ID]                     BIGINT         NOT NULL,
	[DE<PERSON>IL_PREFIX_ID]                 INT            NOT NULL,
	[LOCATION_PROCESSING_LINE_POS]     BIGINT         NOT NULL,
	[LOCATION_PROCESSING_LINE_NO_CD]   VARCHAR (50)   NOT NULL,
	[LOCATION_PROCESSING_LINE_NO_DESC] VARCHAR (250)  NULL,
	[STORAGE_UNIT_CODE]                NVARCHAR (128) CONSTRAINT [DF_D_LOCATION_PROCESSING_LINE_NO_STORAGE_UNIT_CODE] DEFAULT ('D') NOT NULL,
	[IS_INACTIVE]                      BIT            CONSTRAINT [DF_D_LOCATION_PROCESSING_LINE_NO_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
	[IS_DELETED]                       BIT            CONSTRAINT [DF_D_LOCATION_PROCESSING_LINE_NO_IS_DELETED] DEFAULT ((0)) NOT NULL,
	[INSERTED_BY]                      VARCHAR (250)  CONSTRAINT [DF_D_LOCATION_PROCESSING_LINE_NO_INSERTED_BY] DEFAULT ('admin') NOT NULL,
	[INSERTED_DT]                      DATETIME       CONSTRAINT [DF_D_LOCATION_PROCESSING_LINE_NO_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
	[UPDATED_BY]                       VARCHAR (250)  NULL,
	[UPDATED_DT]                       DATETIME       NULL,
	[DELETED_BY]                       VARCHAR (250)  NULL,
	[DELETED_DT]                       DATETIME       NULL,
	CONSTRAINT [PK_D_LOCATION_PROCESSING_LINE_NO] PRIMARY KEY CLUSTERED ([LOCATION_PROCESSING_LINE_NO_ID] ASC),
	CONSTRAINT [FK_D_LOCATION_PROCESSING_LINE_NO_D_LOCATION_DETAIL_PREFIX] FOREIGN KEY ([DETAIL_PREFIX_ID]) REFERENCES [dbo].[D_LOCATION_DETAIL_PREFIX] ([LOCATION_DETAIL_PREFIX_ID]),
	CONSTRAINT [FK_D_LOCATION_PROCESSING_LINE_NO_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]) ON DELETE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is one of locations types. It is used on ITAD module', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_LOCATION_PROCESSING_LINE_NO';


-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 22.04.2014
-- Description:	Return invoices list by customer
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVOICES_BY_CUSTOMER_PAGE]
	@CUSTOMER_ID			BIGINT,
	@SALES_ORDER_INVOICE_ID BIGINT	
AS
BEGIN
	
	DECLARE @query NVARCHAR (MAX) = '
			WITH m_data AS
			(
				SELECT 
					SOI.[INVOICE_ID] AS SALES_ORDER_INVOICE_ID
				FROM dbo.F_INVOICE	SOI	WITH(NOLOCK)
				INNER JOIN dbo.F_SALES_ORDER	SO	WITH(NOLOCK)
					ON SOI.INVOICE_TYPE_ID = 1 AND SOI.[ORDER_ID] = SO.[SALES_ORDER_ID]
				WHERE SO.CUSTOMER_ID = ' + CAST(@CUSTOMER_ID AS VARCHAR(1000)) + '
				  AND (SOI.STATUS_ID = 3 OR SOI.STATUS_ID  = 4 ) AND SOI.IS_VOIDED = 0 -- "Final" and Not "Paid in Full"
			)

			SELECT TOP(1)
				-1								AS RowID,
				COUNT(SALES_ORDER_INVOICE_ID)	AS SALES_ORDER_INVOICE_ID
			FROM m_data
			UNION
			SELECT
				t.RowID,
				t.SALES_ORDER_INVOICE_ID
			FROM 
				(
					SELECT 
						ROW_NUMBER() OVER (ORDER BY M.SALES_ORDER_INVOICE_ID ASC) AS RowID,
						*
					FROM m_data M 
				) t
			WHERE
				T.SALES_ORDER_INVOICE_ID= '+ CAST(@SALES_ORDER_INVOICE_ID AS VARCHAR(100))	
				
		EXEC sp_executesql @query;					 
END
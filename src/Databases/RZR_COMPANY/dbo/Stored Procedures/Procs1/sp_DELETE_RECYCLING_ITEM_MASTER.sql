CREATE PROCEDURE [dbo].[sp_DELETE_RECYCLING_ITEM_MASTER] 
	@DATA		bigint_ID_ARRAY READONLY,
	@USER_ID	BIGINT,
    @IP			BIGINT
AS
BEGIN
    DECLARE @temp bigint_ID_ARRAY

    INSERT INTO @temp 
	   SELECT x.ID
    FROM @DATA x		          
	
    DELETE FROM @temp 
    WHERE ID IN (
		SELECT [RECYCLING_ITEM_MASTER_ID] AS [Id] FROM F_RECYCLING_ORDER_ITEM ROI WITH (NOLOCK)
		UNION ALL
		SELECT [CommodityId] AS [Id] FROM [recycling].[F_CommodityRule] WITH(NOLOCK)
	)

    BEGIN TRY
	   SET XACT_ABORT ON	   
	   BEGIN TRANSACTION
		  
		  --TODO: not to forget about removing this after deleting table
		  PRINT 'Delete from [F_CONTRACT_VALIDATION_REQUIREMENTS]'	
		  
		  DELETE FROM [dbo].[F_CONTRACT_VALIDATION_REQUIREMENTS] WITH(ROWLOCK)
			 WHERE ITEM_MASTER_ID IN (SELECT ID FROM @temp)	
		  
		  PRINT 'Update F_SALES_ORDER_ITEM'
		  UPDATE [dbo].[F_SALES_ORDER_ITEM] WITH(ROWLOCK) SET
			[CommodityRuleId] = NULL
		  WHERE [CommodityRuleId] IN (SELECT [Id] FROM [recycling].[F_CommodityRule] WITH (NOLOCK) WHERE [CommodityId] IN (SELECT ID FROM @temp))
		  
		  PRINT 'Update [F_PURCHASE_ORDER_ITEM]'
		  UPDATE [dbo].[F_PURCHASE_ORDER_ITEM] WITH(ROWLOCK) SET
			[CommodityRuleId] = NULL
		  WHERE [CommodityRuleId] IN (SELECT [Id] FROM [recycling].[F_CommodityRule] WITH (NOLOCK) WHERE [CommodityId] IN (SELECT ID FROM @temp))

		  PRINT 'DELETE [F_RECYCLING_OUTBOUND_CONTAINER]'
		  DELETE FROM F_RECYCLING_OUTBOUND_CONTAINER WITH(ROWLOCK)
			 WHERE RECYCLING_ITEM_MASTER_ID IN (SELECT ID FROM @temp)
		  
		  PRINT 'DELETE [F_RECYCLING_ITEM_MASTER_IMAGE]'
		  DELETE FROM dbo.F_RECYCLING_ITEM_MASTER_IMAGE WITH(ROWLOCK)
		  WHERE ITEM_MASTER_ID IN (SELECT ID FROM @temp)


		  DECLARE @RECYCLING_ORDER_ITEM_ID BIGINT
		  /*We should add log*/
		  DECLARE CUR_DEL_ORDER_ITEM CURSOR FOR SELECT  RECYCLING_ORDER_ITEM_ID FROM [dbo].F_RECYCLING_ORDER_ITEM
				WHERE RECYCLING_ITEM_MASTER_ID IN  (SELECT ID FROM @temp)
		  OPEN CUR_DEL_ORDER_ITEM 
			 FETCH NEXT FROM CUR_DEL_ORDER_ITEM 
				INTO @RECYCLING_ORDER_ITEM_ID
			WHILE @@FETCH_STATUS = 0
			BEGIN
				EXEC [sp_DEL_RECYCLING_ORDER_ITEM]
					@RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID
					,@IS_DELETE_AUDIT_ITEMS = 1/*We should check audit*/
					,@USER_ID			 = @USER_ID
					,@IP				 = @IP

			FETCH NEXT FROM CUR_DEL_ORDER_ITEM 
				INTO @RECYCLING_ORDER_ITEM_ID
			END
			CLOSE CUR_DEL_ORDER_ITEM
			DEALLOCATE CUR_DEL_ORDER_ITEM

		  
		  PRINT 'EXEC [sp_LOG_RECYCLING_ITEM_MASTER_DELETE]'
		  EXEC sp_LOG_RECYCLING_ITEM_MASTER_DELETE
			 @DATA = @temp,
			 @USER_ID = @USER_ID, 
			 @IP = @IP
		  
		  PRINT 'DELETE [F_PRODUCT_MASTER_ACCOUNT]'
		  DELETE A
		  FROM F_PRODUCT_MASTER_ACCOUNT		A	WITH(ROWLOCK)
		  INNER JOIN F_PRODUCT_MASTER		P	WITH (NOLOCK)
		  	 ON P.PRODUCT_MASTER_ID = A.PRODUCT_MASTER_ID
		  WHERE P.MODULE_MASTER_ID IN (SELECT ID FROM @temp )

		  PRINT 'DELETE F_WAREHOUSE_ACCOUNT'		  
		  DELETE FWA		  
		  FROM @temp									T		  
		  INNER JOIN dbo.F_PRODUCT_MASTER				FPM WITH(ROWLOCK)
			ON FPM.MODULE_MASTER_ID = T.ID
		  INNER JOIN dbo.[F_WAREHOUSE_ACCOUNT]	FWA WITH(ROWLOCK)
			ON FWA.PRODUCT_MASTER_ID = FPM.PRODUCT_MASTER_ID

		  PRINT 'DELETE recycling.F_ItemMasterMaterial'		  
		  DELETE FPM		  
		  FROM @temp									T		  
		  INNER JOIN recycling.F_ItemMasterMaterial		FPM WITH(ROWLOCK)
			ON FPM.RecyclingItemMasterId = T.ID

		  PRINT 'DELETE [F_PRODUCT_MASTER]'
		  DELETE FROM [dbo].[F_PRODUCT_MASTER] WITH(ROWLOCK)
		  WHERE MODULE_MASTER_ID IN (SELECT ID FROM @temp)

		  PRINT 'DELETE recycling.F_AlternativeCommodityName';
		  DELETE FROM [recycling].[F_AlternativeCommodityName] WITH(ROWLOCK)
		  WHERE [CommodityId] IN (SELECT ID FROM @temp);

		  PRINT 'DELETE recycling.F_CommodityVersion';
		  UPDATE [dbo].[F_RECYCLING_ITEM_MASTER] WITH(ROWLOCK)
		  SET [ActualVersionId] = NULL
		  WHERE [RECYCLING_ITEM_MASTER_ID] IN (SELECT ID FROM @temp);

		  DELETE FROM [recycling].[F_CommodityVersion] WITH(ROWLOCK)
		  WHERE [CommodityId] IN (SELECT ID FROM @temp);

		  DELETE FROM [recycling].[F_CommodityStateProgram] WITH(ROWLOCK)
		  WHERE [CommodityId] IN (SELECT ID FROM @temp);

		  PRINT 'DELETE F_RECYCLING_ITEM_MASTER';
		  DELETE FROM dbo.F_RECYCLING_ITEM_MASTER WITH(ROWLOCK)
		  WHERE RECYCLING_ITEM_MASTER_ID IN (SELECT ID FROM @temp);
		  
	   COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
	    DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;  

    SELECT   
        @ErrorMessage = ERROR_MESSAGE(),  
        @ErrorSeverity = ERROR_SEVERITY(),  
        @ErrorState = ERROR_STATE();  

	
	   -- There is an error
	   IF @@TRANCOUNT > 0
		  ROLLBACK TRANSACTION
	
		delete from @temp 	
		RAISERROR (@ErrorMessage, -- Message text.  
               @ErrorSeverity, -- Severity.  
               @ErrorState -- State.  
               );  
	
    END CATCH

    SELECT COUNT(ID) FROM @temp
		
END

CREATE TABLE [dbo].[D_CONTRACT_CAPABILITY_TYPE] (
    [CONTRACT_CAPABILITY_TYPE_ID]   INT            NOT NULL,
    [CONTRACT_CAPABILITY_TYPE_NAME] NVARCHAR (150) NOT NULL,
    [CAPABILITY_TYPE_ID]            INT            NULL,
    [INSERTED_BY]                   NVARCHAR (150) NOT NULL,
    [INSERTED_DT]                   DATETIME       NOT NULL,
    [UPDATED_BY]                    NVARCHAR (150) NULL,
    [UPDATED_DT]                    DATETIME       NULL,
    CONSTRAINT [PK_D_CONTRACT_CAPABILITY_TYPE] PRIMARY KEY CLUSTERED ([CONTRACT_CAPABILITY_TYPE_ID] ASC),
    CONSTRAINT [FK_D_CONTRACT_CAPABILITY_TYPE_C_INVENTORY_CAPABILITY_TYPE] FOREIGN KEY ([CAPABILITY_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The system page where for capability type you select system capabilities. E.g. what is CPU Type for current client, what is Screen Size etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_CONTRACT_CAPABILITY_TYPE';


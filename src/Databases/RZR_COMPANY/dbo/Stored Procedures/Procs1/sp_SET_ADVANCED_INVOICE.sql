-- =============================================
-- Author:		O.Evseev
-- Create date: 03/19/2015
-- Description:	Creates an invoice for billing in advance to
-- the Inbound order if any pricing items are populated.
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_ADVANCED_INVOICE] 	
	@ORDER_ID						BIGINT,	
	@RECEIVED_BY_USER_ID			BIGINT,
	@USER_IP						BIGINT,
	@C_CREATE_NOT_UPDATE			BIT = 0, -- only create the PO/SO + invoice, not delete the old ones (wow, if 0 deletes the SO, invoice and payments so far)
	@C_USE_INTERNAL_TRANSACTION		BIT = 1,
	@C_IS_DEBUG						BIT = 0
AS
BEGIN

	DECLARE
		@PROCESS_CD		NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
		,@UTC_NOW		DATETIME		= GETUTCDATE()
		,@MSG			NVARCHAR(128)
		,@AMOUNT_DUE	FLOAT
		,@INVOICE_SUM	FLOAT;

	DECLARE
		@RECYCLING_ORDER_ID		BIGINT	= NULL
		,@BILLING_TYPE_ID		INT		= 0
		,@SALES_ORDER_ID		BIGINT
		,@PURCHASE_ORDER_ID		BIGINT 
		,@SO_TERM_ID			INT
		,@PT_TERM_ID			INT
		,@CUSTOMER_ID			BIGINT
		,@USER_ID				BIGINT
		,@IS_INBOUND			BIT
		,@INVOICE_ID			BIGINT

	IF @C_USE_INTERNAL_TRANSACTION = 1
	BEGIN
		SET XACT_ABORT ON
		BEGIN TRAN
	END
		
		SELECT
			 @RECYCLING_ORDER_ID	= FRO.RECYCLING_ORDER_ID
			,@CUSTOMER_ID			= FRO.CUSTOMER_ID
			,@USER_ID				= FRO.[USER_ID]
			,@IS_INBOUND			= FRO.IS_INBOUND
			,@BILLING_TYPE_ID		= FROI.BILLING_TYPE_ID
			,@SO_TERM_ID			= FC.ST_TERMS_ID
			,@PT_TERM_ID			= FC.PT_TERMS_ID
			,@SALES_ORDER_ID		= FSO.SALES_ORDER_ID
			,@PURCHASE_ORDER_ID		= FPO.PURCHASE_ORDER_ID
		FROM dbo.F_RECYCLING_ORDER					FRO		WITH(NOLOCK)
		INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND	FROI	WITH(NOLOCK)
			ON  FROI.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
			AND FROI.IS_QUOTE			= 0
			AND FROI.IS_REVISION		= 0
			AND FROI.BILLING_TYPE_ID	> 1
		INNER JOIN dbo.F_CUSTOMER					FC		WITH(NOLOCK)
			ON FC.CUSTOMER_ID = FRO.CUSTOMER_ID
		LEFT JOIN dbo.F_SALES_ORDER					FSO		WITH(NOLOCK)
			ON FSO.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
		LEFT JOIN dbo.F_PURCHASE_ORDER				FPO		WITH(NOLOCK)
			ON FPO.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
		WHERE FRO.RECYCLING_ORDER_ID = @ORDER_ID

		IF (@RECYCLING_ORDER_ID IS NULL)
		BEGIN
			IF @C_IS_DEBUG = 1
				PRINT('The order not found or has a standard billing')
			IF @C_USE_INTERNAL_TRANSACTION = 1			
				COMMIT;
			RETURN;
		END

		IF (@C_CREATE_NOT_UPDATE = 1 AND (@SALES_ORDER_ID IS NOT NULL OR @PURCHASE_ORDER_ID IS NOT NULL))
		BEGIN
			IF @C_IS_DEBUG = 1
				PRINT('Orders found. SO id: '+isnull(cast(@SALES_ORDER_ID as nvarchar(20)), 'NULL')+', PO id: '+isnull(cast(@PURCHASE_ORDER_ID as nvarchar(20)), 'NULL; Not updating the orders.'))
			IF @C_USE_INTERNAL_TRANSACTION = 1			
				COMMIT;
			RETURN;
		END
												 

		IF @C_IS_DEBUG = 1
			PRINT('Billing type: '+ISNULL(cast(@BILLING_TYPE_ID as nvarchar(20)), N'NULL'))


		SELECT 
			*
		INTO #ITEMS
		FROM (
			SELECT					
				PM.PRODUCT_MASTER_ID				AS PRODUCT_MASTER_ID
				,PM.PRODUCT_MASTER_TYPE_ID			AS PRODUCT_MASTER_TYPE_ID
				,OI.[Id]  AS RECYCLING_ORDER_ITEM_MASTER_ID
				,CAST(NULL AS BIGINT)				AS SERVICE_ID
				,OI.[PriceTypeId]					AS PRICE_TYPE_ID
				,IIF(OI.PriceTypeId = 3, 1, 0)	AS IS_FLAT_FEE
				,OI.PRICE							AS PRICE
				,OI.PRICE							AS RECEIVED_PRICE
				,OI.[DefaultWeight]					AS QTY		
				,3									AS RECEIVE_STATUS_ID
				,CAST(OI.[PriceChangeNotes] AS varchar(max))	AS DESCR
			FROM [recycling].[F_CommodityRule]	OI WITH (NOLOCK)
			INNER JOIN [dbo].[F_PRODUCT_MASTER]		PM WITH(NOLOCK)
				ON PM.PRODUCT_MASTER_TYPE_ID = 3
				AND OI.[CommodityId] = PM.MODULE_MASTER_ID
			WHERE OI.[RecyclingOrderId] = @ORDER_ID				
			UNION
			SELECT			
				MP.PRODUCT_MASTER_ID						AS PRODUCT_MASTER_ID
				,MP.PRODUCT_MASTER_TYPE_ID					AS PRODUCT_MASTER_TYPE_ID
				,NULL										AS RECYCLING_ORDER_ITEM_MASTER_ID
				,OS.ITEM_SERVICE_TYPE_ID					AS SERVICE_ID
				,NULL										AS PRICE_TYPE_ID
				,NULL										AS IS_FLAT_FEE
				,ISNULL(OS.ITEM_SERVICE_PRICE_FOR_ONE, 0) 	AS PRICE
				,ISNULL(OS.ITEM_SERVICE_PRICE_FOR_ONE, 0) 	AS RECEIVED_PRICE
				,ISNULL(OS.ITEM_SERVICE_COUNT, 0) 			AS QTY 		
				,3											AS RECEIVE_STATUS_ID
				,OS.ITEM_SERVICE_DESCRIPTION				AS DESCR
			FROM dbo.F_RECYCLING_ORDER_ITEM_SERVICE		OS WITH (NOLOCK)
			INNER JOIN [dbo].[F_PRODUCT_MASTER]			MP WITH (NOLOCK)
				ON  MP.[PRODUCT_MASTER_TYPE_ID] = 2
				AND OS.[ITEM_SERVICE_TYPE_ID] = MP.[SERVICE_MASTER_ID]
			WHERE OS.RECYCLING_ORDER_ID = @ORDER_ID 
				AND OS.ITEM_SERVICE_TYPE_ID != 24
		) T

		IF (@BILLING_TYPE_ID = 2)
		BEGIN								
	
			IF @C_IS_DEBUG = 1
			BEGIN
				SET @MSG = 'Items: '+ cast((SELECT count(1) FROM #ITEMS) as nvarchar(20)) + '; SO id: '+ isnull(CAST(@SALES_ORDER_ID AS NVARCHAR(20)), 'NULL')
				PRINT @MSG
			END

			IF (EXISTS(SELECT TOP(1) 1 FROM #ITEMS) OR @SALES_ORDER_ID IS NOT NULL)
			BEGIN					
	
				IF (@SALES_ORDER_ID IS NULL)
				BEGIN

					INSERT INTO F_SALES_ORDER(
						STATUS_ID
						,CUSTOMER_ID
						,TERM_ID
					--	,SALES_ORDER_NUMBER
						,SALES_ORDER_DATE
						,TAX
						,SHIPPING_COST
						,HANDLING_FEE					
						,RECYCLING_ORDER_ID
						,INSERTED_BY
						,INSERTED_DT
					)
					VALUES (
						3 -- APPROVED
						,@CUSTOMER_ID
						,@SO_TERM_ID
						--,[dbo].[fn_str_AUTO_NAME_SALES_ORDER]()
						,@UTC_NOW
						,0
						,0
						,0				
						,@ORDER_ID
						,@PROCESS_CD
						,@UTC_NOW
					)

					SET @SALES_ORDER_ID = SCOPE_IDENTITY()			

					DECLARE @RepUserIds [dbo].[bigint_ID_ARRAY];
			
					INSERT INTO @RepUserIds
					VALUES (@USER_ID)
			
					EXEC [dbo].[sp_SetSalesOrderReps] 
						@OrderId		= @ORDER_ID
						,@RepUserIds	= @RepUserIds
						
				END
				ELSE BEGIN

					DECLARE @OUT_IS_DELETED BIT
					DECLARE C_DELETE_INVOICE_PAYMENTS CURSOR LOCAL FORWARD_ONLY FOR
						SELECT
							[PAYMENT_ID]
						FROM [dbo].[F_INVOICE_PAYMENT]  WITH (NOLOCK)
						WHERE [INVOICE_ID] IN (
							SELECT INVOICE_ID
							FROM dbo.F_INVOICE  WITH (NOLOCK)
							WHERE INVOICE_TYPE_ID = 1
								AND ORDER_ID = @SALES_ORDER_ID
						)
						DECLARE @CUSTOMER_PAYMENT_ID BIGINT
					OPEN C_DELETE_INVOICE_PAYMENTS
						FETCH NEXT FROM C_DELETE_INVOICE_PAYMENTS INTO @CUSTOMER_PAYMENT_ID
						WHILE @@FETCH_STATUS = 0
						BEGIN
		  
							EXEC [dbo].[sp_DEL_CUSTOMER_PAYMENT]
								@CUSTOMER_PAYMENT_ID			= @CUSTOMER_PAYMENT_ID
								,@C_TYPE_BY_ACCOUNT_TYPE_ID		= 1
								,@C_SUPPRESS_MESSAGE_SELECT		= 0 -- will throw the exception and rollback the transaction if failes to delete
								,@C_USER_ID 					= @RECEIVED_BY_USER_ID
								,@C_USER_IP 					= @USER_IP
								,@OUT_IS_DELETED				= @OUT_IS_DELETED OUT
								,@C_USE_INTERNAL_TRANSACTION	= 0

							FETCH NEXT FROM C_DELETE_INVOICE_PAYMENTS INTO @CUSTOMER_PAYMENT_ID
						END

					CLOSE C_DELETE_INVOICE_PAYMENTS
					DEALLOCATE C_DELETE_INVOICE_PAYMENTS
												
					
					DELETE FROM dbo.F_SALES_ORDER_ITEM
					WHERE SALES_ORDER_ID = @SALES_ORDER_ID		

					DELETE FROM dbo.F_INVOICE WITH(ROWLOCK)
					WHERE INVOICE_TYPE_ID = 1
						AND ORDER_ID = @SALES_ORDER_ID
			
				END
		
				INSERT INTO dbo.F_SALES_ORDER_ITEM (
					SALES_ORDER_ID, 
					PRODUCT_MASTER_ID,
					PRODUCT_MASTER_TYPE_ID,
					[CommodityRuleId],				
					PRICE_TYPE_ID,
					IS_FLAT_FEE,
					ITEM_PRICE, 
					RECEIVED_PRICE, 
					ITEM_QUANTITY, 				
					RECEIVE_STATUS_ID,
					ITEM_NOTES,
					IS_RECYCLING,
					INSERTED_BY,
					INSERTED_DT)
				SELECT
					@SALES_ORDER_ID,		
					OI.PRODUCT_MASTER_ID,
					OI.PRODUCT_MASTER_TYPE_ID,
					OI.RECYCLING_ORDER_ITEM_MASTER_ID, 
					OI.PRICE_TYPE_ID,
					OI.IS_FLAT_FEE,
					(-1) * OI.PRICE,
					(-1) * OI.RECEIVED_PRICE,
					OI.QTY,			
					3,
					OI.DESCR,
					1,
					@PROCESS_CD,
					@UTC_NOW
				FROM #ITEMS	OI 					
				WHERE OI.RECYCLING_ORDER_ITEM_MASTER_ID IS NOT NULL
		
				INSERT INTO dbo.F_SALES_ORDER_ITEM (
					SALES_ORDER_ID, 
					PRODUCT_MASTER_ID,	
					PRODUCT_MASTER_TYPE_ID,
					ITEM_PRICE, 
					RECEIVED_PRICE, 
					ITEM_QUANTITY, 		
					RECEIVE_STATUS_ID,
					ITEM_NOTES,
					IS_RECYCLING,
					INSERTED_BY,
					INSERTED_DT)
				SELECT
					@SALES_ORDER_ID,		
					OS.PRODUCT_MASTER_ID,		
					OS.PRODUCT_MASTER_TYPE_ID,
					(-1) * OS.PRICE, 
					(-1) * OS.RECEIVED_PRICE, 
					OS.QTY, 			
					3,
					OS.DESCR,
					1,
					@PROCESS_CD,
					@UTC_NOW
				FROM #ITEMS	OS WITH (NOLOCK)		
				WHERE OS.SERVICE_ID IS NOT NULL
						
				SELECT 
					@AMOUNT_DUE = SUM(ISNULL(RECEIVED_PRICE, 0) * 
							CASE
								WHEN IS_FLAT_FEE = 0 THEN ITEM_QUANTITY
								ELSE 1							
							END
						)
				FROM dbo.F_SALES_ORDER_ITEM SOI	WITH (NOLOCK)		
				WHERE SALES_ORDER_ID = @SALES_ORDER_ID					
				
				INSERT INTO F_INVOICE(
					ORDER_ID
					,DATE_CREATED
					,AMOUNT_DUE				
					,ORIGINAL_AMOUNT
					,STATUS_ID
					,FINALIZED_DT
					,REP_USER_ID
					,IS_VOIDED
					,INSERTED_BY
					,INSERTED_DT
				)
				SELECT
					@SALES_ORDER_ID
					,@UTC_NOW
					,ISNULL(@AMOUNT_DUE, 0)
					,ISNULL(@AMOUNT_DUE, 0)
					,3 --final
					,@UTC_NOW
					,@USER_ID
					,0
					,@PROCESS_CD
					,@UTC_NOW	

			END

		END ELSE
		IF (@BILLING_TYPE_ID = 3)
		BEGIN
		
			BEGIN
				SET @MSG = 'Items: '+ cast((SELECT count(1) FROM #ITEMS) as nvarchar(20)) + '; PO id: '+ ISNULL(CAST(@PURCHASE_ORDER_ID AS NVARCHAR(20)), 'NULL')
				PRINT @MSG
			END

			IF (EXISTS(SELECT TOP(1) 1 FROM #ITEMS) OR @PURCHASE_ORDER_ID IS NOT NULL) BEGIN			

				IF (@PURCHASE_ORDER_ID IS NULL) BEGIN			
				
					DECLARE @T TABLE (ID BIGINT)				
			
					INSERT INTO @T
					EXEC [dbo].[sp_SET_PURCHASE_ORDER] @ORDER_ID, @CUSTOMER_ID, @USER_ID, @PT_TERM_ID, @UTC_NOW, NULL, NULL
			
					SELECT @PURCHASE_ORDER_ID = ID FROM @T
			
					UPDATE F_PURCHASE_ORDER WITH(ROWLOCK) SET
							RECYCLING_ORDER_ID = @ORDER_ID
						,STATUS_ID		    = 3
						,RECEIVED_BY_USER_ID= @RECEIVED_BY_USER_ID
						,UPDATED_BY			= @PROCESS_CD
						,UPDATED_DT			= @UTC_NOW
					WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID
		
				END
				ELSE BEGIN
		
					DELETE FROM dbo.F_PURCHASE_ORDER_ITEM_INVOICE WITH(ROWLOCK) 
					WHERE PURCHASE_ORDER_ITEM_ID IN (
						SELECT 
							PURCHASE_ORDER_ITEM_ID
						FROM dbo.F_PURCHASE_ORDER_ITEM	WITH (NOLOCK)
						WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID
					)

					DELETE FROM dbo.F_PURCHASE_ORDER_ITEM  WITH(ROWLOCK) 
					WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID

					DELETE FROM dbo.F_INVOICE  WITH(ROWLOCK) 
					WHERE INVOICE_TYPE_ID = 2 AND ORDER_ID = @PURCHASE_ORDER_ID
			
				END
			
				INSERT INTO dbo.F_PURCHASE_ORDER_ITEM (
					PURCHASE_ORDER_ID, 
					PRODUCT_MASTER_ID,
					PRODUCT_MASTER_TYPE_ID,
					[CommodityRuleId],
					PRICE_TYPE_ID,
					IS_FLAT_FEE,
					PRICE, 
					RECEIVED_PRICE, 
					QTY, 		
					RECEIVE_STATUS_ID,
					DESCR,
					INSERTED_BY,
					INSERTED_DT)
				SELECT
					@PURCHASE_ORDER_ID,	
					OI.PRODUCT_MASTER_ID,
					OI.PRODUCT_MASTER_TYPE_ID,	
					OI.RECYCLING_ORDER_ITEM_MASTER_ID, 
					OI.PRICE_TYPE_ID,
					OI.IS_FLAT_FEE,
					OI.PRICE,
					OI.RECEIVED_PRICE,
					OI.QTY,				
					3,
					OI.DESCR,
					@PROCESS_CD,
					@UTC_NOW
				FROM #ITEMS OI
				WHERE OI.RECYCLING_ORDER_ITEM_MASTER_ID IS NOT NULL
		
				INSERT INTO dbo.F_PURCHASE_ORDER_ITEM (
					PURCHASE_ORDER_ID, 
					PRODUCT_MASTER_ID,		
					PRODUCT_MASTER_TYPE_ID,					
					PRICE, 
					RECEIVED_PRICE, 
					QTY, 		
					RECEIVE_STATUS_ID,
					DESCR,
					INSERTED_BY,
					INSERTED_DT)
				SELECT
					@PURCHASE_ORDER_ID,		
					OS.PRODUCT_MASTER_ID,
					OS.PRODUCT_MASTER_TYPE_ID,					
					OS.PRICE, 
					OS.RECEIVED_PRICE, 
					OS.QTY, 			
					3,
					OS.DESCR,
					@PROCESS_CD,
					@UTC_NOW
				FROM #ITEMS	OS 		
				WHERE OS.SERVICE_ID IS NOT NULL				
						
				SELECT 
					@AMOUNT_DUE = SUM(ISNULL(RECEIVED_PRICE, 0) * 
							CASE
								WHEN IS_FLAT_FEE = 0 THEN QTY
								ELSE 1							
							END
						)
				FROM dbo.F_PURCHASE_ORDER_ITEM SOI	WITH (NOLOCK)		
				WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID				
		
				INSERT INTO dbo.F_INVOICE (
					ORDER_ID, 
					DATE_CREATED,
					AMOUNT_DUE, 
					ORIGINAL_AMOUNT, 
					INVOICE_TYPE_ID, 
					IS_PAID, 
					IS_VOIDED, 
					IS_ADDITIONAL, 
					INSERTED_BY,
					INSERTED_DT
				)
				SELECT 
					@PURCHASE_ORDER_ID,
					@UTC_NOW,
					ISNULL(@INVOICE_SUM, 0),
					ISNULL(@INVOICE_SUM, 0),
					2,
					0,
					0,
					0,
					@PROCESS_CD,
					@UTC_NOW	
		
				SET @INVOICE_ID = SCOPE_IDENTITY()
		
				INSERT INTO dbo.F_PURCHASE_ORDER_ITEM_INVOICE (PURCHASE_ORDER_INVOICE_ID, PURCHASE_ORDER_ITEM_ID)
				SELECT
					@INVOICE_ID,
					PURCHASE_ORDER_ITEM_ID
				FROM  dbo.F_PURCHASE_ORDER_ITEM	WITH (NOLOCK)
				WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID
		
			END

		END	
	
		DROP TABLE #ITEMS
			

	IF @C_USE_INTERNAL_TRANSACTION = 1
		COMMIT TRAN

END
-- =============================================
-- Author:		
-- Create date: 
-- =============================================
CREATE FUNCTION [dbo].[fn_nvarchar_GET_MASTER_PRODUCT_NAME]
(
	@PRODUCT_MASTER_ID BIGINT
)
RETURNS NVARCHAR(256)
 
AS
BEGIN
	RETURN (
		SELECT TOP(1)
			CASE FPM.PRODUCT_MASTER_TYPE_ID
				WHEN 1 THEN (
					SELECT TOP(1)
						ITEM_NUMBER
					FROM [dbo].[F_ITEM_MASTER] WITH (NOLOCK)
					WHERE ITEM_MASTER_ID = FPM.ITEM_MASTER_ID)

				WHEN 2 THEN (
					SELECT TOP(1)
						SERVICE_TYPE_CD
					FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] WITH (NOLOCK)
					WHERE SERVICE_TYPE_ID = FPM.SERVICE_MASTER_ID)

				WHEN 3 THEN (
					SELECT TOP(1)
						RECYCLING_ITEM_MASTER_NAME
					FROM [dbo].[F_RECYCLING_ITEM_MASTER] WITH (NOLOCK)
					WHERE RECYCLING_ITEM_MASTER_ID = FPM.MODULE_MASTER_ID)
				ELSE NULL
			END
		FROM [dbo].[F_PRODUCT_MASTER]	FPM WITH (NOLOCK)
		WHERE PRODUCT_MASTER_ID = @PRODUCT_MASTER_ID
	) 
END
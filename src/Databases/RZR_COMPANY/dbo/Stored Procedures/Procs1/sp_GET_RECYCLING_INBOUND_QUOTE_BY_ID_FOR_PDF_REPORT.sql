-- Create Procedure sp_GET_RECYCLING_INBOUND_QUOTE_BY_ID_FOR_PDF_REPORT
-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <01/27/2014>
-- Description:	<gets main information over the recyclin order, required to build the Report PDF>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INBOUND_QUOTE_BY_ID_FOR_PDF_REPORT] 
	@ORDER_ID	BIGINT
AS
BEGIN

	SELECT 
		--R.RECYCLING_ORDER_ID
		IR.AUTO_NAME
		,C.CUSTOMER_NAME
		,R.PO_NUMBER
		
		,R.PICKUP_START_DATE AS PICKUP_DATE
		,IR.EQUIPMENT_NOTES
		,IR.SERVICES_NOTES
		,T.TRANSACTION_TERM_VALUE				AS PAYMENT_TERMS
		,DATEADD(dd, T.DUE_DAYS, R.INSERTED_DT) AS DUE_DATE
		--,dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(IR.CUSTOMER_CONTACT_ID)	AS [CONTACT_NAME]
		
		-- ship to
		,SA.POSTAL_CODE							AS SHIP_TO_POSTAL_CODE 
		,SA.COUNTRY								AS SHIP_TO_COUNTRY		
		,SA.[STATE]								AS SHIP_TO_STATE		
		,SA.CITY								AS SHIP_TO_CITY		
		,SA.STREET_1							AS SHIP_TO_STREET_1	
		,SA.STREET_2							AS SHIP_TO_STREET_2	
		,SA.STREET_3							AS SHIP_TO_STREET_3	
		,SA.PHONE								AS SHIP_TO_PHONE
		,SC.CUSTOMER_NAME						AS SHIP_TO_CUSTOMER_NAME
		,SCC.FIRST_NAME + ' ' + ISNULL(SCC.LAST_NAME, '')	AS SHIP_TO_CONTACT_NAME 	
		----

		-- bill to / prepeared for
		,ISNULL(CBA.POSTAL_CODE, BA.POSTAL_CODE)	AS BILL_TO_POSTAL_CODE 
		,ISNULL(CBA.COUNTRY, BA.COUNTRY	)			AS BILL_TO_COUNTRY		
		,ISNULL(CBA.[STATE], BA.[STATE]	)			AS BILL_TO_STATE		
		,ISNULL(CBA.CITY, BA.CITY)					AS BILL_TO_CITY		
		,ISNULL(CBA.STREET_1, BA.STREET_1)			AS BILL_TO_STREET_1	
		,ISNULL(CBA.STREET_2, BA.STREET_2)			AS BILL_TO_STREET_2	
		,ISNULL(CBA.STREET_3, BA.STREET_3)			AS BILL_TO_STREET_3	
		,ISNULL(CBA.PHONE, BA.PHONE)				AS BILL_TO_PHONE
		,ISNULL(CC.CUSTOMER_NAME, C.CUSTOMER_NAME)	AS BILL_TO_CUSTOMER_NAME
		----

		-- pick up location
		,PL.POSTAL_CODE		AS PICKUP_POSTAL_CODE	
		,PL.COUNTRY			AS PICKUP_COUNTRY		
		,PL.[STATE]			AS PICKUP_STATE	
		,PL.CITY			AS PICKUP_CITY		
		,PL.STREET_1		AS PICKUP_STREET_1	
		,PL.STREET_2		AS PICKUP_STREET_2	
		,PL.STREET_3		AS PICKUP_STREET_3	
		,PL.PHONE			AS PICKUP_PHONE		

		-- sales rep
		,dbo.fn_str_GET_USER_AUTO_NAME(U.UserID, 0) AS SALES_REP_USER_NAME
		,U.PHONE_MOBILE								AS SALES_REP_PERSONAL_PHONE
		,U.PHONE_MAIN								AS SALES_REP_COMPANY_PHONE
		,U.Email									AS SALER_REP_EMAIL
				
	FROM		F_RECYCLING_ORDER				R	WITH(NOLOCK)
	INNER JOIN  F_RECYCLING_ORDER_INBOUND		IR  WITH(NOLOCK)
		ON R.RECYCLING_ORDER_ID = IR.RECYCLING_ORDER_ID
	INNER JOIN	F_CUSTOMER						C	WITH(NOLOCK)
		ON R.CUSTOMER_ID = C.CUSTOMER_ID
	INNER JOIN  C_CUSTOMER_TRANSACTION_TERM		T	WITH(NOLOCK)
		ON R.SO_TERM_ID = T.CUSTOMER_TRANSACTION_TERM_ID
	LEFT JOIN	F_CUSTOMER						SC	WITH(NOLOCK)
		ON IR.SHIP_TO_CUSTOMER_ID = SC.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER_CONTACT				SCC	WITH(NOLOCK)
		ON SCC.CUSTOMER_ID = IR.SHIP_TO_CUSTOMER_ID AND SCC.IS_MAIN = 1
	LEFT JOIN	F_CUSTOMER_ADDRESS				SA	WITH(NOLOCK)
		ON IR.SHIP_TO_ADDRESS_ID = SA.CUSTOMER_ADDRESS_ID
	LEFT JOIN	F_CUSTOMER_ADDRESS				BA	WITH(NOLOCK)
		ON  R.CUSTOMER_ID = BA.CUSTOMER_ID
		AND BA.CUSTOMER_ADDRESS_TYPE_ID = 3 AND BA.IS_MAIN = 1 -- "3" is "BILL TO" in C_CUSTOMER_ADDRESS_TYPE
	LEFT JOIN F_RECYCLING_ORDER_CONTRACT		ROC	WITH(NOLOCK)
		ON ROC.RECYCLING_ORDER_ID = R.RECYCLING_ORDER_ID
		AND ROC.IsFirstApplied = 1
	LEFT JOIN [dbo].[F_CONTRACT] CTRCT WITH (NOLOCK)
		ON ROC.CONTRACT_ID = CTRCT.CONTRACT_ID
	LEFT JOIN F_CUSTOMER_ADDRESS CBA WITH (NOLOCK)
		ON CTRCT.BILLING_ADDRESS_ID = CBA.CUSTOMER_ADDRESS_ID
	LEFT JOIN F_CUSTOMER_CONTACT CBC WITH (NOLOCK)
		ON CTRCT.BILLING_CONTACT_ID = CBC.CUSTOMER_CONTACT_ID
	LEFT JOIN	F_CUSTOMER					CC	WITH(NOLOCK)
		ON CTRCT.CUSTOMER_ID = CC.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER_ADDRESS				PL	WITH(NOLOCK)
		ON IR.CUSTOMER_ADDRESS_ID = PL.CUSTOMER_ADDRESS_ID
	LEFT JOIN	tb_User							U	WITH(NOLOCK)
		ON U.UserID = R.USER_ID
	WHERE R.RECYCLING_ORDER_ID = @ORDER_ID
	
END
-- Create Procedure sp_DELETE_CONTRACTS

-- =============================================
-- Author:		R<PERSON><PERSON><PERSON><PERSON>kin
-- Create date: 06/20/2014
-- Description: delete contracts
-- =============================================
CREATE PROCEDURE [dbo].[sp_DELETE_CONTRACTS]
	@CONTRACTS XML
AS
BEGIN
	DECLARE @idoc INT

	EXEC sp_xml_preparedocument @idoc OUTPUT, @CONTRACTS
		
	DECLARE @tCONTRACTS TABLE (ID BIGINT)
	INSERT INTO @tCONTRACTS (ID)
	SELECT 
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( 
		ID BIGINT 
	) AS x		         
	EXEC sp_xml_removedocument @idoc

	SET XACT_ABORT ON
	BEGIN TRANSACTION
		-- Cleanup contract assets
		DELETE CAC
		FROM F_CONTRACT_ASSET_CHARGE_TYPE			AS CAC
		INNER JOIN F_CONTRACT_ASSET					AS CA
			ON CA.CONTRACT_ASSET_ID = CAC.CONTRACT_ASSET_ID
		INNER JOIN @tCONTRACTS						AS	T
			ON T.ID = CA.CONTRACT_ID
		
		DELETE CAC
		FROM F_CONTRACT_ASSET_PROCESSING			AS CAC
		INNER JOIN F_CONTRACT_ASSET					AS CA
			ON CA.CONTRACT_ASSET_ID = CAC.CONTRACT_ASSET_ID
		INNER JOIN @tCONTRACTS						AS	T
			ON T.ID = CA.CONTRACT_ID

		DELETE F_CONTRACT_ASSET
		FROM F_CONTRACT_ASSET	AS CA
		INNER JOIN @tCONTRACTS	AS T
			ON T.ID = CA.CONTRACT_ID

		-- Cleanup commodities
		DELETE FC
		FROM [recycling].[F_CommodityRule]	AS	FC
		INNER JOIN @tCONTRACTS		AS	T 
			ON T.ID = FC.[ContractId]
			
		-- Cleanup services
		DELETE FC
		FROM [recycling].[F_ContractService]		AS	FC
		INNER JOIN @tCONTRACTS		AS	T 
			ON T.ID = FC.[ContractId]

		-- Cleanup SLA rules
		DELETE FC
		FROM [dbo].[F_CommoditySlaRule]		AS	FC
		INNER JOIN @tCONTRACTS				AS	T 
			ON T.ID = FC.[ContractId]

		-- Cleanup notification scope
		DELETE NGS
		FROM F_NOTIFICATION_GROUP_SCOPE	AS	NGS
		INNER JOIN @tCONTRACTS			AS	T 
			ON T.ID = NGS.CONTRACT_ID

		-- Cleanup notification queue -------------------------------
		DECLARE @notificationQueueIds [dbo].[bigint_ID_ARRAY];

		INSERT INTO @notificationQueueIds
		SELECT DISTINCT NQP.[NOTIFICATION_QUEUE_ID]
		FROM [dbo].[F_NOTIFICATION_QUEUE_PARAMS] AS NQP WITH (NOLOCK)
		INNER JOIN @tCONTRACTS AS T
			ON T.[ID] = NQP.[PARAM_VALUE] AND NQP.[PARAM_TYPE_NAME] = N'CONTRACT_ID';
		
		DELETE NQP
		FROM [dbo].[F_NOTIFICATION_QUEUE_PARAMS] AS	NQP WITH (ROWLOCK)
		INNER JOIN @notificationQueueIds AS	IDS 
			ON NQP.[NOTIFICATION_QUEUE_ID] = IDS.[ID];

		DELETE NQ
		FROM [dbo].[F_NOTIFICATION_QUEUE] AS NQ WITH (ROWLOCK)
		INNER JOIN @notificationQueueIds AS IDS
			ON NQ.[NOTIFICATION_QUEUE_ID] = IDS.[ID];
		------------------------------- Cleanup notification queue --


		DELETE FROM F_CONTRACT_FILE
		WHERE CONTRACT_ID IN (SELECT ID FROM @tCONTRACTS)

		--TODO: not to forget about removing this after deleting table
		DELETE FROM [dbo].[F_CONTRACT_VALIDATION_REQUIREMENTS]
		WHERE CONTRACT_ID IN (SELECT ID FROM @tCONTRACTS)

		UPDATE F_CUSTOMER SET
			PRICING_TEMPLATE_ID = NULL
		WHERE PRICING_TEMPLATE_ID IN (SELECT ID FROM @tCONTRACTS)

		UPDATE FC 
			SET FC.PARENT_CONTRACT_ID = NULL
		FROM F_CONTRACT				AS	FC
		INNER JOIN @tCONTRACTS		AS	T 
			ON T.ID = FC.PARENT_CONTRACT_ID

		UPDATE FC 
			SET FC.LAST_APPLIED_TEMPLATE_ID = NULL
		FROM F_CONTRACT				AS	FC
		INNER JOIN @tCONTRACTS		AS	T 
			ON T.ID = FC.LAST_APPLIED_TEMPLATE_ID
	
		--commodity rules
		delete cp
		from		[recycling].[F_CommodityRule] as cp
		inner join	@tCONTRACTS						as c
			on cp.[ContractId] = c.[ID]

		update cp
			set cp.[SourceContractId] = null
		from		[recycling].[F_CommodityRule] as cp
		inner join	@tCONTRACTS						as c
			on cp.[SourceContractId] = c.[ID]

		DELETE FC 
		FROM F_CONTRACT				AS	FC
		INNER JOIN @tCONTRACTS		AS	T 
			ON T.ID = FC.CONTRACT_ID
	COMMIT TRANSACTION
END

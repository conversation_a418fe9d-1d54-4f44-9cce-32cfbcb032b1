CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMER_CONTACTS_FOR_ADDRESS] 		
	@CUSTOMER_ID	BIGINT,
	@TERM			VARCHAR(50),
	@COUNT			INT
AS
BEGIN

	if(@CUSTOMER_ID = 0)
	  begin
		set @CUSTOMER_ID = [dbo].[fn_bigint_GET_MAIN_CUSTOMER_ID]()
	  end

	SELECT TOP(@COUNT)
		cc.CUSTOMER_CONTACT_ID												AS Value,		
		dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID)	AS Label,
		cc.JOB_TITLE														AS [Desc],
		cc.FIRST_NAME														AS FirstName,
		cc.LAST_NAME														AS LastName,
		cc.MIDDLE_INITIAL													AS MiddleInitial
	FROM dbo.F_CUSTOMER_CONTACT					cc	WITH(NOLOCK)
	WHERE cc.CUSTOMER_ID = @CUSTOMER_ID AND	(
			cc.SALUTATION		LIKE @TERM  OR 
			cc.FIRST_NAME		LIKE @TERM  OR
			cc.MIDDLE_INITIAL	LIKE @TERM  OR
			cc.LAST_NAME		LIKE @TERM  OR
			cc.JOB_TITLE		LIKE @TERM  OR 
			dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID) LIKE @TERM)
	ORDER BY 
		CC.IS_MAIN DESC
		,dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID) ASC
		
END
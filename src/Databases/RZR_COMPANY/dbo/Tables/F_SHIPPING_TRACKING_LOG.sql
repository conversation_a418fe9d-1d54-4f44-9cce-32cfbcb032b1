CREATE TABLE [dbo].[F_SHIPPING_TRACKING_LOG] (
    [SHIPPING_ID]            BIGINT         NOT NULL,
    [NEEDS_RETRACK]          BIT            CONSTRAINT [DF_F_SHIPPING_TRACKING_LOG_BEING_TRACKED] DEFAULT ((0)) NOT NULL,
    [PROVIDER_ID]            INT            NOT NULL,
    [MASTER_TRACKING_NO]     NVARCHAR (64)  NULL,
    [LAST_STATUS]            NVARCHAR (512) NULL,
    [PACKAGE_COUNT]          INT            NOT NULL,
    [SHIPPER_ACCOUNT_NO]     NVARCHAR (64)  NULL,
    [TOTAL_WEIGHT]           FLOAT (53)     NOT NULL,
    [TRACKING_ID_TYPE_ID]    INT            NOT NULL,
    [TRACKING_EXCEPTION_MSG] NVARCHAR (MAX) NULL,
    [IS_DELETED]             BIT            CONSTRAINT [DF_F_SHIPPING_TRACKING_INFO_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]            VARCHAR (150)  CONSTRAINT [DF_F_SHIPPING_TRACKING_INFO_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]            DATETIME       CONSTRAINT [DF_F_SHIPPING_TRACKING_INFO_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]             VARCHAR (150)  NULL,
    [UPDATED_DT]             DATETIME       NULL,
    [DELETED_BY]             VARCHAR (150)  NULL,
    [DELETED_DT]             DATETIME       NULL,
    [TOTAL_WEIGHT_KG]        AS             ([TOTAL_WEIGHT]*(0.********)) PERSISTED,
    CONSTRAINT [PK_F_SHIPPING_TRACKING_LOG] PRIMARY KEY CLUSTERED ([SHIPPING_ID] ASC),
    CONSTRAINT [FK_F_SHIPPING_TRACKING_LOG_F_SHIPPING] FOREIGN KEY ([SHIPPING_ID]) REFERENCES [dbo].[F_SHIPPING] ([SHIPPING_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);


















































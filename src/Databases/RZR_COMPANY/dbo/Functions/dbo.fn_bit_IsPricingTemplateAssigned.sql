-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE function [dbo].[fn_bit_IsPricingTemplateAssigned]
(
	@resalePricingId		BIGINT
)
RETURNS bit
AS
BEGIN
	DECLARE @result bit = 0

	if (exists(
		select top(1) 1
		from [dbo].[F_CONTRACT_RESALE_PRICING] with (nolock)			
		where [GlobalPricingId] = @resalePricingId
	))
	begin
		select @result = 1
	end
	
	RETURN 
		@result
END
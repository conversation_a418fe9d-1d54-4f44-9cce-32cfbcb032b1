CREATE TABLE [dbo].[D_CONTRACT_ATTRIBUTE_CAPABILITY] (
    [CONTRACT_ATTRIBUTE_CAPABILITY_ID] INT            IDENTITY (1, 1) NOT NULL,
    [CONTRACT_ATTRIBUTE_TYPE_ID]       INT            NOT NULL,
    [CONTRACT_CAPABILITY_TYPE_ID]      INT            NOT NULL,
    [INSERTED_BY]                      NVARCHAR (150) NOT NULL,
    [INSERTED_DT]                      DATETIME       NOT NULL,
    [UPDATED_BY]                       NVARCHAR (150) NULL,
    [UPDATED_DT]                       DATETIME       NULL,
    CONSTRAINT [PK_D_CONTRACT_ATTRIBUTE_CAPABILITY] PRIMARY KEY CLUSTERED ([CONTRACT_ATTRIBUTE_CAPABILITY_ID] ASC),
    CONSTRAINT [FK_D_CONTRACT_ATTRIBUTE_CAPABILITY_D_CONTRACT_ATTRIBUTE_TYPE] FOREIGN KEY ([CONTRACT_ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[D_CONTRACT_ATTRIBUTE_TYPE] ([CONTRACT_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_D_CONTRACT_ATTRIBUTE_CAPABILITY_D_CONTRACT_CAPABILITY_TYPE] FOREIGN KEY ([CONTRACT_CAPABILITY_TYPE_ID]) REFERENCES [dbo].[D_CONTRACT_CAPABILITY_TYPE] ([CONTRACT_CAPABILITY_TYPE_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is used for recycling ITAD FMV Price Sheet page. It has several tabs, one for attribute type. This table is used to set for which tab (D_CONTRACT_ATTRIBUTE_TYPE) which capability (D_CONTRACT_CAPABILITY_TYPE) is used.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_CONTRACT_ATTRIBUTE_CAPABILITY';


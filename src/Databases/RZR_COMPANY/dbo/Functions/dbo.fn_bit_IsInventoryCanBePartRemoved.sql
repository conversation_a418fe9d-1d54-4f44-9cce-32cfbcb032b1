
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
create function [dbo].[fn_bit_IsInventoryCanBePartRemoved]
(
	@ParentInventoryId		BIGINT,
	@Parts					dbo.bigint_ID_ARRAY readonly
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @result bit = 1
	
	;WITH nesteds AS
	(		
		select 			
			PARENT_ITEM_INVENTORY_ID,  
			ITEM_INVENTORY_ID
		from [dbo].[F_ITEM_INVENTORY]	with (nolock) 
		where PARENT_ITEM_INVENTORY_ID = @ParentInventoryId 
		UNION ALL
		select			
			n.PARENT_ITEM_INVENTORY_ID,  
			n.ITEM_INVENTORY_ID
		from nesteds nn
		inner join [dbo].[F_ITEM_INVENTORY] n	with (nolock) 
			on nn.PARENT_ITEM_INVENTORY_ID = n.ITEM_INVENTORY_ID		
	)

	select top(1) @result = 0 from nesteds with (nolock) where PARENT_ITEM_INVENTORY_ID in (select id from @Parts)
	
	RETURN 
		ISNULL(@result, 0)
END
-- A legacy table - TODO: move all we need from there into F_CONTRACT
CREATE TABLE [dbo].[F_CONTRACT_ASSET] (
    [CONTRACT_ASSET_ID]                                 BIGINT         IDENTITY (1, 1) NOT NULL,
    [CONTRACT_ID]                                       BIGINT         NOT NULL,
    [IS_ITAD_ON_WHOLE_ORDER]                            BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_ITAD_ON_WHOLE_ORDER] DEFAULT ((0)) NOT NULL,
    [GENERAL_REQUIREMENT_NOTES]                         NVARCHAR (MAX) NULL,
    [IS_DATA_WIPE_CERTIFICATE_DISTRUCTION]              BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_DATA_WIPE_CERTIFICATE_DISTRUCTION] DEFAULT ((0)) NULL,
    [DATA_WIPE_NOTES]                                   NVARCHAR (MAX) NULL,
    [IS_PROCESSING_REQUIREMENT_CERTIFICATE_DESTRUCTION] BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_PROCESSING_REQUIREMENT_CERTIFICATE_DESTRUCTION] DEFAULT ((0)) NULL,
    [REQUIREMENT_DESTRUCTION_NOTES]                     NVARCHAR (MAX) NULL,
    [PHOTO_REQUIREMENTS_NOTES]                          NVARCHAR (MAX) NULL,
    [DATA_COLLECTION_PRICE]                             MONEY          NOT NULL,
    [IS_HD_DATA_COLLECTION_SERIAL_NUMBER]               BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_HD_DATA_COLLECTION_SERIAL_NUMBER] DEFAULT ((0)) NOT NULL,
    [IS_HD_DATA_COLLECTION_MANUFACTURE]                 BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_HD_DATA_COLLECTION_MANUFACTURE] DEFAULT ((0)) NOT NULL,
    [IS_HD_DATA_COLLECTION_MODEL]                       BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_HD_DATA_COLLECTION_MODEL] DEFAULT ((0)) NOT NULL,
    [IS_HD_DATA_COLLECTION_PARENT_SERIAL]               BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_HD_DATA_COLLECTION_PARENT_SERIAL] DEFAULT ((0)) NOT NULL,
    [IS_DETAG_ALL_EQUIPMENT]                            BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_DETAG_EQUIPMENT] DEFAULT ((0)) NOT NULL,
    [DETAG_ALL_EQUIPMENT_PRICE]                         MONEY          NOT NULL,
    [IS_GRADE_ALL_ASSETS]                               BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_GRADE_ALL_ASSETS] DEFAULT ((0)) NOT NULL,
    [GRADE_ALL_ASSETS_PRICE]                            MONEY          NOT NULL,
    [IS_INACTIVE]                                       BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                                        BIT            CONSTRAINT [DF_F_CONTRACT_ASSET_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                                       VARCHAR (250)  NOT NULL,
    [INSERTED_DT]                                       DATETIME       NOT NULL,
    [UPDATED_BY]                                        VARCHAR (250)  NULL,
    [UPDATED_DT]                                        DATETIME       NULL,
    [DELETED_BY]                                        VARCHAR (250)  NULL,
    [DELETED_DT]                                        DATETIME       NULL,
    CONSTRAINT [PK_F_CONTRACT_ASSET] PRIMARY KEY CLUSTERED ([CONTRACT_ASSET_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_ASSET_F_CONTRACT] FOREIGN KEY ([CONTRACT_ID]) REFERENCES [dbo].[F_CONTRACT] ([CONTRACT_ID])
);
















GO
CREATE NONCLUSTERED INDEX [IX_CONTRACT_ID]
    ON [dbo].[F_CONTRACT_ASSET]([CONTRACT_ID] ASC);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The contract has ITAD asset tab. Here the details are stored', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_ASSET';


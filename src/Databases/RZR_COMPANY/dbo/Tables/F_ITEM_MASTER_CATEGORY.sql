CREATE TABLE [dbo].[F_ITEM_MASTER_CATEGORY] (
    [ITEM_MASTER_CATEGORY_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [CATEGORY_ID]             INT           NOT NULL,
    [ITEM_MASTER_ID]          BIGINT        NOT NULL,
    [IS_PRIMARY]              BIT           CONSTRAINT [DF_F_ITEM_MASTER_CATEGORY_IS_PRIMARY] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]             BIT           CONSTRAINT [DF_F_ITEM_MASTER_CATEGORY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]              BIT           CONSTRAINT [DF_F_ITEM_MASTER_CATEGORY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]             VARCHAR (150) NOT NULL,
    [INSERTED_DT]             DATETIME      NOT NULL,
    [UPDATED_BY]              VARCHAR (150) NULL,
    [UPDATED_DT]              DATETIME      NULL,
    [DELETED_BY]              VARCHAR (150) NULL,
    [DELETED_DT]              DATETIME      NULL,
    [InsertedByUserId]        BIGINT        NULL,
    [InsertedByUserIp]        BIGINT        NULL,
    [UpdatedByUserId]         BIGINT        NULL,
    [UpdatedByUserIp]         BIGINT        NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_CATEGORY] PRIMARY KEY CLUSTERED ([ITEM_MASTER_CATEGORY_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_CATEGORY_D_CATEGORY_HIERARCHY] FOREIGN KEY ([CATEGORY_ID]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_CATEGORY_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]) ON DELETE CASCADE
);

















GO
CREATE UNIQUE NONCLUSTERED INDEX [IX__F_ITEM_MASTER_CATEGORY__PRIMARY]
    ON [dbo].[F_ITEM_MASTER_CATEGORY]([ITEM_MASTER_ID], [CATEGORY_ID]) 
	WHERE ([IS_PRIMARY] = (1))

GO
CREATE NONCLUSTERED INDEX [IX_MASTER_ID]
    ON [dbo].[F_ITEM_MASTER_CATEGORY]([ITEM_MASTER_ID] ASC, [IS_PRIMARY])
INCLUDE([CATEGORY_ID])

GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_MASTER_CATEGORY__IS_PRIMARY__INCLUDE__CATEGORY_ID__ITEM_MASTER_ID]
    ON [dbo].[F_ITEM_MASTER_CATEGORY]([IS_PRIMARY] ASC)
    INCLUDE([CATEGORY_ID], [ITEM_MASTER_ID]);
GO

CREATE NONCLUSTERED INDEX [IX_MASTER_ID_CATEGORY_ID]
    ON [dbo].[F_ITEM_MASTER_CATEGORY]([ITEM_MASTER_ID] ASC, [CATEGORY_ID] ASC, [IS_PRIMARY] ASC, [IS_DELETED] ASC, [IS_INACTIVE] ASC)
GO

GO

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The set of categories for model.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_CATEGORY';


GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_MASTER_CATEGORY__CATEGORY_ID__IS_DELETED]
    ON [dbo].[F_ITEM_MASTER_CATEGORY]([CATEGORY_ID] ASC, [IS_DELETED] ASC);


GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_MASTER_CATEGORY_PUT_CHANGE]
    ON  [dbo].[F_ITEM_MASTER_CATEGORY]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_MASTER_ID, d.ITEM_MASTER_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_MASTER_ID = d.ITEM_MASTER_ID	
	where isnull(i.[IS_PRIMARY], 0) <> isnull(d.[IS_PRIMARY], 0)
		or isnull(i.[CATEGORY_ID], 0) <> isnull(d.[CATEGORY_ID], 0)
		or isnull(i.[IS_DELETED], 0) <> isnull(d.[IS_DELETED], 0)
		or isnull(i.[IS_INACTIVE], 0) <> isnull(d.[IS_INACTIVE], 0)
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 3--MasterItem
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO
-- =============================================
-- Author:             
-- Author update:      
-- Create date:        
-- Update date:        
-- Reason for update:  
-- Description:	       
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_MASTER_CATEGORY_AFTER_UPDATE]
    ON  [dbo].[F_ITEM_MASTER_CATEGORY]
    AFTER UPDATE, DELETE, INSERT
AS 
BEGIN
		
	DECLARE
		 @PROCESS_CD NVARCHAR(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
	
	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	)

	INSERT INTO @logData
	SELECT @PROCESS_CD
               ,NEW.[USER_ID]
               ,NEW.[USER_IP]
               ,NEW.[OPERATION_NAME]
               ,18 --N'Master Item category add',
               ,NEW.[ENTITY_KEY_VALUE]
               ,NEW.[ENTITY_AUTO_NAME]
	           ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT NEW.category_name                                                   
	                                          FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT))		AS [CHANGES]
           FROM (
                     SELECT -- Fixed columns		  
                            
							case
								when D.ITEM_MASTER_ID is null then 'Inserted'								
								else N'Updated'													
							end AS [OPERATION_NAME]
                           ,ISNULL(I.[UpdatedByUserId], I.[InsertedByUserId])			AS [USER_ID]
                           ,ISNULL(I.[UpdatedByUserIp], I.[InsertedByUserIp])			AS [USER_IP]
                           ,ISNULL(I.ITEM_MASTER_ID, D.ITEM_MASTER_ID)					AS [ENTITY_KEY_VALUE]
                           ,im.ITEM_NUMBER												AS [ENTITY_AUTO_NAME]
                           -- XML columns														
                           ,CASE
                               WHEN isnull(D.[IS_PRIMARY], 0) = 1 and isnull(I.[IS_PRIMARY], 0) = 0
                               THEN N'Category ' + + ch.ITEM_CATEGORY_FULL_PATH   + ' was primary '                                 
                               WHEN isnull(D.[IS_PRIMARY], 0) = 0 and isnull(I.[IS_PRIMARY], 0) = 1
                               THEN N'Category ' + ch.ITEM_CATEGORY_FULL_PATH + ' is primary '  
							   else N'Category ' + ch.ITEM_CATEGORY_FULL_PATH        
                            END																	AS category_name                           													
                       FROM INSERTED                        AS I
                left JOIN DELETED                         AS D                   
					ON I.ITEM_MASTER_ID = D.ITEM_MASTER_ID and i.[CATEGORY_ID] = d.[CATEGORY_ID]
                left join [dbo].[F_ITEM_MASTER]						im with (nolock)
					on I.ITEM_MASTER_ID = im.ITEM_MASTER_ID			
				left join dbo.D_CATEGORY_HIERARCHY					ch with (nolock)
					on 		i.CATEGORY_ID = ch.CATEGORY_ID  
				where  D.ITEM_MASTER_ID is null or  isnull(D.[IS_PRIMARY], 0) <> isnull(I.[IS_PRIMARY], 0)                   
                ) NEW
    
    INSERT INTO F_LOG_DATA 	WITH(ROWLOCK)
	(	
                [SOURCE]
               ,[USER_ID]
               ,[USER_IP]
               ,[OPERATION_NAME]
               ,[ENTITY_TYPE_ID]
               ,[ENTITY_KEY_VALUE]
               ,[ENTITY_AUTO_NAME]
               ,[CHANGES])
         SELECT
				[SOURCE]
               ,[USER_ID]
               ,[USER_IP]
               ,[OPERATION_NAME]
               ,[ENTITY_TYPE_ID]
               ,[ENTITY_KEY_VALUE]
               ,[ENTITY_AUTO_NAME]
               ,[CHANGES]
		from @logData

END
GO

GO



GO
-- =============================================
-- Author:		
-- Create date: 
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_MASTER_CATEGORY_AFTER_DELETE]
    ON  [dbo].[F_ITEM_MASTER_CATEGORY]
    AFTER DELETE
AS 
BEGIN									
    
	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	)
    
    INSERT INTO @logData(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
    SELECT
	   'trg_ITEM_MASTER_CATEGORY_AFTER_DELETE',	   
	   NEW.[USER_ID],
	   NEW.USER_IP,
	   NEW.OPERATION_NAME,
	   18, --N'Master Item category add',
	   NEW.ENTITY_KEY_VALUE,
	   NEW.ENTITY_AUTO_NAME,	   
	   [dbo].[fn_str_STRIP_XML_TAGS]((SELECT
			NEW.category_name			
	   FOR XML
	   PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
    FROM
    (
		SELECT
			-- Fixed columns		  
			N'Deleted'														as operation_name
			,I.InsertedByUserId												as [user_id]
			,I.InsertedByUserIp												as [user_ip]
			,I.ITEM_MASTER_ID												as entity_key_value
			,im.ITEM_NUMBER													as entity_auto_name			
		  -- XML columns	
			,'Category ' + ch.ITEM_CATEGORY_FULL_PATH						as category_name			
		
		FROM		DELETED								i
		left join [dbo].[F_ITEM_MASTER]						im with (nolock)
			on I.ITEM_MASTER_ID = im.ITEM_MASTER_ID
		left join dbo.D_CATEGORY_HIERARCHY					ch with (nolock)
			on 		i.CATEGORY_ID = ch.CATEGORY_ID
    ) NEW


	INSERT INTO [dbo].[F_LOG_DATA] WITH(ROWLOCK)
	(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
	SELECT
		[SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]
	FROM @logData
	
END
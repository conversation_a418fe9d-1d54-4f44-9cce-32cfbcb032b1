
-- =============================================
-- Author:		I.Orobets
-- Create date: 05/18/2016
-- Description: Is Consignment PO has Invoice
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IS_CONSIGNMENT_PO_HAS_INVOICE] 
(
	@PURCHASE_ORDER_ID BIGINT
)
RETURNS BIT
AS
BEGIN

	IF(EXISTS(
		SELECT TOP(1) 1
		FROM F_PURCHASE_ORDER_ITEM_INVOICE		POII	WITH (NOLOCK)
		INNER JOIN F_PURCHASE_ORDER_ITEM		POI		WITH (NOLOCK)
			ON POI.PURCHASE_ORDER_ITEM_ID = POII.PURCHASE_ORDER_ITEM_ID
			AND POI.IS_DELETED = 0
		INNER JOIN F_ORDER_ORDER_SUBJECT_TYPE	OOST	WITH (NOLOCK)
			ON OOST.ORDER_ID = POI.PURCHASE_ORDER_ID
			AND OOST.ENTITY_TYPE_ID = 3 -- PO
		WHERE POI.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID
		AND OOST.[ENTITY_SUBJECT_TYPE_ID] = 7 -- Consignment
	  ))
    BEGIN
	   RETURN 1;
    END
    RETURN 0;

END
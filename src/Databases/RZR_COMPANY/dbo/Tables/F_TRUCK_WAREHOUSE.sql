CREATE TABLE [dbo].[F_TRUCK_WAREHOUSE] (
    [TRUCK_WAREHOUSE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [TRUCK_ID]           BIGINT        NOT NULL,
    [WAREHOUSE_ID]       BIGINT        NOT NULL,
    [INSERTED_BY]        VARCHAR (150) NOT NULL,
    [INSERTED_DT]        DATETIME      NOT NULL,
    [UPDATED_BY]         VARCHAR (150) NULL,
    [UPDATED_DT]         DATETIME      NULL,
    CONSTRAINT [PK_F_TRUCK_WAREHOUSE] PRIMARY KEY CLUSTERED ([TRUCK_WAREHOUSE_ID] ASC),
    CONSTRAINT [FK_F_TRUCK_WAREHOUSE_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]),
    CONSTRAINT [FK_F_TRUCK_WAREHOUSE_F_TRUCK] FOREIGN KEY ([TRUCK_ID]) REFERENCES [dbo].[F_TRUCK] ([TRUCK_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);


CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_LOCATION_DETAILS] 
	@RECYCLING_ORDER_ID				BIGINT,
	@EQUIPMENT_LOCATION				VARCHAR(250),
	@HAS_LOADING_DOCK				BIT,
	@HAS_ELEVATOR					BIT,
	@HAS_STEPS						BIT,
	@LOAD_FROM_GROUND				BIT,
	@FORKLIFT_FROM_GROUND			BIT,
	@BUILDING_NOTES					VARCHAR(550),
	@BOL_SPECIAL_INSTRUCTIONS		VARCHAR(550),
	@PICKUP_PROCESSING_NOTES		VARCHAR(550),
	@IS_LIFT_GATE_REQUIRED			BIT,
	@WORK_PERMISSION_ID				INT,
	@SPECIFIC_PERMITTED_HOURS		VARCHAR(100),
	@CLEARANCE_RESTRICTIONS			BIT,
	@CLEARANCE_RESTRICTIONS_VALUE	FLOAT,

	@IS_DOCK_HEIGHT_OVER_14			BIT,
	@IS_53_SEMI_ACCESSIBLE			BIT,
	@LOCATION_OF_MATERIALS			NVARCHAR(500),
	@INVENTORY_LIST					NVARCHAR(500),
	@ROLLING_BINS_TO_DROP_OFF		INT,
	@ROLLING_BINS_TO_PICK_UP		INT,

	@IS_SINGLE_LOCATION	BIT,
	@IS_SPECIAL_CREDENTIALS			BIT,
	@TRUCK_PARK						NVARCHAR(500),
	@LARGE_ITEMS_INFO				NVARCHAR(500),

	@USER_ID						BIGINT,
	@IP								BIGINT
AS
BEGIN
	DECLARE @LOCATION_DETAILS_ID BIGINT = (
		SELECT TOP(1) 
			OI.LOCATION_DETAILS_ID
		FROM F_RECYCLING_ORDER_INBOUND	OI
		WHERE OI.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	)

	IF (@LOCATION_DETAILS_ID IS NULL)
	BEGIN
		BEGIN TRANSACTION
			INSERT INTO F_RECYCLING_LOCATION_DETAILS(
				LOCATION
				,LOADING_DOCK
				,ELEVATOR_ACCESS		
				,STEPS
				,LOAD_FROM_GROUND
				,FORKLIFT_FROM_GROUND			
				,BUILDING_NOTES
				,BOL_SPECIAL_INSTRUCTIONS
				,PICKUP_PROCESSING_NOTES
				,IS_LIFT_GATE_REQUIRED
				,WORK_PERMISSION_ID
				,SPECIFIC_PERMITTED_HOURS
				,CLEARANCE_RESTRICTIONS
				,CLEARANCE_RESTRICTIONS_VALUE
				,IS_DOCK_HEIGHT_OVER_14		
				,IS_53_SEMI_ACCESSIBLE		
				,LOCATION_OF_MATERIALS		
				,INVENTORY_LIST				
				,ROLLING_BINS_TO_DROP_OFF	
				,ROLLING_BINS_TO_PICK_UP	
				,[IS_LOCATED_IN_SINGLE_LOCATION]	
				,[IS_SPECIAL_CREDENTIALS]
				,[TRUCK_PARK]
				,[LARGE_ITEMS_INFO]	
				,INSERTED_BY
				,INSERTED_DT
			) VALUES (
				 @EQUIPMENT_LOCATION 
				,@HAS_LOADING_DOCK	
				,@HAS_ELEVATOR		
				,@HAS_STEPS
				,@LOAD_FROM_GROUND
				,@FORKLIFT_FROM_GROUND			
				,@BUILDING_NOTES
				,@BOL_SPECIAL_INSTRUCTIONS
				,@PICKUP_PROCESSING_NOTES
				,@IS_LIFT_GATE_REQUIRED
				,@WORK_PERMISSION_ID
				,@SPECIFIC_PERMITTED_HOURS
				,@CLEARANCE_RESTRICTIONS
				,@CLEARANCE_RESTRICTIONS_VALUE
				,@IS_DOCK_HEIGHT_OVER_14		
				,@IS_53_SEMI_ACCESSIBLE		
				,@LOCATION_OF_MATERIALS		
				,@INVENTORY_LIST				
				,@ROLLING_BINS_TO_DROP_OFF	
				,@ROLLING_BINS_TO_PICK_UP	
				,@IS_SINGLE_LOCATION
				,@IS_SPECIAL_CREDENTIALS
				,@TRUCK_PARK
				,@LARGE_ITEMS_INFO
				,N'sp_SET_RECYCLING_ORDER_LOCATION_DETAILS'
				,GETUTCDATE()
			)

			SET @LOCATION_DETAILS_ID = SCOPE_IDENTITY()

			UPDATE F_RECYCLING_ORDER_INBOUND SET
				LOCATION_DETAILS_ID = @LOCATION_DETAILS_ID,
				UPDATED_BY_USER		= @USER_ID,
				UPDATED_BY_IP		= @IP
			WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		COMMIT TRANSACTION
	END
	ELSE
	BEGIN
		UPDATE F_RECYCLING_LOCATION_DETAILS SET
			LOCATION							= 	@EQUIPMENT_LOCATION
			,LOADING_DOCK						= 	@HAS_LOADING_DOCK	
			,ELEVATOR_ACCESS					= 	@HAS_ELEVATOR		
			,STEPS								= 	@HAS_STEPS
			,LOAD_FROM_GROUND					=	@LOAD_FROM_GROUND
			,FORKLIFT_FROM_GROUND				=	@FORKLIFT_FROM_GROUND
			,BUILDING_NOTES						= 	@BUILDING_NOTES	
			,BOL_SPECIAL_INSTRUCTIONS			=	@BOL_SPECIAL_INSTRUCTIONS
			,PICKUP_PROCESSING_NOTES			=	@PICKUP_PROCESSING_NOTES
			,IS_LIFT_GATE_REQUIRED				=	@IS_LIFT_GATE_REQUIRED
			,WORK_PERMISSION_ID					=	@WORK_PERMISSION_ID
			,SPECIFIC_PERMITTED_HOURS			=	@SPECIFIC_PERMITTED_HOURS
			,CLEARANCE_RESTRICTIONS				=   @CLEARANCE_RESTRICTIONS
			,CLEARANCE_RESTRICTIONS_VALUE		=	@CLEARANCE_RESTRICTIONS_VALUE
			,IS_DOCK_HEIGHT_OVER_14				=	@IS_DOCK_HEIGHT_OVER_14		
			,IS_53_SEMI_ACCESSIBLE				=	@IS_53_SEMI_ACCESSIBLE		
			,LOCATION_OF_MATERIALS				=	@LOCATION_OF_MATERIALS		
			,INVENTORY_LIST						=	@INVENTORY_LIST				
			,ROLLING_BINS_TO_DROP_OFF			=	@ROLLING_BINS_TO_DROP_OFF	
			,ROLLING_BINS_TO_PICK_UP			=	@ROLLING_BINS_TO_PICK_UP	
			,[IS_LOCATED_IN_SINGLE_LOCATION]	=	@IS_SINGLE_LOCATION
		    ,[IS_SPECIAL_CREDENTIALS]			=	@IS_SPECIAL_CREDENTIALS
		    ,[TRUCK_PARK]						=	@TRUCK_PARK
		    ,[LARGE_ITEMS_INFO]					=	@LARGE_ITEMS_INFO
			,UPDATED_BY							= N'sp_SET_RECYCLING_ORDER_LOCATION_DETAILS'
			,UPDATED_DT							= GETUTCDATE()
		WHERE RECYCLING_LOCATION_DETAILS_ID = @LOCATION_DETAILS_ID
	END
END
-- =============================================
-- Author:		R<PERSON><PERSON><PERSON>kin
-- Create date: 09/17/2014
-- Description: get notification email list
-- =============================================
CREATE FUNCTION [dbo].[fn_str_GET_NOTIFICATION_EMAIL_LIST] 
(
	@NOTIFICATION_TYPE_ID	BIGINT,
	@USER_ID				BIGINT,
	@CUSTOMER_CONTACT_ID	BIGINT,
	@ONSITE_CONTACT_ID		BIGINT = NULL,
	@NOTIFICATION_SET_ID	BIGINT,
	@CUSTOMER_ID			BIGINT = null
)
RETURNS  NVARCHAR(MAX)
AS
BEGIN
		
	DECLARE @RESULT NVARCHAR(MAX) = N'';
	DECLARE @NOTIFICATION_ID BIGINT = NULL;
	DECLARE @IsAllowPrimaryAccountsReceiveNotifications BIT = 0;

	DECLARE @T_EMAILS TABLE(EMAIL NVARCHAR(512))

	SELECT TOP(1)		
		@IsAllowPrimaryAccountsReceiveNotifications = USS.IsAllowPrimaryAccountsReceiveNotifications
	FROM U_SYSTEM_SETTINGS	USS WITH(NOLOCK)

	SELECT
	  @NOTIFICATION_ID = N.NOTIFICATION_ID
	FROM F_NOTIFICATION_SET_GROUP	NSG WITH(NOLOCK)
	INNER JOIN F_NOTIFICATION			N	WITH(NOLOCK)
		ON N.NOTIFICATION_GROUP_ID = NSG.NOTIFICATION_GROUP_ID
	WHERE N.NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
		AND NSG.NOTIFICATION_SET_ID = @NOTIFICATION_SET_ID
		
	-- get emails for enabled entities
	INSERT INTO @T_EMAILS
	SELECT
	  U.Email
	FROM F_NOTIFICATION_ENTITY	NE	WITH(NOLOCK)
	INNER JOIN tb_User U WITH(NOLOCK)
		ON U.UserID = @USER_ID 
		AND NE.ENTITY_TYPE_ID = 1 -- rep
	WHERE NE.NOTIFICATION_ID = @NOTIFICATION_ID

	-- Get emails for onsite contact from the primary and sub-account, either one or both as applicable
	INSERT INTO @T_EMAILS
	SELECT
		CC.MAIN_EMAIL
	FROM F_NOTIFICATION_ENTITY	NE	WITH(NOLOCK)
	INNER JOIN F_CUSTOMER_CONTACT CC WITH(NOLOCK)
		ON CC.CUSTOMER_CONTACT_ID = @ONSITE_CONTACT_ID
		AND NE.ENTITY_TYPE_ID = 2 -- Onsite Contact
	WHERE NE.NOTIFICATION_ID = @NOTIFICATION_ID

	-- Get emails for customer contact from the primary and sub-account, either one or both as applicable
	INSERT INTO @T_EMAILS
	SELECT
		CC.MAIN_EMAIL
	FROM F_NOTIFICATION_ENTITY	NE	WITH(NOLOCK)
	INNER JOIN F_CUSTOMER_CONTACT CC WITH(NOLOCK)
		ON CC.CUSTOMER_CONTACT_ID = @CUSTOMER_CONTACT_ID
		AND NE.ENTITY_TYPE_ID = 3 -- Customer: Primary Contact
	WHERE NE.NOTIFICATION_ID = @NOTIFICATION_ID


	-- Get emails for the primary customer (only if allowed by the system setting)
	IF @CUSTOMER_ID IS NOT NULL AND @IsAllowPrimaryAccountsReceiveNotifications = 1
	BEGIN
		INSERT INTO @T_EMAILS
		SELECT FCC.MAIN_EMAIL
		FROM [dbo].[F_CUSTOMER] FC WITH (NOLOCK)
			INNER JOIN [dbo].[F_CUSTOMER] FC1 WITH (NOLOCK)
				ON FC.[PrimaryCustomerID] = FC1.[CUSTOMER_ID]
			INNER JOIN F_CUSTOMER_CONTACT FCC WITH (NOLOCK)
				ON FC1.[CUSTOMER_ID] = FCC.[CUSTOMER_ID] AND FCC.IS_MAIN = 1
		WHERE FC.[CUSTOMER_ID] = @CUSTOMER_ID
	END


	-- get emails for users
	INSERT INTO @T_EMAILS
	SELECT
	  U.Email
	FROM F_NOTIFICATION_USER			NU  WITH(NOLOCK)
	INNER JOIN tb_User					U WITH(NOLOCK)
		ON U.UserID = NU.[USER_ID] 
	WHERE NU.NOTIFICATION_ID = @NOTIFICATION_ID
		AND NU.[USER_ID] IS NOT NULL

	-- get additional emails
	INSERT INTO @T_EMAILS
	SELECT
	  NU.USER_EMAIL
	FROM F_NOTIFICATION_USER			NU  WITH(NOLOCK)
	WHERE NU.NOTIFICATION_ID = @NOTIFICATION_ID
		AND NU.[USER_ID] IS NULL
	

	SET @RESULT = (
		SELECT
			EMAIL + ',' AS "data()" 
		FROM (
			SELECT DISTINCT LTRIM(RTRIM(EMAIL)) AS EMAIL
			FROM @T_EMAILS
			WHERE EMAIL IS NOT NULL 
			AND LTRIM(RTRIM(EMAIL)) <> ''
		) AS T
		FOR XML PATH('')
	)

	RETURN @RESULT
END
CREATE TABLE [dbo].[F_SHIPPING_TRACKING_NUMBERS] (
    [TRACKING_ID]   BIGINT         IDENTITY (1, 1) NOT NULL,
    [SHIPPING_ID]   BIGINT         NULL,
    [PROVIDER_ID]   INT            NOT NULL,
    [TRACKING_NO]   NVARCHAR (250) NOT NULL,
    [SHIPPING_DATE] DATETIME       NULL,
    [IS_INACTIVE]   BIT            CONSTRAINT [DF_F_SHIPPING_TRACKING_NUMBERS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]    BIT            CONSTRAINT [DF_F_SHIPPING_TRACKING_NUMBERS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]   VARCHAR (150)  NOT NULL,
    [INSERTED_DT]   DATETIME       NOT NULL,
    [UPDATED_BY]    VARCHAR (150)  NULL,
    [UPDATED_DT]    DATETIME       NULL,
    [DELETED_BY]    VARCHAR (150)  NULL,
    [DELETED_DT]    DATETIME       NULL,
    CONSTRAINT [PK_F_SHIPPING_TRACKING_NUMBERS] PRIMARY KEY CLUSTERED ([TRACKING_ID] ASC),
    CONSTRAINT [FK_F_SHIPPING_TRACKING_NUMBERS_D_SHIPPING_PROVIDER] FOREIGN KEY ([PROVIDER_ID]) REFERENCES [dbo].[D_SHIPPING_PROVIDER] ([PROVIDER_ID]),
    CONSTRAINT [FK_F_SHIPPING_TRACKING_NUMBERS_F_SHIPPING] FOREIGN KEY ([SHIPPING_ID]) REFERENCES [dbo].[F_SHIPPING] ([SHIPPING_ID])
);





GO
CREATE NONCLUSTERED INDEX [IX_SHIPPING_ID]
    ON [dbo].[F_SHIPPING_TRACKING_NUMBERS]([SHIPPING_ID] ASC)
	INCLUDE (TRACKING_NO);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The tracking for ship', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_SHIPPING_TRACKING_NUMBERS';


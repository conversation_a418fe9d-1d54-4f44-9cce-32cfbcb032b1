-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_SHIPMENT_AS_VOIDED_IN_MAGENTO]
	@M_SHIPMENT_ID NVARCHAR(256)
AS
BEGIN
	BEGIN TRANSACTION
		UPDATE fsos
		SET 
			fsos.NEEDS_MAGENTO_VOID = 0
		FROM [F_SHIPPING] AS fsos
		INNER JOIN MAGENTO_ORDERS AS mo
			ON mo.SALES_ORDER_ID = fsos.SALES_ORDER_ID 
		WHERE mo.M_SHIPMENT_ID = @M_SHIPMENT_ID
		
		UPDATE MAGENTO_ORDERS
		SET
			M_PREVIOUS_SHIPMENT_ID = M_SHIPMENT_ID,
			M_SHIPMENT_ID = NULL
		WHERE M_SHIPMENT_ID = @M_SHIPMENT_ID
	 COMMIT TRANSACTION
END
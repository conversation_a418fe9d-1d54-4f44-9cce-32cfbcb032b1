CREATE PROCEDURE [rzrapi].[sp_GetPageOfOutboundOrders]
    @OutboundOrderIds       dbo.bigint_ID_ARRAY readonly,
    @CustomerIds            dbo.bigint_ID_ARRAY readonly,
    @WarehouseIds           dbo.bigint_ID_ARRAY readonly,
    @StatusIds              dbo.bigint_ID_ARRAY readonly,
    @FromScheduledDateUtc	datetime = null,
    @ToScheduledDateUtc     datetime = null,
    @FromCreatedDateUtc     datetime = null,
    @ToCreatedDateUtc       datetime = null,
    @FromUpdatedDateUtc     datetime = null,
    @ToUpdatedDateUtc       datetime = null,
    @IsInternalTransfer     bit = null,
    @SearchTerm				nvarchar(max) = null,
	@PageIndex				int = 0,
	@ItemsPerPage			int = 25,
	@OrderColumnName		nvarchar(64)	= 'Id',
	@OrderDirection			nvarchar(8)		= 'asc',
	@IsDebug				bit = 0
AS
BEGIN
	set @PageIndex = isnull(@PageIndex, 0);
	set @ItemsPerPage = iif(isnull(@ItemsPerPage, 0) <= 0, 25, @ItemsPerPage);
	set @OrderColumnName = isnull(@OrderColumnName, 'Id');
	set @OrderDirection = isnull(@OrderDirection, 'asc');

	set @SearchTerm = '%' + nullif(rtrim(ltrim(@SearchTerm)), N'') + '%'

	declare @query nvarchar(max) = N'
	declare
        @outboundOrderIdsExist	bit = iif(exists(select top(1) 1 from @OutboundOrderIds), 1, 0),
		@customerIdsExist		bit = iif(exists(select top(1) 1 from @CustomerIds), 1, 0),
		@warehouseIdsExist		bit = iif(exists(select top(1) 1 from @WarehouseIds), 1, 0),
		@statusIdsExist			bit = iif(exists(select top(1) 1 from @StatusIds), 1, 0)

	;with cte as (
		select
		    o.RECYCLING_ORDER_ID										AS Id,
		    oo.AUTO_NAME												AS OrderAutoName,
		    rs.RECYCLING_ORDER_STATUS_ID								AS StatusId,
			rs.STATUS_CD												AS StatusName,
		    c.CUSTOMER_ID												AS CustomerId,
			c.CUSTOMER_NAME												AS CustomerName,
		    o.IS_TRANSFER												AS IsInternalTransfer,
		    oo.IS_TRUCKING												AS IsTrucking,
		    iif(oo.IS_TRUCKING = 1,
		        rlt.RECYCLING_LOGISTIC_TYPE_CD,
				rltc.RECYCLING_LOGISTIC_TYPE_CD)						AS LogisticDescription,
			o.PICKUP_START_DATE											AS ScheduledDate,
		    coalesce(o.WORK_INSTRUCTIONS, C.WORK_INSTRUCTIONS, '''')	AS WorkInstructions,
		    u.UserID													AS RepId,
		    dbo.fn_str_GET_USER_AUTO_NAME(u.UserID, 1)					AS RepName,
			isnull(i.LotCount, 0)										AS LotCount,
			isnull(i.ItemCount, 0)										AS ItemCount,
			w.WAREHOUSE_ID												AS WarehouseId,
			w.WAREHOUSE_CD												AS WarehouseName,
		    dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(
				iif(oo.IS_TRUCKING = 1,
					iif(rlt.RECYCLING_LOGISTIC_TYPE_ID = 3, -- Client Drop Off
						rot.FREIGHT_LOCATION_ID,
						rot.SHIPTO_LOCATION_ID), 
					roc.CONSIGNEE_LOCATION_ID))							AS FullAddressLocation,
			oo.INSERTED_DT												AS CreatedDate,
			coalesce(oo.UPDATED_DT, oo.INSERTED_DT)						AS UpdatedDate,
			coalesce(o.IS_HAZARDOUS, 0) | hi.IsHazardous				AS IsHazardous,

			iif(oo.IS_TRUCKING = 1, ''Truck'', ''Container'')			AS ShippingMethodName,
			rot.SHIPPING_METHOD_ID										AS TruckShippingOptionId,
			''''														AS TruckShippingOptionName,
			rot.ESTIMATED_WEIGHT										AS EstimatedWeight,
			rot.PALLET_COUNT											AS PalletCount,
			rot.TRUCK_ID												AS TruckTypeId,
			tt.NAME														AS TruckTypeName,
			ffc.[CUSTOMER_ID]											AS CarrierCustomerId,
			cc.CUSTOMER_NAME											AS CarrierCustomerName,
			roc.SHIPPING_METHOD_ID										AS ContainerShippingOptionId,
			''''														AS ContainerShippingOptionName,
			roc.CONTAINER_SIZE_ID										AS ContainerSizeId,
			cs.CONTAINER_SIZE_CD										AS ContainerSizeName,
			roc.CONTAINER_NUMBER										AS ContainerNumber,
			iif(oo.IS_TRUCKING = 1, rot.SEAL_NUMBER, roc.SEAL_NUMBER)	AS SealNumber,
			roc.BOOKING_NUMBER											AS BookingNumber,
			o.BOL_NUMBER												AS BolNumber,
			o.PO_NUMBER													AS PoNumber,
			o.CustomerContractNo										AS CustomerContractNo,
			oo.IS_DSV_RESTRICTION_ENABLED								AS IsDsvRestrictionEnabled,
			o.DESCR														AS Comments,
			oo.INTERNAL_NOTES											AS Notes,
			ISNULL([dbo].[fn_money_GET_RECYCLING_ORDER_OUTBOUND_SUBTOTAL](o.RECYCLING_ORDER_ID), 0.0)	AS AmountOwed,
			oo.LOADING_DT												AS LoadingDate,
			ffc.QUOTE_RATE												AS QuoteRate,
			ffc.CLIENT_CHARGE											AS ClientCharge,
			o.SO_TERM_ID												AS SoTransactionTermId,
			o.SETTLE_DATE												AS SettleDate,
			sot.TRANSACTION_TERM_VALUE									AS SoTransactionTermName,
			o.CurrencyExchangeId										AS CurrencyExchangeId
		
		from dbo.F_RECYCLING_ORDER										o with(nolock)
		inner join dbo.F_RECYCLING_ORDER_OUTBOUND						oo with(nolock)
		    on o.RECYCLING_ORDER_ID = oo.RECYCLING_ORDER_ID
		inner join dbo.F_CUSTOMER										c with(nolock)
			on o.CUSTOMER_ID = c.CUSTOMER_ID
		inner join dbo.tb_User											u with(nolock)
			on o.USER_ID = u.UserID
		left join dbo.C_RECYCLING_ORDER_STATUS							rs with(nolock)
			on o.RECYCLING_ORDER_STATUS_ID = rs.RECYCLING_ORDER_STATUS_ID
		
		left join dbo.F_RECYCLING_OUTBOUND_TRUCKING						rot with(nolock)
			on rot.RECYCLING_OUTBOUND_TRUCKING_ID = oo.RECYCLING_OUTBOUND_TRUCKING_ID
		left join dbo.C_RECYCLING_LOGISTIC_TYPE							rlt with(nolock)
			on rlt.RECYCLING_LOGISTIC_TYPE_ID = rot.SHIPPING_METHOD_ID
		left join dbo.F_TRUCK											tt with(nolock)
			on rot.TRUCK_ID = tt.TRUCK_ID
		
		left join (
			select
				ffc.[RecyclingOrderId],
				ffc.[CUSTOMER_ID],
				ffc.QUOTE_RATE,
				ffc.CLIENT_CHARGE
			from (
				select 
					ffc.[RecyclingOrderId],
					min(ffc.[FREIGHT_CARRIER_ID])	as [FREIGHT_CARRIER_ID]
				from dbo.F_FREIGHT_CARRIER		ffc with(nolock)
				left join @OutboundOrderIds		fo
					on fo.ID = ffc.[RecyclingOrderId]
				where (@outboundOrderIdsExist = 0			OR fo.ID is not null)
				group by ffc.[RecyclingOrderId]
			)	fcm
			inner join dbo.F_FREIGHT_CARRIER	ffc with(nolock)
				on ffc.[FREIGHT_CARRIER_ID] = fcm.[FREIGHT_CARRIER_ID]
		)	ffc
			on ffc.[RecyclingOrderId] = o.RECYCLING_ORDER_ID
		left join dbo.F_CUSTOMER										cc with(nolock)
			on ffc.[CUSTOMER_ID] = cc.CUSTOMER_ID

		left join dbo.F_RECYCLING_OUTBOUND_CONTAINER					roc with(nolock)
			on roc.RECYCLING_OUTBOUND_CONTAINER_ID = oo.RECYCLING_OUTBOUND_CONTAINER_ID
		left join dbo.C_RECYCLING_LOGISTIC_TYPE							rltc with(nolock)
			on rltc.RECYCLING_LOGISTIC_TYPE_ID = roc.SHIPPING_METHOD_ID
		left join dbo.D_WAREHOUSE										w with(nolock)
			on w.WAREHOUSE_ID = o.WAREHOUSE_ID
		left join dbo.C_CONTAINER_SIZE									cs with(nolock)
			ON roc.CONTAINER_SIZE_ID = cs.CONTAINER_SIZE_ID
		left join dbo.C_CUSTOMER_TRANSACTION_TERM						sot with(nolock)
			ON o.SO_TERM_ID = sot.CUSTOMER_TRANSACTION_TERM_ID

		outer apply (
			select
				cast(iif(exists(
					select 1
					from dbo.F_RECYCLING_ORDER_ITEM						tfroi with(nolock) 
					inner join dbo.F_RECYCLING_ITEM_MASTER				tfrim with(nolock)
						on tfrim.RECYCLING_ITEM_MASTER_ID = tfroi.RECYCLING_ITEM_MASTER_ID
					where tfrim.IS_HAZARDOUS = 1
						and tfroi.OUTBOUND_ORDER_ID = o.RECYCLING_ORDER_ID), 1, 0
				) AS bit)												AS IsHazardous
		) hi

		left join (
			select 
				i.OUTBOUND_ORDER_ID					AS OrderId,
				count(I.RECYCLING_ORDER_ITEM_ID)	AS LotCount,
				sum(I.ITEM_COUNT)					AS ItemCount
			from dbo.F_RECYCLING_ORDER_ITEM				i with(nolock)
			where i.IS_INACTIVE = 0
			group by i.OUTBOUND_ORDER_ID
		) i
			on i.OrderId = o.RECYCLING_ORDER_ID

		left join @OutboundOrderIds										fo
			on fo.ID = o.RECYCLING_ORDER_ID
		left join @CustomerIds											fc
			on fc.ID = c.CUSTOMER_ID
		left join @WarehouseIds											fw
			on fw.ID = w.WAREHOUSE_ID
		left join @StatusIds											fs
			on fs.ID = rs.RECYCLING_ORDER_STATUS_ID

		where (@FromScheduledDateUtc is null		OR @FromScheduledDateUtc <= o.PICKUP_START_DATE)
			and (@ToScheduledDateUtc is null		OR @ToScheduledDateUtc >= o.PICKUP_START_DATE)
			and (@FromCreatedDateUtc is null		OR @FromCreatedDateUtc <= oo.INSERTED_DT)
			and (@ToCreatedDateUtc is null			OR @ToCreatedDateUtc >= oo.INSERTED_DT)			
			and (@FromUpdatedDateUtc is null		OR @FromUpdatedDateUtc <= coalesce(oo.UPDATED_DT, oo.INSERTED_DT))
			and (@ToUpdatedDateUtc is null			OR @ToUpdatedDateUtc >= coalesce(oo.UPDATED_DT, oo.INSERTED_DT))

			and (@IsInternalTransfer is null		OR o.IS_TRANSFER = @IsInternalTransfer)

			and (@outboundOrderIdsExist = 0			OR fo.ID is not null)
			and (@customerIdsExist = 0				OR fc.ID is not null)
			and (@warehouseIdsExist = 0				OR fw.ID is not null)
			and (@statusIdsExist = 0				OR fs.ID is not null)

			and (@SearchTerm is null or oo.AUTO_NAME like @SearchTerm)
	)'

	set @query = @query + N'
	select
        count(1)    AS Id,
        null		AS OrderAutoName,
		null		AS StatusId,
		null		AS StatusName,
		null		AS CustomerId,
		null		AS CustomerName,
		null		AS IsInternalTransfer,
		null		AS IsTrucking,
		null		AS LogisticDescription,
		null		AS ScheduledDate,
		null		AS WorkInstructions,
		null		AS RepId,
		null		AS RepName,
		null		AS LotCount,
		null		AS ItemCount,
		null		AS WarehouseId,
		null		AS WarehouseName,
		null		AS FullAddressLocation,
		null		AS CreatedDate,
		null		AS UpdatedDate,
		null		AS IsHazardous,
		null		AS ShippingMethodName,
		null		AS TruckShippingOptionId,
		null		AS TruckShippingOptionName,
		null		AS EstimatedWeight,
		null		AS PalletCount,
		null		AS TruckTypeId,
		null		AS TruckTypeName,
		null		AS CarrierCustomerId,
		null		AS CarrierCustomerName,
		null		AS ContainerShippingOptionId,
		null		AS ContainerShippingOptionName,
		null		AS ContainerSizeId,
		null		AS ContainerSizeName,
		null		AS ContainerNumber,
		null		AS SealNumber,
		null		AS BookingNumber,
		null		AS BolNumber,
		null		AS PoNumber,
		null		AS CustomerContractNo,
		null		AS IsDsvRestrictionEnabled,
		null		AS Comments,
		null		AS Notes,
		null		AS AmountOwed,
		null		AS LoadingDate,
		null		AS QuoteRate,
		null		AS ClientCharge,
		null		AS SoTransactionTermId,
		null		AS SettleDate,
		null		AS SoTransactionTermName,
		null		AS CurrencyExchangeId
    from cte

	union all

	select
		t.Id,
        t.OrderAutoName,
		t.StatusId,
		t.StatusName,
		t.CustomerId,
		t.CustomerName,
		t.IsInternalTransfer,
		t.IsTrucking,
		t.LogisticDescription,
		t.ScheduledDate,
		t.WorkInstructions,
		t.RepId,
		t.RepName,
		t.LotCount,
		t.ItemCount,
		t.WarehouseId,
		t.WarehouseName,
		t.FullAddressLocation,
		t.CreatedDate,
		t.UpdatedDate,
		t.IsHazardous,
		t.ShippingMethodName,
		t.TruckShippingOptionId,
		t.TruckShippingOptionName,
		t.EstimatedWeight,
		t.PalletCount,
		t.TruckTypeId,
		t.TruckTypeName,
		t.CarrierCustomerId,
		t.CarrierCustomerName,
		t.ContainerShippingOptionId,
		t.ContainerShippingOptionName,
		t.ContainerSizeId,
		t.ContainerSizeName,
		t.ContainerNumber,
		t.SealNumber,
		t.BookingNumber,
		t.BolNumber,
		t.PoNumber,
		t.CustomerContractNo,
		t.IsDsvRestrictionEnabled,
		t.Comments,
		t.Notes,
		t.AmountOwed,
		t.LoadingDate,
		t.QuoteRate,
		t.ClientCharge,
		t.SoTransactionTermId,
		t.SettleDate,
		t.SoTransactionTermName,
		t.CurrencyExchangeId
    from (
        select t.*
        from cte t
        order by
			['+ @OrderColumnName + N'] ' + @OrderDirection + N'
		offset (@PageIndex * @ItemsPerPage) rows
		fetch next (@ItemsPerPage) rows only
    ) t
	'

	declare @params nvarchar(max) = N'
		@OutboundOrderIds		dbo.bigint_ID_Array readonly,
		@CustomerIds			dbo.bigint_ID_Array readonly,
		@WarehouseIds			dbo.bigint_ID_Array readonly,
		@StatusIds				dbo.bigint_ID_Array readonly,
		@FromScheduledDateUtc	datetime,
		@ToScheduledDateUtc		datetime,
		@FromCreatedDateUtc		datetime,
		@ToCreatedDateUtc		datetime,
		@FromUpdatedDateUtc		datetime,
		@ToUpdatedDateUtc		datetime,
		@IsInternalTransfer		bit,
		@SearchTerm				nvarchar(max),
		@PageIndex				int,
		@ItemsPerPage			int';
	
	if @IsDebug = 1
	begin
		print(cast(@params as ntext))
		print(cast(@query as ntext))
	end

	exec sp_executesql @query, @params,
		@OutboundOrderIds		= @OutboundOrderIds,
		@CustomerIds			= @CustomerIds,
		@WarehouseIds			= @WarehouseIds,
		@StatusIds				= @StatusIds,
		@FromScheduledDateUtc	= @FromScheduledDateUtc,
		@ToScheduledDateUtc		= @ToScheduledDateUtc,
		@FromCreatedDateUtc		= @FromCreatedDateUtc,
		@ToCreatedDateUtc		= @ToCreatedDateUtc,
		@FromUpdatedDateUtc		= @FromUpdatedDateUtc,
		@ToUpdatedDateUtc		= @ToUpdatedDateUtc,
		@IsInternalTransfer		= @IsInternalTransfer,
		@SearchTerm				= @SearchTerm,
		@PageIndex				= @PageIndex,
		@ItemsPerPage			= @ItemsPerPage;

END

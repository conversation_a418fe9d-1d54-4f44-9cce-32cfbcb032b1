-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_RECALC_SHIPPING_PACKAGE_DIMENSIONS] 
	@SALES_ORDER_ID BIGINT
AS
BEGIN

	DECLARE @PACKAGE_ID BIGINT
	DECLARE @INSURED_VALUE MONEY
	DECLARE	@packages TABLE (PACKAGE_ID	BIGINT)
	-- DIMENSIONS
	DECLARE	@temp TABLE ( [LENGTH]	FLOAT,
						  WIDTH		FLOAT,
						  HEIGHT	FLOAT,
						  WEIGHT_LB FLOAT,
						  WEIGHT_OZ FLOAT)
	
	-- GET PACKAGES FOR SALES ORDER
	INSERT INTO @packages
	(
		PACKAGE_ID
	)
	SELECT
		fsp.PACKAGE_ID 
	FROM F_SHIPPING_PACKAGE				fsp WITH (NOLOCK)
	INNER JOIN [F_SHIPPING]	fsos WITH (NOLOCK)
	  ON fsos.SHIPPING_ID = fsp.SHIPPING_ID
	WHERE fsos.SALES_ORDER_ID = @SALES_ORDER_ID

	WHILE (SELECT COUNT(PACKAGE_ID) FROM @packages) > 0
	BEGIN
		SELECT TOP(1) @PACKAGE_ID = PACKAGE_ID FROM  @packages

		-- GET DIMENSIONS VALUES
		INSERT INTO @temp
			([LENGTH],
			WIDTH,
			HEIGHT,
			WEIGHT_LB,
			WEIGHT_OZ)
		EXEC sp_CALC_SHIPPING_PACKAGE_DIMENSIONS @SALES_ORDER_ID, @PACKAGE_ID
		
		-- CALC INSURED_VALUE
		SELECT
			@INSURED_VALUE = SUM(I.ITEM_QUANTITY * I.ITEM_PRICE)
		FROM F_SALES_ORDER_ITEM I WITH (NOLOCK)
		WHERE I.SALES_ORDER_ID = @SALES_ORDER_ID
			AND	I.PACKAGE_ID = @PACKAGE_ID
			AND I.IS_DELETED  = 0
			AND I.IS_INACTIVE = 0
		
		-- UPDATE ALL RECALC VALUES
		UPDATE F_SHIPPING_PACKAGE WITH(ROWLOCK)
		SET
			[LENGTH] =
				CASE
					WHEN t.[LENGTH] IS NULL THEN 0 ELSE CEILING(t.[LENGTH]) 
				END,
			WIDTH =
				CASE
					WHEN t.WIDTH IS NULL THEN 0 ELSE CEILING(t.WIDTH) 
				END,
			HEIGHT =
				CASE
					WHEN t.HEIGHT IS NULL THEN 0 ELSE CEILING(t.HEIGHT)
				END,
			WEIGHT_LB =
				CASE
					WHEN t.WEIGHT_LB IS NULL THEN 0 ELSE t.WEIGHT_LB
				END,
			WEIGHT_OZ =
				CASE
					WHEN t.WEIGHT_OZ IS NULL THEN 0 ELSE t.WEIGHT_OZ
				END,
			INSURED_VALUE = 
				CASE
					WHEN @INSURED_VALUE IS NULL THEN 0 ELSE @INSURED_VALUE
				END
		FROM @temp AS t 
		WHERE PACKAGE_ID = @PACKAGE_ID
		-- CLEAR TEMP TABLE
		DELETE FROM @temp
		
		DELETE FROM @packages WHERE PACKAGE_ID = @PACKAGE_ID
	END
END
CREATE TABLE [dbo].[C_TAG_TYPE] (
    [TAG_TYPE_ID] INT            NOT NULL,
    [TAG_TYPE_CD] NVARCHAR (256) NOT NULL,
    CONSTRAINT [PK_C_TAG_TYPE] PRIMARY KEY CLUSTERED ([TAG_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Tag types which stores in one table', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_TAG_TYPE';


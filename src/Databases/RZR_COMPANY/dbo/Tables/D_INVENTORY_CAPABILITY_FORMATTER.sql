CREATE TABLE [dbo].[D_INVENTORY_CAPABILITY_FORMATTER] (
    [FORMATTER_ID]      INT             NOT NULL,
    [FORMATTER_NAME]    NVARCHAR (100)  NOT NULL,
    [FORMATTER_OPTIONS] NVARCHAR (2000) NULL,
    [FORMATTER_DESC]    NVARCHAR (2000) NULL,
    CONSTRAINT [PK_D_INVENTORY_CAPABILITY_FORMATTER] PRIMARY KEY CLUSTERED ([FORMATTER_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'we can set the type of data for capability and the appropriate validation will be used ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_INVENTORY_CAPABILITY_FORMATTER';


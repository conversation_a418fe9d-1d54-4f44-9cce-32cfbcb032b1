CREATE PROCEDURE [dbo].[sp_GetPageOfSlaTemplates]
	@Ids					dbo.bigint_id_ARRAY readonly,
	@SearchTerm				nvarchar(max) = null,
	@MatchPattern			int = 0,
	    -- Contains = 0
	    -- BeginsWith = 1
	    -- EndsWith = 2
	    -- Equals = 3
	@OrderByPropertyName	nvarchar(64)	= '[Name]',
	@OrderDirection			nvarchar(8)		= 'asc',
	@Offset					bigint			= 0,
	@Limit					int				= 25,
	@IsDebug				bit				= 0
AS
BEGIN
	
	set @SearchTerm = nullif(rtrim(ltrim(@SearchTerm)), N'');
	set @SearchTerm = case
		when @MatchPattern = 1 then @SearchTerm + N'%'
		when @MatchPattern = 2 then N'%' + @SearchTerm
		when @MatchPattern = 3 then @SearchTerm
		else N'%' + @SearchTerm + N'%'
	end;
	declare	@SearchStatement nvarchar(max) = iif(@MatchPattern = 3,
		N' t.[Name] = @SearchTerm ',
		N' t.[Name] like @SearchTerm ');

	set @OrderByPropertyName = isnull(@OrderByPropertyName, '[Name]');
	set @OrderDirection = isnull(@OrderDirection, 'asc');
	set @Offset = isnull(@Offset, 0);
	set @Limit = iif(isnull(@Limit, 0) <= 0, 25, @Limit);

	declare @isAllIds bit = iif(exists(select 1 from @Ids), 0, 1);

	declare @q nvarchar(max) = N'
	;with m_data AS (
		select
			t.Id							AS Id,
			t.[Name]						AS [Name],
			t.OrderSla						AS OrderSla,
			t.AccountingSla					AS AccountingSla,
			t.SlaStart						AS SlaStart,
			t.SortSla						AS SortSla,
			t.BasicAuditSla					AS BasicAuditSla,
			t.ProcessingSla					AS ProcessingSla,
			t.IsDefault						AS IsDefault,
			t.IsInactive					AS IsInactive,
			t.IsDeleted						AS IsDeleted,

			json_query((
				select
					c.CommodityId							AS CommodityId,
					m.RECYCLING_ITEM_MASTER_NAME			AS CommodityName,
					c.IsSortSlaEnabled						AS IsSortSlaEnabled,
					c.IsBasicAuditSlaEnabled				AS IsBasicAuditSlaEnabled,
					c.IsProcessingSlaEnabled				AS IsProcessingSlaEnabled

				from dbo.F_SlaTemplateCommodity				c with(nolock)
				inner join dbo.F_RECYCLING_ITEM_MASTER		m with(nolock)
					on c.CommodityId = m.RECYCLING_ITEM_MASTER_ID
				where c.TemplateId = t.Id
				order by m.RECYCLING_ITEM_MASTER_NAME
				for json path
			))	as Commodities

		from dbo.F_SlaTemplate				t with(nolock)
		left join @Ids						ti
			on ti.ID = t.Id
		where t.IsDeleted = 0
			and (@isAllIds = 1 or ti.ID is not null)
			and (@SearchTerm is null or (' + @SearchStatement + N'))
	)

	select
		count(1)			AS Id,
		null				AS [Name],
		null				AS OrderSla,
		null				AS AccountingSla,
		null				AS SlaStart,
		null				AS SortSla,
		null				AS BasicAuditSla,
		null				AS ProcessingSla,
		null				AS IsDefault,
		null				AS IsInactive,
		null				AS IsDeleted,
		null				AS Commodities
	from m_data				m
	
	union all
	select 
		t.Id,
		t.[Name],
		t.OrderSla,
		t.AccountingSla,
		t.SlaStart,
		t.SortSla,
		t.BasicAuditSla,
		t.ProcessingSla,
		t.IsDefault,
		t.IsInactive,
		t.IsDeleted,
		t.Commodities
	from (
		select m.*
		from m_data		m
		order by ' + @OrderByPropertyName + N' ' + @OrderDirection + N'
		offset @Offset rows
		fetch next @Limit rows only
	) t
	';

	if @IsDebug = 1 begin
		print(cast (@q as ntext))
	end

	exec sp_executesql @q, N'
		@Ids			dbo.bigint_id_ARRAY readonly,
		@SearchTerm		nvarchar(max),
		@Offset			bigint,
		@Limit			int,
		@isAllIds		bit',
		@Ids			= @Ids,
		@SearchTerm		= @SearchTerm,
		@Offset			= @Offset,
		@Limit			= @Limit,
		@isAllIds		= @isAllIds

END

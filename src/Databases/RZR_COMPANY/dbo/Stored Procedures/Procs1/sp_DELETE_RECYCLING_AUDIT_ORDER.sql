-- =============================================
-- Author:		<PERSON>
-- Create date: 05/12/2014
-- Description:	Deletes recycling audit order
-- =============================================
-- EXEC [sp_DELETE_RECYCLING_AUDIT_ORDER] 1
CREATE PROCEDURE [dbo].[sp_DELETE_RECYCLING_AUDIT_ORDER]
	 @RECYCLING_AUDIT_ORDER_HEADER_ID	BIGINT
	,@USER_ID							BIGINT
	,@USER_IP							BIGINT
	,@USE_INTERNAL_TRANSACTION			BIT = 1
AS
BEGIN


	IF (@USE_INTERNAL_TRANSACTION = 1)
	BEGIN
		BEGIN TRAN
		SET XACT_ABORT ON
	END
		
		IF EXISTS (
			SELECT TOP(1) 1
			FROM [recycling].[F_AuditSession]				FAS
			INNER JOIN [dbo].[F_RECYCLING_ORDER_INBOUND]	FROI
				ON  FROI.RECYCLING_ORDER_ID = FROI.RECYCLING_ORDER_ID
				AND FROI.IsFromConsumable  = 1
			WHERE FAS.[Id] = @RECYCLING_AUDIT_ORDER_HEADER_ID
		)
		BEGIN
			IF (@USE_INTERNAL_TRANSACTION = 1)
			BEGIN
				-- do not let delete the audit session of From-Consumable recycling order
				COMMIT TRAN;
			END
			RETURN;
		END
		
		DECLARE @AssetIds bigint_ID_ARRAY
		INSERT INTO @AssetIds
		SELECT Id
		FROM [recycling].F_Asset WITH(NOLOCK)
		WHERE [AuditSessionId] = @RECYCLING_AUDIT_ORDER_HEADER_ID

		EXEC [sp_DELETE_RECYCLING_ORDER_AUDIT_ITEMS]
			@RECYCLING_ORDER_AUDIT_ITEM_IDS = @AssetIds
			,@DELETE_INVENTORY = 1
			,@USER_ID = @USER_ID
			,@IP = @USER_IP
		
		DELETE FROM [recycling].[F_AuditSession] WITH(ROWLOCK)
		WHERE [Id] = @RECYCLING_AUDIT_ORDER_HEADER_ID

	IF (@USE_INTERNAL_TRANSACTION = 1)
	BEGIN
		COMMIT TRAN
	END
END

CREATE TABLE [dbo].[C_NOTIFICATION_GROUP_TYPE] (
    [GROUP_TYPE_ID]  BIGINT        NOT NULL,
    [NAME]           VARCHAR (256) NOT NULL,
    [ALLOW_CUSTOMER] BIT           CONSTRAINT [DF_C_NOTIFICATION_GROUP_TYPE_ALLOW_CUSTOMER] DEFAULT ((0)) NOT NULL,
    [ALLOW_ORDER]    BIT           CONSTRAINT [DF_C_NOTIFICATION_GROUP_TYPE_ALLOW_CUSTOMER1] DEFAULT ((0)) NOT NULL,
    [ALLOW_CONTRACT] BIT           CONSTRAINT [DF_C_NOTIFICATION_GROUP_TYPE_ALLOW_CONTRACT] DEFAULT ((0)) NOT NULL,
    [IS_SYSTEM]      BIT           CONSTRAINT [DF_C_NOTIFICATION_GROUP_TYPE_IS_SYSTEM] DEFAULT ((0)) NOT NULL,
    [ALLOW_WORKFLOWS]   BIT        CONSTRAINT [DF_C_NOTIFICATION_GROUP_TYPE_ALLOW_WORKFLOWS] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_C_NOTIFICATION_GROUP_TYPE] PRIMARY KEY CLUSTERED ([GROUP_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Stores the groups of notifications', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_NOTIFICATION_GROUP_TYPE';


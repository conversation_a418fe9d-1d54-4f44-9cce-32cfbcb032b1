-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <01/27/2014>
-- Description:	<gets main information over the recyclin order, required to build the Report PDF>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INBOUND_ORDER_BY_ID_FOR_PDF_REPORT] 
	@ORDER_ID			BIGINT = 561,
	@FreightCarrierId	bigint = null
AS
BEGIN

	DECLARE @ZEROS VARCHAR(10) = '000000000'

	SELECT 
		R.RECYCLING_ORDER_ID
		,IR.AUTO_NAME
		,C.CUSTOMER_NAME
		,R.PO_NUMBER
		,'BOL-' + SUBSTRING(@ZEROS, 1, LEN(@ZEROS) - LEN([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](IR.AUTO_NAME))) +
		  CAST([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](IR.AUTO_NAME) AS NVARCHAR(150)) AS BOL_NUMBER
	    ,R.CustomerContractNo
		,R.REFERENCE_1
		,R.DESCR
		,IR.RECIEVE_DATE
		,IR.COMPLETED_DT
		,R.SETTLE_DATE
		,OS.Id																AS [STATUS_ID]
		,OS.Name															AS  STATUS_CD
		,CTRCT.[AUTO_NAME]													AS [CONTRACT_NAME]
		,dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(IR.CUSTOMER_CONTACT_ID)	AS [CONTACT_NAME]
		,dbo.fn_str_GET_USER_AUTO_NAME(U.UserID, 0)							AS [REP_USER_NAME]
		,dbo.fn_str_GET_USER_AUTO_NAME(IR.RECEIVED_BY_USER_ID, 0)			AS [RECEIVER_USER_NAME]
		,dbo.fn_str_GET_USER_AUTO_NAME(R.INSERTED_BY_USER, 0)				AS [RECEIVER_START_USER_NAME]
		
		,R.IS_TRANSFER
		,R.RECEIVING_NOTES

		-- Our Trucking
		,GETUTCDATE()													  AS NOW_DATE
		,R.PICKUP_START_DATE
		,R.PICKUP_END_DATE
		,dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(IR.SHIP_TO_CONTACT_ID) AS SHIP_TO_CONTACT
		,STET.LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_CD					  AS SHIP_TO_LOCATION_EQUIPMENT
		,CET.LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_CD					  AS CUSTOMER_LOCATION_EQUIPMENT
		,IR.PALLET_COUNT
		,IR.WEIGHT_LBS
		,ffc.IsBlindDropShipment										  as IS_BLIND_DROP_SHIPMENT
		,RC.RECYCLING_CONDITION_CD
		,FC.VALUE														  AS FREIGHT_CLASS
		,PT.PACKAGING_TYPE_ALIAS										  AS PACKAGING_TYPE_DESC
		----
		
		-- ship to
		,SA.POSTAL_CODE						  AS SHIP_TO_POSTAL_CODE 
		,SA.COUNTRY							  AS SHIP_TO_COUNTRY		
		,SA.[STATE]							  AS SHIP_TO_STATE		
		,SA.CITY							  AS SHIP_TO_CITY		
		,SA.STREET_1						  AS SHIP_TO_STREET_1	
		,SA.STREET_2						  AS SHIP_TO_STREET_2	
		,SA.STREET_3						  AS SHIP_TO_STREET_3	
		,SA.PHONE							  AS SHIP_TO_PHONE
		,SC.CUSTOMER_NAME					  AS SHIP_TO_CUSTOMER_NAME
		,SCC.FIRST_NAME + ' ' + ISNULL(SCC.LAST_NAME, '') AS SHIP_TO_CONTACT_NAME  	
		----

		-- bill to
		,ISNULL(CBA.POSTAL_CODE, BA.POSTAL_CODE)				 AS BILL_TO_POSTAL_CODE 
		,ISNULL(CBA.COUNTRY, BA.COUNTRY	)						 AS BILL_TO_COUNTRY		
		,ISNULL(CBA.[STATE], BA.[STATE]	)						 AS BILL_TO_STATE		
		,ISNULL(CBA.CITY, BA.CITY	)							 AS BILL_TO_CITY		
		,ISNULL(CBA.STREET_1, BA.STREET_1)						 AS BILL_TO_STREET_1	
		,ISNULL(CBA.STREET_2, BA.STREET_2)						 AS BILL_TO_STREET_2	
		,ISNULL(CBA.STREET_3, BA.STREET_3)						 AS BILL_TO_STREET_3	
		,ISNULL(CBA.PHONE, BA.PHONE)							 AS BILL_TO_PHONE
		,ISNULL(CC.CUSTOMER_NAME, C.CUSTOMER_NAME)				 AS BILL_TO_CUSTOMER_NAME
		----

		-- pick up location
		,PL.POSTAL_CODE	AS PICKUP_POSTAL_CODE	
		,PL.COUNTRY		AS PICKUP_COUNTRY		
		,PL.[STATE]		AS PICKUP_STATE	
		,PL.CITY		AS PICKUP_CITY		
		,PL.STREET_1	AS PICKUP_STREET_1	
		,PL.STREET_2	AS PICKUP_STREET_2	
		,PL.STREET_3	AS PICKUP_STREET_3	
		,PL.PHONE		AS PICKUP_PHONE
		,(
			CASE PL.IS_COMPANY
				WHEN 0 THEN C.CUSTOMER_NAME
				WHEN 1 THEN ISNULL(PL.COMPANY_NAME, C.CUSTOMER_NAME)
			END
		)				AS PICKUP_CUSTOMER_NAME
		,IR.EQUIPMENT_NOTES

		-- Rep info
		,U.PHONE_MAIN	AS REP_PHONE_MAIN
		,U.PHONE_MOBILE AS REP_PHONE_MOBILE
		,U.Email		AS REP_EMAIL
		----

		--RFQ
		,TT.TRUCK_TYPE_CD
		,IR.PACKAGING_TYPE_ID
		,IR.WEIGHT_LBS
		,ffc.Insurance												as INSURANCE
		,(SELECT 
			',',
			 RIM.RECYCLING_ITEM_MASTER_NAME AS "data()" 
		  FROM [recycling].[F_CommodityRule]		OM  WITH (NOLOCK)		
		  INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		RIM WITH (NOLOCK)
			ON RIM.RECYCLING_ITEM_MASTER_ID = OM.[CommodityId]	
		  LEFT JOIN [dbo].[C_RECYCLING_PRICE_TYPE]		RT  WITH (NOLOCK)
			ON RT.[PRICE_TYPE_ID] = OM.[PriceTypeId]
		  WHERE OM.[RecyclingOrderId] = @ORDER_ID
		  ORDER BY RIM.RECYCLING_ITEM_MASTER_NAME
		  FOR XML PATH(''))		AS MATERIALS
		,IR.PACKAGE_LENGTH
		,IR.PACKAGE_WIDTH
		,IR.PACKAGE_HEIGHT
		,[dbo].fn_str_GET_DEFAULT_STATEMENT_TERM_BY_TYPE(3) AS RECYCLING_CERT_TERM
		,IR.DELIVERY_DT										as DeliveryDate
	FROM		F_RECYCLING_ORDER				  R	 WITH(NOLOCK)
	INNER JOIN  F_RECYCLING_ORDER_INBOUND		  IR	 WITH(NOLOCK)
		ON R.RECYCLING_ORDER_ID = IR.RECYCLING_ORDER_ID
	INNER JOIN [recycling].[C_InboundOrderStatus] OS	 WITH(NOLOCK)
		ON IR.StatusId = OS.Id
	INNER JOIN  [dbo].[tb_User]				  U	 WITH(NOLOCK)
		ON R.[USER_ID] = U.UserID
	INNER JOIN	F_CUSTOMER				  C	 WITH(NOLOCK)
		ON R.CUSTOMER_ID = C.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER					  SC	 WITH(NOLOCK)
		ON IR.SHIP_TO_CUSTOMER_ID = SC.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER_CONTACT				  SCC WITH(NOLOCK)
		ON SCC.CUSTOMER_ID = IR.SHIP_TO_CUSTOMER_ID
		AND SCC.IS_MAIN = 1
	LEFT JOIN	F_CUSTOMER_ADDRESS				  SA	 WITH(NOLOCK)
		ON IR.SHIP_TO_ADDRESS_ID = SA.CUSTOMER_ADDRESS_ID
	LEFT JOIN	F_CUSTOMER_ADDRESS				  BA	 WITH(NOLOCK)
		ON  R.CUSTOMER_ID = BA.CUSTOMER_ID
		AND BA.CUSTOMER_ADDRESS_TYPE_ID = 3
		AND BA.IS_MAIN = 1 -- "3" is "BILL TO" in C_CUSTOMER_ADDRESS_TYPE
	LEFT JOIN F_RECYCLING_ORDER_CONTRACT			ROC	WITH(NOLOCK)
		ON ROC.RECYCLING_ORDER_ID = R.RECYCLING_ORDER_ID
		AND ROC.IsFirstApplied = 1
	LEFT JOIN [dbo].[F_CONTRACT] CTRCT WITH (NOLOCK)
		ON ROC.CONTRACT_ID = CTRCT.CONTRACT_ID
	LEFT JOIN F_CUSTOMER_ADDRESS CBA WITH (NOLOCK)
		ON CTRCT.BILLING_ADDRESS_ID = CBA.CUSTOMER_ADDRESS_ID
	LEFT JOIN F_CUSTOMER_CONTACT CBC WITH (NOLOCK)
		ON CTRCT.BILLING_CONTACT_ID = CBC.CUSTOMER_CONTACT_ID
	LEFT JOIN	F_CUSTOMER					CC	WITH(NOLOCK)
		ON CTRCT.CUSTOMER_ID = CC.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER_ADDRESS				PL	WITH(NOLOCK)
		ON IR.CUSTOMER_ADDRESS_ID = PL.CUSTOMER_ADDRESS_ID
	LEFT JOIN D_RECYCLING_PACKAGING_TYPE		PT	WITH(NOLOCK)
		ON PT.RECYCLING_PACKAGING_TYPE_ID = IR.PACKAGING_TYPE_ID
	LEFT JOIN C_RECYCLING_FREIGHT_CLASS			FC	WITH(NOLOCK)
		ON FC.RECYCLING_FREIGHT_CLASS_ID = IR.FREIGHT_CLASS_ID
	LEFT JOIN C_RECYCLING_CONDITION				RC	WITH(NOLOCK)
		ON RC.RECYCLING_CONDITION_ID = IR.RECYCLING_CONDITION_ID
	LEFT JOIN C_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE STET WITH(NOLOCK)
		ON STET.LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID = IR.SHIP_TO_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID
	LEFT JOIN C_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE CET  WITH(NOLOCK)
		ON CET.LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID = IR.CUSTOMER_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE_ID
	LEFT JOIN C_TRUCK_TYPE						TT  WITH(NOLOCK)
		ON TT.TRUCK_TYPE_ID = IR.TRUCK_TYPE_ID

	left join (
		select
			ffc.[RecyclingOrderId],
			ffc.[Insurance],
			ffc.[IsBlindDropShipment]
		from (
			select 
				ffc.[RecyclingOrderId],
				min(ffc.[FREIGHT_CARRIER_ID])	as [FREIGHT_CARRIER_ID]
			from dbo.F_FREIGHT_CARRIER		ffc with(nolock)
			where @FreightCarrierId is null or ffc.FREIGHT_CARRIER_ID = @FreightCarrierId
			group by ffc.[RecyclingOrderId]
		) fcm
		inner join dbo.F_FREIGHT_CARRIER	ffc with(nolock)
			on ffc.[FREIGHT_CARRIER_ID] = fcm.[FREIGHT_CARRIER_ID]
	)	ffc
		on ffc.[RecyclingOrderId] = R.RECYCLING_ORDER_ID

	WHERE R.RECYCLING_ORDER_ID = @ORDER_ID
	
END
CREATE function [bi].[tvf_GetProductionManagementTotals]
(  
    @from       datetime	 = null,
	@to         datetime     = null,
	@utc_offset int,
	@employee	nvarchar(max) = ''
   ,@commodity	nvarchar(max) = ''
   ,@weight		VARCHAR(50) = ''
   ,@laborHours VARCHAR(10)   = ''
   ,@uom        VARCHAR(2)    = ''
)
RETURNS @T_RESULT TABLE
(	
	NetWeight		DECIMAL(13,2),
	LaborHours		VARCHAR(10),
	Productivity	DECIMAL(13,3)	
)
AS
BEGIN

	declare 
			@net_weight DECIMAL(13,2) = TRY_PARSE(@weight AS DECIMAL(13,2)),
			@weightFrom DECIMAL(13,2),
			@weightTo   DECIMAL(13,2),
			@commodity_value nvarchar(max) = REPLACE(REPLACE(@commodity, '''''', ','), '''', ''),
			@employee_value nvarchar(max) = REPLACE(REPLACE(@employee, '''''', ','), '''', '')

	if (CHARINDEX('-', @weight) > 0)
	begin		
		select @weightFrom = TRY_PARSE(SUBSTRING(@weight, 0, CHARINDEX('-', @weight)) AS DECIMAL(13,2))
		select @weightTo = TRY_PARSE(SUBSTRING(@weight, CHARINDEX('-', @weight) + 1, len(@weight)) AS DECIMAL(13,2))
	end
	else
		select @weightFrom = @net_weight, @weightTo = @net_weight

	declare 
		
		@needConvert bit = iif(@uom = 'kg', 1, 0),
		@lbstokg float = 0.453592,
		@f_laborHours float;

	declare @hrs_index int = CHARINDEX('h', @laborHours)
	declare @hrs varchar(10) = iif(@hrs_index > 0, SUBSTRING(@laborHours, 0, @hrs_index), '')
	declare @minutes_start_index int = iif(@hrs = '', 0, @hrs_index + 1)
	declare @minutes varchar(10) = iif(CHARINDEX('m', @laborHours) > 0, SUBSTRING(@laborHours, @minutes_start_index, CHARINDEX('m', @laborHours) - @minutes_start_index), '')

    select @f_laborHours = (@hrs * 60 + @minutes) / cast(60 as float);	
	
	insert into @T_RESULT
	SELECT 
		sum(gu.NetWeight),
		[dbo].[fn_str_LaborHoursRepresentation](sum(gu.LaborHours)) as LaborHours,		
		sum(gu.NetWeight) / sum(gu.LaborHours) as Productivity
	FROM 
	(
		select 		
			u.UserID,
			roi.[RECYCLING_ITEM_MASTER_ID],
			sum(clm.LaborHours)					as LaborHours,
			cast(iif(@needConvert = 1, sum(ISNULL(roi.[WEIGHT_RECEIVED], 0) - ISNULL(roi.[WEIGHT_TARE], 0)) * @lbstokg, 
				sum(ISNULL(roi.[WEIGHT_RECEIVED], 0) - ISNULL(roi.[WEIGHT_TARE], 0))) as DECIMAL(13,2)) as NetWeight			
		from [dbo].[tb_User] u with (nolock)
		inner join [recycling].[F_ConsumeLaborManagement] clm with (nolock)
			on clm.[EmployeeId] = u.[UserID]
		inner join  [dbo].[F_RECYCLING_ORDER_ITEM]			AS roi WITH(NOLOCK)
			on roi.RECYCLING_ORDER_ITEM_ID = clm.[RecyclingOrderItemId]	
		WHERE roi.[IS_INACTIVE] = 0 
			and (roi.[CONSUMED_DT] is null or roi.[CONSUMED_DT] >= @from and roi.[CONSUMED_DT] <= @to)
			and (@employee_value = 'All' or isnull(u.[FirstName], '') + ' ' + isnull(u.[LastName], '') in (select [value] from [dbo].[fnSplit](@employee_value, ',')))					
		group by u.UserID, roi.[RECYCLING_ITEM_MASTER_ID]
	) gu
	inner join [dbo].[tb_User] u with (nolock)
		on gu.UserID = u.UserID 
	INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		AS FRIM WITH(NOLOCK)
		ON FRIM.[RECYCLING_ITEM_MASTER_ID] = gu.[RECYCLING_ITEM_MASTER_ID]									
	where (@commodity_value = 'All' or FRIM.[RECYCLING_ITEM_MASTER_NAME] in (select [value] from [dbo].[fnSplit](@commodity_value, ',')))
		and (@weight = '' or ((@weightFrom is null or @weightFrom <= gu.NetWeight) and (@weightTo is null or gu.NetWeight <= @weightTo)))
		and (@laborHours = '' or @f_laborHours = gu.LaborHours)
	return

END
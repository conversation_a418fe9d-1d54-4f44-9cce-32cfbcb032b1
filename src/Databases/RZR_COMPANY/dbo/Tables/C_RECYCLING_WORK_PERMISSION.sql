CREATE TABLE [dbo].[C_RECYCLING_WORK_PERMISSION] (
    [RECYCLING_WORK_PERMISSION_ID] INT           NOT NULL,
    [PERMISSION_NAME]              VARCHAR (150) NOT NULL,
    CONSTRAINT [PK_C_RECYCLING_WORK_PERMISSION] PRIMARY KEY CLUSTERED ([RECYCLING_WORK_PERMISSION_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is used for inbound order schedule, which hrs arrive to client', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_WORK_PERMISSION';


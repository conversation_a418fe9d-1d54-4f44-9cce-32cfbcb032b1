-- =============================================
-- Author:	 <O.Evseev>
-- Create date: <01/22/2014>
-- Description: <returns recycling master items>
-- =============================================
-- EXEC [dbo].[sp_GET_RECYCLING_ITEM_MASTERS] @C_IS_DEBUG = 1
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ITEM_MASTERS]
    @ORDER_COLUMN_NAME	VARCHAR(250)	= N'MasterItemId',
    @ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
    @ITEMS_PER_PAGE		INT				= 20,
    @PAGE_INDEX			INT				= 0,
    @FILTER_WHERE		VARCHAR(MAX)	= '',
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN
    DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
    DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE

    DECLARE @filterCondition VARCHAR(MAX) = N'';
    IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
    BEGIN
	   SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
    END
    DECLARE @query NVARCHAR (MAX) = N'
	;WITH m_data AS (
		SELECT
			FRIM.RECYCLING_ITEM_MASTER_ID		AS MasterItemId,
			FRIM.RECYCLING_ITEM_MASTER_NAME		AS [Name],
			FACN.[AlternativeCommodityName]		AS [AlternativeName],
			FRIM.RECYCLING_ITEM_MASTER_DESC		AS Description,
			FRIM.CATEGORY_ID					AS CategoryId,
			C.CATEGORY							AS Category,
			FRIM.COMMODITY_CODE					AS Code,
			ISNULL(I.IMAGE_COUNT, 0)			AS Images,
			ISNULL(L.LINK_COUNT, 0)
				+ isnull(fcpi.LINK_COUNT, 0)	AS LinkCount,
			FRIM.REQUIRES_NOTES					AS RequiresNotes,
			FRIM.REQUIRES_COUNT					AS RequiresCount,
			FRIM.REQUIRES_REFERENCE				AS RequiresReference,
			FRIM.DEFAULT_COUNT 					AS DefaultCount,
			FRIM.DEFAULT_WORKFLOW_TYPE_ID 		AS DefaultWorkflowStep,
			CRWT.WORKFLOW_TYPE_DESC	 			AS DefaultWorkflowTypeCd,
			FRIM.IS_UNIVERSAL_WASTE				AS IsUniversalWaste,
			FRIM.IS_INACTIVE					AS IsInactive,
			FRIM.COMMODITY_GROUP_ID				AS GroupId,
			FCG.COMMODITY_GROUP_NAME			AS [GroupName],
			j.Tags								AS Tags,
			CASE
				WHEN j.TagsString is not null THEN LEFT(j.TagsString, LEN(j.TagsString) - 1)
				ELSE ''''
			END									AS TagsString,
			FRIM.[IS_MATERIAL_TRACKING]			AS IsTrackingEnabled,
			CASE WHEN (FRIM.[IS_MATERIAL_TRACKING]) = 1
					THEN
					''$'' + CAST(ISNULL([recycling].[fn_money_GetCommodityRTIPrice](FRIM.RECYCLING_ITEM_MASTER_ID), 0) as NVARCHAR(128)) +''/'' + UOM.[UnitOfMeasureValue]
				 ELSE ''''
			END									AS RTIPrice,
			FRIM.IS_HAZARDOUS					AS IsHazardous,
			ISNULL(fcv.[EstimatedValue], 0 )	AS EstimatedValue,
			ISNULL(FRIM.[Margin], 0 )			AS Margin,
			(ISNULL(fcv.[EstimatedValue], 0 ) *ISNULL(FRIM.[Margin], 0 ))/100	AS EstimatedPurchasePrice,
			FRIM.MaterialStreamTypeId			AS MaterialStreamTypeId,
			FRIM.DataRequirementsTypeId			AS DataRequirementsTypeId,
			FRIM.ExternalId						AS ExternalId,
			FRIM.IS_NON_STATE_PROGRAM			AS IsNonStateProgram,
			FRIM.ClassTrackingPrice			    AS ClassTrackingPrice,
			FRIM.BusinessUnitId			        AS BusinessUnitId,
			CBU.Name			                AS BusinessUnitName
		FROM dbo.F_RECYCLING_ITEM_MASTER			FRIM	WITH(NOLOCK)
		LEFT JOIN recycling.F_CommodityVersion		fcv		WITH (NOLOCK)
			on fcv.Id = FRIM.ActualVersionId
		LEFT JOIN [recycling].[C_ItemMasterUnitOfMeasure] UOM WITH(NOLOCK)
			ON UOM.[UnitOfMeasureId] = FRIM.[MATERIAL_UNIT_OF_MEASURE_ID]
		LEFT JOIN C_RECYCLING_WORKFLOW_TYPE		CRWT	WITH(NOLOCK)
			ON CRWT.WORKFLOW_TYPE_ID = FRIM.DEFAULT_WORKFLOW_TYPE_ID
		LEFT JOIN F_COMMODITY_GROUP				FCG		WITH(NOLOCK)
			ON FCG.COMMODITY_GROUP_ID = FRIM.COMMODITY_GROUP_ID
		LEFT JOIN [recycling].[F_AlternativeCommodityName] AS FACN WITH(NOLOCK)
			ON FACN.[CommodityId] = FRIM.[RECYCLING_ITEM_MASTER_ID]
			AND FACN.[RecyclingOrderId] IS NULL
			AND FACN.[ContractId] IS NULL
	    left join recycling.C_BusinessUnit CBU with(nolock)
	        on FRIM.BusinessUnitId = CBU.Id
		LEFT JOIN (
			 SELECT
				    CA.CATEGORY_ID,
				    REPLACE(CA.ITEM_CATEGORY_FULL_PATH, N''|'', N'' > '') AS CATEGORY
			 FROM dbo.D_CATEGORY_HIERARCHY CA	WITH (NOLOCK)
			 WHERE CA.IS_INACTIVE = 0) C
			ON FRIM.CATEGORY_ID = C.CATEGORY_ID
		  LEFT JOIN (
			 SELECT
				    IM.ITEM_MASTER_ID,
				    COUNT(IM.IMAGE_ID)		AS IMAGE_COUNT
			 FROM dbo.F_RECYCLING_ITEM_MASTER_IMAGE IM WITH(NOLOCK)
			 GROUP BY IM.ITEM_MASTER_ID)				I
			 ON FRIM.RECYCLING_ITEM_MASTER_ID = I.ITEM_MASTER_ID
		LEFT JOIN (
			SELECT
				IT.RECYCLING_ITEM_MASTER_ID		  AS ITEM_MASTER_ID,
				COUNT(IT.RECYCLING_ORDER_ITEM_ID) AS  LINK_COUNT
			FROM F_RECYCLING_ORDER_ITEM IT	WITH(NOLOCK)
			GROUP BY IT.RECYCLING_ITEM_MASTER_ID
		)				L
			ON FRIM.RECYCLING_ITEM_MASTER_ID = L.ITEM_MASTER_ID
		LEFT JOIN (
			SELECT
				fcpi.CommodityId	AS ITEM_MASTER_ID,
				COUNT(fcpi.Id)				AS LINK_COUNT
			FROM recycling.F_CommodityProfileItem fcpi	WITH(NOLOCK)
			GROUP BY fcpi.CommodityId
		)				fcpi
			ON FRIM.RECYCLING_ITEM_MASTER_ID = fcpi.ITEM_MASTER_ID

		OUTER APPLY (
			SELECT
				(SELECT
					t.TAG_ID			AS value
					,t.TAG_CD			AS label
				FROM dbo.F_ENTITY_TAG	te WITH(NOLOCK)
				INNER JOIN dbo.F_TAG	t WITH(NOLOCK)
					ON te.TAG_ID = t.TAG_ID
				WHERE te.TAG_TYPE_ID = 1 --commodity
					AND FRIM.RECYCLING_ITEM_MASTER_ID = te.[ENTITY_ID]
				ORDER BY te.ENTITY_TAG_ID
				FOR JSON PATH)		AS Tags,

				(SELECT
					t.TAG_CD + '', ''
				FROM dbo.F_ENTITY_TAG	te WITH(NOLOCK)
				INNER JOIN dbo.F_TAG	t WITH(NOLOCK)
					ON te.TAG_ID = t.TAG_ID
				WHERE te.TAG_TYPE_ID = 1 --commodity
					AND FRIM.RECYCLING_ITEM_MASTER_ID = te.[ENTITY_ID]
				ORDER BY te.ENTITY_TAG_ID
				FOR XML PATH('''')) AS TagsString
		) j
	   )
	   ';
	   SET @query = @query + N'
	   SELECT TOP(1)
		  -1					AS RowID,
		  COUNT(MasterItemId)	AS MasterItemId,
		  NULL					AS [Name],
		  NULL					AS [AlternativeName],
		  NULL					AS Description,
		  0						AS CategoryId,
		  NULL					AS Category,
		  NULL					AS Code,
		  0						AS Images,
		  0						AS LinkCount,
		  0						AS RequiresNotes,
		  0						AS RequiresCount,
		  0						AS RequiresReference,
		  0						AS DefaultCount,
		  0						AS DefaultWorkflowStep,
		  NULL					AS DefaultWorkflowTypeCd,
		  0						AS IsUniversalWaste,
		  0						AS IsInactive,
		  0						AS GroupId,
		  NULL					AS [GroupName],
		  NULL					AS Tags,
		  NULL					AS TagsString,
		  NULL					AS IsTrackingEnabled,
		  NULL					AS RTIPrice,
		  0						AS IsHazardous,
		  NULL					AS [EstimatedValue],
		  NULL					AS [Margin],
		  NULL					AS [EstimatedPurchasePrice],
		  NULL					AS MaterialStreamTypeId,
		  NULL					AS DataRequirementsTypeId,
		  NULL					AS ExternalId,
		  0						AS IsNonStateProgram,
		  null					AS ClassTrackingPrice,
		  null					AS BusinessUnitId,
		  null					AS BusinessUnitName
	   FROM m_data
	   ' + @filterCondition + '
	   UNION ALL
	   SELECT
		  t.RowID,
		  t.MasterItemId,
		  t.[Name],
		  t.[AlternativeName],
		  t.Description,
		  t.CategoryId,
		  t.Category,
		  t.Code,
		  t.Images,
		  t.LinkCount,
		  ISNULL(t.RequiresNotes, 0)		AS RequiresNotes,
		  ISNULL(t.RequiresCount, 0)		AS RequiresCount,
		  ISNULL(t.RequiresReference, 0)	AS RequiresReference,
		  t.DefaultCount,
		  t.DefaultWorkflowStep,
		  t.DefaultWorkflowTypeCd,
		  t.IsUniversalWaste,
		  t.IsInactive,
		  t.GroupId,
		  t.[GroupName],
		  t.Tags,
		  t.TagsString,
		  t.IsTrackingEnabled,
		  t.RTIPrice,
		  t.IsHazardous,
		  t.[EstimatedValue],
		  t.[Margin],
		  t.[EstimatedPurchasePrice],
		  t.MaterialStreamTypeId,
		  t.DataRequirementsTypeId,
		  t.ExternalId,
		  t.IsNonStateProgram,
		  t.ClassTrackingPrice,
		  t.BusinessUnitId,
		  t.BusinessUnitName
	   FROM (
		  SELECT
			 ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
			 *
		  FROM m_data ' + @filterCondition + ') t
	   WHERE
		  RowID BETWEEN @startRowNumber AND @endRowNumber'



	declare @queryParams nvarchar(max) =
	N'	@startRowNumber	bigint
		,@endRowNumber	bigint'
	--select @query

	IF @C_IS_DEBUG = 1
	BEGIN
		PRINT (cast('Declare '+@queryParams as ntext))
		PRINT (cast(@query as ntext))
	END

	EXEC sp_executesql @query
		,@queryParams
		,@startRowNumber = @startRowNumber
		,@endRowNumber	 = @endRowNumber;
END
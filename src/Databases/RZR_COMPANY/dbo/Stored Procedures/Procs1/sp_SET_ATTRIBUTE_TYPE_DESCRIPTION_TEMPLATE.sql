-- =============================================
-- Author: <Oleg K. Evseev>
-- Create date: <11/15/2013>
-- Description: <writes attribute type description template>
-- Changes: Edited by <PERSON><PERSON>. @TEMPLATE_ID param added
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE] 	
	@TEMPLATE_ID BIGINT,
	@ATTRIBUTE_TYPE_ID BIGINT,
	@TITLE NVARCHAR(MAX) = N'',
	@DESCR NVARCHAR(MAX) = N'',
	@NAME  NVARCHAR(MAX) = N''
AS
BEGIN
	IF (NOT EXISTS (
		SELECT TOP(1) 1 
		FROM F_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE	T 
		WHERE T.TEMPLATE_NAME = @NAME AND T.TEMPLATE_ID <> @TEMPLATE_ID
			AND T.IS_INACTIVE = 0
			AND T.IS_DELETED = 0))
	BEGIN
		IF (NOT EXISTS (
			SELECT TOP(1) 1 
			FROM F_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE	T 
			WHERE T.TEMPLATE_ID = @TEMPLATE_ID
				AND T.IS_INACTIVE = 0
				AND T.IS_DELETED = 0))
		BEGIN
	
			INSERT INTO F_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE (
				ATTRIBUTE_TYPE_ID,
				TEMPLATE_DESCR,
				TEMPLATE_TEXT,
				TEMPLATE_NAME,
				INSERTED_BY,
				INSERTED_DT	)
			VALUES (
				@ATTRIBUTE_TYPE_ID,
				@TITLE,
				@DESCR,
				@NAME,
				N'sp_SET_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE',
				GETUTCDATE()
			)

			SET @TEMPLATE_ID = SCOPE_IDENTITY()
		END
		ELSE
		BEGIN
			UPDATE T set
				T.ATTRIBUTE_TYPE_ID = @ATTRIBUTE_TYPE_ID
				, T.TEMPLATE_DESCR = @TITLE
				, T.TEMPLATE_TEXT = @DESCR
				, T.TEMPLATE_NAME = @NAME
			FROM F_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE	T 
			WHERE T.TEMPLATE_ID = @TEMPLATE_ID
			  AND T.IS_INACTIVE = 0
			  AND T.IS_DELETED = 0
		END
	END
	ELSE
	BEGIN
		SET @TEMPLATE_ID = -1
	END
	SELECT @TEMPLATE_ID TEMPLATE_ID
END
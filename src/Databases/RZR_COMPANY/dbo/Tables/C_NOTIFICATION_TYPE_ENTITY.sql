CREATE TABLE [dbo].[C_NOTIFICATION_TYPE_ENTITY] (
    [NOTIFICATION_TYPE_ENTITY_ID] BIGINT IDENTITY (1, 1) NOT NULL,
    [NOTIFICATION_TYPE_ID]        BIGINT NOT NULL,
    [ENTITY_TYPE_ID]              BIGINT NOT NULL,
    CONSTRAINT [PK_C_NOTIFICATION_TYPE_ENTITY] PRIMARY KEY CLUSTERED ([NOTIFICATION_TYPE_ENTITY_ID] ASC),
    CONSTRAINT [FK_C_NOTIFICATION_TYPE_ENTITY_C_NOTIFICATION_ENTITY] FOREIGN KEY ([ENTITY_TYPE_ID]) REFERENCES [dbo].[C_NOTIFICATION_ENTITY] ([ENTITY_TYPE_ID]),
    CONSTRAINT [FK_C_NOTIFICATION_TYPE_ENTITY_C_NOTIFICATION_TYPE] FOREIGN KEY ([NOTIFICATION_TYPE_ID]) REFERENCES [dbo].[C_NOTIFICATION_TYPE] ([NOTIFICATION_TYPE_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The table is static and it ties types of notifications with “sent to” user', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_NOTIFICATION_TYPE_ENTITY';


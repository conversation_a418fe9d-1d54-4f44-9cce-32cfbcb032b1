-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ITEMS_FOR_RMA]
	 @RMA_ID			BIGINT			= -1
	,@SALES_ORDER_ID	BIGINT 
	,@ORDER_COLUMN_NAME	NVARCHAR(150)	= N'ITEM_NUMBER'
	,@ORDER_DIRECTION	NVARCHAR(20)	= N'ASC'
	,@ITEMS_PER_PAGE	INT				= 20
	,@PAGE_INDEX		INT				= 0
	,@FILTER_WHERE		NVARCHAR(2000)	= N''
	,@IS_EMAIL_REPORT	BIT				= 0
	,@IS_DEBUG			BIT				= 0
AS
BEGIN

DECLARE @filterCondition NVARCHAR(2006) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END

	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	DECLARE @excludeRmaFromSo NVARCHAR(MAX) = N''

	IF(@IS_EMAIL_REPORT = 0 AND ISNULL(@RMA_ID, 0) <= 0) BEGIN
		SET @excludeRmaFromSo = N' OR FSOR.SALES_ORDER_ID = @SALES_ORDER_ID'
	END

	DECLARE @query NVARCHAR (MAX) = N'
		WITH m_data AS (
			SELECT
				I.SALES_ORDER_ITEM_ID
				,M.ITEM_NUMBER
				,INV.ITEM_INVENTORY_SERIAL			AS [SERIAL]
				,INV.ITEM_INVENTORY_UNIQUE_ID		AS UNIQUE_ID
				,MFG.MANUFACTURER_CD				AS MANUFACTURER
				,T.ITEM_TITLE						AS DESCRIPTION
				,I.ITEM_PRICE * I.ITEM_QUANTITY		AS [PRICE]
			FROM F_SALES_ORDER_ITEM			I	WITH(NOLOCK)
			INNER JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
				ON I.[PRODUCT_MASTER_ID] = PM.PRODUCT_MASTER_ID
			INNER JOIN F_ITEM_MASTER		M	WITH(NOLOCK)
				ON PM.ITEM_MASTER_ID = M.ITEM_MASTER_ID
			INNER JOIN F_ITEM_INVENTORY		INV WITH(NOLOCK)		
				ON I.ITEM_INVENTORY_ID = INV.ITEM_INVENTORY_ID
			INNER JOIN D_MANUFACTURER		MFG WITH(NOLOCK)
				ON M.MANUFACTURER_ID = MFG.MANUFACTURER_ID
			LEFT JOIN F_ITEM_MASTER_TITLE	T	WITH(NOLOCK)
				ON M.ITEM_MASTER_ID = T.ITEM_MASTER_ID		
			WHERE I.SALES_ORDER_ID = @SALES_ORDER_ID -- all RMAs of the Order
				AND I.ITEM_INVENTORY_ID IS NOT NULL		
				-- shipped 
				AND EXISTS(
					SELECT TOP(1)
						1
					FROM dbo.F_SHIPPING_PACKAGE sp WITH(NOLOCK)
					INNER JOIN dbo.F_SHIPPING S
						ON  s.SHIPPING_ID = sp.SHIPPING_ID
						AND s.SALES_ORDER_ID = I.SALES_ORDER_ID
						AND s.STATUS_ID = 3
					WHERE SP.PACKAGE_ID = I.PACKAGE_ID
				)		
				-- not included into other RMAs	
				AND I.SALES_ORDER_ITEM_ID NOT IN ( 
					SELECT 
						fri.SALES_ORDER_ITEM_ID 
					FROM F_RMA_ITEM fri 
					LEFT JOIN F_SALES_ORDER_RMA		fsor	WITH(NOLOCK)
						ON fsor.RMA_ID = fri.RMA_ID
					WHERE fsor.RMA_ID != @RMA_ID
					  AND fsor.RMA_STATUS_ID IN (1, 2)
						' + @excludeRmaFromSo + '
				)
			)
			SELECT TOP(1)
				-1							AS RowID,
				COUNT(SALES_ORDER_ITEM_ID)	AS SALES_ORDER_ITEM_ID,
				NULL						AS ITEM_NUMBER,
				NULL						AS [SERIAL],
				NULL						AS UNIQUE_ID,
				NULL						AS MANUFACTURER,
				NULL						AS DESCRIPTION,
				-1							AS [PRICE],
				0							AS SELECTED,
				-1							AS RETURN_REASON,
				NULL						AS RETURN_REASON_CD,
				NULL						AS PACKAGE_ID				
			FROM m_data ' + @filterCondition + N'
			UNION
			SELECT
				t.*
			FROM
				(SELECT	ROW_NUMBER() OVER (ORDER BY M.'+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N', M.SALES_ORDER_ITEM_ID desc) AS RowID,
						M.SALES_ORDER_ITEM_ID,		
						M.ITEM_NUMBER,
						M.[SERIAL],
						M.UNIQUE_ID,
						M.MANUFACTURER,
						M.DESCRIPTION,
						M.[PRICE],
						ISNULL(ITEM.SELECTED, 0) AS SELECTED,						
						ITEM.RETURN_REASON,
						ITEM.RETURN_REASON_CD,						
						package.PACKAGE_ID
					FROM m_data M 
					LEFT JOIN (
						SELECT
							fri.SALES_ORDER_ITEM_ID AS SALES_ORDER_ITEM_ID
							,1						AS SELECTED
							,fri.RETURN_REASON_ID	AS RETURN_REASON
							,drrr.REASON_CD			AS RETURN_REASON_CD
						FROM F_RMA_ITEM					fri	WITH(NOLOCK)						
						LEFT JOIN D_RMA_RETURN_REASON drrr	WITH(NOLOCK)
							ON drrr.REASON_TYPE_ID = fri.RETURN_REASON_ID
						WHERE fri.RMA_ID = @RMA_ID
					) item
						ON item.SALES_ORDER_ITEM_ID = M.SALES_ORDER_ITEM_ID
					LEFT JOIN (
						SELECT
							fri.SALES_ORDER_ITEM_ID
							,max(frpi.PACKAGE_ID)	AS PACKAGE_ID
						FROM F_RMA_ITEM					fri		WITH(NOLOCK)
						INNER JOIN F_RMA_PACKAGE_ITEM	frpi 	WITH(NOLOCK)
							ON  frpi.IS_INACTIVE = 0
							AND frpi.IS_DELETED = 0
							AND frpi.RMA_ITEM_ID = fri.RMA_ITEM_ID
						WHERE fri.RMA_ID = @RMA_ID
						GROUP BY fri.SALES_ORDER_ITEM_ID
					) package
						on package.SALES_ORDER_ITEM_ID = M.SALES_ORDER_ITEM_ID
					' + @filterCondition + N'
				) t
				WHERE RowID BETWEEN @startRowNumber AND @endRowNumber '
	--select @query
	DECLARE @queryParams nvarchar(max) = 
		N'@SALES_ORDER_ID	bigint
		,@RMA_ID			bigint
		,@startRowNumber	bigint
		,@endRowNumber		bigint'
	EXEC sp_executeSQL @query, @queryParams
		,@SALES_ORDER_ID = @SALES_ORDER_ID
		,@RMA_ID = @RMA_ID
		,@startRowNumber = @startRowNumber
		,@endRowNumber = @endRowNumber

	IF (@IS_DEBUG = 1)
	BEGIN
		PRINT(CAST(@queryParams AS NTEXT))
		PRINT(CAST(@query AS NTEXT))
	END
END


CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_UPDATED_USER]
(		
	@USER_ID	BIGINT
)
RETURNS @T_RESULT TABLE(
	   [UPDATED_USER_ID]		  BIGINT,
	   [UPDATED_TITLE]			  [NVARCHAR](50)  NULL,
	   [UPDATED_JOB_TITLE]		  [NVARCHAR](128) NULL,
	   [UPDATED_NAME]			  [VARCHAR] (32)  NOT NULL,
	   [UPDATED_FIRST_NAME]		  [VARCHAR](128)  NOT NULL,
	   [UPDATED_MIDDLE_INITIAL]	  [VARCHAR](8)    NULL,
	   [UPDATED_LAST_NAME]		  [VARCHAR](128)  NOT NULL,
	   [UPDATED_PRIMARY_EMAIL]	  [VARCHAR](128)  NULL,
	   [UPDATED_SECONDARY_EMAIL]  [NVARCHAR](128) NULL,
	   [UPDATED_PHONE_MAIN]		  [NVARCHAR](20)  NULL,
	   [UPDATED_PHONE_MOBILE]	  [NVARCHAR](20)  NULL,
	   [UPDATED_PHONE_HOME]		  [NVARCHAR](20)  NULL,
	   [UPDATED_PHONE_OTHER]	  [NVARCHAR](20)  NULL,
	   [UPDATED_FAX]			  [NVARCHAR](20)  NULL
    )
AS
BEGIN
    INSERT INTO @T_RESULT (
	    [UPDATED_USER_ID]
	   ,[UPDATED_TITLE]		
	   ,[UPDATED_JOB_TITLE]		
	   ,[UPDATED_NAME]
	   ,[UPDATED_FIRST_NAME]			
	   ,[UPDATED_MIDDLE_INITIAL]			
	   ,[UPDATED_LAST_NAME]		
	   ,[UPDATED_PRIMARY_EMAIL]			
	   ,[UPDATED_SECONDARY_EMAIL]
	   ,[UPDATED_PHONE_MAIN]		
	   ,[UPDATED_PHONE_MOBILE]	
	   ,[UPDATED_PHONE_HOME]	
	   ,[UPDATED_PHONE_OTHER]	
	   ,[UPDATED_FAX]
    )
    SELECT	   
	    U.UserID
	   ,U.[TITLE]		
	   ,U.[JOB_TITLE]
	   ,U.UserName		
	   ,U.[FirstName]		
	   ,U.[Initials]		
	   ,U.[LastName]		
	   ,U.[Email]		
	   ,U.[EMAIL_SECONDARY]	
	   ,U.[PHONE_MAIN]	
	   ,U.[PHONE_MOBILE]	
	   ,U.[PHONE_HOME]	
	   ,U.[PHONE_OTHER]	
	   ,U.[FAX]
    FROM tb_User	U   WITH(NOLOCK)
    WHERE U.UserID = @USER_ID
    RETURN			
END
CREATE TABLE [dbo].[C_NOTIFICATION_TYPE] (
    [NOTIFICATION_TYPE_ID] BIGINT        NOT NULL,
    [GROUP_TYPE_ID]        BIGINT        NOT NULL,
    [NAME]                 VARCHAR (256) NOT NULL,
    CONSTRAINT [PK_C_NOTIFICATION_TYPE] PRIMARY KEY CLUSTERED ([NOTIFICATION_TYPE_ID] ASC),
    CONSTRAINT [FK_C_NOTIFICATION_TYPE_C_NOTIFICATION_GROUP_TYPE] FOREIGN KEY ([GROUP_TYPE_ID]) REFERENCES [dbo].[C_NOTIFICATION_GROUP_TYPE] ([GROUP_TYPE_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Stores types of notifications', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_NOTIFICATION_TYPE';


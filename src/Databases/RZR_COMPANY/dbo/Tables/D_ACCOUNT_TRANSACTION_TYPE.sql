CREATE TABLE [dbo].[D_ACCOUNT_TRANSACTION_TYPE] (
    [ACCOUNT_TRANSACTION_TYPE_ID]   INT             NOT NULL,
    [ACCOUNT_TRANSACTION_TYPE_NAME] NVARCHAR (512)  NOT NULL,
    [ACCOUNT_TRANSACTION_TYPE_DESC] NVARCHAR (2000) NULL,
    [IS_INACTIVE]                   BIT             CONSTRAINT [DF_D_ACCOUNT_TRANSACTION_TYPE_IS_INACTIVE_1] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT             CONSTRAINT [DF_D_ACCOUNT_TRANSACTION_TYPE_IS_DELETED_1] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   VARCHAR (150)   CONSTRAINT [DF_D_ACCOUNT_TRANSACTION_TYPE_INSERTED_BY_1] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                   DATETIME        CONSTRAINT [DF_D_ACCOUNT_TRANSACTION_TYPE_INSERTED_DT_1] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                    VARCHAR (150)   NULL,
    [UPDATED_DT]                    DATETIME        NULL,
    [DELETED_BY]                    VARCHAR (150)   NULL,
    [DELETED_DT]                    DATETIME        NULL,
    CONSTRAINT [PK_D_ACCOUNT_TRANSACTION_TYPE_1] PRIMARY KEY CLUSTERED ([ACCOUNT_TRANSACTION_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is used for accounts system', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ACCOUNT_TRANSACTION_TYPE';


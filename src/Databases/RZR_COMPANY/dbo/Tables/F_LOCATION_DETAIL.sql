CREATE TABLE [dbo].[F_LOCATION_DETAIL] (
    [LOCATION_DETAIL_ID]                   BIGINT        IDENTITY (1, 1) NOT NULL,
    [WAREHOUSE_ID]                         BIGINT        NOT NULL,
    [INVENTORY_CAPABILITY_ID]              BIGINT        NOT NULL,
    [LOCATION_TYPE_ID]                     BIGINT        NOT NULL,
    [LOCATION_SYSTEM_NAME]                 NVARCHAR (250) NOT NULL,
    [LOCATION_ALIAS]                       NVARCHAR (250) NULL,
    [LOCATION_RACK_ID]                     BIGINT        NULL,
    [LOCATION_SECTION_ID]                  BIGINT        NULL,
    [LOCATION_LEVEL_ID]                    BIGINT        NULL,
    [LOCATION_SPACE_ID]                    BIGINT        NULL,
    [LOCATION_BIN_ID]                      BIGINT        NULL,
    [LOCATION_DETAIL_PREFIX_ID]            INT           NULL,
    [LOCATION_DETAIL_DATE]                 DATETIME      NULL,
    [LOCATION_PALLET_NO_ID]                BIGINT        NULL,
    [LOCATION_PROCESSING_LINE_NO_ID]       BIGINT        NULL,
    [LOCATION_SEPARATOR_ID]                INT           NULL,
    [LOCATION_DETAIL_DEPTH]                FLOAT (53)    NOT NULL,
    [LOCATION_DETAIL_WIDTH]                FLOAT (53)    NOT NULL,
    [LOCATION_DETAIL_HEIGHT]               FLOAT (53)    NOT NULL,
    [LOCATION_DETAIL_MAX_WEIGHT]           FLOAT (53)    NULL,
    [LOCATION_DETAIL_MIN_PRICE_VALUE]      MONEY         NULL,
    [LOCATION_DETAIL_MAX_PRICE_VALUE]      MONEY         NULL,
    [LOCATION_DETAIL_CAPACITY]             BIGINT        NULL,
    [LOCATION_ALLOWED_ITEM_ID]             INT           NULL,
    [LOCATION_DETAIL_TIED_ID]              BIGINT        NULL,
    [LOCATION_DETAIL_PRINT_NAME]           NVARCHAR (250) NULL,
    [LOCATION_DETAIL_NOT_SUGGESTED]        BIT           CONSTRAINT [DF_F_LOCATION_DETAIL_LOCATION_DETAIL_NOT_SUGGESTED] DEFAULT ((0)) NULL,
    [LOCATION_DETAIL_NOT_INCLUDED]         BIT           NULL,
    [LOCATION_DETAIL_REMOVE_PALLET_NUMBER] BIT           NULL,
    [RECYCLING_ITEM_MASTER_ID]             BIGINT        NULL,
    [IS_INACTIVE]                          BIT           CONSTRAINT [DF_F_LOCATION_DETAIL_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                           BIT           CONSTRAINT [DF_F_LOCATION_DETAIL_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                          VARCHAR (250) CONSTRAINT [DF_F_LOCATION_DETAIL_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                          DATETIME      CONSTRAINT [DF_F_LOCATION_DETAIL_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                           VARCHAR (250) NULL,
    [UPDATED_DT]                           DATETIME      NULL,
    [DELETED_BY]                           VARCHAR (250) NULL,
    [DELETED_DT]                           DATETIME      NULL,
    [LOCATION_DETAIL_MAX_WEIGHT_KG]        AS            ([LOCATION_DETAIL_MAX_WEIGHT]*(0.45359237)) PERSISTED,
	[DisableNesting]					   BIT           CONSTRAINT [DF_F_LOCATION_DETAIL_DisableNesting] DEFAULT ((0)) NOT NULL,
	[INSERTED_BY_USER_ID]				   BIGINT		 NULL,
    [INSERTED_BY_USER_IP]				   BIGINT		 NULL,
    [UPDATED_BY_USER_ID]				   BIGINT		 NULL,
    [UPDATED_BY_USER_IP]				   BIGINT		 NULL,
	[DELETED_BY_USER_ID]				   BIGINT		 NULL,
    [DELETED_BY_USER_IP]				   BIGINT		 NULL,
	[RestrictCapacity]					   BIT           CONSTRAINT [DF_F_LOCATION_DETAIL_RestrictCapacity] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_F_LOCATION_DETAIL] PRIMARY KEY CLUSTERED ([LOCATION_DETAIL_ID] ASC),
    CONSTRAINT [FK_F_LOCATION_DETAIL_C_LOCATION_ALLOWED_ITEM] FOREIGN KEY ([LOCATION_ALLOWED_ITEM_ID]) REFERENCES [dbo].[C_LOCATION_ALLOWED_ITEM] ([LOCATION_ALLOWED_ITEM_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_C_LOCATION_DETAIL_PREFIX] FOREIGN KEY ([LOCATION_DETAIL_PREFIX_ID]) REFERENCES [dbo].[D_LOCATION_DETAIL_PREFIX] ([LOCATION_DETAIL_PREFIX_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_C_LOCATION_SEPARATOR] FOREIGN KEY ([LOCATION_SEPARATOR_ID]) REFERENCES [dbo].[C_LOCATION_SEPARATOR] ([LOCATION_SEPARATOR_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_C_LOCATION_TYPE] FOREIGN KEY ([LOCATION_TYPE_ID]) REFERENCES [dbo].[C_LOCATION_TYPE] ([LOCATION_TYPE_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_BIN] FOREIGN KEY ([LOCATION_BIN_ID]) REFERENCES [dbo].[D_LOCATION_BIN] ([LOCATION_BIN_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_LEVEL] FOREIGN KEY ([LOCATION_LEVEL_ID]) REFERENCES [dbo].[D_LOCATION_LEVEL] ([LOCATION_LEVEL_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_PALLET_NO] FOREIGN KEY ([LOCATION_PALLET_NO_ID]) REFERENCES [dbo].[D_LOCATION_PALLET_NO] ([LOCATION_PALLET_NO_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_PROCESSING_LINE_NO] FOREIGN KEY ([LOCATION_PROCESSING_LINE_NO_ID]) REFERENCES [dbo].[D_LOCATION_PROCESSING_LINE_NO] ([LOCATION_PROCESSING_LINE_NO_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_RACK] FOREIGN KEY ([LOCATION_RACK_ID]) REFERENCES [dbo].[D_LOCATION_RACK] ([LOCATION_RACK_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_SECTION] FOREIGN KEY ([LOCATION_SECTION_ID]) REFERENCES [dbo].[D_LOCATION_SECTION] ([LOCATION_SECTION_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_LOCATION_SPACE] FOREIGN KEY ([LOCATION_SPACE_ID]) REFERENCES [dbo].[D_LOCATION_SPACE] ([LOCATION_SPACE_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_F_LOCATION] FOREIGN KEY ([INVENTORY_CAPABILITY_ID]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]),
    CONSTRAINT [FK_F_LOCATION_DETAIL_F_RECYCLING_ITEM_MASTER] FOREIGN KEY ([RECYCLING_ITEM_MASTER_ID]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
	CONSTRAINT [FK_F_LOCATION_DETAIL_tb_USER_INSERTED_BY_USER] FOREIGN KEY ([INSERTED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_LOCATION_DETAIL_tb_USER_UPDATED_BY_USER] FOREIGN KEY ([UPDATED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_LOCATION_DETAIL_tb_USER_DELETED_BY_USER] FOREIGN KEY ([DELETED_BY_USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [UNIQ__F_LOCATION_DETAIL__INVENTORY_CAPABILITY_ID] UNIQUE NONCLUSTERED ([INVENTORY_CAPABILITY_ID] ASC)
);





GO
CREATE NONCLUSTERED INDEX [IX_LOCATION_DETAIL_TIED_ID]
    ON [dbo].[F_LOCATION_DETAIL]([LOCATION_DETAIL_TIED_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_IS_INACTIVE_IS_DELETED]
    ON [dbo].[F_LOCATION_DETAIL]([IS_INACTIVE] ASC, [IS_DELETED] ASC)
	INCLUDE ([INVENTORY_CAPABILITY_ID],[LOCATION_SYSTEM_NAME],[LOCATION_DETAIL_NOT_INCLUDED]);

GO
CREATE NONCLUSTERED INDEX [IX__F_LOCATION_DETAIL__PLACING]
    ON [dbo].[F_LOCATION_DETAIL]([WAREHOUSE_ID] ASC, [LOCATION_PALLET_NO_ID] ASC, [LOCATION_PROCESSING_LINE_NO_ID] ASC, [LOCATION_RACK_ID] ASC, [LOCATION_SECTION_ID] ASC, [LOCATION_LEVEL_ID] ASC, [LOCATION_SPACE_ID] ASC, [LOCATION_BIN_ID] ASC);

GO
CREATE NONCLUSTERED INDEX [IX_WAREHOUSE_ID_LOCATION_TYPE_ID_IS_INACTIVE_IS_DELETED]
    ON [dbo].[F_LOCATION_DETAIL]([WAREHOUSE_ID] ASC, [LOCATION_TYPE_ID] ASC, [IS_INACTIVE] ASC, [IS_DELETED] ASC)
    INCLUDE([LOCATION_DETAIL_ID], [LOCATION_DETAIL_PRINT_NAME], [LOCATION_SYSTEM_NAME], [INVENTORY_CAPABILITY_ID], [LOCATION_DETAIL_DEPTH], [LOCATION_DETAIL_WIDTH], [LOCATION_DETAIL_HEIGHT], [LOCATION_DETAIL_MAX_WEIGHT], [LOCATION_DETAIL_CAPACITY], [LOCATION_ALLOWED_ITEM_ID], [LOCATION_DETAIL_NOT_SUGGESTED], LOCATION_ALIAS);
GO

CREATE NONCLUSTERED INDEX [IX_INVENTORY_CAPABILITY_ID]
    ON [dbo].[F_LOCATION_DETAIL]([INVENTORY_CAPABILITY_ID] ASC)
    INCLUDE([LOCATION_DETAIL_ID], [LOCATION_DETAIL_TIED_ID], LOCATION_DETAIL_NOT_INCLUDED, LOCATION_TYPE_ID, LOCATION_DETAIL_DEPTH, LOCATION_DETAIL_WIDTH, LOCATION_DETAIL_HEIGHT,LOCATION_DETAIL_CAPACITY, LOCATION_ALLOWED_ITEM_ID, LOCATION_DETAIL_NOT_SUGGESTED, LOCATION_ALIAS);

GO
CREATE NONCLUSTERED INDEX [IX_LOCATION_TYPE_ID_IS_INACTIVE_IS_DELETED]
    ON [dbo].[F_LOCATION_DETAIL]([LOCATION_TYPE_ID],[IS_INACTIVE],[IS_DELETED])
INCLUDE ([LOCATION_DETAIL_ID],[LOCATION_SYSTEM_NAME],[LOCATION_DETAIL_PRINT_NAME])


GO
CREATE NONCLUSTERED INDEX [idx_F_LOCATION_DETAIL_INVENTORY_CAPABILITY]
    ON [dbo].[F_LOCATION_DETAIL]([INVENTORY_CAPABILITY_ID] ASC)
    INCLUDE([LOCATION_DETAIL_ID], [LOCATION_SYSTEM_NAME]);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The locations details tied 1 to 1 with F_LOCATION', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_LOCATION_DETAIL';

GO
CREATE TRIGGER [dbo].[trg_LOCATION_DETAIL_AFTER_UPDATE]
	ON [dbo].[F_LOCATION_DETAIL]
	AFTER UPDATE, INSERT
AS
BEGIN

	DECLARE @PROCESS_CD NVARCHAR(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + N'.', N'') + OBJECT_NAME(@@PROCID);

	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	);

	INSERT INTO @logData
	SELECT
		@PROCESS_CD,
		NEW.[USER_ID],
		NEW.[USER_IP],
		NEW.[OPERATION_NAME],
		25, -- Location
		NEW.[ENTITY_KEY_VALUE],
		NEW.[ENTITY_AUTO_NAME],
		[dbo].[fn_str_STRIP_XML_TAGS]((
			SELECT new.[ParentLocation]
			FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT
		)) AS [CHANGES]
	FROM (
		SELECT -- Fixed columns
			CASE WHEN D.[LOCATION_DETAIL_ID] IS NULL
				THEN N'Inserted'
				WHEN isnull(I.[LOCATION_DETAIL_TIED_ID], 0) <> isnull(D.[LOCATION_DETAIL_TIED_ID], 0)
				THEN 'Moved'
				ELSE 'Updated'
			END AS [OPERATION_NAME],

			ISNULL(I.[UPDATED_BY_USER_ID], I.[INSERTED_BY_USER_ID])			AS [USER_ID],
			ISNULL(I.[UPDATED_BY_USER_IP], I.[INSERTED_BY_USER_IP])			AS [USER_IP],
			ISNULL(I.[INVENTORY_CAPABILITY_ID], D.[INVENTORY_CAPABILITY_ID])									AS [ENTITY_KEY_VALUE],
			COALESCE(LD.[LOCATION_NAME], D.[LOCATION_SYSTEM_NAME], LI.[LOCATION_NAME], I.[LOCATION_SYSTEM_NAME])	AS [ENTITY_AUTO_NAME],
			-- XML columns
			CASE
				WHEN ISNULL(I.[LOCATION_DETAIL_TIED_ID], 0) <> ISNULL(D.[LOCATION_DETAIL_TIED_ID], 0)
				THEN N'Parent location was "' + ISNULL(PLD.[LOCATION_NAME], N'') + N'" is "' + ISNULL(PLI.[LOCATION_NAME], N'') + N'"'
				ELSE NULL
			END																AS [ParentLocation]
		FROM INSERTED								AS I
		LEFT JOIN DELETED							AS D
			ON I.[LOCATION_DETAIL_ID] = D.[LOCATION_DETAIL_ID]
		LEFT JOIN [dbo].[F_LOCATION]				AS LD
			ON LD.LOCATION_ID = D.INVENTORY_CAPABILITY_ID
		LEFT JOIN [dbo].[F_LOCATION]				AS LI
			ON LI.LOCATION_ID = I.INVENTORY_CAPABILITY_ID
		LEFT JOIN [dbo].[vw_LocationDetails]		AS PLD
			ON PLD.[LOCATION_DETAIL_ID] = D.[LOCATION_DETAIL_TIED_ID]
		LEFT JOIN [dbo].[vw_LocationDetails]		AS PLI
			ON PLI.[LOCATION_DETAIL_ID] = I.[LOCATION_DETAIL_TIED_ID]
		WHERE (
			ISNULL(I.[LOCATION_DETAIL_TIED_ID], 0) <> ISNULL(D.[LOCATION_DETAIL_TIED_ID], 0)
		)
	) AS NEW;

	INSERT INTO [dbo].[F_LOG_DATA] WITH(ROWLOCK)
	(
		[SOURCE],
		[USER_ID],
		[USER_IP],
		[OPERATION_NAME],
		[ENTITY_TYPE_ID],
		[ENTITY_KEY_VALUE],
		[ENTITY_AUTO_NAME],
		[CHANGES]
	)
	SELECT
		[SOURCE],
		[USER_ID],
		[USER_IP],
		[OPERATION_NAME],
		[ENTITY_TYPE_ID],
		[ENTITY_KEY_VALUE],
		[ENTITY_AUTO_NAME],
		ISNULL([CHANGES], N'')
	FROM @logData;

	
	declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.INVENTORY_CAPABILITY_ID, d.INVENTORY_CAPABILITY_ID)
	from inserted		i
	full join deleted	d
		on i.LOCATION_DETAIL_ID = d.LOCATION_DETAIL_ID
	where isnull(i.LOCATION_DETAIL_TIED_ID, 0) <> isnull(d.LOCATION_DETAIL_TIED_ID, 0)
	-- assuming that it is fast enough to update the records in elastic each time
	
	insert into @ids
    select FLD_CHILD.INVENTORY_CAPABILITY_ID
	from inserted i
	full join deleted d
	    on i.LOCATION_DETAIL_ID = d.LOCATION_DETAIL_ID
	inner join dbo.F_LOCATION_DETAIL FLD_CHILD with(nolock)
	    on isnull(i.LOCATION_DETAIL_ID, d.LOCATION_DETAIL_ID) = FLD_CHILD.LOCATION_DETAIL_TIED_ID
	where isnull(i.LOCATION_DETAIL_TIED_ID, 0) <> isnull(d.LOCATION_DETAIL_TIED_ID, 0)
	
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 6 --Location
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO

-- =============================================
-- Author:		V<PERSON> <PERSON><PERSON><PERSON><PERSON>
-- Create date: 1/15/2014
-- Description:	get search. Also used by Client Portal
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ITEM_SEARCH] 
--DECLARE
	@ORDER_COLUMN_NAME	NVARCHAR(250)	= N'ITEM_ID',
	@ORDER_DIRECTION	NVARCHAR(10)	= N'ASC',
	@ITEMS_PER_PAGE		INT				= 25,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		NVARCHAR(MAX)	= NULL,
	@IS_GET_ALL_COLUMNS BIT				= 0,
	@C_IS_DEBUG			BIT				= 0,
	@ITEM_NUMBER_IDS	nvarchar_LABEL_ARRAY READONLY,
	@WAREHOUSE_IDS		bigint_ID_ARRAY READONLY
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		
	
	DECLARE @item_number_filter NVARCHAR(MAX) = N' WHERE 1=1',
			@warehouse_filter	NVARCHAR(MAX) = N'',
			@post_query			NVARCHAR(MAX) = N'';

	-- SET TO "0" IF THE QUERY RUNS TOO LONG
	DECLARE @IS_LOCATION_AND_CUSTOMER_GROUPING BIT = ISNULL((SELECT TOP(1) USS.IS_EXTRA_COLUMNS_FOR_UNALLOCATED FROM U_SYSTEM_SETTINGS USS WITH(NOLOCK)), 0)		

	DECLARE @GradeId INT = ISNULL((
	SELECT TOP 1
		[INVENTORY_CAPABILITY_TYPE_ID]
	FROM [dbo].[C_INVENTORY_CAPABILITY_TYPE] WITH (NOLOCK)
	WHERE [INVENTORY_CAPABILITY_VALUE] = 'GRADE'
	AND IS_DELETED = 0), 0);
	

	DECLARE @GROUPING NVARCHAR(MAX) = CASE
		WHEN @IS_GET_ALL_COLUMNS = 0 THEN N'
		FROM 
		(
			SELECT
				MAX(FIID.Id) AS ITEM_INVENTORY_ID
				,FIID.[ItemMasterId] as ITEM_MASTER_ID								
				,FIID.[CustomerId] as CUSTOMER_ID
				,FIID.[LocationId] as LOCATION_ID																	
				,SUM(FIID.Qty) AS QUANTITY
			FROM [dbo].[vw_InventoryDetailsMain]		FIID WITH (NOLOCK)	
			WHERE FIID.[IsAvailable] = 1				  			 
			GROUP BY 
				FIID.ItemMasterId
				,FIID.[ConditionId]
				,FIID.CustomerId
				,FIID.LocationId
		) fii
		'
		WHEN @IS_LOCATION_AND_CUSTOMER_GROUPING = 0 THEN N'
		FROM
		(		
			SELECT
				MAX(FII.ITEM_INVENTORY_ID) AS ITEM_INVENTORY_ID
				,FII.ITEM_ID								
				,FII.ProductCodeIdHeci as PRODUCT_CODE_ID_HECI
				,pc.[PRODUCT_CODE_VALUE] as PRODUCT_CODE_CD_HECI
				,rev.INVENTORY_CAPABILITY_VALUE as    ITEM_INVENTORY_REVISION
				,rev.INVENTORY_CAPABILITY_ID as ITEM_INVENTORY_REVISION_ID
				,SUM(FII.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED) AS QUANTITY
			FROM dbo.F_ITEM_INVENTORY AS FII WITH (NOLOCK)	
			left join [dbo].[F_PRODUCT_CODE] pc with (nolock)
				on fii.[ProductCodeIdHeci] = pc.[PRODUCT_CODE_ID]
			left join [dbo].[F_SALES_ORDER_ITEM] soi with (nolock)
                on FII.SalesOrderItemId = soi.SALES_ORDER_ITEM_ID
            LEFT JOIN dbo.F_ORDER_ORDER_SUBJECT_TYPE    OOST    WITH (NOLOCK)
                ON  OOST.ENTITY_TYPE_ID = 1 AND soi.SALES_ORDER_ID = OOST.ORDER_ID 
			left join [dbo].[D_INVENTORY_CAPABILITY] rev with (nolock)
				on fii.RevisionId = rev.INVENTORY_CAPABILITY_ID		           			       
			WHERE  fii.is_deleted = 0 and CASE
						WHEN  FII.ITEM_STATUS_ID IN (1) 
							AND (FII.AUDIT_STATUS_ID IN (0, 1, 2))
					                    and (FII.SalesOrderItemId IS NULL OR OOST.ENTITY_SUBJECT_TYPE_ID = 12) THEN 1 --12 is repair sales order
                        ELSE 0                    
					END     = 1
			GROUP BY 
				 FII.ITEM_ID
				,FII.ProductCodeIdHeci
				,pc.[PRODUCT_CODE_VALUE]
				,rev.INVENTORY_CAPABILITY_VALUE
				,rev.INVENTORY_CAPABILITY_ID
		) fii				
		'
		ELSE N'
		FROM
		(
			SELECT
			     MAX(fii.ITEM_INVENTORY_ID) AS ITEM_INVENTORY_ID
				,fii.ITEM_ID								
				,fii.CUSTOMER_ID
				,fii.LOCATION_ID																		
				,fii.ProductCodeIdHeci as PRODUCT_CODE_ID_HECI
				,pc.[PRODUCT_CODE_VALUE] as PRODUCT_CODE_CD_HECI
				,rev.INVENTORY_CAPABILITY_VALUE as ITEM_INVENTORY_REVISION
				,rev.INVENTORY_CAPABILITY_ID as ITEM_INVENTORY_REVISION_ID
				,SUM(fii.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED) AS QUANTITY
			FROM dbo.F_ITEM_INVENTORY		fii WITH (NOLOCK)	
			left join [dbo].[F_PRODUCT_CODE] pc with (nolock)
				on fii.[ProductCodeIdHeci] = pc.[PRODUCT_CODE_ID]
			left join [dbo].[F_SALES_ORDER_ITEM] soi with (nolock)
                on FII.SalesOrderItemId = soi.SALES_ORDER_ITEM_ID
            LEFT JOIN dbo.F_ORDER_ORDER_SUBJECT_TYPE    OOST    WITH (NOLOCK)
                ON  OOST.ENTITY_TYPE_ID = 1 AND soi.SALES_ORDER_ID = OOST.ORDER_ID            
			left join [dbo].[D_INVENTORY_CAPABILITY] rev with (nolock)
				on fii.RevisionId = rev.INVENTORY_CAPABILITY_ID		     
			WHERE fii.is_deleted = 0 and CASE
						WHEN  fii.ITEM_STATUS_ID IN (1) 
							AND (fii.AUDIT_STATUS_ID IN (0, 1, 2))
					                    and (fii.SalesOrderItemId IS NULL OR OOST.ENTITY_SUBJECT_TYPE_ID = 12) THEN 1 --12 is repair sales order
                        ELSE 0                    
					END     = 1
			GROUP BY 
				 fii.ITEM_ID
				,fii.CUSTOMER_ID
				,fii.LOCATION_ID
				,fii.ProductCodeIdHeci
				,pc.[PRODUCT_CODE_VALUE]
				,rev.INVENTORY_CAPABILITY_VALUE
				,rev.INVENTORY_CAPABILITY_ID
		) fii							
		'
	END

	DECLARE @SELECT_ADDITIONAL_SECTION NVARCHAR(MAX) = CASE
		WHEN @IS_LOCATION_AND_CUSTOMER_GROUPING = 0 AND @IS_GET_ALL_COLUMNS = 1 THEN N'
				NULL	AS CUSTOMER_ID,
				NULL	AS CUSTOMER_NAME,
				NULL	AS LOCATION_ID,
				NULL	AS LOCATION_NAME,
				NULL	AS WAREHOUSE_ID,
				NULL	AS WAREHOUSE_CD
		'
		ELSE N'
				FII.CUSTOMER_ID,
				FII2.[CVCode] as CUSTOMER_NAME,
				FII.LOCATION_ID,
				l.LOCATION_NAME,
				l.WAREHOUSE_ID,
				w.WAREHOUSE_CD
		'
	END

	IF(EXISTS(SELECT TOP(1) 1 FROM @ITEM_NUMBER_IDS)) BEGIN
		SET @post_query = @post_query + N'
		DROP TABLE #ITEM_NUMBER_IDS
		'
 
		SELECT *
		INTO #ITEM_NUMBER_IDS
		FROM @ITEM_NUMBER_IDS

		SET @item_number_filter = N' WHERE FII2.[MasterItemNumber] IN (SELECT LABEL FROM #ITEM_NUMBER_IDS)'
	END	

	IF((@IS_LOCATION_AND_CUSTOMER_GROUPING = 1 OR @IS_GET_ALL_COLUMNS = 0) AND EXISTS(SELECT TOP(1) 1 FROM @WAREHOUSE_IDS)) BEGIN
		SET @post_query = @post_query + N'
		DROP TABLE #WAREHOUSE_IDS
		'

		SELECT *
		INTO #WAREHOUSE_IDS
		FROM @WAREHOUSE_IDS

		SET @warehouse_filter = N' AND l.WAREHOUSE_ID IN (SELECT ID FROM #WAREHOUSE_IDS)'
	END

	DECLARE @query NVARCHAR (MAX) = CASE WHEN @IS_GET_ALL_COLUMNS = 1 THEN '
		
		SELECT
			 FII.ITEM_ID
			,FII2.[MasterItemNumber] as ITEM_NUMBER
			,FII2.[ItemMasterId] as ITEM_MASTER_ID
			,FII2.[MFGCd] as MANUFACTURER_CD
			,FII2.[MasterItemTitle] as ITEM_TITLE
			,FII2.[Condition] as ITEM_CONDITION_CD
			,FII2.[ConditionId] as ITEM_CONDITION_ID
			,fii.QUANTITY
			,FII.PRODUCT_CODE_ID_HECI				AS PRODUCT_CODE_ID
			,FII.PRODUCT_CODE_CD_HECI				AS PRODUCT_CODE_VALUE
			,fii.ITEM_INVENTORY_REVISION_ID			AS REVISION_ID
			,fii.ITEM_INVENTORY_REVISION			AS ITEM_INVENTORY_REVISION
			,fimsm.ITEM_MASTER_SALES_PRICE_AVG		AS SALES_PRICE_AVG
			,FISAV.INVENTORY_CAPABILITY_VALUE		AS GradeLevel
			,' + @SELECT_ADDITIONAL_SECTION +  N'
		INTO #T_ADVANCED_SEARCH_SRC
		--FROM dbo.F_ITEM							FI	WITH (NOLOCK)		
		'+ @GROUPING +N' 			
		INNER JOIN [dbo].[vw_InventoryDetails]		FII2 WITH (NOLOCK)	
			ON FII.ITEM_INVENTORY_ID = FII2.Id	
		left join 	[dbo].[F_LOCATION]	l with (nolock)
			on FII2.LocationId = l.LOCATION_ID
		left join [dbo].[D_WAREHOUSE] w with (nolock)
			on l.WAREHOUSE_ID = w.WAREHOUSE_ID
		LEFT OUTER JOIN F_ITEM_MASTER_SALES_MEASURE			fimsm		WITH (NOLOCK)
			ON FII2.[ItemMasterId] = fimsm.ITEM_MASTER_ID
		LEFT JOIN [dbo].[F_ITEM]					AS FI	WITH (NOLOCK)
			ON FII.[ITEM_ID] = FI.[ITEM_ID]
		LEFT JOIN (
			SELECT TOP 100 PERCENT
				ITEM_MASTER_ID,
				INVENTORY_CAPABILITY_VALUE,
				ITEM_MASTER_SKU_ATTRB_ID
			FROM F_ITEM_SKU_ATTRB_VALUE		WITH(NOLOCK)
			WHERE INVENTORY_CAPABILITY_TYPE_ID = @GradeId
			GROUP BY 
				ITEM_MASTER_ID,
				INVENTORY_CAPABILITY_VALUE,
				ITEM_MASTER_SKU_ATTRB_ID
			ORDER BY
				ITEM_MASTER_ID,
				INVENTORY_CAPABILITY_VALUE,
				ITEM_MASTER_SKU_ATTRB_ID
		) AS FISAV
			ON FISAV.ITEM_MASTER_SKU_ATTRB_ID = FI.ITEM_MASTER_SKU_ATTRB_ID
		' + @item_number_filter + '
		' + @warehouse_filter + '
	'
	ELSE '
		SELECT
			 NULL									AS ITEM_ID
			,FII2.[MasterItemNumber] as ITEM_NUMBER
			,FII.ITEM_MASTER_ID
			,FII2.[MFGCd] as MANUFACTURER_CD
			,FII2.[MasterItemTitle] as ITEM_TITLE
			,FII2.[Condition] as ITEM_CONDITION_CD
			,FII2.[ConditionId] as ITEM_CONDITION_ID
			,FII.QUANTITY
			,NULL									AS PRODUCT_CODE_ID
			,NULL									AS PRODUCT_CODE_VALUE
			,NULL									AS REVISION_ID
			,NULL									AS ITEM_INVENTORY_REVISION
			,fimsm.ITEM_MASTER_SALES_PRICE_AVG		AS SALES_PRICE_AVG
			,NULL									AS GradeLevel
			,' + @SELECT_ADDITIONAL_SECTION +  N'
		INTO #T_ADVANCED_SEARCH_SRC
		'+ @GROUPING +N'				
		INNER JOIN [dbo].[vw_InventoryDetails]		FII2 WITH (NOLOCK)	
			ON FII.ITEM_INVENTORY_ID = FII2.Id	
		left join 	[dbo].[F_LOCATION]	l with (nolock)
			on FII2.LocationId = l.LOCATION_ID
		left join [dbo].[D_WAREHOUSE] w with (nolock)
			on l.WAREHOUSE_ID = w.WAREHOUSE_ID
		LEFT OUTER JOIN F_ITEM_MASTER_SALES_MEASURE			fimsm		WITH (NOLOCK)
			ON FII.ITEM_MASTER_ID = fimsm.ITEM_MASTER_ID					
		' + @item_number_filter + '
		' + @warehouse_filter + '
	'
	END

	DECLARE @query2 NVARCHAR (MAX) = '
			
		SELECT TOP(1)
			CAST(COUNT (' + CASE WHEN @IS_GET_ALL_COLUMNS = 1 THEN 'ITEM_ID' ELSE 'ITEM_MASTER_ID' END + ') AS BIGINT) AS SkuId,
			NULL			AS ItemNumber,
			0				AS MasterItemId,
			NULL			AS Mfg,	
			NULL			AS SkuTitle,
			NULL			AS ConditionCd,
			0				AS ConditionId,
			SUM (QUANTITY)	AS QtyOnHand,
			NULL			AS CodeId,
			NULL			AS Heci,
			NULL			AS RevisionId,
			NULL			AS Revision,
			0				AS SalesPriceAvg,
			NULL			AS GradeLevel,
			NULL			AS CustomerId,
			NULL			AS CustomerName,
			NULL			AS LocationId,
			NULL			AS LocationName,
			NULL			AS WarehouseId,
			NULL			AS WarehouseName,
			0				AS Qty,
			NULL			AS Price
		FROM #T_ADVANCED_SEARCH_SRC ' + @filterCondition + '
		UNION ALL
		SELECT
			t.ITEM_ID					AS SkuId,
			t.ITEM_NUMBER				AS ItemNumber,
			t.ITEM_MASTER_ID			AS MasterItemId,
			t.MANUFACTURER_CD			AS Mfg,
			t.ITEM_TITLE				AS SkuTitle,
			t.ITEM_CONDITION_CD			AS ConditionCd,
			t.ITEM_CONDITION_ID			AS ConditionId,
			t.QUANTITY					AS QtyOnHand,
			t.PRODUCT_CODE_ID			AS CodeId,
			t.PRODUCT_CODE_VALUE		AS Heci,
			t.REVISION_ID				AS RevisionId,
			t.ITEM_INVENTORY_REVISION   AS Revision,
			t.SALES_PRICE_AVG			AS SalesPriceAvg,
			t.GradeLevel				AS GradeLevel,
			t.CUSTOMER_ID				AS CustomerId,
			t.CUSTOMER_NAME				AS CustomerName,
			t.LOCATION_ID				AS LocationId,
			t.LOCATION_NAME				AS LocationName,
			t.WAREHOUSE_ID				AS WarehouseId,
			t.WAREHOUSE_CD				AS WarehouseName,
			0							AS Qty,
			NULL						AS Price
		FROM (SELECT TOP(@endRowNumber)
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				* 
			FROM #T_ADVANCED_SEARCH_SRC	' + @filterCondition + ') t
		WHERE RowID BETWEEN @startRowNumber AND @endRowNumber
	
		DROP TABLE #T_ADVANCED_SEARCH_SRC
	'

	SET @query = @query + @query2 + @post_query;

	IF (@C_IS_DEBUG = 1)
		SELECT @query AS ItemNumber
	
	EXEC sp_executesql @query,
			N'@startRowNumber	bigint
			, @endRowNumber		bigint
			, @GradeId          int '
			,@startRowNumber = @startRowNumber
			,@endRowNumber   = @endRowNumber
			,@GradeId        = @GradeId;
		
END

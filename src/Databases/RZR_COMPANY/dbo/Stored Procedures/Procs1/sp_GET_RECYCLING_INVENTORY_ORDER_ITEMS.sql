-- EXEC [dbo].[sp_GET_RECYCLING_INVENTORY_ORDER_ITEMS] @C_IS_DEBUG = 1
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INVENTORY_ORDER_ITEMS]
--DECLARE
	 @RECYCLING_ORDER_IDS	bigint_ID_ARRAY		READONLY
	,@CUSTOMER_IDS			bigint_ID_ARRAY		READONLY
	,@LOT_IDS				bigint_ID_ARRAY		READONLY--'<Root><Items ID="363" /><Items ID="362" /></Root>'
	,@COMMODITY_IDS			bigint_ID_ARRAY		READONLY
	,@REP_IDS				bigint_ID_ARRAY		READONLY
	,@START_DATE			DATETIME			= NULL
	,@END_DATE				DATETIME			= NULL
	,@WORKFLOW_STEP_ID		INT					= ''
	,@IS_MERGE				BIT					= 0
	,@WAREHOUSE_IDS			bigint_ID_ARRAY		READONLY
	,@ORDER_COLUMN_NAME		VARCHAR(250)		= N'RECYCLING_ORDER_ITEM_ID'
	,@ORDER_DIRECTION		VARCHAR(10)			= N'ASC'
	,@ITEMS_PER_PAGE		INT					= 20
	,@PAGE_INDEX			INT					= 0
	,@QUICK_FILTER			VARCHAR(MAX)		= ''
	,@FILTER_WHERE			VARCHAR(MAX)		= ''
	,@C_IS_DEBUG			BIT					= 0
AS
BEGIN
	DECLARE @filterCondition			VARCHAR(MAX) = NULLIF(NULLIF(@FILTER_WHERE,''),'null');
	DECLARE @quickFilter				VARCHAR(MAX) = NULLIF(NULLIF(@QUICK_FILTER,''),'null');
	DECLARE @internalFilterCondition	VARCHAR(MAX) ;
	DECLARE @idoc						INT;	
	DECLARE @joins						NVARCHAR(MAX) = N'';
	DECLARE @lotsWhere					NVARCHAR(MAX) = N'';
	DECLARE @warehouseWhereCondition	NVARCHAR(MAX) = N'';

	IF (EXISTS(SELECT ID FROM @WAREHOUSE_IDS))
	BEGIN 
		SET @warehouseWhereCondition = N' OR COALESCE(ROT.WAREHOUSE_ID, loc.WAREHOUSE_ID, RO.WAREHOUSE_ID) IN (SELECT ID FROM @WAREHOUSE_IDS) ';
	END
	IF (EXISTS(SELECT ID FROM @LOT_IDS))
	BEGIN
		SET @internalFilterCondition = ISNULL(@internalFilterCondition+' AND ','') + ' froi.RECYCLING_ORDER_ITEM_ID IN (SELECT ID FROM @LOT_IDS) ' ;
		SET @lotsWhere = N' OR froi.RECYCLING_ORDER_ITEM_ID IN (SELECT ID FROM @LOT_IDS) ';
	END
	
	DECLARE @repsWhere VARCHAR(MAX) = N'';
	IF (EXISTS(SELECT ID FROM @REP_IDS))
	BEGIN
		SET @repsWhere = N' C.REP_ID in (SELECT ID FROM @REP_IDS) AND RO.USER_ID IN (SELECT ID FROM @REP_IDS) ';
		SET @internalFilterCondition = ISNULL(@internalFilterCondition+' AND ','') + @repsWhere ;
	END

	DECLARE @customersWhere VARCHAR(MAX) = N'';
	IF (EXISTS(SELECT ID FROM @CUSTOMER_IDS))
	BEGIN
		SET @customersWhere = N' RO.CUSTOMER_ID IN (SELECT ID FROM @CUSTOMER_IDS) ';
		SET @internalFilterCondition = ISNULL(@internalFilterCondition+' AND ','') + @customersWhere ;
	END
	DECLARE @rosWhere VARCHAR(MAX) = N'';
	IF (EXISTS(SELECT ID FROM @RECYCLING_ORDER_IDS))
	BEGIN
		SET @rosWhere = N' froi.RECYCLING_ORDER_ID IN (SELECT ID FROM @RECYCLING_ORDER_IDS) ';
		SET @internalFilterCondition = ISNULL(@internalFilterCondition+' AND ','') + @rosWhere ;
	END
	DECLARE @commoditiesWhere VARCHAR(MAX) = N'';
	IF (EXISTS(SELECT ID FROM @COMMODITY_IDS))
	BEGIN
		SET @commoditiesWhere = N' froi.RECYCLING_ITEM_MASTER_ID IN (SELECT ID FROM @COMMODITY_IDS) ';
		SET @internalFilterCondition = ISNULL(@internalFilterCondition+' AND ','') + @commoditiesWhere ;
	END

	DECLARE @START_DATE_IS_NULL BIT;
	DECLARE @END_DATE_IS_NULL BIT;
	
	IF(@START_DATE IS NULL)
	   SET @START_DATE_IS_NULL = 1
	ELSE
	   SET @START_DATE_IS_NULL = 0

	IF(@END_DATE IS NULL)
	   SET @END_DATE_IS_NULL = 1
	ELSE
	   SET @END_DATE_IS_NULL = 0
	

	IF (@filterCondition IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @filterCondition +N')';
	END			
	SET @filterCondition = ISNULL(@filterCondition,N'WHERE (1=1)');

	
	IF (@quickFilter IS NOT NULL)
	BEGIN
		DECLARE @likeQuickFilter VARCHAR(MAX) = N' LIKE N''%' + @quickFilter + N'%''';
		SET @quickFilter = N' AND (T.ITEM_AUTO_NAME' + @likeQuickFilter
			+ N' OR T.AUTO_NAME' + @likeQuickFilter
			+ N' OR T.ORDER_STATUS_CD' + @likeQuickFilter
			+ N' OR T.CUSTOMER_NAME' + @likeQuickFilter
			+ N' OR T.ITEM_TYPE_CD' + @likeQuickFilter
			+ N' OR T.WORKFLOW_TYPE_CD' + @likeQuickFilter
			+ N' OR T.PACKAGING_TYPE_CD' + @likeQuickFilter
			+ N' OR T.LOCATION_CD' + @likeQuickFilter
			+ N' OR T.NOTES' + @likeQuickFilter
			+ N')';
	END			
	SET @quickFilter = ISNULL(@quickFilter,N'');


	IF (@internalFilterCondition IS NOT NULL)
	BEGIN
		SET @internalFilterCondition = N' AND ('+ @internalFilterCondition +N')';
	END			
	SET @internalFilterCondition = ISNULL(@internalFilterCondition,N'');
	
	----ITAD STEP additional info----

	DECLARE @ITAD_WORKFLOW_SET_ID BIGINT = (SELECT TOP(1) WORKFLOW_TYPE_ID FROM C_RECYCLING_WORKFLOW_TYPE WHERE WORKFLOW_SYSTEM_ID = 7)

	DECLARE @ITAD_ITEMS_COUNT_JOINS_BLOCK NVARCHAR(MAX) = CASE
		WHEN (@WORKFLOW_STEP_ID = @ITAD_WORKFLOW_SET_ID) THEN N'
		LEFT JOIN 
		(
			SELECT
				TC_FII.RECYCLING_ORDER_ITEM_ID			AS RECYCLING_ORDER_ITEM_ID
				,COUNT(TC_FII.ITEM_INVENTORY_ID)		AS TOTAL_ITEMS_COUNT
			FROM F_ITEM_INVENTORY		TC_FII WITH(NOLOCK)
			WHERE RECYCLING_ORDER_ITEM_ID IS NOT NULL
				AND TC_FII.IS_DELETED = 0 
			GROUP BY TC_FII.RECYCLING_ORDER_ITEM_ID
		) TC
			ON froi.RECYCLING_ORDER_ITEM_ID = TC.RECYCLING_ORDER_ITEM_ID
		LEFT JOIN
		(
			SELECT
				CC_FII.RECYCLING_ORDER_ITEM_ID			AS RECYCLING_ORDER_ITEM_ID
				,COUNT(CC_FII.ITEM_INVENTORY_ID)		AS COMPLETED_ITEMS_COUNT
			FROM [dbo].[F_ITEM_INVENTORY_ASSET_STATE]	CC_IIAS WITH (nolock)
			INNER JOIN F_ITEM_INVENTORY					CC_FII	WITH (nolock)
				ON CC_FII.RECYCLING_ORDER_ITEM_ID IS NOT NULL 
				AND CC_IIAS.ITEM_INVENTORY_ID = CC_FII.ITEM_INVENTORY_ID
			WHERE CC_IIAS.[ASSET_STATE_ID] IN (8, 9) 
				AND CC_IIAS.[IS_CURRENT] = 1 
				AND CC_FII.IS_DELETED = 0 
			GROUP BY CC_FII.RECYCLING_ORDER_ITEM_ID
		) CC
			ON froi.RECYCLING_ORDER_ITEM_ID = CC.RECYCLING_ORDER_ITEM_ID
		'
		ELSE N''
	END

	DECLARE @ITAD_ITEMS_COUNT_SELECT_BLOCK NVARCHAR(MAX) = CASE
		WHEN (@WORKFLOW_STEP_ID = @ITAD_WORKFLOW_SET_ID) 
		THEN N'
		,TC.TOTAL_ITEMS_COUNT						AS TOTAL_ITEMS_COUNT
		,CC.COMPLETED_ITEMS_COUNT					AS COMPLETED_ITEMS_COUNT'
		ELSE N''
	END

	DECLARE @ITAD_ITEMS_COUNT_SELECT_BLOCK_2 NVARCHAR(MAX) = CASE 
		WHEN (@WORKFLOW_STEP_ID = @ITAD_WORKFLOW_SET_ID) THEN N'
		,t.TOTAL_ITEMS_COUNT							AS TotalItemsCount
		,t.COMPLETED_ITEMS_COUNT						AS CompletedItemsCount'
		ELSE N'
		,0 AS TotalItemsCount
		,0 AS CompletedItemsCount'
	END

	--------------------------------
	DECLARE @PRODUCTION_STATUS_0 NVARCHAR(MAX) = N'
				,IIF(EXISTS(select top(1) 1
							from		[recycling].[F_CommodityRule]				as cr	with(nolock)
							inner join	[recycling].[F_CommodityRuleRequirement]	as crr	with(nolock)
								on cr.[Id] = crr.[CommodityRuleId]
							where cr.[RecyclingOrderId] = T.RECYCLING_ORDER_ID
								and cr.[CommodityId] = T.ITEM_TYPE_ID
								and crr.[RequirementTemplateId] in (202, 203, 204, 205, 207) --item number, manufacturer, model, serial, category
								and crr.[IsRequired] = 1)
					, 1, 0) AS IsAuditNeed
				,IIF(EXISTS(SELECT TOP(1) 1
							FROM [recycling].[F_CommodityRule] WITH(NOLOCK)
							WHERE [RecyclingOrderId] = T.RECYCLING_ORDER_ID
								  AND [CommodityId] = T.ITEM_TYPE_ID
								  AND [IsResaleAllowed] = 1
								  )
								  --TODO: implement correct restriction
									,1, 0 )AS IsTestNeed
	'


	DECLARE @PRODUCTION_STATUS NVARCHAR(MAX) = N'
				,IIF(froi.WORKFLOW_STEP_ID = 1 /*Sort*/, 1, 0 ) AS IS_SORT_NEED
				,froi.IS_TEST_COMPLETE						  AS IS_TEST
				,IIF(NOT EXISTS(SELECT TOP(1)  1
						  FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
						  WHERE I.PARENT_ID = froi.RECYCLING_ORDER_ITEM_ID)
					,1 ,0) AS IS_SORT_OPEN
				,IIF(EXISTS(SELECT TOP(1)  1
						  FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
						  WHERE I.PARENT_ID = froi.RECYCLING_ORDER_ITEM_ID
						  AND froi.WORKFLOW_STEP_ID <> 8)
					,1, 0) AS IS_SORT_INPROCESS
				 ,IIF(EXISTS(SELECT TOP(1)  1
						  FROM F_RECYCLING_ORDER_ITEM I WITH(NOLOCK)
						  WHERE I.PARENT_ID = froi.RECYCLING_ORDER_ITEM_ID
						  AND froi.WORKFLOW_STEP_ID = 8)
					,1, 0) AS IS_SORT_COMPLETED
				,IIF(NOT EXISTS(SELECT TOP(1) 1
						  FROM [recycling].[F_Asset] AI WITH(NOLOCK)
						  WHERE AI.[RecyclingOrderItemId] = froi.RECYCLING_ORDER_ITEM_ID)
					,1, 0) AS IS_AUDIT_OPEN
				,IIF(EXISTS(SELECT TOP(1) 1
						  FROM [recycling].[F_Asset] AI WITH(NOLOCK)
						  WHERE AI.[RecyclingOrderItemId] = froi.RECYCLING_ORDER_ITEM_ID)
					,1, 0) AS IS_AUDIT_INPROCESS
				,froi.IS_AUDIT_COMPLETE							  AS IS_AUDIT_COMPLETED
				,(SELECT 
					crwt.WORKFLOW_SYSTEM_ID 
				  FROM C_RECYCLING_WORKFLOW_TYPE crwt WITH(NOLOCK)
				  WHERE crwt.WORKFLOW_TYPE_ID = froi.WORKFLOW_STEP_ID) AS PRODUCTION_STATUS
	';
	
	DECLARE @query NVARCHAR (MAX) = N'
		  WITH m_data AS (
				SELECT * FROM (
				 SELECT
					froi.RECYCLING_ORDER_ITEM_ID
					,froi.LOT_AUTO_NAME								AS [ITEM_AUTO_NAME]
					,RO.RECYCLING_ORDER_ID
					,FROIN.AUTO_NAME
					,OS.Id											as RECYCLING_ORDER_STATUS_ID
					,OS.name										AS [ORDER_STATUS_CD]
					,C.CUSTOMER_ID
					,C.CUSTOMER_NAME
					,frim.RECYCLING_ITEM_MASTER_ID					AS [ITEM_TYPE_ID]
					,frim.RECYCLING_ITEM_MASTER_NAME				AS [ITEM_TYPE_CD]
					,crwt.WORKFLOW_TYPE_DESC						AS [WORKFLOW_TYPE_CD]
					,crwt.WORKFLOW_SYSTEM_ID						AS [WORKFLOW_SYSTEM_ID]
					,CAST(DATEDIFF(hh, froi.INSERTED_DT, GETUTCDATE()) / 24 AS VARCHAR(100)) + ''.'' + CAST(DATEDIFF(hh, froi.INSERTED_DT, GETUTCDATE()) % 24 AS VARCHAR(100))  AS [AGE]
					,IIF(os.Id = 6 /*--Settlement Complete*/
						,NULL
						,CAST([dbo].[fn_int_GetDaysBetweenInboundOrderDueDateBySla](FROIN.[SLA_BEGINNING], FROIN.[INSERTED_DT], FROIN.[RECIEVE_DATE],
							FROIN.[COMPLETED_DT], FROIN.[DELIVERY_DT], FROIN.[SLA_DAYS]) AS NVARCHAR(20))
					)												AS DueDate
					,DATEDIFF(hh, froi.INSERTED_DT, GETUTCDATE())	AS SortAge
					,FROIN.[SLA_DAYS]								AS SlaDays
					,[dbo].[fn_datetime_GetSlaDate](FROIN.[SLA_BEGINNING], FROIN.[INSERTED_DT], FROIN.[RECIEVE_DATE], FROIN.[COMPLETED_DT], FROIN.[DELIVERY_DT], FROIN.[SLA_DAYS]) AS SlaDate
					,crpt.RECYCLING_PACKAGING_TYPE_ID				AS [PACKAGING_TYPE_ID]
					,crpt.PACKAGING_TYPE_DESC						AS [PACKAGING_TYPE_CD]
					,froi.WEIGHT_RECEIVED
					,froi.WEIGHT_TARE			
					,froi.WEIGHT_RECEIVED - froi.WEIGHT_TARE		  AS [NET]
					,froi.LOCATION_ID												AS [LOCATION_ID]
					,dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(froi.LOCATION_ID)	AS [LOCATION_CD]
					,COALESCE(ROT.WAREHOUSE_ID, loc.WAREHOUSE_ID, RO.WAREHOUSE_ID)	AS [WAREHOUSE_ID]
					,WH.WAREHOUSE_CD
					,froi.NOTES
					,froi.WORKFLOW_STEP_ID						  AS [WORKFLOW_TYPE_ID]
					,froi.WEIGHT_REMAIN
					,froi.ITEM_COUNT
					,ISNULL(froi.PROCESSING_STATE_ID, 1)		  AS PROCESSING_STATE_ID	-- 1 - NOT_STARTED
					,PS.RECYCLING_ORDER_ITEM_PROCESSING_STATE_DESC AS PROCESSING_STATE_DESC
					' + @PRODUCTION_STATUS + N'
					,[AuditSession].[AuditSessionId] AS AUDIT_ORDER_ID
					
					,froi.IS_MERGED								  AS IS_MERGE_PRIMARY
					
					,froi.RECYCLING_ORDER_ITEM_MERGED_ID
					,FROIN.[IsFromConsumable]
					,iif(FROIN.[StatusId] = 8, 1, 0) as IsReadyToBePriced
					,froi.FINALIZE_BUILD_UP_DT
					,froi.MERGE_DT					
					,MU.UserName									AS MergedByUserName
					,froi.MAIN_INNER_LOT_IN_MERGE_ID				AS MainInnerLotInMergeId
					,CO.CONTRACT_ID									AS CONTRACT_ID
					,CO.AUTO_NAME									AS CONTRACT_NAME
					,COALESCE(RO.[WORK_INSTRUCTIONS], C.[WORK_INSTRUCTIONS]) AS [WorkInstructions]
					,CAST(IIF(COALESCE(RO.WORK_INSTRUCTIONS, C.WORK_INSTRUCTIONS) IS NOT NULL, 1, 0) AS BIT) AS HAS_WORK_INSTRUCTIONS
					' + @ITAD_ITEMS_COUNT_SELECT_BLOCK + N'
				 FROM dbo.F_RECYCLING_ORDER_ITEM			froi	WITH(NOLOCK)
				 INNER JOIN  dbo.F_RECYCLING_ORDER		RO   WITH(NOLOCK)
					ON froi.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
				 INNER JOIN  dbo.F_RECYCLING_ORDER_INBOUND FROIN WITH (NOLOCK)
					ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID					
				 INNER JOIN  [recycling].[C_InboundOrderStatus]		OS   WITH(NOLOCK)
					ON FROIN.StatusId = OS.Id
				 INNER JOIN 	dbo.F_CUSTOMER				C    WITH(NOLOCK)
					ON RO.CUSTOMER_ID = C.CUSTOMER_ID
				 INNER JOIN 	F_RECYCLING_ITEM_MASTER		frim	WITH (NOLOCK)	
					ON froi.RECYCLING_ITEM_MASTER_ID = frim.RECYCLING_ITEM_MASTER_ID
				 LEFT JOIN 	D_RECYCLING_PACKAGING_TYPE	crpt	WITH (NOLOCK)	
					ON froi.PACKAGING_TYPE_ID = crpt.RECYCLING_PACKAGING_TYPE_ID
				 LEFT JOIN 	C_RECYCLING_WORKFLOW_TYPE	crwt	WITH (NOLOCK)	
					ON froi.WORKFLOW_STEP_ID = crwt.WORKFLOW_TYPE_ID
				 ' + @joins + N'
				 LEFT JOIN 	dbo.F_LOCATION								loc  WITH (NOLOCK)	
					ON froi.LOCATION_ID = loc.LOCATION_ID
				 LEFT JOIN F_RECYCLING_ORDER							OO  WITH(NOLOCK)
					ON froi.OUTBOUND_ORDER_ID = OO.RECYCLING_ORDER_ID
					AND OO.RECYCLING_ORDER_STATUS_ID != 4
				 LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER				ROIT WITH(NOLOCK)
					ON  ROIT.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
					AND ROIT.OUTBOUND_ORDER_ID		 = FROI.OUTBOUND_ORDER_ID -- only the actual one
				 LEFT JOIN dbo.F_RECYCLING_ORDER						ROT   WITH(NOLOCK)
					ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
				 LEFT JOIN C_RECYCLING_ORDER_ITEM_PROCESSING_STATE		PS  WITH(NOLOCK)
					ON PS.RECYCLING_ORDER_ITEM_PROCESSING_STATE_ID = froi.PROCESSING_STATE_ID
				 LEFT JOIN [dbo].[F_RECYCLING_ORDER_CONTRACT]			LROC	WITH(NOLOCK)
					ON LROC.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
					AND LROC.IsFirstApplied = 1
				 LEFT JOIN dbo.F_CONTRACT								CO	WITH(NOLOCK)
					ON LROC.CONTRACT_ID = CO.CONTRACT_ID
				 LEFT JOIN dbo.tb_User									MU	WITH(NOLOCK)
					ON	MU.UserId = froi.MERGED_BY_USER
				 LEFT JOIN (
					SELECT DISTINCT
						FROI_FROM_CONSUMED.[RECYCLING_ORDER_ITEM_ID]
					FROM [dbo].[F_RECYCLING_ORDER_ITEM_CONSUMED] AS FROC WITH(NOLOCK)
					INNER JOIN [dbo].[F_RECYCLING_ORDER_ITEM] AS FROI_CONSUMED WITH(NOLOCK)
						ON FROI_CONSUMED.[RECYCLING_ORDER_ITEM_ID] = FROC.[PARENT_ID]
							AND FROI_CONSUMED.[WORKFLOW_STEP_ID] = 9
					INNER JOIN [dbo].[F_RECYCLING_ORDER_ITEM] AS FROI_FROM_CONSUMED WITH(NOLOCK)
						ON FROI_FROM_CONSUMED.[RECYCLING_ORDER_ITEM_ID] = FROC.[RECYCLING_ORDER_ITEM_ID]
							AND FROI_FROM_CONSUMED.[WORKFLOW_STEP_ID] = 9
				)	AS CONSUMED_FROM_CONSUMED
					ON CONSUMED_FROM_CONSUMED.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
				LEFT JOIN	dbo.D_WAREHOUSE								WH WITH (NOLOCK)
					ON COALESCE(ROT.WAREHOUSE_ID, loc.WAREHOUSE_ID, RO.WAREHOUSE_ID) = WH.WAREHOUSE_ID
				OUTER APPLY (
					SELECT COALESCE(
						(SELECT TOP(1) FA.[AuditSessionId] FROM [recycling].[F_Asset] AS FA WITH(NOLOCK) WHERE FA.[RecyclingOrderItemId] = froi.[RECYCLING_ORDER_ITEM_ID]),
						(SELECT TOP(1) FAS.[Id] FROM [recycling].[F_AuditSession] AS FAS WITH(NOLOCK) WHERE FAS.[RecyclingOrderId] = froi.[RECYCLING_ORDER_ID])
					) AS [AuditSessionId]
				) AS [AuditSession]
				' + @ITAD_ITEMS_COUNT_JOINS_BLOCK + N'
				  WHERE ((' + CAST(@IS_MERGE AS NVARCHAR(20)) + N' = 1 				 						
								OR RO.WAREHOUSE_ID IS NULL AND loc.WAREHOUSE_ID IS NULL 
								' + @warehouseWhereCondition + N') 
					AND (' + CAST(@IS_MERGE AS NVARCHAR(20)) + N' = 1 OR ((' + CAST(@WORKFLOW_STEP_ID AS NVARCHAR(20))+ N' = 0 and CONSUMED_FROM_CONSUMED.RECYCLING_ORDER_ITEM_ID IS NULL
					AND (froi.IS_CONSUMED_OR_PROCESSED = 0) OR (' + CAST(@WORKFLOW_STEP_ID AS NVARCHAR(20))+ N' = 8 AND froi.IS_CONSUMED_OR_PROCESSED = 1) OR froi.WORKFLOW_STEP_ID = ' + CAST(@WORKFLOW_STEP_ID AS NVARCHAR(20)) + N')))
					AND (OO.RECYCLING_ORDER_ID IS NULL ' + @lotsWhere + N'
						OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL)
					AND (froi.IS_INACTIVE = 0 ' + @lotsWhere + N')
					AND ( ' + CAST(@START_DATE_IS_NULL AS NVARCHAR(10)) + N'=1 OR DATEDIFF(dd, froi.INSERTED_DT,' + '''' + CAST(ISNULL(@START_DATE, GETUTCDATE()) AS NVARCHAR(100)) + '''' + N') <= 0) AND (' + CAST(@END_DATE_IS_NULL AS NVARCHAR(10))  + N'=1 OR DATEDIFF(dd, froi.INSERTED_DT,' + '''' + CAST(ISNULL(@END_DATE, GETUTCDATE()) AS NVARCHAR(100)) + '''' + N') >= 0)			
					' + @internalFilterCondition + N')
					) T ' 
	   + @filterCondition 
	   + @quickFilter
	   + N'
		)';		
		
		
	   SET @query = @query + N'	
		 SELECT TOP(1)
			(SELECT COUNT(1) FROM m_data) AS RecyclingOrderItemId,	
			NULL					    AS ItemAutoName,
			0						    AS RecyclingOrderId,
			NULL					    AS RecyclingOrder,
			0						    AS RecyclingOrderStatusId,
			NULL					    AS RecyclingOrderStatusCd,
			0							AS CustomerId,
			NULL					    AS Customer,
			0						    AS ItemTypeId,
			NULL					    AS ItemTypeCD,
			NULL					    AS WorkflowTypeCD,
			NULL						AS WorkflowSystemId,
			NULL					    AS [Age],
			NULL						AS DueDate,
			NULL						AS SlaDays,
			NULL						AS SlaDate,
			NULL						AS SortAge,
			0						    AS PackagingTypeId,
			NULL					    AS PackagingTypeCD,
			NULL					    AS Weight,
			NULL					    AS Tare,
			NULL					    AS [Net],
			0						    AS [LocationId],
			NULL					    AS [LocationCD],
			0						    AS [WarehouseId],
			NULL						AS [WarehouseCd],
			NULL					    AS Notes,
			0						    AS WorkflowTypeId,			
			NULL					    AS WeightRemain,
			0						    AS ImageCount,
			0						    AS ItemCount,
			0							AS ProcessingStateId,
			NULL						AS ProcessingStateCd,
			0							AS IsAuditNeed,
			0							AS IsTestNeed,
			0							AS IsSortNeed,
			0							AS IsTest,
			0							AS IsSortOpen,
			0							AS IsSortInprocess,
			0							AS IsSortCompleted,
			0							AS IsAuditOpen,
			0							AS IsAuditInprocess,
			0							AS IsAuditCompleted,
			0							AS ProductionStatus,
			0							AS AuditOrderId,
			0						    AS IsMergePrimary,
			0						    AS IsPartOfMerged,
			0							AS [IsFromConsumable],
			0							AS [IsReadyToBePriced],
			NULL						AS FinalizeBuildUpDt,
			NULL						AS MergeDt,
			NULL						AS MergedByUserName,
			NULL						AS MainInnerLotInMergeId,
			0							AS ContractId,
			NULL						AS ContractName,
			NULL						AS [WorkInstructions],
			1							AS HasWorkInstructions,
			0							AS TotalItemsCount,
			0							AS CompletedItemsCount
		UNION ALL
		SELECT * FROM 
	   (SELECT 
		  t.RECYCLING_ORDER_ITEM_ID		AS RecyclingOrderItemId
		  ,t.ITEM_AUTO_NAME			    AS ItemAutoName
		  ,t.RECYCLING_ORDER_ID  		AS RecyclingOrderId
		  ,t.AUTO_NAME					AS RecyclingOrder
		  ,t.RECYCLING_ORDER_STATUS_ID  AS RecyclingOrderStatusId
		  ,t.ORDER_STATUS_CD			AS RecyclingOrderStatusCd
		  ,t.CUSTOMER_ID				AS CustomerId
		  ,t.CUSTOMER_NAME				AS Customer
		  ,t.ITEM_TYPE_ID			    AS ItemTypeId
		  ,t.ITEM_TYPE_CD			    AS ItemTypeCD
		  ,t.WORKFLOW_TYPE_CD		    AS WorkflowTypeCD
		  ,t.WORKFLOW_SYSTEM_ID			AS WorkflowSystemId
		  ,t.[AGE]					    AS Age
		  ,t.DueDate					AS DueDate
		  ,t.SlaDays
		  ,t.SlaDate
		  ,t.SortAge
		  ,t.PACKAGING_TYPE_ID		    AS PackagingTypeId
		  ,t.PACKAGING_TYPE_CD		    AS PackagingTypeCD
		  ,t.WEIGHT_RECEIVED			AS Weight
		  ,t.WEIGHT_TARE				AS Tare
		  ,t.[NET]					    AS Net
		  ,t.LOCATION_ID				AS LocationId	
		  ,t.LOCATION_CD				AS LocationCD
		  ,t.WAREHOUSE_ID			    AS WarehouseId
		  ,t.WAREHOUSE_CD				AS WarehouseCd
		  ,t.NOTES					    AS Notes
		  ,t.WORKFLOW_TYPE_ID		    AS WorkflowTypeId
		  ,t.WEIGHT_REMAIN			    AS WeightRemain
		  ,(SELECT 
			 COUNT(IM.RECYCLING_ORDER_ITEM_IMAGE_ID)
		  FROM F_RECYCLING_ORDER_ITEM_IMAGE IM WITH(NOLOCK)
		  WHERE IM.RECYCLING_ORDER_ITEM_ID = t.RECYCLING_ORDER_ITEM_ID)	 AS ImageCount
		  ,t.ITEM_COUNT				    AS ItemCount
		  ,t.PROCESSING_STATE_ID		AS ProcessingStateId
		  ,t.PROCESSING_STATE_DESC	    AS ProcessingStateCd
		  ' + @PRODUCTION_STATUS_0 + '
		  ,t.IS_SORT_NEED				AS IsSortNeed
		  ,t.IS_TEST					AS IsTest
		  ,t.IS_SORT_OPEN				AS IsSortOpen
		  ,t.IS_SORT_INPROCESS			AS IsSortInprocess
		  ,t.IS_SORT_COMPLETED			AS IsSortCompleted
		  ,t.IS_AUDIT_OPEN				AS IsAuditOpen
		  ,t.IS_AUDIT_INPROCESS			AS IsAuditInprocess
		  ,t.IS_AUDIT_COMPLETED			AS IsAuditCompleted
		  ,t.PRODUCTION_STATUS			AS ProductionStatus
		  ,t.AUDIT_ORDER_ID				AS AuditOrderId
		  ,t.IS_MERGE_PRIMARY		    AS IsMergePrimary
		  ,IIF(t.RECYCLING_ORDER_ITEM_MERGED_ID IS NULL, 0 ,1)
										AS IsPartOfMerged
		  ,t.[IsFromConsumable]
		  ,t.[IsReadyToBePriced]
		  ,t.FINALIZE_BUILD_UP_DT		AS FinalizeBuildUpDt
		  ,t.MERGE_DT					AS MergeDt
		  ,t.MergedByUserName			AS MergedByUserName
		  ,t.MainInnerLotInMergeId		AS MainInnerLotInMergeId
		  ,t.CONTRACT_ID				AS ContractId
		  ,t.CONTRACT_NAME				AS ContractName
		  ,t.[WorkInstructions]
		  ,t.HAS_WORK_INSTRUCTIONS		AS HasWorkInstructions
		  ' + @ITAD_ITEMS_COUNT_SELECT_BLOCK_2 + N'
	   FROM m_data T 
	   ORDER BY ' + @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N'
	   OFFSET ' + CAST(@PAGE_INDEX * @ITEMS_PER_PAGE  AS NVARCHAR(10)) + N' ROWS FETCH NEXT ' + CAST(@ITEMS_PER_PAGE AS NVARCHAR(10)) + N' ROWS ONLY) tt;';
	   
	   IF (@C_IS_DEBUG = 1)
			PRINT(CAST(@query AS NTEXT))

	   DECLARE @ParmDefinition nvarchar(MAX)
	   SET @ParmDefinition = '
			@WAREHOUSE_IDS			bigint_ID_ARRAY readonly
			,@LOT_IDS				bigint_ID_ARRAY readonly
			,@REP_IDS				bigint_ID_ARRAY readonly
			,@CUSTOMER_IDS			bigint_ID_ARRAY readonly
			,@RECYCLING_ORDER_IDS	bigint_ID_ARRAY readonly
			,@COMMODITY_IDS			bigint_ID_ARRAY readonly' 
			
	   EXEC sp_executesql 
			@query,
			@ParmDefinition,
			@WAREHOUSE_IDS = @WAREHOUSE_IDS,
			@LOT_IDS = @LOT_IDS,			
			@REP_IDS = @REP_IDS,
			@CUSTOMER_IDS = @CUSTOMER_IDS,
			@RECYCLING_ORDER_IDS = @RECYCLING_ORDER_IDS,
			@COMMODITY_IDS = @COMMODITY_IDS;			
END
GO
CREATE TABLE [dbo].[U_SMTP_SETTINGS] (
    [SMTP_SETTING_ID]        BIGINT         IDENTITY (1, 1) NOT NULL,
    [USER_ID]                BIGINT         NULL,
    [SMTP_HOST]              NVARCHAR (256) COLLATE Latin1_General_CS_AS NOT NULL,
    [SMTP_PORT]              INT            CONSTRAINT [DF_U_SMTP_SETTINGS_SMTP_PORT] DEFAULT ((587)) NULL,
    [SMTP_USER]              NVARCHAR (128) COLLATE Latin1_General_CS_AS NULL,
    [SMTP_AUTH_TYPE]         INT            CONSTRAINT [DF_U_SMTP_SETTINGS_SMTP_AUTH] DEFAULT (N'Basic') NOT NULL,
    [SMTP_PASSWORD]          NVARCHAR (128) COLLATE Latin1_General_CS_AS NULL,
    [SMTP_SSL_ENABLED]       BIT            CONSTRAINT [DF_U_SMTP_SETTINGS_SMTP_SSL_ENABLED] DEFAULT ((1)) NOT NULL,
    [SMTP_DEFAULT_SEND_FROM] NVARCHAR (256) COLLATE Latin1_General_CS_AS NULL,
    [IS_INACTIVE]            BIT            CONSTRAINT [DF_U_SMTP_SETTINGS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]            NVARCHAR (128) NOT NULL,
    [INSERTED_DT]            DATETIME       NOT NULL,
    [UPDATED_BY]             NVARCHAR (128) NULL,
    [UPDATED_DT]             DATETIME       NULL,
    CONSTRAINT [PK_U_SMTP_SETTINGS] PRIMARY KEY CLUSTERED ([SMTP_SETTING_ID] ASC),
    CONSTRAINT [FK_U_SMTP_SETTINGS_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE ON UPDATE CASCADE
);


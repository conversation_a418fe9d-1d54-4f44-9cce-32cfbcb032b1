CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_PURCHASE_ORDER_STATUS_CHANGE_FROM_QUEUE]
	 @C_COUNT		INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID BIGINT = 15
    DECLARE @IS_ALLOWED		     BIT	  = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID);

	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY = N'sp_GET_NOTIFICATIONS_PURCHASE_ORDER_STATUS_CHANGE_FROM_QUEUE',
			UPDATED_DT = GETUTCDATE()
		WHERE IS_INACTIVE = 0 AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);
	
	SELECT
			NQ.NotificationQueueId					AS [NOTIFICATION_QUEUE_ID]
			,PO.PURCHASE_ORDER_ID					AS [PURCHASE_ORDER_ID]
			,PO.AUTO_NAME							AS [PURCHASE_ORDER_AUTO_NAME]
			,POS_OLD.PURCHASE_ORDER_STATUS_CD		AS [OLD_PURCHASE_ORDER_STATUS]
			,POS_NEW.PURCHASE_ORDER_STATUS_CD		AS [NEW_PURCHASE_ORDER_STATUS]
			,DC.*
			,DU.*
			,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, PO.[USER_ID], NULL, NULL, NQ.NotificationSetId, NQ.CustomerId) AS EMAILS		
		FROM @tNotifyItemWithNotifySet		NQ
 	    CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
		INNER JOIN F_PURCHASE_ORDER PO WITH (NOLOCK)
			ON PO.PURCHASE_ORDER_ID = QP.PURCHASE_ORDER_ID
		INNER JOIN [dbo].[C_PURCHASE_ORDER_STATUS]		POS_OLD	WITH(NOLOCK)
			ON qp.OLD_STATUS_ID = POS_OLD.PURCHASE_ORDER_STATUS_ID
		INNER JOIN [dbo].[C_PURCHASE_ORDER_STATUS]		POS_NEW	WITH(NOLOCK)
			ON qp.NEW_STATUS_ID = POS_NEW.PURCHASE_ORDER_STATUS_ID
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER]		(PO.CUSTOMER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)	DC
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER]			(PO.[USER_ID])								DU
		WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
			AND QP.PURCHASE_ORDER_ID IS NOT NULL	
END
CREATE TABLE [dbo].[C_STORAGE_INFORMATION] (
    [STORAGE_INFORMATION_ID] INT           IDENTITY (1, 1) NOT NULL,
    [STORAGE_TYPE]           VARCHAR (256) NOT NULL,
    [FILE_NAME]              VARCHAR (256) NOT NULL,
    CONSTRAINT [PK_C_STORAGE_INFORMATION] PRIMARY KEY CLUSTERED ([STORAGE_INFORMATION_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is used to store template file names. I used on resale PO screen', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_STORAGE_INFORMATION';


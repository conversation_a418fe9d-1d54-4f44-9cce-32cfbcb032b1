CREATE FUNCTION [dbo].[fn_bigint_CompanyHomeCurrencyExchangeId]()
RETURNS BIGINT
AS
BEGIN
	DECLARE
		@homeCurrencyCodeId int = [dbo].[fn_int_CompanyCurrencyCodeId](),
		@ret bigint = NULL

	SELECT TOP(1)
		@ret = ce.Id
	FROM dbo.D_CurrencyExchange		ce with(nolock)
	WHERE ce.ForeignCurrencyId = @homeCurrencyCodeId
		AND ce.HomeCurrencyId = @homeCurrencyCodeId
		AND ce.ClosedDate IS NULL
	ORDER BY ce.Id DESC

	IF (@ret is null)
		RETURN CAST('The home currency exchange rate not found' AS BIGINT);
	
	RETURN @ret
END

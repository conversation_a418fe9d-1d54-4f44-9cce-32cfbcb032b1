CREATE TABLE [dbo].[F_ITEM_INVENTORY_ASSET_STATE] (
    [ITEM_INVENTORY_ASSET_STATE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_INVENTORY_ID]             BIGINT        NOT NULL,
    [ASSET_STATE_ID]                BIGINT        NOT NULL,
    [IS_CURRENT]                    BIT           CONSTRAINT [DF_F_ITEM_INVENTORY_ASSET_STATE_IS_CURRENT] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   VARCHAR (250) NOT NULL,
    [INSERTED_DT]                   DATETIME      NOT NULL,
    CONSTRAINT [PK_F_RECYCLING_ORDER_ITEM_ASSET_STATE] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_ASSET_STATE_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_ASSET_STATE_C_ASSET_STATE1] FOREIGN KEY ([ASSET_STATE_ID]) REFERENCES [dbo].[C_ASSET_STATE] ([ASSET_STATE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_ASSET_STATE_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID])
);






GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ASSET_STATE]
    ON [dbo].[F_ITEM_INVENTORY_ASSET_STATE]([ITEM_INVENTORY_ID] ASC, [ASSET_STATE_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ASSET_STATE_IS_CURRENT]
    ON [dbo].[F_ITEM_INVENTORY_ASSET_STATE]([IS_CURRENT] ASC)
    INCLUDE([ITEM_INVENTORY_ID], [ASSET_STATE_ID]);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The itad module allow to receive inventory. Inventory goes through steps. The steps are stores here', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_ASSET_STATE';


CREATE PROCEDURE [dbo].[sp_DELETE_NOTIFICATION_SETS] 
	@T_SET_IDS	bigint_ID_ARRAY READONLY
AS
BEGIN
	SET XACT_ABORT ON
	BEGIN TRANSACTION
		
		DELETE N
		FROM F_NOTIFICATION_SET				S
		INNER JOIN F_NOTIFICATION_SET_GROUP G
		  ON S.NOTIFICATION_SET_ID = G.NOTIFICATION_SET_ID
		INNER JOIN F_NOTIFICATION			N
		  ON G.NOTIFICATION_GROUP_ID = N.NOTIFICATION_GROUP_ID
		INNER JOIN @T_SET_IDS				I
		  ON S.NOTIFICATION_SET_ID = I.ID

		DELETE G
		FROM F_NOTIFICATION_SET				S
		INNER JOIN F_NOTIFICATION_SET_GROUP G
		  ON S.NOTIFICATION_SET_ID = G.NOTIFICATION_SET_ID
		INNER JOIN @T_SET_IDS				I
		  ON S.NOTIFICATION_SET_ID = I.ID

		DELETE C
		FROM [dbo].[F_NOTIFICATION_SET_CUSTOMER] AS C WITH(NOLOCK)
		INNER JOIN @T_SET_IDS				I
		  ON C.NOTIFICATION_SET_ID = I.ID;

		DELETE S
		FROM F_NOTIFICATION_SET				S
		INNER JOIN @T_SET_IDS				I
		  ON S.NOTIFICATION_SET_ID = I.ID

	COMMIT TRANSACTION
END
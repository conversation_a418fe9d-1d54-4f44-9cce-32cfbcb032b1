/*
EXEC sp_GET_SALES_ORDER_INVOICES 
	 @ORDER_ID          = 0
	,@DUE_STATE_ID      = 0
	,@WAREHOUSE_ID      = 0
	,@ORDER_COLUMN_NAME = N'ConsolidatedInAutoName'
	,@ORDER_DIRECTION   = N'asc'
	,@ITEMS_PER_PAGE    = 100
	,@PAGE_INDEX        = 0
	,@FILTER_WHERE      = N'((([InvoiceAutoName] LIKE N''%INV1300632%'' ESCAPE ''\'') OR ([InvoiceAutoName] LIKE N''%INV 1300632%'' ESCAPE ''\'') OR ([InvoiceAutoName] LIKE N''%INV-1300632%'' ESCAPE ''\'')))'
*/
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_INVOICES]
--declare
    @ORDER_ID			bigint			= 0,
	@REP_IDS			bigint_ID_ARRAY	READONLY,
    @DUE_STATE_ID		int				= 0,
	@WAREHOUSE_ID		BIGINT			= 0,
    @ORDER_COLUMN_NAME	varchar(150)	= N'InvoiceId',
    @ORDER_DIRECTION	varchar(20)		= N'DESC',
    @ITEMS_PER_PAGE		int				= 20,
    @PAGE_INDEX			int				= 0,
	@DATE_CREATED_FROM	DATETIME		= null,
	@DATE_CREATED_TO	DATETIME		= null,
    @FILTER_WHERE		varchar(2000)	= N'',
	@DEBUG				BIT				= 0

	
AS
BEGIN

	DECLARE @rep_condition	NVARCHAR(MAX) = N'';
	-- rep restricion
	IF (EXISTS(SELECT ID FROM @REP_IDS)) BEGIN
	   SET @rep_condition = N'
			AND ((I.ORDER_REP_USER_ID in (SELECT ID FROM @REP_IDS))
				OR (I.IS_INVOICE_CONSOLIDATED = 1 AND I.REP_USER_ID in (SELECT ID FROM @REP_IDS)))
	   '
    END

    SET @ORDER_COLUMN_NAME	= ISNULL(NULLIF(@ORDER_COLUMN_NAME,''), 'InvoiceId')
	SET @WAREHOUSE_ID		= NULLIF(@WAREHOUSE_ID, 0)  
	SET @ORDER_ID			= NULLIF(@ORDER_ID, 0)
    DECLARE @now		   DATETIME	    = GETUTCDATE();

    DECLARE
	   @filterCondition	   NVARCHAR(2006)   = N'',
	   @dueCondition	   NVARCHAR(2006)   = 
		  CASE	--																																												 was   AND I.ORIGINAL_AMOUNT > 0 for open and current both
			 WHEN @DUE_STATE_ID = 0 THEN N'AND I.IS_VOIDED = 0 AND (I.ORDER_STATUS_ID != 2 OR I.ORDER_ID IS NULL OR I.IS_INVOICE_CONSOLIDATED = 1) AND I.STATUS_ID < 5 AND DATEDIFF(Day, I.DATE_CREATED, @now) >  I.DUE_DAYS AND CAST(I.AMOUNT_DUE AS DECIMAL(18,2)) > 0.00' -- open (overdued)
			 WHEN @DUE_STATE_ID = 1 THEN N'AND I.IS_VOIDED = 0 AND (I.ORDER_STATUS_ID != 2 OR I.ORDER_ID IS NULL OR I.IS_INVOICE_CONSOLIDATED = 1) AND I.STATUS_ID < 5 AND DATEDIFF(Day, I.DATE_CREATED, @now) <= I.DUE_DAYS AND CAST(I.AMOUNT_DUE AS DECIMAL(18,2)) > 0.00' -- current (not overdued)
				--																					was just  OR I.ORIGINAL_AMOUNT = 0)
			 WHEN @DUE_STATE_ID = 2 THEN N'AND(I.IS_VOIDED = 1 OR   I.ORDER_STATUS_ID  = 2 OR I.STATUS_ID = 5 OR I.ORIGINAL_AMOUNT = 0 OR CAST(I.AMOUNT_DUE AS DECIMAL(18,2)) <= 0.00) ' -- history (paid/voided)
			 ELSE N''
	END
	
    IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
    BEGIN
	   SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
    END 
	
	
    DECLARE
	   @query NVARCHAR (max) = N'
		  ;WITH m_data_result as (
			SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				*
			 FROM (
				 SELECT 
					I.INVOICE_ID						AS InvoiceId
					,I.AUTO_NAME						as InvoiceAutoName
					,I.ORDER_ID							AS SalesOrderId
					,I.ORDER_AUTO_NAME					AS SoNo
					,C.CUSTOMER_ID						AS CustomerId 
					,C.CUSTOMER_NAME					AS CustomerCd
					,[dbo].[fn_str_GET_USER_AUTO_NAME](I.SENDER_USER_ID, 0)		AS SenderUser
					,I.SEND_INVOICE_DT					AS SendInvoiceDate
					,I.DATE_CREATED						AS DateCreated
					,I.DATE_DUE							AS DateDue
					,I.STATUS_ID						AS StatusId
					,invs.[STATUS_NAME]					as Status
					,I.IS_VOIDED						AS IsVoided
					,I.IS_FULLY_PAID					AS IsFullyPaid
					,I.IS_RECYCLING						AS IsRecycling
					,ISNULL(I.ORDER_REP_USER_ID, I.REP_USER_ID) AS RepUserId
					,[dbo].[fn_str_GET_USER_AUTO_NAME](ISNULL(I.ORDER_REP_USER_ID, I.REP_USER_ID), 0)	AS RepUserCd
					,I.AMOUNT_DUE						as AmountDue 
					,I.ORIGINAL_AMOUNT					AS Amount
					,I.PAID_AMOUNT						AS PaidAmount
					,W.WAREHOUSE_CD						AS Warehouse
					,I.IS_INVOICE_CONSOLIDATED			AS IsInvoiceConsolidated
					,I.CONSOLIDATED_ID					AS InvoiceConsolidatedId
					,I.IS_ACTIVE						AS IsActive
					,I.ORDERS_COUNT						AS CntSalesOrders
					,I.CONSOLIDATED_IN_AUTO_NAME		AS ConsolidatedInAutoName
					,I.KidNumber						as KidNumber
					,isnull(cm.CreditMemosCount, 0)		as CreditMemosCount
					,isnull(cm.CreditMemosAmount, 0.0)	as CreditMemosAmount
					,I.ExternalId
				FROM dbo.vw_AR_INVOICE					I	WITH(NOLOCK)
				INNER JOIN F_CUSTOMER					C	WITH(NOLOCK)
					ON C.CUSTOMER_ID = I.CUSTOMER_ID
				LEFT JOIN tb_User						R	WITH(NOLOCK)
					ON I.ORDER_REP_USER_ID = R.UserID
				LEFT JOIN [dbo].[F_RECYCLING_ORDER]		RO	WITH (NOLOCK)
					ON I.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID			
				LEFT JOIN D_WAREHOUSE					W	WITH (NOLOCK)
					ON W.WAREHOUSE_ID = IsNULL(I.WAREHOUSE_ID,RO.WAREHOUSE_ID) 
				left join C_SALES_ORDER_INVOICE_STATUS invs
					on i.STATUS_ID = invs.INVOICE_STATUS_ID
				left join (
					select
						cm.SalesOrderId as SalesOrderId
						,count(cm.Id)	as CreditMemosCount
						,sum(cm.Credit)	as CreditMemosAmount
					from [dbo].[vw_CreditMemo] cm with(nolock)
					group by cm.SalesOrderId
				) cm
					on cm.SalesOrderId = I.ORDER_ID
				WHERE (@ORDER_ID IS NULL OR I.ORDER_ID = @ORDER_ID)
				  AND (@WAREHOUSE_ID IS NULL
					  OR I.IS_INVOICE_CONSOLIDATED = 1
					  OR W.WAREHOUSE_ID = @WAREHOUSE_ID)
					 AND (@DATE_CREATED_FROM is null or I.DATE_DUE >= @DATE_CREATED_FROM)
					 AND (@DATE_CREATED_TO     is null or I.DATE_DUE <= @DATE_CREATED_TO)
				  ' + @rep_condition + '
				  ' + @dueCondition + '
			 ) t
			'+ @filterCondition + '
		  )'
	DECLARE @SELECT_query NVARCHAR(MAX) = N'

		  SELECT TOP(1)
			 -1		AS RowID,
			 COUNT(InvoiceId) AS InvoiceId,
			 NULL	AS InvoiceAutoName,
			 0		AS IsActive,
			 -1		AS SalesOrderId,
			 NULL	AS SoNo,
			 -1		AS CustomerId,
			 NULL	AS CustomerCd,
			 NULL	AS SenderUser,
			 NULL	AS SendInvoiceDate,
			 NULL	AS DateCreated,
			 NULL	AS DateDue,
			 NULL	AS StatusId,
			 NULL	as Status,
			 0		AS IsFullyPaid,
			 0		AS IsRecycling,
			 0		AS IsVoided,
			 -1		AS RepUserId,
			 NULL	AS RepUserCd,
			 0		AS Amount,
			 0		AS AmountDue,
			 0		AS PaidAmount,
			 NULL   as OrderCurrency,
			 0		AS OrderCurrencyAmount,
			 0		AS OrderCurrencyAmountDue,
			 0		AS OrderCurrencyPaidAmount,
			 0		AS OrderCurrencyCreditMemosAmount,
			 NULL   AS Warehouse,
			 0		AS IsInvoiceConsolidated,
			 NULL	AS InvoiceConsolidatedId,
			 0		AS CntSalesOrders,
			 NULL	AS ConsolidatedInAutoName,
			 NULL	as KidNumber,
			 0		as CreditMemosCount,
			 0.0	as CreditMemosAmount,
			 NULL	as ExternalId
		  FROM m_data_result
		  UNION ALL
			SELECT * FROM ( SELECT 
				M.RowID,	
				M.InvoiceId as InvoiceId,
				M.InvoiceAutoName,
				M.IsActive,
				M.SalesOrderId,
				M.SoNo,
				M.CustomerId,
				M.CustomerCd,
				M.SenderUser,
				M.SendInvoiceDate,
				M.DateCreated,
				M.DateDue,
				M.StatusId,
				M.Status,
				M.IsFullyPaid,
				M.IsRecycling,
				M.IsVoided,
				M.RepUserId,
				M.RepUserCd,
				M.Amount,
				M.AmountDue,
				M.PaidAmount,
				[dbo].[fn_str_GetFormattedCurrencyMark](c.Symbol, c.Abbreviation, ce.PREFER_CURRENCY_ABBREVIATION) as OrderCurrency,
				M.Amount			/ isnull(e.ForeignToHome, 1) as OrderCurrencyAmount,
				M.AmountDue			/ isnull(e.ForeignToHome, 1) as OrderCurrencyAmountDue,
				M.PaidAmount		/ isnull(e.ForeignToHome, 1) as OrderCurrencyPaidAmount,
				M.CreditMemosAmount / isnull(e.ForeignToHome, 1) as OrderCurrencyCreditMemosAmount,
				M.Warehouse,
				M.IsInvoiceConsolidated,
				M.InvoiceConsolidatedId,
				M.CntSalesOrders,
				M.ConsolidatedInAutoName,
				M.KidNumber,
				M.CreditMemosCount,
				M.CreditMemosAmount,
				M.ExternalId
			FROM m_data_result						M
			left JOIN dbo.F_SALES_ORDER				o	WITH(NOLOCK)
				ON o.SALES_ORDER_ID = M.SalesOrderId				
			left join [dbo].[D_CurrencyExchange]	e	WITH(NOLOCK)
				on o.CurrencyExchangeId = e.Id
			left join [dbo].[C_Currency]			c	WITH(NOLOCK)
				ON e.[ForeignCurrencyId] = c.Id
			cross join [dbo].[U_SYSTEM_SETTINGS]	ce	with(nolock)
			ORDER BY RowID
				OFFSET((@PAGE_INDEX)* @ITEMS_PER_PAGE) ROWS
				FETCH NEXT @ITEMS_PER_PAGE ROWS ONLY
			) S'
	SET @query = @query + @SELECT_query	   
    IF @DEBUG = 1
		PRINT CAST(@query AS NTEXT)
	ELSE 
		EXEC sp_executeSQL 
		@query,
		N'@ORDER_ID		BIGINT,
		@WAREHOUSE_ID	BIGINT,
		@PAGE_INDEX		INT,
		@ITEMS_PER_PAGE INT,
		@REP_IDS		dbo.bigint_ID_ARRAY READONLY,
		@now			DATETIME,
		@DATE_CREATED_FROM 	datetime,
		@DATE_CREATED_TO	datetime',
		@ORDER_ID		= @ORDER_ID,
		@WAREHOUSE_ID	= @WAREHOUSE_ID,
		@PAGE_INDEX		= @PAGE_INDEX,
		@ITEMS_PER_PAGE = @ITEMS_PER_PAGE,
		@REP_IDS		= @REP_IDS,
		@now			= @now,
		@DATE_CREATED_FROM	= @DATE_CREATED_FROM,
		@DATE_CREATED_TO       = @DATE_CREATED_TO
	
END
GO


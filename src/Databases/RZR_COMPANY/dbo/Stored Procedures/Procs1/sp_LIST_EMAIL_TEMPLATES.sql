-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 05/12/2014
-- Description:	list email templates
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_EMAIL_TEMPLATES]
	@EMAIL_TEMPLATE_TYPE_ID INT,
	@TERM		 nvarchar(max) = N'',
	@COUNT		 int = 20
AS
BEGIN
	SET @TERM = LTRIM(RTRIM(@TERM))
	
	SELECT TOP(@COUNT)
		fet.EMAIL_TEMPLATE_ID		AS [VALUE],
		fet.EMAIL_TEMPLATE_TITLE	AS [LABEL]
	FROM F_EMAIL_TEMPLATE	fet WITH (NOLOCK)
	WHERE fet.EMAIL_TEMPLATE_TITLE  LIKE @TERM AND
		  fet.EMAIL_TEMPLATE_TYPE_ID = @EMAIL_TEMPLATE_TYPE_ID
END
CREATE TABLE [dbo].[TagToInventory] (
    [Id]             BIGINT   IDENTITY (1, 1) NOT NULL,
    [TagId]          BIGINT   NOT NULL,
    [InventoryId]    BIGINT   NOT NULL,
    [InsertedDate]   DATETIME NOT NULL,
    [InsertedUserId] BIGINT   NOT NULL,
    [InsertedUserIp] BIGINT   NOT NULL,
    CONSTRAINT [PK_TagToInventory] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TagToInventory_F_ITEM_INVENTORY] FOREIGN KEY ([InventoryId]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_TagToInventory_F_TAG] FOREIGN KEY ([TagId]) REFERENCES [dbo].[F_TAG] ([TAG_ID]),
    CONSTRAINT [FK_TagToInventory_tb_User] FOREIGN KEY ([InsertedUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);








GO



CREATE TRIGGER [dbo].[trg_TagToInventory_PUT_CHANGE]
    ON  [dbo].[TagToInventory]
    AFTER INSERT, DELETE
AS 
BEGIN									

    declare @inventoryIds dbo.bigint_id_array

	insert into @inventoryIds
	select distinct [InventoryId]
	from 
	(
		select i.[InventoryId]
		from inserted		i
		union
		select d.[InventoryId]
		from deleted	d		
	)  i
	-- assuming that it is fast enough to update the records in elastic each time 		

	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker	
	
	exec search.sp_SetEntityChanged
		@TypeId		= 1 --Inventory
		,@EntityIds = @inventoryIds
		,@Invoker	= @invoker


END
GO

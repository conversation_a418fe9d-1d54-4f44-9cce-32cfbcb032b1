CREATE TABLE [dbo].[C_LOCATION_SEPARATOR] (
    [LOCATION_SEPARATOR_ID] INT           NOT NULL,
    [LOCATION_SEPARATOR_CD] VARCHAR (250) NOT NULL,
    CONSTRAINT [PK_C_LOCATION_SEPARATOR] PRIMARY KEY CLUSTERED ([LOCATION_SEPARATOR_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Stores possible separates for location name', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_LOCATION_SEPARATOR';


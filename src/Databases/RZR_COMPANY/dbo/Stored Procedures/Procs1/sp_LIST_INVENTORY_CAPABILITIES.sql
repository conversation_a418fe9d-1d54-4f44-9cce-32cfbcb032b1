-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_INVENTORY_CAPABILITIES] 
(@TERM nvarchar(max) = N''
 ,@COUNT int = 20
 ,@CAPABILITY_TYPE_ID bigint)
AS
BEGIN
	DECLARE @trimmed nvarchar(max) = LTRIM(RTRIM(@TERM))

	SELECT DISTINCT
		C.INVENTORY_CAPABILITY_ID		AS VALUE
		,C.INVENTORY_CAPABILITY_VALUE	AS LABEL
	FROM 
		D_INVENTORY_CAPABILITY C with (nolock)		
	WHERE C.INVENTORY_CAPABILITY_TYPE_ID = @CAPABILITY_TYPE_ID
	  AND C.INVENTORY_CAPABILITY_VALUE LIKE @trimmed
	  AND C.IS_DELETED = 0
	  AND C.IS_INACTIVE = 0

END
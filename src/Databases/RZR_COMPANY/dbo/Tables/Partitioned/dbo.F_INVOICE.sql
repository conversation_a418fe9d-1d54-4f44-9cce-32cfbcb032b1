CREATE TABLE [dbo].[F_INVOICE] (
    [INVOICE_ID]              BIGINT         IDENTITY (1, 1) NOT NULL,
    [DATE_CREATED]            DATETIME       NOT NULL,
    [ORDER_ID]                BIGINT         NULL,
    [REP_USER_ID]             BIGINT         NULL,
    [AMOUNT_DUE]              MONEY          CONSTRAINT [DF_F_INVOICE_AMOUNT_DUE] DEFAULT ((0.00)) NOT NULL,
    [STATUS_ID]               INT            NULL,
    [IS_VOIDED]               BIT            CONSTRAINT [DF_F_INVOICE_IS_FULLY_PAID] DEFAULT ((0)) NOT NULL,
    [PAID_DATE]               DATETIME       NULL,
    [PAID_AMOUNT]             MONEY          CONSTRAINT [DF_F_INVOICE_INVOICE_PAID_AMOUNT] DEFAULT ((0.00)) NOT NULL,
    [ORIGINAL_AMOUNT]         MONEY          NOT NULL,
    [INVOICE_TYPE_ID]         INT            CONSTRAINT [DF_F_INVOICE_INVOICE_TYPE_ID] DEFAULT ((1)) NOT NULL,
    [MISC_CHARGE]             MONEY          CONSTRAINT [DF_F_INVOICE_MISC_CHARGE] DEFAULT ((0)) NOT NULL,
    [IS_EXPENSE_INCLUDED]     BIT            CONSTRAINT [DF_F_INVOICE_IS_EXPENSE_INCLUDED] DEFAULT ((0)) NOT NULL,
    [IS_PAID]                 BIT            CONSTRAINT [DF_F_INVOICE_IS_PAID] DEFAULT ((0)) NOT NULL,
    [SCHEDULE_DT]             DATETIME       NULL,
    [IS_ADDITIONAL]           BIT            CONSTRAINT [DF_F_INVOICE_IS_ADDITIONAL] DEFAULT ((0)) NULL, --Is created for an amount decrease
    [TIED_INVOICE_ID]         BIGINT         NULL,
    [INVOICE_CONSOLIDATED_ID] BIGINT         NULL,
    [TEMP_COLUMN]             BIGINT         NULL,
    [IS_DELETED]              BIT            CONSTRAINT [DF_F_INVOICE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]             VARCHAR (150)  NOT NULL,
    [INSERTED_DT]             DATETIME       NOT NULL,
    [UPDATED_BY]              VARCHAR (250)  NULL,
    [UPDATED_DT]              DATETIME       NULL,
    [DELETED_BY]              VARCHAR (150)  NULL,
    [DELETED_DT]              DATETIME       NULL,
    [SELF_ID]                 AS             ([INVOICE_ID]),
    [SELF_NAME]               AS             ([AUTO_NAME]),
    [SELF_CREATE_DATE]        AS             (isnull([DATE_CREATED],'2000-01-01')),
    [PARENT_ID]               AS             (CONVERT([bigint],isnull(NULL,(-1)),(0))),
    [AUTO_NAME]               NVARCHAR (250) NULL,
    [KidNumber]               NVARCHAR (250) NULL,
    [FINALIZED_DT]            DATETIME       NULL,
    [SENDER_USER_ID]          INT            NULL,
    [SEND_INVOICE_DT]         DATETIME       NULL,
    [ExternalId]              NVARCHAR (350) NULL,
    [IsInSyncWithExternal]	  BIT			 CONSTRAINT [DF_F_INVOICE_IsInSyncWithExternal]  DEFAULT ((0))  NOT NULL,
	[CustomerId]			  BIGINT		 NULL,
    CONSTRAINT [PK_F_INVOICE]								PRIMARY KEY NONCLUSTERED ([INVOICE_ID] ASC) on [PRIMARY],
    CONSTRAINT [FK_F_INVOICE_C_INVOICE_TYPE]				FOREIGN KEY ([INVOICE_TYPE_ID])			REFERENCES [dbo].[C_INVOICE_TYPE] ([INVOICE_TYPE_ID]),
    CONSTRAINT [FK_F_INVOICE_C_SALES_ORDER_INVOICE_STATUS]	FOREIGN KEY ([STATUS_ID])				REFERENCES [dbo].[C_SALES_ORDER_INVOICE_STATUS] ([INVOICE_STATUS_ID]),
    CONSTRAINT [FK_F_INVOICE_F_INVOICE]						FOREIGN KEY ([TIED_INVOICE_ID])			REFERENCES [dbo].[F_INVOICE] ([INVOICE_ID]),
    CONSTRAINT [FK_F_INVOICE_F_INVOICE_CONSOLIDATED]		FOREIGN KEY ([INVOICE_CONSOLIDATED_ID]) REFERENCES [dbo].[F_INVOICE_CONSOLIDATED] ([INVOICE_CONSOLIDATED_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_INVOICE_tb_User_REP_USER_ID]			FOREIGN KEY ([REP_USER_ID])				REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_INVOICE_F_CUSTOMER]					FOREIGN KEY ([CustomerId])				REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID])
) on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID)
go

CREATE unique CLUSTERED INDEX ix__F_INVOICE__ps_INVOICE_TYPE_ID
    ON [dbo].[F_INVOICE] (INVOICE_TYPE_ID, INVOICE_ID)
on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_F_INVOICE__IS_VOIDED__INVOICE_TYPE_ID]
    ON [dbo].[F_INVOICE]([IS_VOIDED] ASC, [INVOICE_TYPE_ID] ASC)
    INCLUDE([INVOICE_ID], [DATE_CREATED], [ORDER_ID], [REP_USER_ID], [AMOUNT_DUE], [PAID_AMOUNT], [STATUS_ID], [PAID_DATE], [ORIGINAL_AMOUNT], [MISC_CHARGE])
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO


CREATE NONCLUSTERED INDEX [IX_INVOICE_TYPE_ID_SALES_ORDER_ID_VOIDED]
    ON [dbo].[F_INVOICE]([INVOICE_TYPE_ID] ASC, [ORDER_ID] ASC, [IS_VOIDED] ASC, PAID_DATE)
    INCLUDE([INVOICE_ID], [INSERTED_DT], [STATUS_ID], PAID_AMOUNT,[DATE_CREATED],[REP_USER_ID], [AMOUNT_DUE],[ORIGINAL_AMOUNT],[INVOICE_CONSOLIDATED_ID],[IS_DELETED],[SELF_NAME])
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO


CREATE UNIQUE NONCLUSTERED INDEX [UIX__F_INVOICE__SALES_ORDER_ID__INVOICE_TYPE_ID__IS_VOIDED]
	ON [dbo].[F_INVOICE]([ORDER_ID] ASC, [INVOICE_TYPE_ID] ASC, [IS_VOIDED] ASC)
	INCLUDE([INVOICE_ID], [INSERTED_DT])
	WHERE ([INVOICE_TYPE_ID] = (1) AND [IS_VOIDED] = (0) AND [ORDER_ID] IS NOT NULL)
	ON ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO


CREATE NONCLUSTERED INDEX [IX_F_INVOICE]
    ON [dbo].[F_INVOICE]([ORDER_ID] ASC, [IS_VOIDED] ASC, [INVOICE_TYPE_ID] ASC, [IS_DELETED] ASC)
    INCLUDE([AMOUNT_DUE], [PAID_AMOUNT])
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO


CREATE NONCLUSTERED INDEX [IX_STATUS_ID]
    ON [dbo].[F_INVOICE]([STATUS_ID] ASC)
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_SALES_ORDER_ID]
    ON [dbo].[F_INVOICE]([ORDER_ID] ASC, INVOICE_TYPE_ID, [STATUS_ID], IS_PAID, [IS_VOIDED], IS_ADDITIONAL)
	INCLUDE([INVOICE_ID])WHERE([INVOICE_TYPE_ID]=(1))
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_PO_ORDER_ID]
    ON [dbo].[F_INVOICE]([ORDER_ID] ASC, INVOICE_TYPE_ID, [STATUS_ID], IS_PAID, [IS_VOIDED], IS_ADDITIONAL)
	INCLUDE([INVOICE_ID])WHERE ([INVOICE_TYPE_ID]=(2))
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_RMA_ORDER_ID]
    ON [dbo].[F_INVOICE]([ORDER_ID] ASC, INVOICE_TYPE_ID, [STATUS_ID], IS_PAID, [IS_VOIDED], IS_ADDITIONAL)
	INCLUDE([INVOICE_ID])WHERE ([INVOICE_TYPE_ID]=(3))
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_INSERTED_DT]
    ON [dbo].[F_INVOICE]([INSERTED_DT] ASC)
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts

GO
CREATE NONCLUSTERED INDEX [IX_F_INVOICE_TIED_INVOICE_ID]
	ON [dbo].[F_INVOICE] ([TIED_INVOICE_ID])
	INCLUDE ([INVOICE_ID])
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX_F_INVOICE_FOR_FINANCE_PAYMENTS]
    ON [dbo].[F_INVOICE]([IS_VOIDED] ASC, [INVOICE_TYPE_ID] ASC, [IS_DELETED] ASC, [STATUS_ID] ASC, [ORIGINAL_AMOUNT] ASC)
    INCLUDE([INVOICE_ID], [ORDER_ID])
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO

CREATE NONCLUSTERED INDEX [IX__F_INVOICE__ORDER_ID]
	ON [dbo].[F_INVOICE] ([ORDER_ID], INVOICE_TYPE_ID, IS_VOIDED)
	INCLUDE (INVOICE_ID, [INVOICE_CONSOLIDATED_ID],  REP_USER_ID, INSERTED_DT)
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts
GO
CREATE NONCLUSTERED INDEX [IX_F_INVOICE__INVOICE_CONSOLIDATED]
    ON [dbo].[F_INVOICE]([INVOICE_CONSOLIDATED_ID] ASC, [INVOICE_TYPE_ID])
    INCLUDE([INVOICE_ID], [ORDER_ID], DATE_CREATED)
	on ps_Invoice_InvoiceTypeId(INVOICE_TYPE_ID) -- create all indexes for partitioned tables manually in Post-scripts


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Indicates that this invoice includes the expenses link Shipping Cost, Misc Charge, Tax and so on', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_INVOICE', @level2type = N'COLUMN', @level2name = N'IS_EXPENSE_INCLUDED';


GO
-- =============================================
-- Author:		I.Orobets
-- Create date: 05/13/2016
-- Description:	Logging Void Invoice FOR SALES ORDERS and PURCHASE ORDERS
-- =============================================
CREATE TRIGGER [dbo].[trg_INVOICE_AFTER_UPDATE_LOG]
    ON  [dbo].[F_INVOICE]
    AFTER UPDATE
AS 
BEGIN

	INSERT INTO [dbo].[F_LOG_ACTION_DATA]
       ([ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE])
	SELECT
		CASE
			WHEN SII.ITEM_INVENTORY_ID IS NOT NULL THEN 15
			WHEN PII.ITEM_INVENTORY_ID IS NOT NULL THEN 18
		END																	AS [ACTION_ID]		-- Invoiced Voided
	    ,9																	AS [ENTITY_TYPE_ID]	-- Receive Inventory
		,ISNULL(SII.ITEM_INVENTORY_ID, PII.ITEM_INVENTORY_ID)				AS [ENTITY_KEY_VALUE]
		,ISNULL(SII.ITEM_INVENTORY_UNIQUE_ID, PII.ITEM_INVENTORY_UNIQUE_ID)	AS [ENTITY_AUTO_NAME]
		,ISNULL(ISNULL(SII.MODIFIER_USER_ID, SII.AUTHOR_USER_ID), ISNULL(PII.MODIFIER_USER_ID, PII.AUTHOR_USER_ID))	AS [USER_ID]
		,ISNULL(ISNULL(SII.UPDATED_BY_IP, SII.INSERTED_BY_IP), ISNULL(PII.UPDATED_BY_IP, PII.INSERTED_BY_IP))		AS [USER_IP]
		,'trg_INVOICE_AFTER_UPDATE'											AS [SOURCE]
	FROM INSERTED					I
	INNER JOIN	DELETED				D
		ON I.INVOICE_ID = D.INVOICE_ID
	LEFT JOIN F_SALES_ORDER			SO	WITH (NOLOCK)
		ON I.INVOICE_TYPE_ID = 1 -- SALES
		AND SO.SALES_ORDER_ID = I.ORDER_ID
	LEFT JOIN F_SALES_ORDER_ITEM	SOI	WITH (NOLOCK)
		ON SOI.SALES_ORDER_ID = SO.SALES_ORDER_ID AND SOI.ITEM_INVENTORY_ID IS NOT NULL	
	LEFT JOIN F_ITEM_INVENTORY		SII	WITH (NOLOCK)
		ON SOI.ITEM_INVENTORY_ID = SII.ITEM_INVENTORY_ID
	LEFT JOIN F_PURCHASE_ORDER		PO	WITH (NOLOCK)
		ON I.INVOICE_TYPE_ID = 2 -- PURCHASE
		AND PO.PURCHASE_ORDER_ID = I.ORDER_ID	
	LEFT JOIN F_PURCHASE_ORDER_ITEM	POI	WITH (NOLOCK)
		ON POI.PURCHASE_ORDER_ID = PO.PURCHASE_ORDER_ID AND POI.INVENTORY_ITEM_ID IS NOT NULL	
	LEFT JOIN F_ITEM_INVENTORY		PII	WITH (NOLOCK)
		ON POI.INVENTORY_ITEM_ID = PII.ITEM_INVENTORY_ID
	WHERE I.IS_VOIDED = 1 AND I.IS_VOIDED != D.IS_VOIDED 
		AND (SOI.ITEM_INVENTORY_ID IS NOT NULL
					OR POI.INVENTORY_ITEM_ID IS NOT NULL)
		
END
GO
CREATE TRIGGER [dbo].[trg_INVOICE_AFTER_INSERT_AUTO_NAME]
    ON  [dbo].[F_INVOICE]
    AFTER INSERT
AS 
BEGIN
    
	DECLARE @Ids dbo.[bigint_ID_ARRAY]
		,@INVOICE_ID bigint
		,@Results XML
		,@t_results dbo.[bigint_PARE_ARRAY]
	
	INSERT INTO @Ids
	SELECT INVOICE_ID FROM inserted WHERE INVOICE_TYPE_ID = 1
	AND [AUTO_NAME] IS NULL
	IF EXISTS(SELECT TOP(1) 1 FROM  @Ids)
	BEGIN
		SET @Results = NULL
		exec dbo.sp_GetIndentityValue
			@TABLE_NAME = 'F_INVOICE_AR',
			@Ids = @Ids,
			@Results = @Results OUT
		INSERT INTO @t_results(ID, value)
		select --row.value('@ID', 'bigint')
			t.row.value('@Id', 'bigint') as Id
			,t.row.value('@Value', 'bigint') as Value
			from @Results.nodes('/root/row') t(row)
		DELETE FROM @Ids
	END

	INSERT INTO @Ids
	SELECT INVOICE_ID FROM inserted WHERE INVOICE_TYPE_ID = 2
	AND [AUTO_NAME] IS NULL
	IF EXISTS(SELECT TOP(1) 1 FROM  @Ids) 
	BEGIN
		SET @Results = NULL
		exec dbo.sp_GetIndentityValue
			@TABLE_NAME = 'F_INVOICE_AP',
			@Ids = @Ids,
			@Results = @Results OUT
		INSERT INTO @t_results(ID, value)
		select --row.value('@ID', 'bigint')
			t.row.value('@Id', 'bigint') as Id
			,t.row.value('@Value', 'bigint') as Value
			from @Results.nodes('/root/row') t(row)
		DELETE FROM @Ids
	END

	INSERT INTO @Ids
	SELECT INVOICE_ID FROM inserted WHERE INVOICE_TYPE_ID = 3
	AND [AUTO_NAME] IS  NULL
	IF EXISTS(SELECT TOP(1) 1 FROM  @Ids) 
	BEGIN
		SET @Results = NULL
		exec dbo.sp_GetIndentityValue
			@TABLE_NAME = 'F_INVOICE_RMA',
			@Ids = @Ids,
			@Results = @Results OUT
		INSERT INTO @t_results(ID, value)
		select --row.value('@ID', 'bigint')
			t.row.value('@Id', 'bigint') as Id
			,t.row.value('@Value', 'bigint') as Value
			from @Results.nodes('/root/row') t(row)
		DELETE FROM @Ids
	END

    DECLARE @prefixInvoice NVARCHAR(10);
	DECLARE @prefixRmaInvoice NVARCHAR(10);

	SELECT TOP(1) 
		 @prefixInvoice = IIF([PREFIX_INVOICE] = N'', N'', [PREFIX_INVOICE] + N'-'),
		 @prefixRmaInvoice = IIF([PREFIX_RMA_INVOICE] = N'', N'', [PREFIX_RMA_INVOICE] + N'-')
	FROM [dbo].[U_SYSTEM_SETTINGS] WITH (NOLOCK);

	UPDATE I
		SET [AUTO_NAME] = IIF([INVOICE_TYPE_ID] = 3, @prefixRmaInvoice, @prefixInvoice) +  CAST([VALUE] AS NVARCHAR)
	FROM [dbo].[F_INVOICE] AS I WITH(ROWLOCK)
	INNER JOIN @t_results t
		ON t.[ID] = I.[INVOICE_ID]	
END
GO
-- =============================================
-- Author:		I.Orobets
-- Create date: 06/22/2016
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_INVOICE_AFTER_INSERT_NOTIFICATION]
    ON  [dbo].[F_INVOICE]
    AFTER INSERT
AS 
BEGIN
    
    DECLARE @params dbo.NOTIFICATION_QUEUE_PARAMS
	INSERT INTO @params (NAME, VALUE) 
	SELECT
		'INVOICE_ID'
		,INVOICE_ID
	FROM INSERTED
	WHERE INVOICE_TYPE_ID = 1

	DECLARE @CustomerId BIGINT = (SELECT CUSTOMER_ID 
									FROM F_SALES_ORDER SO WITH (NOLOCK) 
									WHERE SO.SALES_ORDER_ID = (SELECT TOP (1) ORDER_ID 
																FROM INSERTED 
																WHERE INVOICE_TYPE_ID = 1)) 

	----add notifications about item change
	EXEC sp_ADD_NOTIFICATION_TO_QUEUE
		@C_NOTIFICATION_TYPE_ID = 25 -- Invoice of Sales Order is Generated
		,@C_PARAMS = @params
		,@C_ENTITY_CUSTOMER_ID = @CustomerId
		
END
GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This table stores all invoices types', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_INVOICE';

GO
-- =============================================
-- Author:		A.Maslov
-- Create date: 04/07/2022
-- Description:	<Description,,>
-- =============================================

CREATE TRIGGER [dbo].[trg_INVOICE_LOG_DATA]
    ON  [dbo].[F_INVOICE]
    AFTER UPDATE
AS 
BEGIN
  	DECLARE @TAIL NVARCHAR(255) = N'"';
	DECLARE @DEF  NVARCHAR(255) = N'-';
	DECLARE @C_OBJECT_NAME nvarchar(255) = 'trg_INVOICE_LOG_DATA'
  
  INSERT INTO F_LOG_DATA(	 
	  [SOURCE]  
	  ,[USER_ID]
	  ,[USER_IP]
	  ,OPERATION_NAME
	  ,ENTITY_TYPE_ID
	  ,ENTITY_KEY_VALUE
	  ,ENTITY_AUTO_NAME
	  ,[CHANGES]
    )
    SELECT DISTINCT
	    @C_OBJECT_NAME
		,new.[USER_ID]
	   ,new.[USER_IP]
	   ,N'Updated'
	   ,26 -- Invoice
	   ,NEW.ENTITY_KEY_VALUE
	   ,NEW.ENTITY_AUTO_NAME
	   ,ISNULL(NEW.[CHANGES], '')
    FROM (
	   SELECT 
		  INVOICE_ID
		  ,[USER_ID]
		  ,[USER_IP]
		  ,ENTITY_KEY_VALUE
		  ,ENTITY_AUTO_NAME
			,NEW.SALES_ORDER_ID
			--,NEW.PURCHASE_ORDER_ID
		  ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT 
											NEW.DESCRIPTION_VOIDED,	
											NEW.DESCRIPTION_AMOUNT,
											NEW.ITEM_MASTER_NAME,
											NEW.STATUS_INVOICE,
											NEW.STATUS_PARTIALLY_INVOICE

											
		  FOR XML
		  PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
	   FROM (
		  SELECT  DISTINCT
			 -- Fixed columns
				I.INVOICE_ID
				,I.ORDER_ID					as ORDER_ID
				,so.SALES_ORDER_ID			as SALES_ORDER_ID
				,ISNULL(SII.MODIFIER_USER_ID, SO.INSERTED_BY_USER)		AS [USER_ID]
				,ISNULL(SII.UPDATED_BY_IP, SII.INSERTED_BY_IP)				AS [USER_IP]
				, SO.SALES_ORDER_ID		AS [ENTITY_KEY_VALUE]
				,ISNULL(I.AUTO_NAME,'-')									AS [ENTITY_AUTO_NAME]
			 -- XML columns	
			 ,CASE
					WHEN D.IS_VOIDED					<> I.IS_VOIDED
					THEN N'AR Invoice has been voided '
					ELSE NULL
				END AS  DESCRIPTION_VOIDED

			,CASE
					WHEN D.AMOUNT_DUE	<> I.AMOUNT_DUE AND I.STATUS_ID != 4 AND I.STATUS_ID != 5	--Partially Paid AND Fully paid
					THEN N'The AR invoice amount has been changed from ' +
						N'"' + CAST(D.ORIGINAL_AMOUNT AS VARCHAR(255)) + N'" to ' +
						N'"' + CAST(I.ORIGINAL_AMOUNT AS VARCHAR(255)) + @TAIL
					ELSE NULL
				END AS  DESCRIPTION_AMOUNT
				,CASE
					WHEN D.STATUS_ID	!= I.STATUS_ID AND I.STATUS_ID != 4 -- for status Invoice
					THEN 'The invoice status has been changed to - "' +  SOIS.STATUS_NAME  +  '"'
					ELSE NULL
				END		
				AS  STATUS_INVOICE
				,CASE
					WHEN I.STATUS_ID = 4 -- for status Invoice
					THEN 'Payment #'+ CAST(FIP.PAYMENT_ID AS VARCHAR(255)) +
					' with amount "'+ CAST(FIP.AMOUNT AS VARCHAR(255)) +
					'" has been applied. The AR invoice Amount Due has been changed from "'+ CAST(D.AMOUNT_DUE AS VARCHAR(255)) +
					'" to "'+ CAST(I.AMOUNT_DUE AS VARCHAR(255)) +'"'
					ELSE NULL
				END		
				AS  STATUS_PARTIALLY_INVOICE
					  ,CASE
			 WHEN D.AUTO_NAME <> I.AUTO_NAME
			 THEN N'Name was ' + 
				N'"' + D.AUTO_NAME + N'" is ' +
				N'"' + I.AUTO_NAME + @TAIL
			 ELSE NULL
		  END										AS ITEM_MASTER_NAME
		  FROM	INSERTED							I LEFT JOIN DELETED	D on I.INVOICE_ID = D.INVOICE_ID
			
			left JOIN F_SALES_ORDER					SO		WITH(NOLOCK)	ON I.INVOICE_TYPE_ID = 1 and  SO.SALES_ORDER_ID = I.ORDER_ID	-- for Sales Orders
			LEFT JOIN F_SALES_ORDER_ITEM			SOI	WITH (NOLOCK)		ON SOI.SALES_ORDER_ID = SO.SALES_ORDER_ID AND SOI.ITEM_INVENTORY_ID IS NOT NULL	
			LEFT JOIN F_ITEM_INVENTORY				SII	WITH (NOLOCK)		ON SOI.ITEM_INVENTORY_ID = SII.ITEM_INVENTORY_ID
			LEFT JOIN dbo.C_SALES_ORDER_INVOICE_STATUS SOIS WITH(NOLOCK)	ON I.STATUS_ID = SOIS.INVOICE_STATUS_ID 
			LEFT JOIN dbo.F_INVOICE_PAYMENT			FIP WITH(NOLOCK)		ON I.INVOICE_ID = FIP.INVOICE_ID AND FIP.INVOICE_PAYMENT_ID = (
				SELECT TOP(1) INVOICE_PAYMENT_ID
				FROM dbo.F_INVOICE_PAYMENT
				WHERE INVOICE_ID = FIP.INVOICE_ID
				ORDER BY 1 DESC

			)
			
				where  D.AUTO_NAME	   != I.AUTO_NAME
			or D.AMOUNT_DUE					!= I.AMOUNT_DUE
			or D.IS_VOIDED					!= I.IS_VOIDED
			or D.IS_PAID					!= I.IS_PAID
			or D.ORIGINAL_AMOUNT			!= I.ORIGINAL_AMOUNT
			or D.STATUS_ID					!= I.STATUS_ID
				
	   ) NEW   where NEW.ENTITY_KEY_VALUE is not null and NEW.USER_ID is not null
    ) NEW 
   
END
GO
-- =============================================
-- Author:		A.Maslov
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_INVOICE_LOG_DATA_FOR_PO]
    ON  [dbo].[F_INVOICE]
    AFTER UPDATE
AS 
BEGIN
  	DECLARE @TAIL NVARCHAR(255) = N'"';
	DECLARE @DEF  NVARCHAR(255) = N'-';
	DECLARE @C_OBJECT_NAME nvarchar(255) = 'trg_INVOICE_LOG_DATA_FOR_PO'
  
  INSERT INTO F_LOG_DATA(	 
	  [SOURCE]  
	  ,USER_ID
	  ,USER_IP
	  ,OPERATION_NAME
	  ,ENTITY_TYPE_ID
	  ,ENTITY_KEY_VALUE
	  ,ENTITY_AUTO_NAME
	  ,[CHANGES]
    )
    SELECT
	    @C_OBJECT_NAME
		,new.USER_ID
	   ,new.USER_IP
	   ,N'Updated'
	   ,26 -- Invoice
	   ,NEW.ENTITY_KEY_VALUE
	   ,NEW.ENTITY_AUTO_NAME
	   ,ISNULL(NEW.[CHANGES], '')
    FROM (
	   SELECT
		  INVOICE_ID
		  ,USER_ID
		  ,USER_IP
		  ,ENTITY_KEY_VALUE
		  ,ENTITY_AUTO_NAME
		  ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT 
											NEW.DESCRIPTION_VOIDED,	
											NEW.DESCRIPTION_PAID,
											NEW.DESCRIPTION_AMOUNT,
											NEW.ITEM_MASTER_NAME

											
		  FOR XML
		  PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
	   FROM (
		  SELECT DISTINCT
			 -- Fixed columns
				I.INVOICE_ID
				,I.ORDER_ID														AS ORDER_ID
				,ISNULL(POI.UPDATED_BY_ID, PO.USER_ID)							AS [USER_ID]
				,ISNULL(PII.UPDATED_BY_IP, PII.INSERTED_BY_IP)					AS [USER_IP]
				,ISNULL(ISNULL(PII.ITEM_INVENTORY_ID, po.PURCHASE_ORDER_ID),1)	AS [ENTITY_KEY_VALUE]
				,ISNULL(I.AUTO_NAME,'-')										AS [ENTITY_AUTO_NAME]
			 -- XML columns	
				,CASE
					WHEN D.IS_PAID					<> I.IS_PAID
					THEN N'AP Invoice has been applied was ' + 
						N'"' + CAST(D.IS_PAID AS VARCHAR(255)) + N'" is ' +
						N'"' + CAST(I.IS_PAID AS VARCHAR(255)) + @TAIL
					ELSE NULL
				END AS  DESCRIPTION_PAID

				,CASE
					WHEN D.IS_VOIDED					<> I.IS_VOIDED
					THEN N'AP Invoice has been voided '
					ELSE NULL
				END AS  DESCRIPTION_VOIDED

				,CASE
					WHEN D.AMOUNT_DUE					<> I.AMOUNT_DUE
					THEN N'The AP invoice amount has been changed was ' + 
						N'"' + CAST(D.ORIGINAL_AMOUNT AS VARCHAR(255))  + N'" is ' + N'qwq '+
						N'"' + CAST(I.ORIGINAL_AMOUNT AS VARCHAR(255)) + @TAIL
					ELSE NULL
				END AS  DESCRIPTION_AMOUNT
					  ,CASE
			 WHEN D.AUTO_NAME <> I.AUTO_NAME
			 THEN N'Name was ' + 
				N'"' + D.AUTO_NAME + N'" is ' +
				N'"' + I.AUTO_NAME + @TAIL
			 ELSE NULL
		  END										AS ITEM_MASTER_NAME
		  FROM	INSERTED							I
			Left JOIN F_PURCHASE_ORDER			PO		WITH(NOLOCK)	ON I.INVOICE_TYPE_ID = 2  AND PO.PURCHASE_ORDER_ID = I.ORDER_ID	-- for Purchase orders
			left JOIN DELETED	D										ON I.INVOICE_ID = D.INVOICE_ID
			LEFT JOIN F_PURCHASE_ORDER_ITEM		POI	WITH (NOLOCK)		ON POI.PURCHASE_ORDER_ID = PO.PURCHASE_ORDER_ID AND POI.INVENTORY_ITEM_ID IS NOT NULL	
			LEFT JOIN F_ITEM_INVENTORY			PII	WITH (NOLOCK)		ON POI.INVENTORY_ITEM_ID = PII.ITEM_INVENTORY_ID
			
			where  D.AUTO_NAME				!= I.AUTO_NAME
			or D.AMOUNT_DUE					!= I.AMOUNT_DUE
			or D.IS_VOIDED					!= I.IS_VOIDED
			or D.IS_PAID					!= I.IS_PAID
			or D.ORIGINAL_AMOUNT			!= I.ORIGINAL_AMOUNT
	   ) NEW  where NEW.ENTITY_KEY_VALUE is not null and  NEW.USER_ID is not null
    ) NEW 
   
END
GO
-- =============================================
-- Author:		V.Burykin
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_INVOICE_AFTER_UPDATE_FOR_SYNC]
   ON  [dbo].[F_INVOICE]
   AFTER UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

	IF (UPDATE(AMOUNT_DUE) or UPDATE(STATUS_ID) or UPDATE(IS_VOIDED) or UPDATE(PAID_AMOUNT) or
		 UPDATE(ORIGINAL_AMOUNT))
	BEGIN
		UPDATE INV 
		SET IsInSyncWithExternal = 0
		FROM
			(SELECT INVOICE_ID, AMOUNT_DUE, STATUS_ID, IS_VOIDED, PAID_AMOUNT, ORIGINAL_AMOUNT
			 from INSERTED I
			 EXCEPT
			 select INVOICE_ID, AMOUNT_DUE, STATUS_ID, IS_VOIDED, PAID_AMOUNT, ORIGINAL_AMOUNT
			 from DELETED D) as r
		 INNER JOIN [dbo].[F_INVOICE] as INV WITH(ROWLOCK)
		 ON INV.INVOICE_ID = r.INVOICE_ID AND INV.IS_DELETED = 0 AND INV.IsInSyncWithExternal = 1
	END
END
GO

DISABLE TRIGGER [dbo].[trg_INVOICE_LOG_DATA_FOR_PO]
    ON  [dbo].[F_INVOICE];
GO
GO
DISABLE TRIGGER [dbo].[trg_INVOICE_LOG_DATA]
    ON  [dbo].[F_INVOICE];
GO

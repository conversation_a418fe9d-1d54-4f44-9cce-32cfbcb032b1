
-- =============================================
-- Author:	 <Author,,Name>
-- Create date: <Create Date,,>
-- Description: <Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_MIGRATION_INSERT_NEW_PAGE_GROUP]
    @GROUP_NAME	    NVARCHAR(512) = NULL,
    @GROUP_DESC	    NVARCHAR(512) = NULL,
    @EVERYONE_CAN_READ  BIT		   = 0,
    @INSERT_MAIN_PAGE   BIT		   = 1,
	@SORT_ORDER			INT			= 1
AS
BEGIN
    SET @GROUP_NAME	= LTRIM(RTRIM(@GROUP_NAME));
    SET @GROUP_DESC = LTRIM(RTRIM(@GROUP_DESC));

    IF(ISNULL(@GROUP_NAME, '') = '') BEGIN
	   SELECT N'@GROUP_NAME is required'
	   RETURN
    END

    DECLARE
	   @ENTITY_TYPE_ID  INT		  = 1, -- page group
	   @NOW			DATETIME	  = GETUTCDATE(),
	   @GROUP_ID	     BIGINT	  = NULL,
	   @ENTITY_ID		BIGINT	  = NULL,
	   @SP_NAME		NVARCHAR(50)= N'sp_MIGRATION_INSERT_NEW_PAGE_GROUP'

    SET XACT_ABORT ON
    BEGIN TRANSACTION

	   -- NEED TO HAVE A CATEGORY TYPE
	   SELECT 
		  @GROUP_ID = [PERMISSION_CATEGORY_ID]
	   FROM D_PERMISSION_CATEGORY
	   WHERE LTRIM(RTRIM([PERMISSION_CATEGORY_CD])) = @GROUP_NAME
	   IF (@GROUP_ID IS NULL) BEGIN
		  SET @GROUP_ID = ISNULL((SELECT MAX(PERMISSION_CATEGORY_ID) FROM D_PERMISSION_CATEGORY), 0) + 1
		  
		  INSERT INTO D_PERMISSION_CATEGORY (
			 PERMISSION_CATEGORY_ID,
			 PERMISSION_CATEGORY_CD,
			 INSERTED_DT,
			 INSERTED_BY
		  ) VALUES (
			 @GROUP_ID,
			 @GROUP_NAME,
			 @NOW,
			 1 --main user
		  )

	   END

	   -- Page group may has been created
	   SELECT 
		  @ENTITY_ID = PERMISSION_CATEGORY_ENTITY_ID
	   FROM D_PERMISSION_CATEGORY_ENTITY
	   WHERE LTRIM(RTRIM(PERMISSION_CATEGORY_ENTITY_CD)) = @GROUP_NAME
	     AND ENTITY_TYPE_ID		  = @ENTITY_TYPE_ID
	     AND PERMISSION_CATEGORY_ID = @GROUP_ID

	   IF (@ENTITY_ID IS NOT NULL) BEGIN
		  SELECT N'Page Group "'+ @GROUP_NAME + N'" is already added.'
	   END ELSE
	   BEGIN
		  -- Insert a page/an entity
		  SET @ENTITY_ID = ISNULL((SELECT MAX(PERMISSION_CATEGORY_ENTITY_ID) FROM D_PERMISSION_CATEGORY_ENTITY), 0) + 1
			
		  INSERT INTO D_PERMISSION_CATEGORY_ENTITY (
			 [PERMISSION_CATEGORY_ENTITY_ID],
			 [PERMISSION_CATEGORY_ID],
			 [ENTITY_TYPE_ID],
			 [PERMISSION_CATEGORY_ENTITY_CD],
			 [PERMISSION_CATEGORY_ENTITY_DESC],
			 SORT_ORDER	,
			 [INSERTED_DT],
			 [INSERTED_BY]
		  ) VALUES (
			 @ENTITY_ID,
			 @GROUP_ID,
			 @ENTITY_TYPE_ID,
			 @GROUP_NAME,
			 @GROUP_DESC,
			 @SORT_ORDER,
			 @NOW,
			 1 -- main user
		  )

		  IF (@EVERYONE_CAN_READ = 1) BEGIN
			 INSERT INTO F_PERMISSION_ROLE_CATEGORY_ACTIONS(
				 ROLE_ID,
				 PERMISSION_CATEGORY_ID,
				 PERMISSION_CATEGORY_ENTITY_ID,
				 PERMISSION_CATEGORY_ACTION_ID,
				 INSERTED_BY,
				 INSERTED_DT
			 ) SELECT DISTINCT
				R.UserRoleID,
				@GROUP_ID,
				@ENTITY_ID,
				1, -- read permission
				1, -- main user
				@NOW
			 FROM tb_UserRole	 R
		  END

		  SET @ENTITY_ID = NULL

		  IF (@INSERT_MAIN_PAGE = 1) BEGIN
			 DECLARE @T_ENTITY_IDS TABLE (ENTITY_ID_OF_PAGE BIGINT)
			 INSERT INTO @T_ENTITY_IDS
			 EXEC dbo.sp_MIGRATION_INSERT_NEW_PAGE
				@CATEGORY_ID		   = @GROUP_ID,
				@PAGE_NAME		    = @GROUP_NAME,
				@PAGE_DESC		    = @GROUP_NAME,
				@USE_GROUP_PERMISSIONS = 1

			 SET @ENTITY_ID = (SELECT TOP(1) ENTITY_ID_OF_PAGE FROM @T_ENTITY_IDS)
		  END

	   END

	   DECLARE @idoc INT
	   
    COMMIT TRANSACTION
    
    SELECT
	   @GROUP_ID	AS CATEGORY_ID_OF_PAGES_GROUP,
	   @ENTITY_ID	AS ENTITY_ID_OF_PAGES_GROUP,
	   @GROUP_NAME AS GROUP_NAME
END
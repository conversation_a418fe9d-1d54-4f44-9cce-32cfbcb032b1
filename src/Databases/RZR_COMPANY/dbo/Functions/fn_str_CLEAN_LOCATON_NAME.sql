-- =============================================
-- Author:		<O.Evseev>
-- Create date: <02/16/2016>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_CLEAN_LOCATON_NAME]
	(@C_LOCATON_NAME	NVARCHAR(512)
	,@C_LOCATION_ID		BIGINT = NULL)
RETURNS NVARCHAR(256)
AS
BEGIN

	SET @C_LOCATON_NAME = LTRIM(RTRIM(@C_LOCATON_NAME));

	-- IF HAS A PARENT LOCATION
	IF (ISNULL(@C_LOCATION_ID, 0) > 0 AND EXISTS (
		SELECT TOP(1) 1
		FROM F_LOCATION_DETAIL	FLD	WITH(NOLOCK)
		WHERE FLD.INVENTORY_CAPABILITY_ID = @C_LOCATION_ID
		  AND FLD.LOCATION_DETAIL_TIED_ID IS NOT NULL))
	BEGIN
		SET @C_LOCATON_NAME = (
			ISNULL(
				NULLIF(
					RTRIM(
						SUBSTRING(@C_LOCATON_NAME, 1, PATINDEX('% (%', @C_LOCATON_NAME))
					)
				, '')
			,@C_LOCATON_NAME)
		)
	END

		
	-- ELSE TRIM AND RETURN AS IS
	RETURN @C_LOCATON_NAME
END
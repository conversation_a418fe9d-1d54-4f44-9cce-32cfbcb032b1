-- =============================================
-- Author:		<PERSON>
-- Create date: 07/20/2017
-- Description:	Gets item master substitutes ids
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_ITEM_MASTER_SUBSTITUTES_IDS] 
(	
	@C_MASTER_ITEM_IDS		[dbo].[bigint_ID_ARRAY] READONLY
)
RETURNS TABLE 
AS
RETURN 
(
	SELECT
		 FIMS.ITEM_MASTER_ID
		,FIMS.SUBSTITUTE_ITEM_MASTER_ID
	FROM @C_MASTER_ITEM_IDS			C_IM
	INNER JOIN [dbo].[vw_F_ITEM_MASTER_SUBSTITUTES_ALL] FIMS WITH(NOLOCK)
		ON FIMS.ITEM_MASTER_ID	= C_IM.ID
)
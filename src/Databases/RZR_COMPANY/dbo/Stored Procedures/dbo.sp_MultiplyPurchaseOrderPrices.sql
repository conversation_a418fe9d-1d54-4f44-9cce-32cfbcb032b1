CREATE PROCEDURE [dbo].[sp_MultiplyPurchaseOrderPrices]
	@PurchaseOrderId	bigint,
	@Multiplier			DOUBLE PRECISION,
	@IsDebug			bit = 0
AS
BEGIN

	DECLARE @PROCESS_CD	NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
		   ,@UTC_NOW	DATETIME		= GETUTCDATE()
	
	if (@IsDebug=1)
	begin
		select 
			PRICE			
			,RECEIVED_PRICE	
			,InternalCost	
		from f_purchase_order_item fpoi with(rowlock)
		where purchase_order_id = @PurchaseOrderId
	end

	begin tran MultiplyPurchaseOrderPrices
	set xact_abort on

		-- the values should not change, po pick the order in the home currency, 
		update fpoi set 
			PRICE			= PRICE				* @Multiplier	
			,RECEIVED_PRICE	= RECEIVED_PRICE	* @Multiplier	
			,InternalCost	= InternalCost		* @Multiplier
			,UPDATED_BY		= @PROCESS_CD
			,UPDATED_DT		= @UTC_NOW
		from f_purchase_order_item fpoi with(rowlock)
		where purchase_order_id = @PurchaseOrderId
				
		-- update Item inventory original cost related to po items
		UPDATE fii WITH(ROWLOCK) SET
			fii.ITEM_INVENTORY_UNIT_COST_ORIGINAL	= fpoi.RECEIVED_PRICE
			,UPDATED_BY								= @PROCESS_CD
			,UPDATED_DT								= @UTC_NOW
		from f_purchase_order_item		fpoi with(rowlock)
		inner join dbo.F_ITEM_INVENTORY fii  with(rowlock)
			on fii.ITEM_INVENTORY_ID = fpoi.INVENTORY_ITEM_ID
		where fpoi.purchase_order_id = @PurchaseOrderId
			
		update fpo set
			SHIPPING_COST	= SHIPPING_COST * @Multiplier		
			,MISC_COST		= MISC_COST	    * @Multiplier	
			,UPDATED_BY		= @PROCESS_CD
			,UPDATED_DT		= @UTC_NOW
		from F_purchase_order fpo with(rowlock)
		where purchase_order_id = @PurchaseOrderId

		declare @openInvoiceId bigint = (
			select top(1)
				INVOICE_ID
			from F_INVOICE	I	WITH (NOLOCK)			
            WHERE I.INVOICE_TYPE_ID = 2 -- PO invoice type
              AND I.ORDER_ID        = @PurchaseOrderId
              AND I.PAID_AMOUNT     = 0 --Shouldn't change the amount of partially paid invoices
              --AND I.IS_PAID		= 0
              AND I.IS_VOIDED		= 0
		)

		EXEC [dbo].[sp_UPD_PURCHASE_ORDER_INVOICE_AMOUNT]
			@INVOICE_ID = @openInvoiceId

	commit tran MultiplyPurchaseOrderPrices
	
	if (@IsDebug=1)
	begin
		select 
			PRICE			
			,RECEIVED_PRICE	
			,InternalCost	
		from f_purchase_order_item fpoi with(rowlock)
		where purchase_order_id = @PurchaseOrderId
	end

END

CREATE TABLE [dbo].[F_ITEM_MASTER_SUBSTITUTE] (
    [ITEM_MASTER_SUBSTITUTE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_GROUP_ID]      BIGINT        NOT NULL,
    [ITEM_MASTER_ID]            BIGINT        NOT NULL,
    [INSERTED_BY]               VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_SUBSTITUTE_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]               DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_SUBSTITUTE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [INSERTED_BY_USER_ID]       BIGINT        NULL,
    [INSERTED_BY_USER_IP]       BIGINT        NULL,
    [UPDATED_BY]                VARCHAR (150) NULL,
    [UPDATED_DT]                DATETIME      NULL,
    [UPDATED_BY_USER_ID]        BIGINT        NULL,
    [UPDATED_BY_USER_IP]        BIGINT        NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_SUBSTITUTE] PRIMARY KEY CLUSTERED ([ITEM_MASTER_SUBSTITUTE_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_SUBSTITUTE_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [UC__F_ITEM_MASTER_SUBSTITUTE__ITEM_MASTER_ID] UNIQUE NONCLUSTERED ([ITEM_MASTER_ID] ASC)
);
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_SUBSTITUTE__ITEM_MASTER_GROUP_ID]
ON [dbo].[F_ITEM_MASTER_SUBSTITUTE] ([ITEM_MASTER_GROUP_ID])
INCLUDE ([ITEM_MASTER_ID])
GO




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'it stores substitutes for model. All models with the same group id are substitutes of each other.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_SUBSTITUTE';

GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_UNIQUE__F_ITEM_MASTER_SUBSTITUTE__ITEM_MASTER_ID]
ON [dbo].[F_ITEM_MASTER_SUBSTITUTE](
	[ITEM_MASTER_ID] ASC
)
INCLUDE(
	[ITEM_MASTER_GROUP_ID]
);
GO

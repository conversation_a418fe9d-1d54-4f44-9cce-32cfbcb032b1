CREATE TABLE [dbo].[F_USER_WORK_LOG] (
    [WORK_BIT_ID]        BIGINT         IDENTITY (1, 1) NOT NULL,
    [RECYCLING_ORDER_ID] BIGINT         NOT NULL,
    [USER_ID]            BIGINT         NOT NULL,
    [LOGGED_BY]          BIGINT         NOT NULL,
    [WORK_FROM]          DATETIME       NOT NULL,
    [WORK_MINUTES]       INT            NOT NULL,
    [DESCR]              NVARCHAR (500) NULL,
    [INSERTED_BY]        VARCHAR (256)  NOT NULL,
    [INSERTED_DT]        DATETIME       CONSTRAINT [DF_F_USER_WORK_LOG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]         VARCHAR (256)  NULL,
    [UPDATED_DT]         DATETIME       NULL,
    [DELETED_BY]         VARCHAR (256)  NULL,
    [DELETED_DT]         DATETIME       NULL,
    [BatchId]            BIGINT         NULL,
    [WorkersCount]       INT            NULL,
    [Division]           NVARCHAR (50)  NULL,
    CONSTRAINT [PK_F_USER_WORK_LOG] PRIMARY KEY CLUSTERED ([WORK_BIT_ID] ASC),
    CONSTRAINT [FK_F_USER_WORK_LOG_F_RECYCLING_ORDER] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_USER_WORK_LOG_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_USER_WORK_LOG_tb_User1] FOREIGN KEY ([LOGGED_BY]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_USER_WORK_LOG_F_BatchOfLots] FOREIGN KEY ([BatchId]) REFERENCES [recycling].[F_BatchOfLots] ([Id])
);


-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ORDERS_NEED_TO_MAGENTO_SHIPMENT]
	@MAGENTO_SHOP_ID INT
AS
BEGIN

	SELECT
		FSOS.[SALES_ORDER_ID]												AS [SalesOrderId]
		,ISNULL(SP.EXTERNAL_SHIPPING_PROVIDER_ID, FSOS.PROVIDER_ID + 1)		AS [ProviderId]
		,DSP.PROVIDER_ABBR													AS [ProviderAbbr]
		,DSP.PROVIDER_NAME													AS [ProviderName]
		,SP.TRACKING_NO														AS [TrackingNo]
		,MO.INCREMENТ_ID													AS [IncrementId]
		,MO.M_ORDER_ID														AS [MOrderId]
		,IIF(SP.[TRACKING_NO] = (
			SELECT MIN(FSP.TRACKING_NO)
			FROM [dbo].[F_SHIPPING_PACKAGE] AS FSP WITH (NOLOCK)
			WHERE FSP.[SHIPPING_ID] = FSOS.[SHIPPING_ID]
		), 1, 0)															AS [IsMasterTrack]
		,SP.[PACKAGE_ID]													AS [TrackSourceId]
		,MO.[M_PREVIOUS_SHIPMENT_ID]										AS [PreviousShipmentId]
		,SP.[MAGENTO_TRACK_ID]												AS [MagentoTrackId]
	FROM [dbo].[F_SHIPPING] AS fsos WITH(NOLOCK)
	INNER JOIN [dbo].[MAGENTO_ORDERS] AS MO WITH(NOLOCK)
		ON  mo.SALES_ORDER_ID	= fsos.SALES_ORDER_ID
		AND mo.SHOP_ID			= @MAGENTO_SHOP_ID
	INNER JOIN dbo.F_SHIPPING_PACKAGE			SP		WITH(NOLOCK)
		ON SP.SHIPPING_ID = FSOS.SHIPPING_ID		
	INNER JOIN D_SHIPPING_PROVIDER			DSP	 WITH(NOLOCK)
		ON DSP.PROVIDER_ID = ISNULL(SP.EXTERNAL_SHIPPING_PROVIDER_ID, FSOS.PROVIDER_ID + 1)
	WHERE NULLIF(LTRIM(SP.TRACKING_NO), N'') IS NOT NULL
		AND fsos.STATUS_ID = 3
		AND mo.M_SHIPMENT_ID IS NULL

END
CREATE PROCEDURE [dbo].[sp_DELETE_RECYCLING_ORDER] 
--declare
    @RECYCLING_ORDER_ID BIGINT = 11341,
    @USER_ID		    BIGINT,
	@IP					BIGINT = 0,
	@IS_DELETE_QUOTE	BIT = 0
AS
BEGIN

    IF (EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_ITEM WITH(NOLOCK) WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)) 
    BEGIN
		;THROW 51000, N'Failed to delete the Order(s) that had lots. Please delete the lots first', 1;   

	   RETURN;
    END

	IF (EXISTS(SELECT TOP(1) 1 FROM dbo.F_PURCHASE_ORDER WITH(ROWLOCK) WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
		OR EXISTS(SELECT TOP(1) 1 FROM dbo.F_SALES_ORDER WITH(ROWLOCK) WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	) 
    BEGIN
		;THROW 51000, N'Failed to delete the Order(s) that had related Order(s). Please undo settlement first', 1;  

	   	RETURN;
    END

	IF (EXISTS(SELECT TOP(1) 1 FROM [cp].[F_TakebackOrderRequest] tor WITH(ROWLOCK)
				inner join [dbo].[F_RECYCLING_ORDER_INBOUND] roi with (nolock)
					on tor.Id = roi.TakebackOrderRequestId
				WHERE roi.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)		
	) 
    BEGIN
		;THROW 51000, N'Failed to delete the Order(s) that had related Takeback Order(s).', 1;  

	   	RETURN;
    END

	IF (EXISTS(SELECT TOP(1) 1 FROM [recycling].[F_InboundOrderJob] j WITH(ROWLOCK)				
				WHERE j.RecyclingOrderId = @RECYCLING_ORDER_ID)		
	) 
    BEGIN
		;THROW 51000, N'Failed to delete the Order that has related Inbound Order Job. Please, use cancel instead.', 1;  

	   	RETURN;
    END

  
    BEGIN TRY
	   SET XACT_ABORT ON
	   BEGIN TRANSACTION
		  IF ((SELECT TOP(1) IS_INBOUND FROM F_RECYCLING_ORDER  WITH(NOLOCK) WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID) = 1)
		  BEGIN
			 EXEC sp_LOG_RECYCLING_ORDER_INBOUND_DELETED
				@RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID,
				@USER_ID = @USER_ID,
				@IP = @IP
		  END
		  ELSE BEGIN
			 EXEC sp_LOG_RECYCLING_ORDER_OUTBOUND_DELETED
				@RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID,
				@USER_ID = @USER_ID,
				@IP = @IP
		  END

		  IF (@IS_DELETE_QUOTE = 0)
		  BEGIN
			UPDATE cp.F_ORDER_SCHEDULED WITH(ROWLOCK) 
					SET RecyclingOrderId = NULL
			WHERE RecyclingOrderId = @RECYCLING_ORDER_ID
		  END
		  ELSE BEGIN
			DELETE FROM cp.F_ORDER_SCHEDULED WITH(ROWLOCK) 
			WHERE RecyclingOrderId = @RECYCLING_ORDER_ID
		  END
			
		  DELETE FROM dbo.F_RECYCLING_CUSTOMER_CERTIFICATES WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  DELETE FROM dbo.F_RECYCLING_CUSTOMER_REPORTS WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  DELETE FROM dbo.F_RECYCLING_DATA_SECURITY_COLLECTION WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	
	
		  DELETE FROM dbo.F_RECYCLING_EQUIPMENT_COLLECTION WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	
	
		  DELETE FROM dbo.F_RECYCLING_HARD_DRIVE_SHRED WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	
	
		  DELETE FROM dbo.F_RECYCLING_OTHER_SERVICES WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  DELETE FROM dbo.F_RECYCLING_TAPE_DRIVE_SHRED WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  DELETE FROM dbo.F_RECYCLING_ORDER_SERVICE WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  DELETE FROM dbo.F_RECYCLING_ORDER_DOCUMENT WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  DELETE FROM dbo.F_RECYCLING_ORDER_EQUIPMENT WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
		  -- delete F_RECYCLING_LOCATION_DETAILS if inbound order
		  DECLARE @LOCATION_DETAILS_ID BIGINT
		  
		  SET @LOCATION_DETAILS_ID = (SELECT TOP (1) 
				 LD.RECYCLING_LOCATION_DETAILS_ID
			  FROM F_RECYCLING_LOCATION_DETAILS		LD  WITH(ROWLOCK)
			  INNER JOIN	F_RECYCLING_ORDER_INBOUND	OI
			  ON OI.LOCATION_DETAILS_ID = LD.RECYCLING_LOCATION_DETAILS_ID
			  WHERE OI.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
		  
		  UPDATE F_RECYCLING_ORDER_INBOUND WITH(ROWLOCK)
			SET LOCATION_DETAILS_ID = NULL
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  DELETE FROM [dbo].[F_RECYCLING_LOCATION_DETAILS] WITH(ROWLOCK)
		  WHERE RECYCLING_LOCATION_DETAILS_ID = @LOCATION_DETAILS_ID

		  --commodity rules
		  DELETE FROM [recycling].[F_CommodityRule] WITH(ROWLOCK)
		  WHERE [RecyclingOrderId] = @RECYCLING_ORDER_ID	

		  DELETE FROM dbo.F_RECYCLING_ORDER_ITEM_SERVICE WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	

		  DELETE FROM [dbo].[F_RECYCLING_INBOUND_FILE] WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

          DELETE f
		  FROM [recycling].[F_InboundOrderStateProgram]	as f   WITH(ROWLOCK)
		  where RecyclingOrderId = @RECYCLING_ORDER_ID
	
		  -- delete the inbound order
		  DELETE FROM dbo.F_RECYCLING_ORDER_INBOUND		WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  --TODO: not to forget about removing this after deleting table
		  DELETE FROM dbo.F_CONTRACT_VALIDATION_REQUIREMENTS WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	

		  DELETE FROM dbo.F_CUSTOMER_MASTER_TAG WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  DELETE FROM dbo.F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT WITH(ROWLOCK)
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  UPDATE dbo.F_RECYCLING_ORDER_INBOUND WITH(ROWLOCK) 
			SET QUOTE_ID = NULL
		  WHERE QUOTE_ID = @RECYCLING_ORDER_ID

		  DELETE FROM recycling.F_RecyclingOrderInboundCertificate WITH(ROWLOCK)
		  WHERE RecyclingOrderId = @RECYCLING_ORDER_ID

		  delete from recycling.F_InboundDraftAsset
		  where RecyclingOrderId = @RECYCLING_ORDER_ID

		  --commodity sla rules
		  delete from dbo.F_CommoditySlaRule with(rowlock)
		  where RecyclingOrderId = @RECYCLING_ORDER_ID
		  
		  delete from recycling.F_AuditSession
		  where RecyclingOrderId = @RECYCLING_ORDER_ID
		  
		  delete from [reports].[F_RecyclingOrderAdditionalData]
		  where RecyclingOrderId = @RECYCLING_ORDER_ID
		  
		  delete from [recycling].[F_AlternativeCommodityName]
		  where RecyclingOrderId = @RECYCLING_ORDER_ID

		  UPDATE  dbo.F_RECYCLING_ORDER_ITEM_TRANSFER WITH(ROWLOCK) 
			SET INBOUND_ORDER_ID = NULL
		  WHERE INBOUND_ORDER_ID = @RECYCLING_ORDER_ID		 

		  DELETE FROM dbo.F_RECYCLING_ORDER	WITH(ROWLOCK)	
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	
	   
	   COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
	   -- There is an error
	   IF @@TRANCOUNT > 0
		  ROLLBACK TRANSACTION
		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;  

    SELECT   
        @ErrorMessage = ERROR_MESSAGE(),  
        @ErrorSeverity = ERROR_SEVERITY(),  
        @ErrorState = ERROR_STATE();  

		-- Use RAISERROR inside the CATCH block to return error  
		-- information about the original error that caused  
		-- execution to jump to the CATCH block.  
		RAISERROR (@ErrorMessage, -- Message text.  
				   @ErrorSeverity, -- Severity.  
				   @ErrorState -- State.  
				   );  



    END CATCH
END
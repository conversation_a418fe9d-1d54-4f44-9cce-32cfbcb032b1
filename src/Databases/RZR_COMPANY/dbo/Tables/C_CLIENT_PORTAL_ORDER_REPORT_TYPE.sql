CREATE TABLE [dbo].[C_CLIENT_PORTAL_ORDER_REPORT_TYPE] (
    [CLIENT_PORTAL_ORDER_REPORT_TYPE_ID] INT            NOT NULL,
    [CLIENT_PORTAL_ORDER_REPORT_TYPE_CD] NVARCHAR (250) NULL,
    [CLIENT_PORTAL_ORDER_TYPE_ID]        INT            NOT NULL,
    [ReportTemplateId]                   BIGINT         NULL,
    [FormatId]                           INT            NULL,
    CONSTRAINT [PK_C_CLIENT_PORTAL_ORDER_REPORT_TYPE] PRIMARY KEY CLUSTERED ([CLIENT_PORTAL_ORDER_REPORT_TYPE_ID] ASC),
    CONSTRAINT [FK_C_CLIENT_PORTAL_ORDER_REPORT_TYPE_C_CLIENT_PORTAL_ORDER_TYPE] FOREIGN KEY ([CLIENT_PORTAL_ORDER_TYPE_ID]) REFERENCES [dbo].[C_CLIENT_PORTAL_ORDER_TYPE] ([CLIENT_PORTAL_ORDER_TYPE_ID]),
    CONSTRAINT [FK_C_CLIENT_PORTAL_ORDER_REPORT_TYPE_F_REPORT_TEMPLATE] FOREIGN KEY ([ReportTemplateId]) REFERENCES [reports].[F_REPORT_TEMPLATE] ([Id])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Report types which are allowed for client portal', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_CLIENT_PORTAL_ORDER_REPORT_TYPE';


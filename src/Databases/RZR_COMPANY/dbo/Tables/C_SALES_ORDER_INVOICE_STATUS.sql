CREATE TABLE [dbo].[C_SALES_ORDER_INVOICE_STATUS] (
    [INVOICE_STATUS_ID] INT           NOT NULL,
    [STATUS_NAME]       NVARCHAR (64) NOT NULL,
    [SELF_ID]           AS            (isnull(CONVERT([bigint],[INVOICE_STATUS_ID],0),CONVERT([bigint],(-1),0))),
    [SELF_NAME]         AS            ([STATUS_NAME]),
    [SELF_CREATE_DATE]  AS            (isnull(CONVERT([datetime],'2000-01-01',0),CONVERT([datetime],'2000-01-01',0))),
    [PARENT_ID]         AS            (CONVERT([bigint],isnull(NULL,(-1)),0)),
    CONSTRAINT [PK_C_SALES_ORDER_INVOICE_STATUS] PRIMARY KEY CLUSTERED ([INVOICE_STATUS_ID] ASC)
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The invoice statuses', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_SALES_ORDER_INVOICE_STATUS';


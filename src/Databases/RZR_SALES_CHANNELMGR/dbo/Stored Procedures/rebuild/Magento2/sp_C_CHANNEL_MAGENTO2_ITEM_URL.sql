CREATE PROCEDURE [dbo].[sp_C_CHANNEL_MAGENTO2_ITEM_URL]
(
	@C_IS_FULL_REBUILD		CHAR(1)			= 'N'
	,@C_ITEM_MASTER_ID		BIGINT			= 0
	,@C_ITEM_ID				BIGINT			= 0
	,@C_ITEM_MASTER_IDS		NVARCHAR(MAX)	= N'<Root/>'
	,@C_ITEM_IDs			NVARCHAR(MAX)	= N'<Root/>'
	,@C_COMPANY_ID			BIGINT			= 0
	,@C_COMPANY_ECOMM_ID	BIGINT			= 0
	,@C_IS_DEBUG			BIT				= 0
)
AS
BEGIN
	
	DECLARE @T_ITEM_MASTER_IDs [dbo].[bigint_ID_ARRAY];
	DECLARE @IS_ALL_MASTER_ITEMS BIT = 0;
	
	INSERT INTO @T_ITEM_MASTER_IDs
	SELECT DISTINCT [ID] FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](@C_ITEM_MASTER_IDS, N'Items')
	UNION
	SELECT @C_ITEM_MASTER_ID WHERE @C_ITEM_MASTER_ID != 0;

	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_MASTER_IDs))
	BEGIN
		SET @IS_ALL_MASTER_ITEMS = 1;
	END
	
	DECLARE @T_ITEM_IDs [dbo].[bigint_ID_ARRAY];
	DECLARE @IS_ALL_ITEMS BIT = 0;

	INSERT INTO @T_ITEM_IDs
	SELECT [ID] FROM [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](@C_ITEM_IDs, N'Items')
	UNION
	SELECT @C_ITEM_ID WHERE @C_ITEM_ID != 0;
	
	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_IDs) 
		AND @IS_ALL_MASTER_ITEMS = 0)
	BEGIN
		INSERT INTO @T_ITEM_IDs
		SELECT DISTINCT [ITEM_ID]
		FROM [dbo].[F_COMPANY_SALES_ITEM] WITH(NOLOCK)
		WHERE [ITEM_MASTER_ID] IN (SELECT ID FROM @T_ITEM_MASTER_IDs)
			AND [IS_DELETED] = 0
			AND [IS_INACTIVE] = 0
			AND [ITEM_MASTER_TYPE_ID] = 1
	END
	
	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_IDs))
	BEGIN
		SET @IS_ALL_ITEMS = 1;
	END
	
	DECLARE @C_COMPANY_RZR_ERP_DATABASE_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_RZR_ERP_DATABASE_NAME_FROM_COMPANY_ID](@C_COMPANY_ID);
	DECLARE @C_CHANNEL_MAGENTO_DOMAIN_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_CHANNEL_ECOMM_DOMAIN_NAME](@C_COMPANY_ID, @C_COMPANY_ECOMM_ID);
	DECLARE @C_CHANNEL_MAGENTO_DATABASE_NAME NVARCHAR(100) = [dbo].[fn_nvarchar_CHANNEL_ECOMM_DATABASE_NAME](@C_COMPANY_ID, @C_COMPANY_ECOMM_ID);

	DECLARE @magentoTablePrefix NVARCHAR (100) = (
		SELECT ISNULL([MAGENTO_TABLE_PREFIX], N'')
		FROM [dbo].[vw_F_COMPANY_ECOMM] WITH (NOLOCK)
		WHERE [COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
	);

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT @C_COMPANY_ID AS [C_COMPANY_ID], @C_COMPANY_RZR_ERP_DATABASE_NAME AS [C_COMPANY_RZR_ERP_DATABASE_NAME],
			@C_CHANNEL_MAGENTO_DOMAIN_NAME AS [C_CHANNEL_MAGENTO_DOMAIN_NAME], @C_CHANNEL_MAGENTO_DOMAIN_NAME AS [C_CHANNEL_MAGENTO_DOMAIN_NAME];
	END

	IF (@C_IS_FULL_REBUILD = 'Y')
	BEGIN
		DECLARE @C_SQL_DELETE NVARCHAR(MAX) = N'
		DELETE FROM OPENQUERY ([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''SELECT * FROM ' + @magentoTablePrefix + 'url_rewrite WHERE entity_type =''''product'''''')';
		
		IF (@C_IS_DEBUG = 1)
		BEGIN
			PRINT @C_SQL_DELETE;
		END
		ELSE
		BEGIN
			EXEC @C_SQL_DELETE;
		END

		--DELETE FROM [magento].[core_url_rewrite]
		--WHERE COMPANY_ID = @C_COMPANY_ID AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;
	END


	-- *************************************************************************************************
	-- *******************		Product Entity - START CODE	        ************************************
	-- *************************************************************************************************

	DECLARE @C_ENTITY_IDS NVARCHAR(MAX);
	DECLARE @C_CATEGORY_IDS NVARCHAR(MAX);

	EXEC [dbo].[sp_GET_CHANNEL_MAGENTO2_KEYS]
		@T_ITEM_IDs = @T_ITEM_IDs,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_CHANNEL_MAGENTO_DOMAIN_NAME = @C_CHANNEL_MAGENTO_DOMAIN_NAME,
		@MagentoTablePrefix = @magentoTablePrefix,
		@C_ENTITY_IDS = @C_ENTITY_IDS OUTPUT,
		@C_CATEGORY_IDS = @C_CATEGORY_IDS OUTPUT;
	
	IF (@C_IS_DEBUG = 1)
	BEGIN
		PRINT N'@C_ENTITY_IDS = ' + @C_ENTITY_IDS;
		PRINT N'@C_CATEGORY_IDS = ' + @C_CATEGORY_IDS;
	END

	DECLARE @T_ENTITY_IDS [dbo].[bigint_ID_ARRAY];
	DECLARE @T_CATEGORY_IDS [dbo].[bigint_ID_ARRAY];

	INSERT INTO @T_ENTITY_IDS
	SELECT DISTINCT CAST([VALUE] AS BIGINT)
	FROM [dbo].[fnSplit](@C_ENTITY_IDS, ',');

	INSERT INTO @T_CATEGORY_IDS 
	SELECT DISTINCT CAST([VALUE] AS BIGINT)
	FROM [dbo].[fnSplit](@C_CATEGORY_IDS, ',');

	DECLARE @C_SQL_QUERY NVARCHAR(MAX);
	DECLARE @C_SQL_PARAMS NVARCHAR(MAX);

	DECLARE @catalog_product_entity_type_id INT;
	DECLARE @catalog_product_attribute_set_id INT;

	SET @C_SQL_QUERY = N'
	SELECT
		@catalog_product_entity_type_id = t.[entity_type_id],
		@catalog_product_attribute_set_id = t.[attribute_set_id]
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT eet.entity_type_id, eas.attribute_set_id FROM ' + @magentoTablePrefix + 'eav_entity_type eet
		INNER JOIN ' + @magentoTablePrefix + 'eav_attribute_set eas ON eas.entity_type_id = eet.entity_type_id
			AND eas.attribute_set_name = ''''Default''''
		WHERE eet.entity_type_code = ''''catalog_product''''
	'') t';

	SET @C_SQL_PARAMS = N'
		@catalog_product_entity_type_id INT OUT,
		@catalog_product_attribute_set_id INT OUT';

	IF (@C_IS_DEBUG = 1)
	BEGIN
		PRINT @C_SQL_QUERY;
	END

	EXEC [sys].[sp_executesql]
		@C_SQL_QUERY,
		@C_SQL_PARAMS,
		@catalog_product_entity_type_id = @catalog_product_entity_type_id OUT,
		@catalog_product_attribute_set_id = @catalog_product_attribute_set_id OUT;


/*
	PRINT N'DELETE FROM [magento].[core_url_rewrite]';
	DELETE FROM [magento].[core_url_rewrite]
	WHERE
		(cast(product_id as nvarchar(20)) IN (SELECT value FROM #T_ENTITY_IDS) OR ('0') IN (SELECT value FROM #T_ENTITY_IDS))
		AND (@C_CATEGORY_IDS = N'0' OR [category_id] IN (SELECT [ID] FROM @T_CATEGORY_IDS))
		--(CAST(category_id  as nvarchar(20)) IN (SELECT value FROM #T_CATEGORY_IDS) OR ('0') IN (SELECT value FROM #T_CATEGORY_IDS))
		AND COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;
	
	PRINT N'INSERT INTO [magento].[core_url_rewrite]';

	SET @C_SQL_INIT_01 = 
	N'INSERT INTO magento.core_url_rewrite (COMPANY_ID, COMPANY_ECOMM_ID, url_rewrite_id, store_id, id_path, request_path, target_path, is_system, options, description, category_id, product_id)
	SELECT @C_COMPANY_ID as C_COMPANY_ID, @C_COMPANY_ECOMM_ID as COMPANY_ECOMM_ID, s.* 
	FROM OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], ''SELECT url_rewrite_id, store_id, id_path, request_path, target_path, is_system, options, description, category_id, product_id FROM core_url_rewrite 
		WHERE (product_id IN (' + @C_ENTITY_IDS + ') OR (0) IN (' + @C_ENTITY_IDS + '))'')  s
	'
	SET @C_SQL_PARAM_DEFINITION_INIT=
		'
			@C_COMPANY_ID BIGINT,
			@C_COMPANY_ECOMM_ID BIGINT
		'
	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_INIT_01
	
	EXEC sp_executesql 
			@C_SQL_INIT_01,
			@C_SQL_PARAM_DEFINITION_INIT,
			@C_COMPANY_ID = @C_COMPANY_ID,
			@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID
*/
	
	IF OBJECT_ID('tempdb..#temp_product') IS NOT NULL
	BEGIN
		DROP TABLE #temp_product;
	END

	CREATE TABLE #temp_product ([ITEM_ID] BIGINT, [ITEM_MASTER_CATEGORY_ID] BIGINT, [store_id] NUMERIC(5,0), [entity_id] NUMERIC(10,0));

	INSERT INTO #temp_product
	SELECT DISTINCT 
		fcsi.[ITEM_ID],
		fcsi.[ITEM_MASTER_CATEGORY_ID],
		1 AS [store_id],
		cpe.[entity_id]
	FROM [magento].[catalog_product_entity] AS cpe WITH (NOLOCK)
	INNER JOIN (
		SELECT [ITEM_ID], [ITEM_MASTER_CATEGORY_ID] FROM [dbo].[F_COMPANY_SALES_ITEM] fcsi WITH (NOLOCK)
		WHERE @IS_ALL_ITEMS = 1
			AND fcsi.[COMPANY_ID] = @C_COMPANY_ID
			AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
		UNION
		SELECT [ITEM_ID], [ITEM_MASTER_CATEGORY_ID] FROM [dbo].[F_COMPANY_SALES_ITEM] fcsi WITH (NOLOCK)
		INNER JOIN @T_ITEM_IDs AS t ON t.[ID] = fcsi.[ITEM_ID]
		WHERE fcsi.[COMPANY_ID] = @C_COMPANY_ID
			AND fcsi.[ITEM_MASTER_TYPE_ID] = 1
	) AS fcsi
		ON CONVERT(NVARCHAR(20), fcsi.[ITEM_ID]) = cpe.[sku]
	WHERE cpe.[COMPANY_ID] = @C_COMPANY_ID
		AND cpe.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID;

	CREATE INDEX IDX_temp_product ON #temp_product ([ITEM_ID])
		INCLUDE ([ITEM_MASTER_CATEGORY_ID], [store_id], [entity_id]);

	-- *************************************************************************************************
	-- ******************************      Product URL Rewrite      ************************************
	-- *************************************************************************************************
	-- Category & Product Tables
	PRINT N'Product URL Rewrite';

	CREATE TABLE #temp_core_url_rewrite (
		[CORE_URL_REWRITE_ROW_ID] BIGINT IDENTITY(1,1) NOT NULL,
		[entity_type] VARCHAR(32) NOT NULL,
		[entity_id] NUMERIC(10) NOT NULL,
		[request_path] VARCHAR(255) NULL,
		[target_path] VARCHAR(255) NULL,
		[redirect_type] NUMERIC(5,0) NOT NULL,
		[store_id] NUMERIC(5,0) NOT	NULL,
		[description] VARCHAR(255) NULL,
		[is_autogenerated] NUMERIC(5,0)	NOT NULL,
		[metadata] VARCHAR(255) NULL
	);

	SET @C_SQL_QUERY = N'
	SELECT DISTINCT
		''product'' AS [entity_type],
		fcsi.[entity_id],
		cpev.[value] AS [request_path],
		''catalog/product/view/id/'' + CONVERT(nvarchar(20), fcsi.[entity_id]) AS [target_path],
		0 AS [redirect_type],
		fcsi.[store_id],
		NULL AS [description],
		1 AS [is_autogenerated],
		NULL AS [metadata]
	FROM #temp_product AS fcsi
	INNER JOIN [magento].[catalog_category_entity] AS cce
		ON cce.[COMPANY_ID] = @C_COMPANY_ID 
		AND cce.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND fcsi.[ITEM_MASTER_CATEGORY_ID] = cce.[entity_id]
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], N''
		SELECT DISTINCT cpev.entity_id, cpev.store_id, cpev.value
		FROM ' + @magentoTablePrefix + 'catalog_product_entity_varchar AS cpev
		INNER JOIN ' + @magentoTablePrefix + 'eav_attribute AS ea ON ea.attribute_id = cpev.attribute_id
			AND ea.attribute_code = ''''url_path''''
		INNER JOIN ' + @magentoTablePrefix + 'eav_entity_type eet ON eet.entity_type_id = ea.entity_type_id
			AND eet.entity_type_code = ''''catalog_product''''
		' + IIF(@C_ENTITY_IDS = N'0', N'', N' WHERE entity_id IN (' + @C_ENTITY_IDS + N')') + N'''
	) AS cpev
		ON fcsi.[entity_id] = cpev.[entity_id] -- AND fcsi.[store_id] = cpev.[store_id]
	INNER JOIN [dbo].[F_COMPANY_SALES_ITEM_URL] AS fccsiu WITH (NOLOCK)
		ON fccsiu.[COMPANY_ID] = @C_COMPANY_ID
		AND fccsiu.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND fcsi.[ITEM_ID] = fccsiu.[ITEM_ID]';

	SET @C_SQL_PARAMS = N'
		@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
		@IS_ALL_ITEMS BIT,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_QUERY;
	
	INSERT INTO #temp_core_url_rewrite ([entity_type], [entity_id], [request_path], [target_path], [redirect_type], [store_id], [description], [is_autogenerated], [metadata])
	EXEC [sys].[sp_executesql]
		@C_SQL_QUERY,
		@C_SQL_PARAMS,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@IS_ALL_ITEMS = @IS_ALL_ITEMS,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;

/*
	PRINT N'Category';

	SET @C_SQL_QUERY = N'
	SELECT
		fcsi.store_id
		,''product'' + ''/'' + CONVERT(nvarchar(20), fcsi.entity_id) + ''/'' + CONVERT(nvarchar(20), cce.entity_id) id_path
		,CONVERT(varchar(255), REPLACE(ccev.value, ''.html'', '''') + ''/'' + cpev.value) request_path
		,''catalog/product/view/id/'' + CONVERT(nvarchar(20), fcsi.entity_id) + ''/category/'' + CONVERT(nvarchar(20), cce.entity_id) target_path
		,1 is_system
		,NULL options
		,NULL description
		,cce.entity_id category_id
		,fcsi.entity_id product_id

	FROM #temp_product fcsi
	INNER JOIN [magento].[catalog_category_entity] AS cce
		ON cce.[COMPANY_ID] = @C_COMPANY_ID 
		AND cce.[COMPANY_ECOMM_ID] = @C_COMPANY_ECOMM_ID
		AND fcsi.[ITEM_MASTER_CATEGORY_ID] = cce.[entity_id]
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], N''
		SELECT DISTINCT ccev.entity_id, ccev.store_id, ccev.value FROM catalog_category_entity_varchar ccev
		INNER JOIN eav_attribute ea ON ea.attribute_id = ccev.attribute_id
					AND ea.attribute_code = ''''url_path''''
				INNER JOIN eav_entity_type eet ON eet.entity_type_id = ea.entity_type_id
					AND eet.entity_type_code = ''''catalog_category''''
		'') ccev 
		ON cce.entity_id = ccev.entity_id
			AND fcsi.store_id = ccev.store_id
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], 
		''SELECT DISTINCT cpev.entity_id, cpev.store_id, cpev.value FROM catalog_product_entity_varchar cpev
			INNER JOIN eav_attribute ea ON ea.attribute_id = cpev.attribute_id
					AND ea.attribute_code = ''''url_path''''
				INNER JOIN eav_entity_type eet ON eet.entity_type_id = ea.entity_type_id
					AND eet.entity_type_code = ''''catalog_product''''
			WHERE '+ CASE @C_ENTITY_IDS WHEN '0'THEN '0=0' ELSE 'entity_id IN (' + @C_ENTITY_IDS + ')' END +''') cpev
		ON fcsi.entity_id = cpev.entity_id 
			AND fcsi.store_id = cpev.store_id
	INNER JOIN dbo.[F_COMPANY_SALES_ITEM_URL] fccsiu WITH (NOLOCK)
		ON fcsi.COMPANY_ID = fccsiu.COMPANY_ID 
			AND fccsiu.COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID 
			AND fcsi.ITEM_ID = fccsiu.ITEM_ID
	'

	SET @C_SQL_PARAMS = N'
		@T_ITEM_IDs [dbo].[bigint_ID_ARRAY] READONLY,
		@IS_ALL_ITEMS BIT,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_QUERY;
	

	INSERT INTO #temp_core_url_rewrite ([entity_type], [entity_id], [request_path], [target_path], [redirect_type], [store_id], [description], [is_autogenerated], [metadata])
	EXEC sp_executesql 
		@C_SQL_QUERY,
		@C_SQL_PARAMS,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@IS_ALL_ITEMS= @IS_ALL_ITEMS,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;
*/

/*
	SET @C_SQL_QUERY = N'
	SELECT
		fcsi.store_id
		,cpev2.value id_path
		,cpev2.value + ''.html'' request_path
		,cpev.value target_path
		,0 is_system
		,''RP'' options
		,NULL description
		,NULL category_id
		,fcsi.entity_id product_id
	FROM #temp_product fcsi
		INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], 
			''SELECT DISTINCT cpev.entity_id, cpev.store_id, cpev.value 
				FROM catalog_product_entity_varchar cpev
				INNER JOIN eav_attribute ea ON ea.attribute_id = cpev.attribute_id
					AND ea.attribute_code = ''''url_path''''
				INNER JOIN eav_entity_type eet ON eet.entity_type_id = ea.entity_type_id
					AND eet.entity_type_code = ''''catalog_product''''
				WHERE '+ CASE @C_ENTITY_IDS WHEN '0'THEN '0=0' ELSE 'entity_id IN (' + @C_ENTITY_IDS + ')' END +''') cpev
			ON fcsi.entity_id = cpev.entity_id 
				AND fcsi.store_id = cpev.store_id
		INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], 
			''SELECT DISTINCT cpev2.entity_id, cpev2.store_id, cpev2.value
			FROM catalog_product_entity_varchar cpev2
			INNER JOIN eav_attribute ea ON ea.attribute_id = cpev2.attribute_id
					AND ea.attribute_code = ''''url_key''''
				INNER JOIN eav_entity_type eet ON eet.entity_type_id = ea.entity_type_id
					AND eet.entity_type_code = ''''catalog_product''''
			WHERE '+ CASE @C_ENTITY_IDS WHEN '0'THEN '0=0' ELSE 'entity_id IN (' + @C_ENTITY_IDS + ')' END +''') cpev2
			ON fcsi.entity_id = cpev2.entity_id 
				AND fcsi.store_id = cpev2.store_id
		INNER JOIN dbo.[F_COMPANY_SALES_ITEM_URL] fccsiu WITH (NOLOCK)
			ON fcsi.COMPANY_ID = fccsiu.COMPANY_ID 
				AND fccsiu.COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID
				AND fcsi.ITEM_ID = fccsiu.ITEM_ID
	WHERE 
		(
			cpev.value != (cpev2.value + ''.html'')
		)
	'
	SET @C_SQL_PARAMS = N'
		@T_ITEM_IDs dbo.[bigint_ID_ARRAY] READONLY, 
		@IS_ALL_ITEMS BIT,
		@C_COMPANY_ID BIGINT,
		@C_COMPANY_ECOMM_ID BIGINT';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_QUERY;
		
	INSERT INTO #temp_core_url_rewrite ([entity_type], [entity_id], [request_path], [target_path], [redirect_type], [store_id], [description], [is_autogenerated], [metadata])
	EXEC [sys].[sp_executesql]
		@C_SQL_QUERY,
		@C_SQL_PARAMS,
		@T_ITEM_IDs = @T_ITEM_IDs,
		@IS_ALL_ITEMS= @IS_ALL_ITEMS,
		@C_COMPANY_ID = @C_COMPANY_ID,
		@C_COMPANY_ECOMM_ID = @C_COMPANY_ECOMM_ID;
*/
	
	CREATE INDEX IDX_temp_core_url_rewrite_store_id_request_path ON #temp_core_url_rewrite (request_path) INCLUDE(CORE_URL_REWRITE_ROW_ID);

	DELETE st FROM #temp_core_url_rewrite st
	WHERE ISNULL(st.[request_path], '') IN ('#NAME?', '(blank)', 'rootcategory.html', '');
	
	DROP INDEX IDX_temp_core_url_rewrite_store_id_request_path ON #temp_core_url_rewrite;
	
/*
	CREATE INDEX idx_temp_core_url_CORE_URL_REWRITE_ROW_ID ON #temp_core_url_rewrite (CORE_URL_REWRITE_ROW_ID)
	INCLUDE(id_path, store_id, is_system, request_path);
	
	SELECT CORE_URL_REWRITE_ROW_ID
		,DENSE_RANK() OVER (PARTITION BY st.id_path, st.store_id, st.is_system  ORDER BY st.CORE_URL_REWRITE_ROW_ID) UNQ_PATH_STORE_SYSTEM_KEY_RANK
		,DENSE_RANK() OVER (PARTITION BY st.request_path, st.store_id ORDER BY st.CORE_URL_REWRITE_ROW_ID) UNQ_REQUEST_PATH_STORE_KEY_RANK
	into #t
	FROM
		#temp_core_url_rewrite st
	CREATE INDEX idx_t_UNQ_REQUEST_PATH_KEY_RANK ON #t(UNQ_PATH_STORE_SYSTEM_KEY_RANK) INCLUDE(CORE_URL_REWRITE_ROW_ID)
	CREATE INDEX idx_t_UNQ_REQUEST_PATH_STORE_KEY_RANK ON #t(UNQ_REQUEST_PATH_STORE_KEY_RANK) INCLUDE(CORE_URL_REWRITE_ROW_ID)
	
	DELETE st
	FROM #temp_core_url_rewrite st 
	INNER JOIN #t t ON st.CORE_URL_REWRITE_ROW_ID = t.CORE_URL_REWRITE_ROW_ID
	AND t.UNQ_PATH_STORE_SYSTEM_KEY_RANK!=1

	DELETE st
	FROM #temp_core_url_rewrite st
	INNER JOIN #t t ON st.CORE_URL_REWRITE_ROW_ID = t.CORE_URL_REWRITE_ROW_ID
	AND t.UNQ_REQUEST_PATH_STORE_KEY_RANK!=1
*/	

	--CREATE INDEX IDX_temp_core_url_rewrite ON #temp_core_url_rewrite ([store_id], [request_path])
	--INCLUDE(CORE_URL_REWRITE_ROW_ID,  target_path, options, [description], category_id, product_id);
 

	PRINT N'INSERT INTO [' + @C_CHANNEL_MAGENTO_DOMAIN_NAME +'].[' + @magentoTablePrefix + 'url_rewrite]';

	SET @C_SQL_QUERY = N'
	UPDATE cur SET
		cur.[entity_type] = st.[entity_type],
		cur.[entity_id] = st.[entity_id],
		cur.[target_path] = st.[target_path],
		cur.[redirect_type] = st.[redirect_type],
		cur.[description] = st.[description],
		cur.[is_autogenerated] = st.[is_autogenerated],
		cur.[metadata] = st.[metadata]
	FROM #temp_core_url_rewrite AS st
	INNER JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + N'], N''
		SELECT entity_type, entity_id, request_path, target_path, redirect_type, store_id, description, is_autogenerated, metadata
		FROM ' + @magentoTablePrefix + 'url_rewrite
		' + IIF(@C_ENTITY_IDS = N'0', N'', N' WHERE entity_id IN (' + @C_ENTITY_IDS + N')') + N'
	'') AS cur
		ON st.[store_id] = cur.[store_id] AND st.[request_path] = cur.[request_path]
	WHERE cur.[entity_type] != st.[entity_type]
		OR cur.[entity_id] != st.[entity_id]
		OR ISNULL(cur.[target_path], '''') != ISNULL(st.[target_path], '''')
		OR cur.[redirect_type] != st.[redirect_type]
		OR ISNULL(cur.[description], '''') != ISNULL(st.[description], '''')
		OR cur.[is_autogenerated] != st.[is_autogenerated]
		OR ISNULL(cur.[metadata], '''') != ISNULL(st.[metadata], '''');

	INSERT INTO OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], N''
		SELECT entity_type, entity_id, request_path, target_path, redirect_type, store_id, description, is_autogenerated, metadata
		FROM ' + @magentoTablePrefix + 'url_rewrite
	'')
	SELECT DISTINCT
		st.[entity_type],
		st.[entity_id],
		st.[request_path],
		st.[target_path],
		st.[redirect_type],
		st.[store_id],
		st.[description],
		st.[is_autogenerated],
		st.[metadata]
	FROM #temp_core_url_rewrite AS st
	LEFT JOIN OPENQUERY([' + @C_CHANNEL_MAGENTO_DOMAIN_NAME + '], N''
		SELECT url_rewrite_id, store_id, request_path
		FROM ' + @magentoTablePrefix + 'url_rewrite
	'') AS cur
		ON st.[store_id] = cur.[store_id]
		AND st.[request_path] = cur.[request_path]
	WHERE cur.[url_rewrite_id] IS NULL;';

	IF (@C_IS_DEBUG = 1) PRINT @C_SQL_QUERY;
	
	EXEC [sys].[sp_executesql] @C_SQL_QUERY;
	
	IF OBJECT_ID('tempdb..#temp_core_url_rewrite') IS NOT NULL DROP TABLE #temp_core_url_rewrite;
	--IF OBJECT_ID('tempdb..#t') IS NOT NULL DROP TABLE #t;

END

CREATE TABLE [dbo].[F_RECYCLING_ORDER_ITEM] (
    [RECYCLING_ORDER_ITEM_ID]        BIGINT         NOT NULL,
    [RECYCLING_ORDER_ID]             BIGINT         NOT NULL,
    [OUTBOUND_ORDER_ID]              BIGINT         NULL,
    [RECYCLING_ITEM_MASTER_ID]       BIGINT         NOT NULL,
    [AUTO_NAME]                      VARCHAR (200)  NULL,
    [ITEM_COUNT]                     INT            NULL,
    [ITEM_PRICE]                     FLOAT (53)     NULL,
    [ITEM_PRICE_OUTBOUND]            FLOAT (53)     NULL,
    [PRICE_TYPE_ID]                  INT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_PRICE_TYPE_ID] DEFAULT ((1)) NULL,
    [PRICE_TYPE_ID_OUTBOUND]         INT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_PRICE_TYPE_ID_OUTBOUND] DEFAULT ((1)) NULL,
    [WEIGHT_RECEIVED]                FLOAT (53)     CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_WEIGHT_RECIEVED] DEFAULT ((0)) NOT NULL,
    [WEIGHT_TARE]                    FLOAT (53)     CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_WEIGHT_TARE] DEFAULT ((0)) NOT NULL,
    [PACKAGING_TYPE_ID]              INT            NOT NULL,
    [WORKFLOW_STEP_ID]               INT            NOT NULL,
    [LOCATION_ID]                    BIGINT         NULL,
    [NOTES]                          NVARCHAR (MAX) NULL,
    [PRICE_CHANGE_COMMENT]           NVARCHAR (MAX) NULL,
    [INSERTED_BY_USER]               BIGINT         NULL,
    [PARENT_ID]                      BIGINT         NULL,
    [PARENTS_KEY]                    VARCHAR (700)  NULL,
    [WEIGHT_REMAIN]                  FLOAT (53)     CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_WEIGHT_REMAIN] DEFAULT ((0)) NULL,
    [WEIGHT_LOOSE_LOAD]              FLOAT (53)     NULL,
    [EXPORTED_DT]                    DATETIME       NULL,
    [CONSUMED_IN_WAREHOUSE_ID]       BIGINT         NULL,
    [CONSUMED_DT]                    DATETIME       NULL,
    [IS_GET_AFTER_SETTLE]            BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_EXCESS_FROM_SCRAP1] DEFAULT ((0)) NOT NULL,
    [IS_MERGED]                      BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_MERGED] DEFAULT ((0)) NOT NULL,
    [RECYCLING_ORDER_ITEM_MERGED_ID] BIGINT         NULL,
    [RECYCLING_ORDER_INITIAL_ID]     BIGINT         NULL,
    [IS_INCLUDE_IN_INITIAL]          BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_FROM_INITIAL] DEFAULT ((1)) NOT NULL,
    [IS_EXLUDE_FROM_INITIAL]         BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_EXLUDE_FROM_INITIAL] DEFAULT ((0)) NOT NULL,
    [IS_TEST_COMPLETE]               BIT            NULL,
    [IS_AUDIT_COMPLETE]              BIT            NULL,
    [IS_EXCESS_FROM_SCRAP]           BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_EXCESS_FROM_SCRAP] DEFAULT ((0)) NULL,
    [USE_FOR_PRICE_CALCULATION]      BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_USE_FOR_PRICE_CALCULATION] DEFAULT ((0)) NOT NULL,
    [PROCESSING_STATE_ID]            BIGINT         NULL,
    [IS_INACTIVE]                    BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                     BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY_IP]                 BIGINT         NULL,
    [INSERTED_BY]                    VARCHAR (250)  CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                    DATETIME       CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY_USER]                BIGINT         NULL,
    [UPDATED_BY_IP]                  BIGINT         NULL,
    [UPDATED_BY]                     VARCHAR (250)  NULL,
    [UPDATED_DT]                     DATETIME       NULL,
    [DELETED_BY]                     VARCHAR (250)  NULL,
    [DELETED_DT]                     DATETIME       NULL,
    [WEIGHT_RECEIVED_KG]             AS             ([WEIGHT_RECEIVED]*(0.45359237)) PERSISTED,
    [WEIGHT_TARE_KG]                 AS             ([WEIGHT_TARE]*(0.45359237)) PERSISTED,
    [WEIGHT_REMAIN_KG]               AS             ([WEIGHT_REMAIN]*(0.45359237)) PERSISTED,
    [WEIGHT_LOOSE_LOAD_KG]           AS             ([WEIGHT_LOOSE_LOAD]*(0.45359237)) PERSISTED,
    [ProfitMargin]                   INT            NULL,
    [FINALIZE_BUILD_UP_DT]           DATETIME       NULL,
    [MERGE_DT]                       DATETIME       NULL,
    [MERGED_BY_USER]                 BIGINT         NULL,
    [MAIN_INNER_LOT_IN_MERGE_ID]     BIGINT         NULL,
    [LOT_AUTO_NAME]                  AS             (CONVERT([varchar](200),case when [IS_MERGED]=(1) AND [MAIN_INNER_LOT_IN_MERGE_ID] IS NOT NULL then isnull([AUTO_NAME],CONVERT([varchar](50),[RECYCLING_ORDER_ITEM_ID])) else CONVERT([varchar](50),[RECYCLING_ORDER_ITEM_ID])+isnull((' ('+[AUTO_NAME])+')','') end)) PERSISTED,
    [NO_EXTERNAL_LOTS_MERGED_INTO]   BIT            CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_NO_EXTERNAL_LOTS_MERGED_INTO] DEFAULT ((1)) NOT NULL,
    [WorkflowStepPrevId]             INT            NULL,
    [ShortLotAutoName]               AS             (CONVERT([nvarchar](200),isnull([MAIN_INNER_LOT_IN_MERGE_ID],[RECYCLING_ORDER_ITEM_ID]))) PERSISTED,
    [AuditCompleteDate]              DATETIME       NULL,
    [AuditStartDate]                 DATETIME       NULL,
    [ConsumedWeight]                 FLOAT (53)     NULL,
    [ConsumedByUser]                 BIGINT         NULL,
    [MATERIAL_STREAM_TYPE_ID]        BIGINT         NULL,
    [DATA_REQUIREMENTS_TYPE_ID]      BIGINT         NULL,
    [REFERENCE]                      VARCHAR (50)   NULL,
    [AlternativeName]                NVARCHAR (250) NULL,
    [FmvDiscount]                    INT            NULL,
    [InitialWorkflowId]              BIGINT         NULL,
	[DistributedCost]				 MONEY	        NULL,
	[IS_CONSUMED_OR_PROCESSED]		 AS (CONVERT([bit],case when [WORKFLOW_STEP_ID]=(9) OR [WORKFLOW_STEP_ID]=(8) then (1) else (0) end)) PERSISTED,
	[NotForRawActionLog]			 BIT			CONSTRAINT [DF_F_RECYCLING_ORDER_ITEM_NotForRawActionLog] DEFAULT ((0)) NOT NULL,
    [StateProgramId]                 bigint         null,
	[IsCertifiedDestruction]         BIT            CONSTRAINT [D_F_RECYCLING_ORDER_ITEM_IsCertifiedDestruction] DEFAULT ((0)) NOT NULL,
    [BusinessUnitId]                 INT            NULL, 
    CONSTRAINT [PK_F_RECYCLING_ORDER_ITEM] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_ITEM_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM__UPDATED_tb_User] FOREIGN KEY ([UPDATED_BY_USER]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM__CONSUMED_IN_WAREHOUSE_ID__D_WAREHOUSE__WAREHOUSE_ID] FOREIGN KEY ([CONSUMED_IN_WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_C_RECYCLING_ORDER_ITEM_PROCESSING_STATE] FOREIGN KEY ([PROCESSING_STATE_ID]) REFERENCES [dbo].[C_RECYCLING_ORDER_ITEM_PROCESSING_STATE] ([RECYCLING_ORDER_ITEM_PROCESSING_STATE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_C_RECYCLING_PRICE_TYPE] FOREIGN KEY ([PRICE_TYPE_ID]) REFERENCES [dbo].[C_RECYCLING_PRICE_TYPE] ([PRICE_TYPE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_D_RECYCLING_PACKAGING_TYPE] FOREIGN KEY ([PACKAGING_TYPE_ID]) REFERENCES [dbo].[D_RECYCLING_PACKAGING_TYPE] ([RECYCLING_PACKAGING_TYPE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_D_RECYCLING_WORKFLOW_TYPE] FOREIGN KEY ([WORKFLOW_STEP_ID]) REFERENCES [dbo].[C_RECYCLING_WORKFLOW_TYPE] ([WORKFLOW_TYPE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_D_RECYCLING_WORKFLOW_TYPE_PREV] FOREIGN KEY ([WorkflowStepPrevId]) REFERENCES [dbo].[C_RECYCLING_WORKFLOW_TYPE] ([WORKFLOW_TYPE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_LOCATION] FOREIGN KEY ([LOCATION_ID]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER] FOREIGN KEY ([RECYCLING_ITEM_MASTER_ID]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_ITEM_MAIN_INNER_LOT_IN_MERGE_ID] FOREIGN KEY ([MAIN_INNER_LOT_IN_MERGE_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_ITEM_PARENT_ID] FOREIGN KEY ([PARENT_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_ITEM_RECYCLING_ORDER_ITEM_MERGED_ID] FOREIGN KEY ([RECYCLING_ORDER_ITEM_MERGED_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_OUTBOUND_ORDER_ID] FOREIGN KEY ([OUTBOUND_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_RECYCLING_ORDER_ID] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_F_RECYCLING_ORDER_RECYCLING_ORDER_INITIAL_ID] FOREIGN KEY ([RECYCLING_ORDER_INITIAL_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_INSERTED_USER_tb_User] FOREIGN KEY ([INSERTED_BY_USER]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_MERGED_BY_USER_tb_User] FOREIGN KEY ([MERGED_BY_USER]) REFERENCES [dbo].[tb_User] ([UserID]),
    constraint [FK_F_RECYCLING_ORDER_ITEM_F_State_Program] foreign key ([StateProgramId]) references [recycling].[F_StateProgram] ([Id]),
	constraint [FK_F_RECYCLING_ORDER_ITEM_C_BusinessUnit] foreign key ([BusinessUnitId]) references [recycling].[C_BusinessUnit] ([Id])
)
GO

CREATE NONCLUSTERED INDEX [IX__dbo_F_RECYCLING_ORDER_ITEM__PARENTS_KEY__INCLUDE__RECYCLING_ORDER_ITEM_ID]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] (
	  [PARENTS_KEY]
	)
	INCLUDE (
	  [RECYCLING_ORDER_ITEM_ID]
	)
GO

CREATE UNIQUE NONCLUSTERED INDEX [uix__dbo__f_recycling_order_item__order_id]
	on [dbo].[F_RECYCLING_ORDER_ITEM](RECYCLING_ORDER_ITEM_ID, RECYCLING_ORDER_ID, IS_CONSUMED_OR_PROCESSED)
go


CREATE NONCLUSTERED INDEX [IX__dbo_F_RECYCLING_ORDER_ITEM__LOCATION_ID__RECYCLING_ORDER_ITEM_MERGED_ID__IS_INACTIVE__IS_DELETED_WORKFLOW_STEP_ID]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] (
	  [LOCATION_ID], [RECYCLING_ORDER_ITEM_MERGED_ID], [IS_INACTIVE], [IS_DELETED], [WORKFLOW_STEP_ID], [IS_CONSUMED_OR_PROCESSED]
	)
GO

CREATE NONCLUSTERED INDEX [IX_ORDER_INITIAL_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([RECYCLING_ORDER_INITIAL_ID] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_MAIN_INNER_LOT_IN_MERGE_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([MAIN_INNER_LOT_IN_MERGE_ID] ASC)
GO

GO
CREATE NONCLUSTERED INDEX [IX_MERGED_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM](IS_MERGED, [RECYCLING_ORDER_ITEM_MERGED_ID] ASC)
	INCLUDE([RECYCLING_ORDER_ITEM_ID], [RECYCLING_ORDER_ID],OUTBOUND_ORDER_ID, [RECYCLING_ITEM_MASTER_ID], [AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName, ITEM_COUNT, [WEIGHT_RECEIVED], [WEIGHT_TARE], [PARENT_ID], [WEIGHT_REMAIN], EXPORTED_DT, INSERTED_DT)
GO

CREATE NONCLUSTERED INDEX [IX_RECYCLING_ITEM_MASTER_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([RECYCLING_ITEM_MASTER_ID] ASC) INCLUDE(RECYCLING_ORDER_ITEM_ID)
GO
CREATE NONCLUSTERED INDEX [IX_PARENT_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([PARENT_ID],[IS_DELETED],[INSERTED_DT], [IS_EXCESS_FROM_SCRAP] ASC)
INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ORDER_ID],[OUTBOUND_ORDER_ID],[RECYCLING_ITEM_MASTER_ID],[AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName,[ITEM_COUNT],[WEIGHT_RECEIVED],[WEIGHT_TARE],[WEIGHT_REMAIN],[EXPORTED_DT])
GO
CREATE NONCLUSTERED INDEX [IX__F_RECYCLING_ORDER_ITEM__MASS_BALANCE]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] ([IS_INACTIVE],[IS_DELETED])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ORDER_ID],[OUTBOUND_ORDER_ID],[RECYCLING_ITEM_MASTER_ID],[AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName,[ITEM_COUNT],[WEIGHT_RECEIVED],[WEIGHT_TARE],[WEIGHT_REMAIN],[EXPORTED_DT],[INSERTED_DT])
GO
CREATE NONCLUSTERED INDEX [IX_RECYCLING_ITEM_MASTER_ID_WORKFLOW_STEP_ID_CONSUMED_DT]
	ON [dbo].[F_RECYCLING_ORDER_ITEM](
		[RECYCLING_ITEM_MASTER_ID],
		[WORKFLOW_STEP_ID] ASC,
		[IS_CONSUMED_OR_PROCESSED], 
		[CONSUMED_DT],
		IS_DELETED,
		IS_INACTIVE,
		[INSERTED_DT],
		[UPDATED_DT],
		DELETED_DT
	)
	INCLUDE (
		[RECYCLING_ORDER_ITEM_ID],
		[RECYCLING_ORDER_ID],
		[AUTO_NAME],
		[LOT_AUTO_NAME],
		ShortLotAutoName,
		[ITEM_COUNT],
		[WEIGHT_RECEIVED],
		[WEIGHT_TARE],
		[NOTES],
		[CONSUMED_IN_WAREHOUSE_ID],
		[UPDATED_BY_USER],
		OUTBOUND_ORDER_ID,
		ITEM_PRICE,
		ITEM_PRICE_OUTBOUND,
		PRICE_TYPE_ID,
		PRICE_TYPE_ID_OUTBOUND,
		PACKAGING_TYPE_ID,
		LOCATION_ID,
		PRICE_CHANGE_COMMENT,
		INSERTED_BY_USER,
		PARENT_ID,
		PARENTS_KEY,
		WEIGHT_REMAIN,
		EXPORTED_DT,
		IS_GET_AFTER_SETTLE,
		IS_MERGED,
		RECYCLING_ORDER_ITEM_MERGED_ID,
		IS_INCLUDE_IN_INITIAL,
		IS_EXLUDE_FROM_INITIAL,
		IS_TEST_COMPLETE,
		IS_AUDIT_COMPLETE,
		IS_EXCESS_FROM_SCRAP,
		USE_FOR_PRICE_CALCULATION,
		PROCESSING_STATE_ID
)
GO
--this one makes dbo.sp_GET_RECYCLING_AUDIT_ORDERS twice fast
CREATE NONCLUSTERED INDEX [IX__F_RECYCLING_ORDER_ITEM__IS_DELETED__INCLUDE__RECYCLING_ORDER_ITEM_ID__RECYCLING_ORDER_ID]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] ([IS_DELETED])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ORDER_ID])
GO
CREATE NONCLUSTERED INDEX IX_F_RECYCLING_ORDER_ITEM_IS_DELETED
	ON F_RECYCLING_ORDER_ITEM(
		IS_INACTIVE,
		[IS_DELETED],
		[INSERTED_DT],
		[UPDATED_DT] ,
		[DELETED_DT] 
	)
	INCLUDE(RECYCLING_ORDER_ID, RECYCLING_ORDER_ITEM_ID, 
		ITEM_PRICE, ITEM_PRICE_OUTBOUND, PRICE_TYPE_ID, PRICE_TYPE_ID_OUTBOUND, PRICE_CHANGE_COMMENT, INSERTED_BY_USER, PARENTS_KEY, EXPORTED_DT, CONSUMED_DT, IS_EXLUDE_FROM_INITIAL, IS_EXCESS_FROM_SCRAP, USE_FOR_PRICE_CALCULATION, UPDATED_BY_USER,
		OUTBOUND_ORDER_ID, RECYCLING_ITEM_MASTER_ID, AUTO_NAME, [LOT_AUTO_NAME], ShortLotAutoName, ITEM_COUNT, WEIGHT_RECEIVED, WEIGHT_TARE, PACKAGING_TYPE_ID, WORKFLOW_STEP_ID, [IS_CONSUMED_OR_PROCESSED], LOCATION_ID, NOTES, PARENT_ID, WEIGHT_REMAIN, IS_GET_AFTER_SETTLE, IS_MERGED, RECYCLING_ORDER_ITEM_MERGED_ID, IS_INCLUDE_IN_INITIAL, IS_TEST_COMPLETE, IS_AUDIT_COMPLETE, PROCESSING_STATE_ID
	)


GO
CREATE NONCLUSTERED INDEX [IX_WORKFLOW_STEP_ID_CONSUMED_DT]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([WORKFLOW_STEP_ID], [IS_CONSUMED_OR_PROCESSED], [CONSUMED_DT])
INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ITEM_MASTER_ID])

GO
CREATE NONCLUSTERED INDEX [IX_LOCATION_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([LOCATION_ID] ASC)

GO
CREATE NONCLUSTERED INDEX [IX_RECYCLING_ORDER_ID_IS_MERGED_INSERTED_DT_PARENT_ID]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([RECYCLING_ORDER_ID] ASC, [IS_MERGED] ASC, [INSERTED_DT] ASC, [PARENT_ID] ASC, IS_GET_AFTER_SETTLE, IS_INACTIVE)
    INCLUDE(
		[AUTO_NAME],
		[LOT_AUTO_NAME],
		ShortLotAutoName,
		[RECYCLING_ITEM_MASTER_ID], 
		[RECYCLING_ORDER_ITEM_ID], 
		[RECYCLING_ORDER_ITEM_MERGED_ID], 
		[WEIGHT_RECEIVED], 
		[WEIGHT_TARE], 
		[WEIGHT_REMAIN], 
		ITEM_COUNT, 
		IS_INCLUDE_IN_INITIAL,
		PACKAGING_TYPE_ID, 
		WORKFLOW_STEP_ID, 
		[IS_CONSUMED_OR_PROCESSED], 
		LOCATION_ID, 
		NOTES, 
		IS_TEST_COMPLETE, 
		IS_AUDIT_COMPLETE, 
		PROCESSING_STATE_ID, 
		OUTBOUND_ORDER_ID
	)
GO

CREATE NONCLUSTERED INDEX [IX_OUTBOUND_ORDER_ID_IS_MERGED_EXPORTED_DT_USE_FOR_PRICE_CALCULATION]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([OUTBOUND_ORDER_ID] ASC, [IS_MERGED] ASC, [EXPORTED_DT] ASC, [USE_FOR_PRICE_CALCULATION])
    INCLUDE([AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName, [ITEM_COUNT], [ITEM_PRICE_OUTBOUND], [RECYCLING_ITEM_MASTER_ID], [RECYCLING_ORDER_ITEM_ID], [WEIGHT_RECEIVED], [WEIGHT_TARE], [WEIGHT_REMAIN], [PRICE_TYPE_ID_OUTBOUND], [INSERTED_DT])
GO

--used by [reports].[sp_GetRawRecyclingOnHandSummary]
CREATE NONCLUSTERED INDEX IX_F_RECYCLING_ORDER_ITEM_IS_INACTIVE_WORKFLOW_STEP_ID
	ON [dbo].[F_RECYCLING_ORDER_ITEM] (
		[IS_INACTIVE],
		[WORKFLOW_STEP_ID],
		[IS_CONSUMED_OR_PROCESSED],
		[PACKAGING_TYPE_ID],
		[RECYCLING_ORDER_ID]
	)
	INCLUDE (
		[RECYCLING_ORDER_ITEM_ID],
		[OUTBOUND_ORDER_ID],
		[RECYCLING_ITEM_MASTER_ID],
		[AUTO_NAME],
		[LOT_AUTO_NAME], 
		ShortLotAutoName,
		[ITEM_COUNT],
		[ITEM_PRICE],
		[PRICE_TYPE_ID],
		[ITEM_PRICE_OUTBOUND],
		[PRICE_TYPE_ID_OUTBOUND],
		[WEIGHT_RECEIVED],
		[WEIGHT_TARE],
		[LOCATION_ID],
		[NOTES],
		[WEIGHT_REMAIN],
		[IS_MERGED],
		[RECYCLING_ORDER_ITEM_MERGED_ID],
		[IS_TEST_COMPLETE],
		[IS_AUDIT_COMPLETE],
		[PROCESSING_STATE_ID],
		[INSERTED_DT],
		PARENT_ID
	)

GO
CREATE NONCLUSTERED INDEX [IX_IS_MERGED_INSERTED_DT]
    ON [dbo].[F_RECYCLING_ORDER_ITEM]([IS_MERGED] ASC, [INSERTED_DT] ASC)
    INCLUDE([RECYCLING_ORDER_ITEM_ID], [RECYCLING_ORDER_ID],OUTBOUND_ORDER_ID, EXPORTED_DT, [RECYCLING_ITEM_MASTER_ID], [AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName, [WEIGHT_RECEIVED], [WEIGHT_TARE], [PARENT_ID], [WEIGHT_REMAIN], [RECYCLING_ORDER_ITEM_MERGED_ID], ITEM_COUNT)
GO


CREATE NONCLUSTERED INDEX [IX__F_RECYCLING_ORDER_ITEM__RECYCLING_PURCHASES]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ID],[IS_GET_AFTER_SETTLE],[IS_MERGED])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ITEM_MASTER_ID],[AUTO_NAME], [LOT_AUTO_NAME], ShortLotAutoName,[ITEM_COUNT],[ITEM_PRICE],[PRICE_TYPE_ID],[WEIGHT_RECEIVED],[WEIGHT_TARE],[LOCATION_ID],[NOTES],[IS_INCLUDE_IN_INITIAL])
GO

CREATE NONCLUSTERED INDEX [IX_F_RECYCLING_ORDER_ITEM_RECYCLING_ORDER_ITEM_MERGED_ID]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_MERGED_ID])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ITEM_MASTER_ID],[ITEM_COUNT],[ITEM_PRICE_OUTBOUND],[PRICE_TYPE_ID_OUTBOUND],[WEIGHT_RECEIVED],[WEIGHT_TARE],[NOTES],[IS_MERGED],[USE_FOR_PRICE_CALCULATION],[MAIN_INNER_LOT_IN_MERGE_ID],[LOT_AUTO_NAME])

GO
--used by [reports].[sp_GetRecyclingDetailedBreakout]
CREATE NONCLUSTERED INDEX [IX__F_RECYCLING_ORDER_ITEM__IS_MERGED__INCLUDES]
	ON [dbo].[F_RECYCLING_ORDER_ITEM] ([IS_MERGED])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[RECYCLING_ORDER_ID],[OUTBOUND_ORDER_ID],[RECYCLING_ITEM_MASTER_ID],[ITEM_COUNT],[ITEM_PRICE],[ITEM_PRICE_OUTBOUND],[PRICE_TYPE_ID],[PRICE_TYPE_ID_OUTBOUND],[WEIGHT_RECEIVED],[WEIGHT_TARE],[PARENT_ID],[WEIGHT_REMAIN],[EXPORTED_DT],[RECYCLING_ORDER_ITEM_MERGED_ID],[INSERTED_DT],[LOT_AUTO_NAME],[IS_INCLUDE_IN_INITIAL])
GO

CREATE NONCLUSTERED INDEX [IX_F_RECYCLING_ORDER_ITEM_IS_INACTIVE_IS_CONSUMED_OR_PROCESSED]
ON [dbo].[F_RECYCLING_ORDER_ITEM] ([IS_INACTIVE],[IS_CONSUMED_OR_PROCESSED])
INCLUDE ([RECYCLING_ORDER_ID],[OUTBOUND_ORDER_ID],[RECYCLING_ITEM_MASTER_ID],[LOCATION_ID],[MAIN_INNER_LOT_IN_MERGE_ID],[StateProgramId])

GO

-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_UPD]
   ON  dbo.F_RECYCLING_ORDER_ITEM
   AFTER INSERT, UPDATE
AS 
BEGIN									

	UPDATE  dbo.F_RECYCLING_ORDER_ITEM SET
			CONSUMED_DT = GETUTCDATE()
	WHERE RECYCLING_ORDER_ITEM_ID IN 
		(
			SELECT I.RECYCLING_ORDER_ITEM_ID
			FROM INSERTED I 
			LEFT JOIN DELETED D 
				ON I.RECYCLING_ORDER_ITEM_ID = D.RECYCLING_ORDER_ITEM_ID
			WHERE I.WORKFLOW_STEP_ID = 8 AND I.WORKFLOW_STEP_ID <> ISNULL(D.WORKFLOW_STEP_ID, 0)	
		)
		
	UPDATE  dbo.F_RECYCLING_ORDER_ITEM SET
			CONSUMED_DT = NULL
	WHERE RECYCLING_ORDER_ITEM_ID IN 
		(
			SELECT I.RECYCLING_ORDER_ITEM_ID
			FROM INSERTED I 
			INNER JOIN DELETED D 
				ON I.RECYCLING_ORDER_ITEM_ID = D.RECYCLING_ORDER_ITEM_ID
			WHERE D.WORKFLOW_STEP_ID = 8 AND I.WORKFLOW_STEP_ID <> D.WORKFLOW_STEP_ID
		)
		
END
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_AFTER_INSERT_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM]
    AFTER INSERT
AS 
BEGIN									

    DECLARE @TAIL NVARCHAR(4) = N'"';
    DECLARE @DEF  NVARCHAR(4) = N'"-"';
    
    INSERT INTO F_LOG_DATA(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
    SELECT
	   'trg_RECYCLING_ORDER_ITEM_AFTER_INSERT',	   
	   NEW.[USER_ID],
	   NEW.USER_IP,
	   NEW.OPERATION_NAME,
	   5, --N'Recycling Lot',
	   NEW.ENTITY_KEY_VALUE,
	   NEW.ENTITY_AUTO_NAME,	   
	   [dbo].[fn_str_STRIP_XML_TAGS]((SELECT 
		  NEW.ORDER_AUTO_NAME
		  ,NEW.ITEM_TYPE
		  ,NEW.ITEM_COUNT
		  ,NEW.ITEM_PRICE
		  ,NEW.ITEM_PRICE_OUTBOUND
		  ,NEW.PRICE_TYPE_DESC
		  ,NEW.WEIGHT_RECEIVED
		  ,NEW.WEIGHT_TARE
		  ,NEW.PACKAGING_TYPE_DESC
		  ,NEW.WORKFLOW_TYPE_DESC
		  ,NEW.LOCATION_NAME		 
		  ,NEW.NOTES
		  ,NEW.REFERENCE
		  ,NEW.SORT_INFO
	   FOR XML
	   PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
    FROM
    (
	   SELECT
		  -- Fixed columns		  
		  N'Inserted'																			 AS OPERATION_NAME
		  ,I.INSERTED_BY_USER																	 AS [USER_ID]
		  ,I.INSERTED_BY_IP																		 AS USER_IP
		  ,I.RECYCLING_ORDER_ITEM_ID																 AS ENTITY_KEY_VALUE
		  ,CONVERT(NVARCHAR(30), I.RECYCLING_ORDER_ITEM_ID) + 
			 ISNULL(N'/'+ CASE 
				WHEN O.IS_INBOUND = 1 THEN OI.AUTO_NAME
				ELSE OO.AUTO_NAME
			 END, N'')																		 AS ENTITY_AUTO_NAME		  
		  -- XML columns
		  ,CASE
			 WHEN O.IS_INBOUND = 1 AND OI.IS_QUOTE = 1	    THEN N'Inbound Quote "'	  + OI.AUTO_NAME
			 WHEN O.IS_INBOUND = 1 AND OI.IS_REVISION = 1    THEN N'Inbound Revision "'  + OI.AUTO_NAME
			 WHEN O.IS_INBOUND = 1					    THEN N'Inbound Order "'	  + OI.AUTO_NAME
			 ELSE N'Outbound Order "'+  OO.AUTO_NAME
		  END																	+ @TAIL		 AS ORDER_AUTO_NAME
		  ,N'Item Type is "'		+ IM.RECYCLING_ITEM_MASTER_NAME						+ @TAIL		 AS ITEM_TYPE		  
		  ,N'Item Count is '		+ ISNULL(N'"' + CAST(I.ITEM_COUNT AS VARCHAR(100))		+ @TAIL, @DEF)	 AS ITEM_COUNT		  
		  ,N'Item Price is '		+ ISNULL(N'"' + CAST(I.ITEM_PRICE AS VARCHAR(20))			+ @TAIL, @DEF)	 AS ITEM_PRICE
		  ,N'Item Price Outbound is ' + ISNULL(N'"' + CAST(I.ITEM_PRICE_OUTBOUND AS VARCHAR(20))	+ @TAIL, @DEF)	 AS ITEM_PRICE_OUTBOUND
		  ,N'Price Type is '		+ ISNULL(N'"' + PT.PRICE_TYPE_DESC						+ @TAIL, @DEF)	 AS PRICE_TYPE_DESC
		  ,N'Weight Received is "'	+ CONVERT(NVARCHAR(20), I.WEIGHT_RECEIVED)				+ @TAIL		 AS WEIGHT_RECEIVED
		  ,N'Weight Tare is "'		+ CONVERT(NVARCHAR(20), I.WEIGHT_TARE)					+ @TAIL		 AS WEIGHT_TARE
		  ,N'Packaging Type is "'	+ PKGT.PACKAGING_TYPE_DESC							+ @TAIL		 AS PACKAGING_TYPE_DESC
		  ,N'Workflow Step is "'		+ WT.WORKFLOW_TYPE_DESC								+ @TAIL		 AS WORKFLOW_TYPE_DESC
		  ,N'Location is '			+ ISNULL(N'"' + L.LOCATION_NAME						+ @TAIL, @DEF)	 AS LOCATION_NAME
		  ,N'Notes are "'			+ I.NOTES											+ @TAIL		 AS NOTES
		  ,N'Reference is "'		+ I.REFERENCE										+ @TAIL		 AS REFERENCE
		  ,CASE
			WHEN IT.RECYCLING_ORDER_ITEM_ID IS NULL THEN NULL
			ELSE N'Lot is sorted from ' + IT.LOT_AUTO_NAME
		  END AS SORT_INFO
	   FROM		INSERTED						 I
	   INNER JOIN F_RECYCLING_ORDER				 O	    WITH (NOLOCK)
		  ON O.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_INBOUND			 OI	    WITH (NOLOCK)
		  ON OI.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 OO	    WITH (NOLOCK)
		  ON OO.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID

	   INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]	 IM	WITH (NOLOCK)
		ON I.RECYCLING_ITEM_MASTER_ID = IM.RECYCLING_ITEM_MASTER_ID
	   LEFT JOIN [dbo].[C_RECYCLING_PRICE_TYPE]		 PT	WITH (NOLOCK)
		ON I.PRICE_TYPE_ID = PT.PRICE_TYPE_ID
	   INNER JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	 PKGT WITH (NOLOCK)
		ON I.PACKAGING_TYPE_ID = PKGT.RECYCLING_PACKAGING_TYPE_ID
	   INNER JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]	 WT	WITH (NOLOCK)
		ON I.WORKFLOW_STEP_ID = WT.WORKFLOW_TYPE_ID
	   LEFT JOIN F_LOCATION						 L	WITH (NOLOCK)
		ON I.LOCATION_ID = l.LOCATION_ID	    
	   LEFT JOIN [dbo].[F_RECYCLING_ORDER_ITEM]		 IT	WITH (NOLOCK)
		ON I.PARENT_ID = IT.RECYCLING_ORDER_ITEM_ID
    ) NEW
		
END
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_AFTER_UPDATE_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM]
    AFTER UPDATE
AS 
BEGIN
								
    DECLARE @TAIL NVARCHAR(4) = N'"';
    DECLARE @DEF  NVARCHAR(4) = N'"-"';
    
    INSERT INTO F_LOG_DATA(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
    SELECT
	   'trg_RECYCLING_ORDER_ITEM_AFTER_UPDATE',	   
	   NEW.[USER_ID],
	   NEW.USER_IP,
	   NEW.OPERATION_NAME,
	   5, --N'Recycling Lot',
	   NEW.ENTITY_KEY_VALUE,
	   NEW.ENTITY_AUTO_NAME,	   
	   [dbo].[fn_str_STRIP_XML_TAGS]((SELECT 
		  NEW.ORDER_AUTO_NAME
		  ,NEW.ITEM_TYPE
		  ,NEW.ITEM_COUNT
		  ,NEW.ITEM_PRICE
		  ,NEW.ITEM_PRICE_OUTBOUND
		  ,NEW.PRICE_TYPE_DESC
		  ,NEW.WEIGHT_RECEIVED
		  ,NEW.WEIGHT_TARE
		  ,NEW.WEIGHT_REMAIN
		  ,NEW.PACKAGING_TYPE_DESC
		  ,NEW.WORKFLOW_TYPE_DESC
		  ,NEW.LOCATION_NAME -- THIS CAUSES Procedure trg_RECYCLING_ORDER_ITEM_AFTER_UPDATE, Line 15 - Error converting data type nvarchar to bigint.
		  ,NEW.NOTES
		  ,NEW.REFERENCE
		  ,NEW.OUTBOUND_ORDER
		  ,NEW.UPDATED_BY
	   FOR XML
	   PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
    FROM
    (
	   SELECT
		  -- Fixed columns		  
		  N'Updated'												AS OPERATION_NAME
		  ,I.UPDATED_BY_USER										AS [USER_ID]
		  ,I.UPDATED_BY_IP											AS [USER_IP]
		  ,ISNULL(I.RECYCLING_ORDER_ITEM_ID, D.RECYCLING_ORDER_ITEM_ID)		AS ENTITY_KEY_VALUE
		  ,CASE
			 WHEN O.RECYCLING_ORDER_ID IS NOT NULL THEN
				I.LOT_AUTO_NAME + 
				ISNULL(N'/'+ CASE 
				    WHEN O.IS_INBOUND = 1 THEN OI.AUTO_NAME
				    ELSE OO.AUTO_NAME
				END, N'')
			 ELSE D.LOT_AUTO_NAME + 
				ISNULL(N'/'+ CASE 
				    WHEN D_O.IS_INBOUND = 1 THEN D_OI.AUTO_NAME
				    ELSE D_OO.AUTO_NAME
				END, N'')		 
		  END													AS ENTITY_AUTO_NAME
		  -- XML columns
		  ,CASE
			 WHEN O.IS_INBOUND = 1 AND OI.IS_QUOTE = 1	    THEN N'Inbound Quote "'	  + OI.AUTO_NAME
			 WHEN O.IS_INBOUND = 1 AND OI.IS_REVISION = 1    THEN N'Inbound Revision "'  + OI.AUTO_NAME
			 WHEN O.IS_INBOUND = 1					    THEN N'Inbound Order "'	  + OI.AUTO_NAME
			 ELSE N'Outbound Order "'+  OO.AUTO_NAME
		  END + @TAIL												AS ORDER_AUTO_NAME
		  ,CASE
			 WHEN D.RECYCLING_ITEM_MASTER_ID <> I.RECYCLING_ITEM_MASTER_ID
			 THEN N'Item Type was ' + 
				N'"' + IM_D.RECYCLING_ITEM_MASTER_NAME	+ N'" is ' +
				N'"' + IM.RECYCLING_ITEM_MASTER_NAME	+ @TAIL
			 ELSE NULL
		  END													AS ITEM_TYPE
		  
		  ,CASE
			 WHEN ISNULL(D.ITEM_COUNT, 0) <> ISNULL(I.ITEM_COUNT, 0)
			 THEN N'Item Count was ' + 
				ISNULL(N'"' + CAST(D.ITEM_COUNT AS VARCHAR(100)) + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + CAST(I.ITEM_COUNT AS VARCHAR(100)) + @TAIL, @DEF)
			 ELSE NULL
		  END													AS ITEM_COUNT
		  
		  ,CASE
			 WHEN ISNULL(D.ITEM_PRICE, 0) <> ISNULL(I.ITEM_PRICE, 0)
			 THEN N'Item Price was ' + 
				ISNULL(N'"' + CAST(D.ITEM_PRICE AS VARCHAR(20)) + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + CAST(I.ITEM_PRICE AS VARCHAR(20)) + @TAIL, @DEF)
			 ELSE NULL
		  END													AS ITEM_PRICE
		  ,CASE
			 WHEN ISNULL(D.ITEM_PRICE_OUTBOUND, 0) <> ISNULL(I.ITEM_PRICE_OUTBOUND, 0)
			 THEN N'Item Price Outbound was ' + 
				ISNULL(N'"' + CAST(D.ITEM_PRICE_OUTBOUND AS VARCHAR(20)) + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + CAST(I.ITEM_PRICE_OUTBOUND AS VARCHAR(20)) + @TAIL, @DEF)
			 ELSE NULL
		  END													AS ITEM_PRICE_OUTBOUND
		  ,CASE
			 WHEN ISNULL(PT_D.PRICE_TYPE_DESC, N'') <> ISNULL(PT.PRICE_TYPE_DESC, N'')
			 THEN N'Price Type was ' + 
				ISNULL(N'"' + PT_D.PRICE_TYPE_DESC	 + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + PT.PRICE_TYPE_DESC	 + @TAIL, @DEF)
			 ELSE NULL
		  END													AS PRICE_TYPE_DESC
		  ,CASE
			 WHEN D.WEIGHT_RECEIVED <> I.WEIGHT_RECEIVED
			 THEN N'Weight Received was ' + 
				N'"' + CONVERT(NVARCHAR(20), D.WEIGHT_RECEIVED) + N'" is ' +
				N'"' + CONVERT(NVARCHAR(20), I.WEIGHT_RECEIVED) + @TAIL
			 ELSE NULL
		  END													AS WEIGHT_RECEIVED
		  ,CASE
			 WHEN D.WEIGHT_TARE <> I.WEIGHT_TARE
			 THEN N'Weight Tare was ' + 
				N'"' + CONVERT(NVARCHAR(20), D.WEIGHT_TARE) + N'" is ' +
				N'"' + CONVERT(NVARCHAR(20), I.WEIGHT_TARE) + @TAIL
			 ELSE NULL
		  END													AS WEIGHT_TARE
		  ,CASE
			 WHEN D.WEIGHT_REMAIN <> I.WEIGHT_REMAIN
			 THEN N'Weight Remain was ' + 
				N'"' + CONVERT(NVARCHAR(20), D.WEIGHT_REMAIN) + N'" is ' +
				N'"' + CONVERT(NVARCHAR(20), I.WEIGHT_REMAIN) + @TAIL
			 ELSE NULL
		  END													AS WEIGHT_REMAIN
		  ,CASE
			 WHEN D.PACKAGING_TYPE_ID <> I.PACKAGING_TYPE_ID
			 THEN N'Packaging Type was ' + 
				N'"' + PKGT_D.PACKAGING_TYPE_DESC	 + N'" is ' +
				N'"' + PKGT.PACKAGING_TYPE_DESC	 + @TAIL
			 ELSE NULL
		  END													AS PACKAGING_TYPE_DESC
		  ,CASE
			 WHEN D.WORKFLOW_STEP_ID <> I.WORKFLOW_STEP_ID
			 THEN N'Workflow Step was ' + 
				N'"' + WT_D.WORKFLOW_TYPE_DESC  + N'" is ' +
				N'"' + WT.WORKFLOW_TYPE_DESC	  + @TAIL
			 ELSE NULL
		  END													AS WORKFLOW_TYPE_DESC
		  ,CASE
			 WHEN ISNULL(D.LOCATION_ID, 0) <> ISNULL(I.LOCATION_ID, 0)
			 THEN N'Location was ' + 
				ISNULL(N'"' + L_D.LOCATION_NAME + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + L.LOCATION_NAME	  + @TAIL, @DEF)
			 ELSE NULL
		  END													AS LOCATION_NAME
		  ,CASE
			 WHEN ISNULL(D.NOTES, @DEF) <> ISNULL(I.NOTES, @DEF)
			 THEN N'Notes was ' + 
				ISNULL(N'"' + D.NOTES + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + I.NOTES + @TAIL, @DEF)
			 ELSE NULL
		  END AS NOTES
		  ,CASE
			WHEN ISNULL(D.REFERENCE, @DEF) <> ISNULL(I.REFERENCE, @DEF)
			THEN N'Reference was ' +
				ISNULL(N'"' + D.REFERENCE + N'"', @DEF) + N' is ' +
				ISNULL(N'"' + I.REFERENCE + @TAIL, @DEF)
			ELSE NULL
		  END AS REFERENCE
		  --Lot 13 was added into order OUT-381 | Commodity name: Computers - Scavenged | Weight: 978
		  ,CASE
			 WHEN ISNULL(D.OUTBOUND_ORDER_ID, 0)		!= ISNULL(I.OUTBOUND_ORDER_ID, 0)
			 THEN N'Lot ' + I.LOT_AUTO_NAME + 
					CASE 
						WHEN D.OUTBOUND_ORDER_ID IS NULL THEN ' was added into ' 
						ELSE ' was removed from '
					END
					+ ' order ' + ISNULL(OOO.AUTO_NAME, OOO2.AUTO_NAME) + ' | Commodity name: ' + 
				IM.RECYCLING_ITEM_MASTER_NAME + ' | Weight: ' + CAST(I.WEIGHT_RECEIVED 	AS VARCHAR(20))
			 ELSE NULL
		  END													AS OUTBOUND_ORDER
		  ,' Updated by "' + I.UPDATED_BY + '"'				AS UPDATED_BY
	   FROM		INSERTED						 I
	   LEFT JOIN F_RECYCLING_ORDER				 O	    WITH (NOLOCK)
		  ON O.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_INBOUND			 OI	    WITH (NOLOCK)
		  ON OI.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 OO	    WITH (NOLOCK)
		  ON OO.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 OOO	    WITH (NOLOCK)
		  ON I.OUTBOUND_ORDER_ID = OOO.RECYCLING_ORDER_ID	  
	   
	   INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]	 IM	    WITH (NOLOCK)
		  ON I.RECYCLING_ITEM_MASTER_ID = IM.RECYCLING_ITEM_MASTER_ID
	   LEFT JOIN [dbo].[C_RECYCLING_PRICE_TYPE]		 PT	    WITH (NOLOCK)
		  ON I.PRICE_TYPE_ID = PT.PRICE_TYPE_ID
	   INNER JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	 PKGT    WITH (NOLOCK)
		  ON I.PACKAGING_TYPE_ID = PKGT.RECYCLING_PACKAGING_TYPE_ID
	   INNER JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]	 WT	    WITH (NOLOCK)
		  ON I.WORKFLOW_STEP_ID = WT.WORKFLOW_TYPE_ID
	   LEFT JOIN F_LOCATION						 L	    WITH (NOLOCK)
		  ON I.LOCATION_ID = l.LOCATION_ID	   
	   
	   INNER JOIN	DELETED						 D
		  ON D.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_ID	 
	   LEFT JOIN F_RECYCLING_ORDER				 D_O	    WITH (NOLOCK)
		  ON D_O.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_INBOUND			 D_OI    WITH (NOLOCK)
		  ON D_OI.RECYCLING_ORDER_ID = D_O.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 D_OO    WITH (NOLOCK)
		  ON D_OO.RECYCLING_ORDER_ID = D_O.RECYCLING_ORDER_ID
	   LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 OOO2	    WITH (NOLOCK)
		  ON D.OUTBOUND_ORDER_ID = OOO2.RECYCLING_ORDER_ID
		   
	   INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]	 IM_D    WITH (NOLOCK)
		  ON D.RECYCLING_ITEM_MASTER_ID = IM_D.RECYCLING_ITEM_MASTER_ID
	   LEFT JOIN [dbo].[C_RECYCLING_PRICE_TYPE]		 PT_D    WITH (NOLOCK)
		  ON D.PRICE_TYPE_ID = PT_D.PRICE_TYPE_ID
	   INNER JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	 PKGT_D  WITH (NOLOCK)
		  ON D.PACKAGING_TYPE_ID = PKGT_D.RECYCLING_PACKAGING_TYPE_ID
	   INNER JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]	 WT_D    WITH (NOLOCK)
		  ON D.WORKFLOW_STEP_ID = WT_D.WORKFLOW_TYPE_ID
	   LEFT JOIN F_LOCATION						 L_D	    WITH (NOLOCK)
		  ON D.LOCATION_ID = L_D.LOCATION_ID
	   WHERE D.RECYCLING_ITEM_MASTER_ID			!= I.RECYCLING_ITEM_MASTER_ID
		  OR ISNULL(D.ITEM_COUNT, 0)			!= ISNULL(I.ITEM_COUNT, 0)
		  OR ISNULL(D.ITEM_PRICE, 0)			!= ISNULL(I.ITEM_PRICE, 0)
		  OR ISNULL(D.ITEM_PRICE_OUTBOUND, 0)	!= ISNULL(I.ITEM_PRICE_OUTBOUND, 0)
		  OR ISNULL(D.PRICE_TYPE_ID, 0)		!= ISNULL(I.PRICE_TYPE_ID, 0)
		  OR D.WEIGHT_RECEIVED				!= I.WEIGHT_RECEIVED
		  OR D.WEIGHT_TARE					!= I.WEIGHT_TARE
		  OR D.WEIGHT_REMAIN				!= I.WEIGHT_REMAIN
		  OR D.PACKAGING_TYPE_ID				!= I.PACKAGING_TYPE_ID
		  OR D.WORKFLOW_STEP_ID				!= I.WORKFLOW_STEP_ID
		  OR ISNULL(D.LOCATION_ID, 0)			!= ISNULL(I.LOCATION_ID, 0)
		  OR ISNULL(D.NOTES, '')				!= ISNULL(I.NOTES, '')	
		  OR ISNULL(D.REFERENCE, '')				!= ISNULL(I.REFERENCE, '')	
		  OR ISNULL(D.OUTBOUND_ORDER_ID, 0)		!= ISNULL(I.OUTBOUND_ORDER_ID, 0)    
    ) NEW
		
END
GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Items of the recycling order having the type of "item"', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_RECYCLING_ORDER_ITEM';

GO
CREATE TRIGGER dbo.tgr_AddNotification_AfterUpdate
 ON dbo.[F_RECYCLING_ORDER_ITEM]
    AFTER  UPDATE
AS
BEGIN
    SET NoCount ON

	DECLARE @params dbo.NOTIFICATION_QUEUE_PARAMS
	declare @tParams TABLE(
    itemId bigint,
    orderId bigint,
    customerId bigint )

	Declare @CustomerId BIGINT

	INSERT INTO @tParams
	SELECT
		i.RECYCLING_ORDER_ITEM_ID,
		i.RECYCLING_ORDER_ID,
		ro.CUSTOMER_ID
	FROM inserted i
		INNER JOIN deleted d ON i.[RECYCLING_ORDER_ITEM_ID] = d.[RECYCLING_ORDER_ITEM_ID]
		inner join dbo.F_RECYCLING_ORDER ro on i.RECYCLING_ORDER_ID = ro.RECYCLING_ORDER_ID
	WHERE i.IS_AUDIT_COMPLETE = 1 /*COMPLETED*/
		AND Coalesce(d.IS_AUDIT_COMPLETE, 0) != 1

	IF EXISTS (SELECT TOP(1) 1 FROM @tParams)
	BEGIN
		insert into @params (name, value) values 
		('RECYCLING_ORDER_ITEM_ID', (select top(1) itemId from @tParams)),
		('ORDER_ID', (select top(1) orderId from @tParams)),
		('CUSTOMER_ID', (select top(1) customerId from @tParams))

		SET @CustomerId = (select top(1) customerId from @tParams);

		----add notifications about item change
		EXEC sp_ADD_NOTIFICATION_TO_QUEUE
			@C_NOTIFICATION_TYPE_ID = 31 -- Asset audit is completed
			,@C_PARAMS = @params
			,@C_ENTITY_CUSTOMER_ID = @CustomerId
	END
END


GO
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_AFTER_INS_UPD_RAW_ACTION_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM]
    AFTER INSERT, UPDATE
AS 
BEGIN
	IF (ROWCOUNT_BIG() = 0)
		RETURN;

	DECLARE	@utc datetime = getutcdate();
	DECLARE @currentTransactionId bigint = CURRENT_TRANSACTION_ID();
								
    INSERT INTO [dbo].[F_LotActionLogRaw] (
		[PreviousLotId],
		[PreviousLotShortAutoName],
		[ActualLotId],
		[ActualLotShortAutoName],
		[PreviousInboundOrderId],
		[PreviousInboundOrderAutoName],
		[ActualInboundOrderId],
		[ActualInboundOrderAutoName],
		[PreviousOutboundOrderId],
		[PreviousOutboundOrderAutoName],
		[ActualOutboundOrderId],
		[ActualOutboundOrderAutoName],
		[PreviousCommodityId],
		[PreviousCommodityName],
		[ActualCommodityId],
		[ActualCommodityName],
		[PreviousWeightReceived],
		[ActualWeightReceived],
		[PreviousWeightTare],
		[ActualWeightTare],
		[PreviousWeightRemain],
		[ActualWeightRemain],
		[PreviousWeightLooseLoad],
		[ActualWeightLooseLoad],
		[PreviousPackagingTypeId],
		[PreviousPackagingTypeName],
		[ActualPackagingTypeId],
		[ActualPackagingTypeName],
		[PreviousWorkflowStepId],
		[PreviousWorkflowStepName],
		[ActualWorkflowStepId],
		[ActualWorkflowStepName],
		[PreviousLocationId],
		[PreviousLocationName],
		[ActualLocationId],
		[ActualLocationName],
		[PreviousWarehouseId],
		[PreviousWarehouseName],
		[ActualWarehouseId],
		[ActualWarehouseName],
		[PreviousParentLotId],
		[PreviousParentLotShortAutoName],
		[ActualParentLotId],
		[ActualParentLotShortAutoName],
		[PreviousExportedDt],
		[ActualExportedDt],
		[PreviousConsumedInWarehouseId],
		[PreviousConsumedInWarehouseName],
		[ActualConsumedInWarehouseId],
		[ActualConsumedInWarehouseName],
		[PreviousConsumedDt],
		[ActualConsumedDt],
		[PreviousIsMerged],
		[ActualIsMerged],
		[PreviousLotMergedId],
		[PreviousLotMergedShortAutoName],
		[ActualLotMergedId],
		[ActualLotMergedShortAutoName],
		[PreviousInitialInboundOrderId],
		[PreviousInitialInboundOrderAutoName],
		[ActualInitialInboundOrderId],
		[ActualInitialInboundOrderAutoName],
		[PreviousIsAuditComplete],
		[ActualIsAuditComplete],
		[PreviousIsInactive],
		[ActualIsInactive],
		[PreviousMergeDt],
		[ActualMergeDt],
		[PreviousMainInnerLotInMergeId],
		[ActualMainInnerLotInMergeId],
		[PreviousAuditCompleteDate],
		[ActualAuditCompleteDate],
		[PreviousAuditStartDate],
		[ActualAuditStartDate],
		[PreviousConsumedWeight],
		[ActualConsumedWeight],
		[OutboundOrderWarehouseId],
		[InsertedByUserId],
		[CurrentTransactionId]
    )
	SELECT
		D.RECYCLING_ORDER_ITEM_ID							AS [PreviousLotId],
		D.ShortLotAutoName									AS [PreviousLotShortAutoName],
		I.RECYCLING_ORDER_ITEM_ID							AS [ActualLotId],
		I.ShortLotAutoName									AS [ActualLotShortAutoName],
		D.RECYCLING_ORDER_ID								AS [PreviousInboundOrderId],
		DOI.AUTO_NAME										AS [PreviousInboundOrderAutoName],
		I.RECYCLING_ORDER_ID								AS [ActualInboundOrderId],
		IOI.AUTO_NAME										AS [ActualInboundOrderAutoName],
		D.OUTBOUND_ORDER_ID									AS [PreviousOutboundOrderId],
		DOO.AUTO_NAME										AS [PreviousOutboundOrderAutoName],
		I.OUTBOUND_ORDER_ID									AS [ActualOutboundOrderId],
		IOO.AUTO_NAME										AS [ActualOutboundOrderAutoName],
		D.RECYCLING_ITEM_MASTER_ID							AS [PreviousCommodityId],
		DC.RECYCLING_ITEM_MASTER_NAME						AS [PreviousCommodityName],
		I.RECYCLING_ITEM_MASTER_ID							AS [ActualCommodityId],
		IC.RECYCLING_ITEM_MASTER_NAME						AS [ActualCommodityName],
		D.WEIGHT_RECEIVED									AS [PreviousWeightReceived],
		I.WEIGHT_RECEIVED									AS [ActualWeightReceived],
		D.WEIGHT_TARE										AS [PreviousWeightTare],
		I.WEIGHT_TARE										AS [ActualWeightTare],
		D.WEIGHT_REMAIN										AS [PreviousWeightRemain],
		I.WEIGHT_REMAIN										AS [ActualWeightRemain],
		D.WEIGHT_LOOSE_LOAD									AS [PreviousWeightLooseLoad],
		I.WEIGHT_LOOSE_LOAD									AS [ActualWeightLooseLoad],
		D.PACKAGING_TYPE_ID									AS [PreviousPackagingTypeId],
		DPACK.PACKAGING_TYPE_DESC							AS [PreviousPackagingTypeName],
		I.PACKAGING_TYPE_ID									AS [ActualPackagingTypeId],
		IPACK.PACKAGING_TYPE_DESC							AS [ActualPackagingTypeName],
		D.WORKFLOW_STEP_ID									AS [PreviousWorkflowStepId],
		DWT.WORKFLOW_TYPE_DESC								AS [PreviousWorkflowStepName],
		I.WORKFLOW_STEP_ID									AS [ActualWorkflowStepId],
		IWT.WORKFLOW_TYPE_DESC								AS [ActualWorkflowStepName],
		D.LOCATION_ID										AS [PreviousLocationId],
		DL.LOCATION_NAME									AS [PreviousLocationName],
		I.LOCATION_ID										AS [ActualLocationId],
		IL.LOCATION_NAME									AS [ActualLocationName],
		ISNULL(DL.[WAREHOUSE_ID], DRO.[WAREHOUSE_ID])		AS [PreviousWarehouseId],
		DWH.WAREHOUSE_CD									AS [PreviousWarehouseName],
		ISNULL(IL.[WAREHOUSE_ID], IRO.[WAREHOUSE_ID])		AS [ActualWarehouseId],
		IWH.WAREHOUSE_CD									AS [ActualWarehouseName],
		D.PARENT_ID											AS [PreviousParentLotId],
		DPL.ShortLotAutoName								AS [PreviousParentLotShortAutoName],
		I.PARENT_ID											AS [ActualParentLotId],
		IPL.ShortLotAutoName								AS [ActualParentLotShortAutoName],
		D.EXPORTED_DT										AS [PreviousExportedDt],
		I.EXPORTED_DT										AS [ActualExportedDt],
		D.CONSUMED_IN_WAREHOUSE_ID							AS [PreviousConsumedInWarehouseId],
		DCW.WAREHOUSE_CD									AS [PreviousConsumedInWarehouseName],
		I.CONSUMED_IN_WAREHOUSE_ID							AS [ActualConsumedInWarehouseId],
		ICW.WAREHOUSE_CD									AS [ActualConsumedInWarehouseName],
		D.CONSUMED_DT										AS [PreviousConsumedDt],
		I.CONSUMED_DT										AS [ActualConsumedDt],
		D.IS_MERGED											AS [PreviousIsMerged],
		I.IS_MERGED											AS [ActualIsMerged],
		D.RECYCLING_ORDER_ITEM_MERGED_ID					AS [PreviousLotMergedId],
		DML.ShortLotAutoName								AS [PreviousLotMergedShortAutoName],
		I.RECYCLING_ORDER_ITEM_MERGED_ID					AS [ActualLotMergedId],
		IML.ShortLotAutoName								AS [ActualLotMergedShortAutoName],
		D.RECYCLING_ORDER_INITIAL_ID						AS [PreviousInitialInboundOrderId],
		DIOI.AUTO_NAME										AS [PreviousInitialInboundOrderAutoName],
		I.RECYCLING_ORDER_INITIAL_ID						AS [ActualInitialInboundOrderId],
		IIOI.AUTO_NAME										AS [ActualInitialInboundOrderAutoName],
		D.IS_AUDIT_COMPLETE									AS [PreviousIsAuditComplete],
		I.IS_AUDIT_COMPLETE									AS [ActualIsAuditComplete],
		D.IS_INACTIVE										AS [PreviousIsInactive],
		I.IS_INACTIVE										AS [ActualIsInactive],
		D.MERGE_DT											AS [PreviousMergeDt],
		I.MERGE_DT											AS [ActualMergeDt],
		D.MAIN_INNER_LOT_IN_MERGE_ID						AS [PreviousMainInnerLotInMergeId],
		I.MAIN_INNER_LOT_IN_MERGE_ID						AS [ActualMainInnerLotInMergeId],
		D.AuditCompleteDate									AS [PreviousAuditCompleteDate],
		I.AuditCompleteDate									AS [ActualAuditCompleteDate],
		D.AuditStartDate									AS [PreviousAuditStartDate],
		I.AuditStartDate									AS [ActualAuditStartDate],
		D.ConsumedWeight									AS [PreviousConsumedWeight],
		I.ConsumedWeight									AS [ActualConsumedWeight],
		ISNULL(IROO.WAREHOUSE_ID, DROO.WAREHOUSE_ID)		AS [OutboundOrderWarehouseId],

		IIf(D.INSERTED_BY_USER IS NULL AND I.INSERTED_BY_USER IS NOT NULL,
			I.INSERTED_BY_USER,
			ISNULL(I.UPDATED_BY_USER, I.INSERTED_BY_USER))	AS [InsertedByUserId],

		@currentTransactionId
    
	FROM INSERTED									I
	LEFT JOIN DELETED								D
		ON D.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_ID

	LEFT JOIN F_RECYCLING_ORDER						IRO WITH (NOLOCK)
		ON IRO.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				IOI WITH (NOLOCK)
		ON IOI.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	
	LEFT JOIN F_RECYCLING_ORDER						DRO WITH (NOLOCK)
		ON DRO.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				DOI WITH (NOLOCK)
		ON DOI.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_ID

	LEFT JOIN F_RECYCLING_ORDER						IROO WITH (NOLOCK)
		ON IROO.RECYCLING_ORDER_ID = I.OUTBOUND_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			IOO WITH (NOLOCK)
		ON IOO.RECYCLING_ORDER_ID = I.OUTBOUND_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER						DROO WITH (NOLOCK)
		ON DROO.RECYCLING_ORDER_ID = D.OUTBOUND_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			DOO WITH (NOLOCK)
		ON DOO.RECYCLING_ORDER_ID = D.OUTBOUND_ORDER_ID
	   
	LEFT JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		IC WITH (NOLOCK)
		ON IC.RECYCLING_ITEM_MASTER_ID = I.RECYCLING_ITEM_MASTER_ID
	LEFT JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		DC WITH (NOLOCK)
		ON DC.RECYCLING_ITEM_MASTER_ID = D.RECYCLING_ITEM_MASTER_ID

	LEFT JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	IPACK WITH (NOLOCK)
		ON IPACK.RECYCLING_PACKAGING_TYPE_ID = I.PACKAGING_TYPE_ID
	LEFT JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	DPACK WITH (NOLOCK)
		ON DPACK.RECYCLING_PACKAGING_TYPE_ID = D.PACKAGING_TYPE_ID

	LEFT JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]		IWT WITH (NOLOCK)
		ON IWT.WORKFLOW_TYPE_ID = I.WORKFLOW_STEP_ID
	LEFT JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]		DWT WITH (NOLOCK)
		ON DWT.WORKFLOW_TYPE_ID = D.WORKFLOW_STEP_ID

	LEFT JOIN F_LOCATION							IL WITH (NOLOCK)
		ON IL.LOCATION_ID = I.LOCATION_ID
	LEFT JOIN F_LOCATION							DL WITH (NOLOCK)
		ON DL.LOCATION_ID = D.LOCATION_ID

	LEFT JOIN [dbo].[D_WAREHOUSE]					IWH WITH(NOLOCK)
		ON IWH.[WAREHOUSE_ID] = ISNULL(IL.[WAREHOUSE_ID], IRO.[WAREHOUSE_ID])
	LEFT JOIN [dbo].[D_WAREHOUSE]					DWH WITH(NOLOCK)
		ON DWH.[WAREHOUSE_ID] = ISNULL(DL.[WAREHOUSE_ID], DRO.[WAREHOUSE_ID])

	LEFT JOIN F_RECYCLING_ORDER_ITEM				IPL WITH (NOLOCK)
		ON IPL.RECYCLING_ORDER_ITEM_ID = I.PARENT_ID
	LEFT JOIN F_RECYCLING_ORDER_ITEM				DPL WITH (NOLOCK)
		ON DPL.RECYCLING_ORDER_ITEM_ID = D.PARENT_ID
	   
	LEFT JOIN D_WAREHOUSE							ICW WITH (NOLOCK)
		ON ICW.WAREHOUSE_ID = I.CONSUMED_IN_WAREHOUSE_ID
	LEFT JOIN D_WAREHOUSE							DCW WITH (NOLOCK)
		ON DCW.WAREHOUSE_ID = D.CONSUMED_IN_WAREHOUSE_ID

	LEFT JOIN F_RECYCLING_ORDER_ITEM				IML WITH (NOLOCK)
		ON IML.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_MERGED_ID
	LEFT JOIN F_RECYCLING_ORDER_ITEM				DML WITH (NOLOCK)
		ON DML.RECYCLING_ORDER_ITEM_ID = D.RECYCLING_ORDER_ITEM_MERGED_ID

	LEFT JOIN F_RECYCLING_ORDER_INBOUND				IIOI WITH (NOLOCK)
		ON IIOI.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_INITIAL_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				DIOI WITH (NOLOCK)
		ON DIOI.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_INITIAL_ID

	WHERE I.[NotForRawActionLog] = 0
		AND (
			ISNULL(D.RECYCLING_ORDER_ID, 0) != ISNULL(I.RECYCLING_ORDER_ID, 0)
			OR ISNULL(D.OUTBOUND_ORDER_ID, 0) != ISNULL(I.OUTBOUND_ORDER_ID, 0)
			OR ISNULL(D.RECYCLING_ITEM_MASTER_ID, 0) != ISNULL(I.RECYCLING_ITEM_MASTER_ID, 0)
			OR ISNULL(D.WEIGHT_RECEIVED, 0) != ISNULL(I.WEIGHT_RECEIVED, 0)
			OR ISNULL(D.WEIGHT_TARE, 0) != ISNULL(I.WEIGHT_TARE, 0)
			OR ISNULL(D.WEIGHT_REMAIN, 0) != ISNULL(I.WEIGHT_REMAIN, 0)
			OR ISNULL(D.WEIGHT_LOOSE_LOAD, 0) != ISNULL(I.WEIGHT_LOOSE_LOAD, 0)
			OR ISNULL(D.PACKAGING_TYPE_ID, 0) != ISNULL(I.PACKAGING_TYPE_ID, 0)
			OR ISNULL(D.WORKFLOW_STEP_ID, 0) != ISNULL(I.WORKFLOW_STEP_ID, 0)
			OR ISNULL(D.LOCATION_ID, 0) != ISNULL(I.LOCATION_ID, 0)
			OR ISNULL(D.PARENT_ID, 0) != ISNULL(I.PARENT_ID, 0)
			OR ISNULL(D.EXPORTED_DT, @utc) != ISNULL(I.EXPORTED_DT, @utc)
			OR ISNULL(D.CONSUMED_IN_WAREHOUSE_ID, 0) != ISNULL(I.CONSUMED_IN_WAREHOUSE_ID, 0)
			OR ISNULL(D.CONSUMED_DT, @utc) != ISNULL(I.CONSUMED_DT, @utc)
			OR ISNULL(D.IS_MERGED, 0) != ISNULL(I.IS_MERGED, 0)
			OR ISNULL(D.RECYCLING_ORDER_ITEM_MERGED_ID, 0) != ISNULL(I.RECYCLING_ORDER_ITEM_MERGED_ID, 0)
			OR ISNULL(D.RECYCLING_ORDER_INITIAL_ID, 0) != ISNULL(I.RECYCLING_ORDER_INITIAL_ID, 0)
			OR ISNULL(D.IS_AUDIT_COMPLETE, 0) != ISNULL(I.IS_AUDIT_COMPLETE, 0)
			OR ISNULL(D.IS_INACTIVE, 0) != ISNULL(I.IS_INACTIVE, 0)
			OR ISNULL(D.MERGE_DT, @utc) != ISNULL(I.MERGE_DT, @utc)
			OR ISNULL(D.MAIN_INNER_LOT_IN_MERGE_ID, 0) != ISNULL(I.MAIN_INNER_LOT_IN_MERGE_ID, 0)
			OR ISNULL(D.AuditCompleteDate, @utc) != ISNULL(I.AuditCompleteDate, @utc)
			--OR ISNULL(D.AuditStartDate, @utc) != ISNULL(I.AuditStartDate, @utc)
			OR ISNULL(D.ConsumedWeight, 0) != ISNULL(I.ConsumedWeight, 0)
		)
		
END
GO

CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_AFTER_DEL_RAW_ACTION_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM]
    AFTER DELETE
AS 
BEGIN
	IF (ROWCOUNT_BIG() = 0)
		RETURN;

	DECLARE	@utc datetime = getutcdate();
	DECLARE @currentTransactionId bigint = CURRENT_TRANSACTION_ID();
								
    INSERT INTO [dbo].[F_LotActionLogRaw] (
		[PreviousLotId],
		[PreviousLotShortAutoName],
		[ActualLotId],
		[ActualLotShortAutoName],
		[PreviousInboundOrderId],
		[PreviousInboundOrderAutoName],
		[ActualInboundOrderId],
		[ActualInboundOrderAutoName],
		[PreviousOutboundOrderId],
		[PreviousOutboundOrderAutoName],
		[ActualOutboundOrderId],
		[ActualOutboundOrderAutoName],
		[PreviousCommodityId],
		[PreviousCommodityName],
		[ActualCommodityId],
		[ActualCommodityName],
		[PreviousWeightReceived],
		[ActualWeightReceived],
		[PreviousWeightTare],
		[ActualWeightTare],
		[PreviousWeightRemain],
		[ActualWeightRemain],
		[PreviousWeightLooseLoad],
		[ActualWeightLooseLoad],
		[PreviousPackagingTypeId],
		[PreviousPackagingTypeName],
		[ActualPackagingTypeId],
		[ActualPackagingTypeName],
		[PreviousWorkflowStepId],
		[PreviousWorkflowStepName],
		[ActualWorkflowStepId],
		[ActualWorkflowStepName],
		[PreviousLocationId],
		[PreviousLocationName],
		[ActualLocationId],
		[ActualLocationName],
		[PreviousWarehouseId],
		[PreviousWarehouseName],
		[ActualWarehouseId],
		[ActualWarehouseName],
		[PreviousParentLotId],
		[PreviousParentLotShortAutoName],
		[ActualParentLotId],
		[ActualParentLotShortAutoName],
		[PreviousExportedDt],
		[ActualExportedDt],
		[PreviousConsumedInWarehouseId],
		[PreviousConsumedInWarehouseName],
		[ActualConsumedInWarehouseId],
		[ActualConsumedInWarehouseName],
		[PreviousConsumedDt],
		[ActualConsumedDt],
		[PreviousIsMerged],
		[ActualIsMerged],
		[PreviousLotMergedId],
		[PreviousLotMergedShortAutoName],
		[ActualLotMergedId],
		[ActualLotMergedShortAutoName],
		[PreviousInitialInboundOrderId],
		[PreviousInitialInboundOrderAutoName],
		[ActualInitialInboundOrderId],
		[ActualInitialInboundOrderAutoName],
		[PreviousIsAuditComplete],
		[ActualIsAuditComplete],
		[PreviousIsInactive],
		[ActualIsInactive],
		[PreviousMergeDt],
		[ActualMergeDt],
		[PreviousMainInnerLotInMergeId],
		[ActualMainInnerLotInMergeId],
		[PreviousAuditCompleteDate],
		[ActualAuditCompleteDate],
		[PreviousAuditStartDate],
		[ActualAuditStartDate],
		[PreviousConsumedWeight],
		[ActualConsumedWeight],
		[InsertedByUserId],
		[CurrentTransactionId]
    )
	SELECT
		D.RECYCLING_ORDER_ITEM_ID							AS [PreviousLotId],
		D.ShortLotAutoName									AS [PreviousLotShortAutoName],
		I.RECYCLING_ORDER_ITEM_ID							AS [ActualLotId],
		I.ShortLotAutoName									AS [ActualLotShortAutoName],
		D.RECYCLING_ORDER_ID								AS [PreviousInboundOrderId],
		DOI.AUTO_NAME										AS [PreviousInboundOrderAutoName],
		I.RECYCLING_ORDER_ID								AS [ActualInboundOrderId],
		IOI.AUTO_NAME										AS [ActualInboundOrderAutoName],
		D.OUTBOUND_ORDER_ID									AS [PreviousOutboundOrderId],
		DOO.AUTO_NAME										AS [PreviousOutboundOrderAutoName],
		I.OUTBOUND_ORDER_ID									AS [ActualOutboundOrderId],
		IOO.AUTO_NAME										AS [ActualOutboundOrderAutoName],
		D.RECYCLING_ITEM_MASTER_ID							AS [PreviousCommodityId],
		DC.RECYCLING_ITEM_MASTER_NAME						AS [PreviousCommodityName],
		I.RECYCLING_ITEM_MASTER_ID							AS [ActualCommodityId],
		IC.RECYCLING_ITEM_MASTER_NAME						AS [ActualCommodityName],
		D.WEIGHT_RECEIVED									AS [PreviousWeightReceived],
		I.WEIGHT_RECEIVED									AS [ActualWeightReceived],
		D.WEIGHT_TARE										AS [PreviousWeightTare],
		I.WEIGHT_TARE										AS [ActualWeightTare],
		D.WEIGHT_REMAIN										AS [PreviousWeightRemain],
		I.WEIGHT_REMAIN										AS [ActualWeightRemain],
		D.WEIGHT_LOOSE_LOAD									AS [PreviousWeightLooseLoad],
		I.WEIGHT_LOOSE_LOAD									AS [ActualWeightLooseLoad],
		D.PACKAGING_TYPE_ID									AS [PreviousPackagingTypeId],
		DPACK.PACKAGING_TYPE_DESC							AS [PreviousPackagingTypeName],
		I.PACKAGING_TYPE_ID									AS [ActualPackagingTypeId],
		IPACK.PACKAGING_TYPE_DESC							AS [ActualPackagingTypeName],
		D.WORKFLOW_STEP_ID									AS [PreviousWorkflowStepId],
		DWT.WORKFLOW_TYPE_DESC								AS [PreviousWorkflowStepName],
		I.WORKFLOW_STEP_ID									AS [ActualWorkflowStepId],
		IWT.WORKFLOW_TYPE_DESC								AS [ActualWorkflowStepName],
		D.LOCATION_ID										AS [PreviousLocationId],
		DL.LOCATION_NAME									AS [PreviousLocationName],
		I.LOCATION_ID										AS [ActualLocationId],
		IL.LOCATION_NAME									AS [ActualLocationName],
		ISNULL(DL.[WAREHOUSE_ID], DRO.[WAREHOUSE_ID])		AS [PreviousWarehouseId],
		DWH.WAREHOUSE_CD									AS [PreviousWarehouseName],
		ISNULL(IL.[WAREHOUSE_ID], IRO.[WAREHOUSE_ID])		AS [ActualWarehouseId],
		IWH.WAREHOUSE_CD									AS [ActualWarehouseName],
		D.PARENT_ID											AS [PreviousParentLotId],
		DPL.ShortLotAutoName								AS [PreviousParentLotShortAutoName],
		I.PARENT_ID											AS [ActualParentLotId],
		IPL.ShortLotAutoName								AS [ActualParentLotShortAutoName],
		D.EXPORTED_DT										AS [PreviousExportedDt],
		I.EXPORTED_DT										AS [ActualExportedDt],
		D.CONSUMED_IN_WAREHOUSE_ID							AS [PreviousConsumedInWarehouseId],
		DCW.WAREHOUSE_CD									AS [PreviousConsumedInWarehouseName],
		I.CONSUMED_IN_WAREHOUSE_ID							AS [ActualConsumedInWarehouseId],
		ICW.WAREHOUSE_CD									AS [ActualConsumedInWarehouseName],
		D.CONSUMED_DT										AS [PreviousConsumedDt],
		I.CONSUMED_DT										AS [ActualConsumedDt],
		D.IS_MERGED											AS [PreviousIsMerged],
		I.IS_MERGED											AS [ActualIsMerged],
		D.RECYCLING_ORDER_ITEM_MERGED_ID					AS [PreviousLotMergedId],
		DML.ShortLotAutoName								AS [PreviousLotMergedShortAutoName],
		I.RECYCLING_ORDER_ITEM_MERGED_ID					AS [ActualLotMergedId],
		IML.ShortLotAutoName								AS [ActualLotMergedShortAutoName],
		D.RECYCLING_ORDER_INITIAL_ID						AS [PreviousInitialInboundOrderId],
		DIOI.AUTO_NAME										AS [PreviousInitialInboundOrderAutoName],
		I.RECYCLING_ORDER_INITIAL_ID						AS [ActualInitialInboundOrderId],
		IIOI.AUTO_NAME										AS [ActualInitialInboundOrderAutoName],
		D.IS_AUDIT_COMPLETE									AS [PreviousIsAuditComplete],
		I.IS_AUDIT_COMPLETE									AS [ActualIsAuditComplete],
		D.IS_INACTIVE										AS [PreviousIsInactive],
		I.IS_INACTIVE										AS [ActualIsInactive],
		D.MERGE_DT											AS [PreviousMergeDt],
		I.MERGE_DT											AS [ActualMergeDt],
		D.MAIN_INNER_LOT_IN_MERGE_ID						AS [PreviousMainInnerLotInMergeId],
		I.MAIN_INNER_LOT_IN_MERGE_ID						AS [ActualMainInnerLotInMergeId],
		D.AuditCompleteDate									AS [PreviousAuditCompleteDate],
		I.AuditCompleteDate									AS [ActualAuditCompleteDate],
		D.AuditStartDate									AS [PreviousAuditStartDate],
		I.AuditStartDate									AS [ActualAuditStartDate],
		D.ConsumedWeight									AS [PreviousConsumedWeight],
		I.ConsumedWeight									AS [ActualConsumedWeight],
		
		IIf(D.INSERTED_BY_USER IS NULL AND I.INSERTED_BY_USER IS NOT NULL,
			I.INSERTED_BY_USER,
			ISNULL(I.UPDATED_BY_USER, I.INSERTED_BY_USER))	AS [InsertedByUserId],

		@currentTransactionId
    
	FROM DELETED									D
	LEFT JOIN INSERTED								I
		ON D.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_ID

	LEFT JOIN F_RECYCLING_ORDER						IRO WITH (NOLOCK)
		ON IRO.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				IOI WITH (NOLOCK)
		ON IOI.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
	
	LEFT JOIN F_RECYCLING_ORDER						DRO WITH (NOLOCK)
		ON DRO.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				DOI WITH (NOLOCK)
		ON DOI.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_ID

	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			IOO WITH (NOLOCK)
		ON IOO.RECYCLING_ORDER_ID = I.OUTBOUND_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			DOO WITH (NOLOCK)
		ON DOO.RECYCLING_ORDER_ID = D.OUTBOUND_ORDER_ID
	   
	LEFT JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		IC WITH (NOLOCK)
		ON IC.RECYCLING_ITEM_MASTER_ID = I.RECYCLING_ITEM_MASTER_ID
	LEFT JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		DC WITH (NOLOCK)
		ON DC.RECYCLING_ITEM_MASTER_ID = D.RECYCLING_ITEM_MASTER_ID

	LEFT JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	IPACK WITH (NOLOCK)
		ON IPACK.RECYCLING_PACKAGING_TYPE_ID = I.PACKAGING_TYPE_ID
	LEFT JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	DPACK WITH (NOLOCK)
		ON DPACK.RECYCLING_PACKAGING_TYPE_ID = D.PACKAGING_TYPE_ID

	LEFT JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]		IWT WITH (NOLOCK)
		ON IWT.WORKFLOW_TYPE_ID = I.WORKFLOW_STEP_ID
	LEFT JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]		DWT WITH (NOLOCK)
		ON DWT.WORKFLOW_TYPE_ID = D.WORKFLOW_STEP_ID

	LEFT JOIN F_LOCATION							IL WITH (NOLOCK)
		ON IL.LOCATION_ID = I.LOCATION_ID
	LEFT JOIN F_LOCATION							DL WITH (NOLOCK)
		ON DL.LOCATION_ID = D.LOCATION_ID

	LEFT JOIN [dbo].[D_WAREHOUSE]					IWH WITH(NOLOCK)
		ON IWH.[WAREHOUSE_ID] = ISNULL(IL.[WAREHOUSE_ID], IRO.[WAREHOUSE_ID])
	LEFT JOIN [dbo].[D_WAREHOUSE]					DWH WITH(NOLOCK)
		ON DWH.[WAREHOUSE_ID] = ISNULL(DL.[WAREHOUSE_ID], DRO.[WAREHOUSE_ID])

	LEFT JOIN F_RECYCLING_ORDER_ITEM				IPL WITH (NOLOCK)
		ON IPL.RECYCLING_ORDER_ITEM_ID = I.PARENT_ID
	LEFT JOIN F_RECYCLING_ORDER_ITEM				DPL WITH (NOLOCK)
		ON DPL.RECYCLING_ORDER_ITEM_ID = D.PARENT_ID
	   
	LEFT JOIN D_WAREHOUSE							ICW WITH (NOLOCK)
		ON ICW.WAREHOUSE_ID = I.CONSUMED_IN_WAREHOUSE_ID
	LEFT JOIN D_WAREHOUSE							DCW WITH (NOLOCK)
		ON DCW.WAREHOUSE_ID = D.CONSUMED_IN_WAREHOUSE_ID

	LEFT JOIN F_RECYCLING_ORDER_ITEM				IML WITH (NOLOCK)
		ON IML.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_MERGED_ID
	LEFT JOIN F_RECYCLING_ORDER_ITEM				DML WITH (NOLOCK)
		ON DML.RECYCLING_ORDER_ITEM_ID = D.RECYCLING_ORDER_ITEM_MERGED_ID

	LEFT JOIN F_RECYCLING_ORDER_INBOUND				IIOI WITH (NOLOCK)
		ON IIOI.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_INITIAL_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND				DIOI WITH (NOLOCK)
		ON DIOI.RECYCLING_ORDER_ID = D.RECYCLING_ORDER_INITIAL_ID

	WHERE D.[NotForRawActionLog] = 0
		AND (
			ISNULL(D.RECYCLING_ORDER_ID, 0) != ISNULL(I.RECYCLING_ORDER_ID, 0)
			OR ISNULL(D.OUTBOUND_ORDER_ID, 0) != ISNULL(I.OUTBOUND_ORDER_ID, 0)
			OR ISNULL(D.RECYCLING_ITEM_MASTER_ID, 0) != ISNULL(I.RECYCLING_ITEM_MASTER_ID, 0)
			OR ISNULL(D.WEIGHT_RECEIVED, 0) != ISNULL(I.WEIGHT_RECEIVED, 0)
			OR ISNULL(D.WEIGHT_TARE, 0) != ISNULL(I.WEIGHT_TARE, 0)
			OR ISNULL(D.WEIGHT_REMAIN, 0) != ISNULL(I.WEIGHT_REMAIN, 0)
			OR ISNULL(D.WEIGHT_LOOSE_LOAD, 0) != ISNULL(I.WEIGHT_LOOSE_LOAD, 0)
			OR ISNULL(D.PACKAGING_TYPE_ID, 0) != ISNULL(I.PACKAGING_TYPE_ID, 0)
			OR ISNULL(D.WORKFLOW_STEP_ID, 0) != ISNULL(I.WORKFLOW_STEP_ID, 0)
			OR ISNULL(D.LOCATION_ID, 0) != ISNULL(I.LOCATION_ID, 0)
			OR ISNULL(D.PARENT_ID, 0) != ISNULL(I.PARENT_ID, 0)
			OR ISNULL(D.EXPORTED_DT, @utc) != ISNULL(I.EXPORTED_DT, @utc)
			OR ISNULL(D.CONSUMED_IN_WAREHOUSE_ID, 0) != ISNULL(I.CONSUMED_IN_WAREHOUSE_ID, 0)
			OR ISNULL(D.CONSUMED_DT, @utc) != ISNULL(I.CONSUMED_DT, @utc)
			OR ISNULL(D.IS_MERGED, 0) != ISNULL(I.IS_MERGED, 0)
			OR ISNULL(D.RECYCLING_ORDER_ITEM_MERGED_ID, 0) != ISNULL(I.RECYCLING_ORDER_ITEM_MERGED_ID, 0)
			OR ISNULL(D.RECYCLING_ORDER_INITIAL_ID, 0) != ISNULL(I.RECYCLING_ORDER_INITIAL_ID, 0)
			OR ISNULL(D.IS_AUDIT_COMPLETE, 0) != ISNULL(I.IS_AUDIT_COMPLETE, 0)
			OR ISNULL(D.IS_INACTIVE, 0) != ISNULL(I.IS_INACTIVE, 0)
			OR ISNULL(D.MERGE_DT, @utc) != ISNULL(I.MERGE_DT, @utc)
			OR ISNULL(D.MAIN_INNER_LOT_IN_MERGE_ID, 0) != ISNULL(I.MAIN_INNER_LOT_IN_MERGE_ID, 0)
			OR ISNULL(D.AuditCompleteDate, @utc) != ISNULL(I.AuditCompleteDate, @utc)
			--OR ISNULL(D.AuditStartDate, @utc) != ISNULL(I.AuditStartDate, @utc)
			OR ISNULL(D.ConsumedWeight, 0) != ISNULL(I.ConsumedWeight, 0)
		)

END
GO


-- when workflow changed and previous workflow BusinessUnitId <> current workflow BusinessUnitId
-- or audit started but lot (workflow/inbound order/customer) has recycling BusinessUnitId (1)
create trigger [dbo].[trg_RECYCLING_ORDER_ITEM_AfterUpdate_LogLotWorkflowChange]
    on [dbo].[F_RECYCLING_ORDER_ITEM]
    after update
    as
begin
    set nocount on;

    insert into [recycling].[F_LotWorkflowChangeLog] ([LotId],
                                                      [BusinessUnitId],
                                                      [WorkflowId],
                                                      [RecyclingOrderId],
                                                      [WarehouseId],
                                                      [LocationId],
                                                      [PackagingId],
                                                      [CommodityId],
                                                      [CustomerId],
                                                      [Activity],
                                                      [WeightReceived],
                                                      [WeightTare],
                                                      [WeightRemain],
                                                      [WeightLooseLoad],
                                                      [ItemPrice],
                                                      [ItemPriceTypeId],
                                                      [ItemCount],
                                                      [InsertedBy],
                                                      [InsertedByUserId])
    select NEW_ITEM.[RECYCLING_ORDER_ITEM_ID],
           coalesce(NEW_ITEM.[BusinessUnitId], CRWT.[BusinessUnitId], FROINB.[BusinessUnitId], FC.[BusinessUnitId]),
           NEW_ITEM.[WORKFLOW_STEP_ID],
           NEW_ITEM.[RECYCLING_ORDER_ID],
           FRO.[WAREHOUSE_ID],
           NEW_ITEM.[LOCATION_ID],
           NEW_ITEM.[PACKAGING_TYPE_ID],
           NEW_ITEM.[RECYCLING_ITEM_MASTER_ID],
           FRO.[CUSTOMER_ID],
           N'Workflow Step Changed',
           NEW_ITEM.[WEIGHT_RECEIVED],
           NEW_ITEM.[WEIGHT_TARE],
           NEW_ITEM.[WEIGHT_REMAIN],
           NEW_ITEM.[WEIGHT_LOOSE_LOAD],
           NEW_ITEM.[ITEM_PRICE],
           NEW_ITEM.[PRICE_TYPE_ID],
           NEW_ITEM.[ITEM_COUNT],
           isnull(object_schema_name(@@procid) + '.', '') + object_name(@@procid),
           coalesce(NEW_ITEM.[UPDATED_BY_USER], NEW_ITEM.[INSERTED_BY_USER], 1)
    from inserted NEW_ITEM
             inner join deleted OLD_ITEM
                        on NEW_ITEM.[RECYCLING_ORDER_ITEM_ID] = OLD_ITEM.[RECYCLING_ORDER_ITEM_ID]
             inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT with (nolock)
                        on NEW_ITEM.[WORKFLOW_STEP_ID] = CRWT.[WORKFLOW_TYPE_ID]
             inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT_OLD with (nolock)
                        on OLD_ITEM.[WORKFLOW_STEP_ID] = CRWT_OLD.[WORKFLOW_TYPE_ID]
             inner join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
                        on NEW_ITEM.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID]
             inner join [dbo].[F_CUSTOMER] FC with (nolock)
                        on FRO.[CUSTOMER_ID] = FC.[CUSTOMER_ID]
             left join [dbo].[F_RECYCLING_ORDER_INBOUND] FROINB with (nolock)
                       on FRO.[RECYCLING_ORDER_ID] = FROINB.[RECYCLING_ORDER_ID]
    where NEW_ITEM.[BusinessUnitId] <> OLD_ITEM.[BusinessUnitId]
		and CRWT.IsFinal = 1

    insert into [recycling].[F_LotWorkflowChangeLog] ([LotId],
                                                      [BusinessUnitId],
                                                      [WorkflowId],
                                                      [RecyclingOrderId],
                                                      [WarehouseId],
                                                      [LocationId],
                                                      [PackagingId],
                                                      [CommodityId],
                                                      [CustomerId],
                                                      [Activity],
                                                      [WeightReceived],
                                                      [WeightTare],
                                                      [WeightRemain],
                                                      [WeightLooseLoad],
                                                      [ItemPrice],
                                                      [ItemPriceTypeId],
                                                      [ItemCount],
                                                      [InsertedBy],
                                                      [InsertedByUserId])
    select NEW_ITEM.[RECYCLING_ORDER_ITEM_ID],
           coalesce(NEW_ITEM.[BusinessUnitId], CRWT.[BusinessUnitId], FROINB.[BusinessUnitId], FC.[BusinessUnitId]),
           NEW_ITEM.[WORKFLOW_STEP_ID],
           NEW_ITEM.[RECYCLING_ORDER_ID],
           FRO.[WAREHOUSE_ID],
           NEW_ITEM.[LOCATION_ID],
           NEW_ITEM.[PACKAGING_TYPE_ID],
           NEW_ITEM.[RECYCLING_ITEM_MASTER_ID],
           FRO.[CUSTOMER_ID],
           N'Audit Started',
           NEW_ITEM.[WEIGHT_RECEIVED],
           NEW_ITEM.[WEIGHT_TARE],
           NEW_ITEM.[WEIGHT_REMAIN],
           NEW_ITEM.[WEIGHT_LOOSE_LOAD],
           NEW_ITEM.[ITEM_PRICE],
           NEW_ITEM.[PRICE_TYPE_ID],
           NEW_ITEM.[ITEM_COUNT],
           isnull(object_schema_name(@@procid) + '.', '') + object_name(@@procid),
           coalesce(NEW_ITEM.[UPDATED_BY_USER], NEW_ITEM.[INSERTED_BY_USER], 1)
    from inserted NEW_ITEM
             inner join deleted OLD_ITEM
                        on NEW_ITEM.[RECYCLING_ORDER_ITEM_ID] = OLD_ITEM.[RECYCLING_ORDER_ITEM_ID]
             inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT with (nolock)
                        on NEW_ITEM.[WORKFLOW_STEP_ID] = CRWT.[WORKFLOW_TYPE_ID]
             inner join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
                        on NEW_ITEM.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID]
             inner join [dbo].[F_CUSTOMER] FC with (nolock)
                        on FRO.[CUSTOMER_ID] = FC.[CUSTOMER_ID]
             left join [dbo].[F_RECYCLING_ORDER_INBOUND] FROINB with (nolock)
                       on FRO.[RECYCLING_ORDER_ID] = FROINB.[RECYCLING_ORDER_ID]
    where OLD_ITEM.[AuditStartDate] is null
      and NEW_ITEM.[AuditStartDate] is not null
      and coalesce(NEW_ITEM.[BusinessUnitId], CRWT.[BusinessUnitId], FROINB.[BusinessUnitId], FC.[BusinessUnitId]) =
          1 -- Recycling
end

go

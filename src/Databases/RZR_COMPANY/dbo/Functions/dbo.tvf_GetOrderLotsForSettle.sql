CREATE FUNCTION [dbo].[tvf_GetOrderLotsForSettle]
(
	@OrderId bigint
)
RETURNS @t TABLE
(
	LotId bigint
)
AS
BEGIN
	declare
		@settlementStateId	int,
		@isInbound			bit,
		@inboundStatusId	int,
		@outboundStatusId	int

	select
		@settlementStateId = o.RECYCLING_SETTLEMENT_STATE_ID,
		@isInbound = o.IS_INBOUND,
		@inboundStatusId = i.StatusId,
		@outboundStatusId = o.RECYCLING_ORDER_STATUS_ID
	from dbo.F_RECYCLING_ORDER				o with(nolock)
	left join dbo.F_RECYCLING_ORDER_INBOUND	i with(nolock)
		on o.RECYCLING_ORDER_ID = i.RECYCLING_ORDER_ID
	where o.RECYCLING_ORDER_ID = @OrderId

	if (
		(@isInbound = 1 and @inboundStatusId not in (5, 6))		-- Received, Settlement Complete
		or
		(@isInbound = 0 and @outboundStatusId not in (7, 5))	-- Loading Complete, Settlement Complete
		)
	begin
		return;
	end 


	insert @t(LotId)
	select		
		oi.RECYCLING_ORDER_ITEM_ID				AS LotId
	from dbo.F_RECYCLING_ORDER_ITEM				oi with(nolock)
	inner join [dbo].[F_PRODUCT_MASTER]			pm with(nolock)
		on PM.PRODUCT_MASTER_TYPE_ID = 3
		and OI.RECYCLING_ITEM_MASTER_ID = PM.MODULE_MASTER_ID		
	left join recycling.F_SortedLotFromBatch	FSL with(nolock)
		on FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
	where
		(
			@isInbound = 1
				and OI.RECYCLING_ORDER_ID = @OrderId
				and OI.IS_MERGED = 0
				and OI.IS_GET_AFTER_SETTLE = 0	-- SORTED BEFORE THE SETTLEMENT
				and FSL.InputId is null			-- the lot that came out of the shredder is not sent to reprocessing
			or
			@isInbound = 0
				and OI.OUTBOUND_ORDER_ID = @OrderId
				and OI.USE_FOR_PRICE_CALCULATION = 1
		)
		and
		(
			@isInbound = 0
			or
				(
					(
						@settlementStateId = 2
						or
						@settlementStateId = 3
							and not exists(select top(1) 1 from [recycling].F_Asset with (nolock) where [RecyclingOrderItemId] = OI.RECYCLING_ORDER_ITEM_ID and [IsGetAfterSettle] = 0)
					) 
					and
					(
						not exists(select top(1) 1
									from F_RECYCLING_ORDER_ITEM TOI with (nolock)
									where OI.RECYCLING_ORDER_ID = TOI.RECYCLING_ORDER_ID
										and TOI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
										and 
											(
												TOI.PARENT_ID = OI.RECYCLING_ORDER_ITEM_ID
												or
												TOI.PARENT_ID in (
													select RECYCLING_ORDER_ITEM_PRIMARY_ID
													from [dbo].[F_RECYCLING_ORDER_ITEM_MERGE] with (nolock)
													where RECYCLING_ORDER_ITEM_ID = OI.RECYCLING_ORDER_ITEM_ID)
											)
						)
					) 
					or
					(@settlementStateId = 1 or @settlementStateId = 5)
						and OI.IS_INCLUDE_IN_INITIAL = 1
				)
		)


	return
END

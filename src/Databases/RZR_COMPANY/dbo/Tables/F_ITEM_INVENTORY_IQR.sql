CREATE TABLE [dbo].[F_ITEM_INVENTORY_IQR] (
    [ITEM_MASTER_ID]         BIGINT         NULL,
    [ITEM_NUMBER]            NVARCHAR (256) NULL,
    [IQR_INVENTORY_ID]       INT            NULL,
    [IQR_PT_ID]              INT            NULL,
    [IQR_PT_LINE]            INT            NULL,
    [IQR_UNIT_COST]          MONEY          NULL,
    [IQR_UNIT_COST_ORIGINAL] MONEY          NULL,
    [IQR_CONDITION_CD]       CHAR (4)       NULL,
    [IQR_LOCATION]           CHAR (10)      NULL,
    [IQR_SERIAL_NUMBER]      CHAR (25)      NULL,
    [IQR_COMMENTS]           TEXT           NULL,
    [IQR_SYS_COMMENTS]       TEXT           NULL,
    [IQR_STATUS_CD]          CHAR (11)      NULL,
    [IQR_RECEIVE_STATUS_CD]  CHAR (18)      NULL,
    [IQR_DATE_RECEIVED]      DATE           NULL,
    [IS_INACTIVE]            BIT            NOT NULL,
    [IS_DELETED]             BIT            NOT NULL,
    [INSERTED_BY]            VARCHAR (150)  NOT NULL,
    [INSERTED_DT]            DATETIME       NOT NULL,
    [UPDATED_BY]             VARCHAR (150)  NULL,
    [UPDATED_DT]             DATETIME       NULL,
    [DELETED_BY]             VARCHAR (150)  NULL,
    [DELETED_DT]             DATETIME       NULL
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_IQR';


 -- =============================================
-- Author:		<PERSON>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_SUBSTITUTE_ITEMS]
--declare 
	@ITEM_MASTER_ID BIGINT = 356736
AS
BEGIN
	
	SELECT
		  @ITEM_MASTER_ID			AS MasterItemId
		 ,NULL						AS ItemNumber
		 ,NULL						AS ItemId
		 ,FIM.ITEM_NUMBER			AS ItemNumberSub
		 ,T.ItemIdSub				AS ItemIdSub
		 ,(
			SELECT
				COUNT(DISTINCT ITEM_INVENTORY_ID) FROM F_ITEM_INVENTORY WITH (NOLOCK) 
			WHERE ITEM_MASTER_ID = FIM.ITEM_MASTER_ID
		 )							AS Qty
		,T.Inserted					AS Inserted
	FROM (	
		SELECT
			FIMS.SUBSTITUTE_ITEM_MASTER_ID	AS ItemIdSub,
			MIN(FIMS.INSERTED_DT)			AS Inserted
		FROM [dbo].[vw_F_ITEM_MASTER_SUBSTITUTES_ALL]	FIMS WITH(NOLOCK)
		WHERE FIMS.ITEM_MASTER_ID = @ITEM_MASTER_ID
		GROUP BY 
			FIMS.SUBSTITUTE_ITEM_MASTER_ID
	) T
	INNER JOIN F_ITEM_MASTER	FIM	WITH(NOLOCK)
		ON FIM.ITEM_MASTER_ID = T.ItemIdSub
END
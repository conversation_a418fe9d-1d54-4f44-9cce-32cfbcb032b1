

-- =============================================
-- Author:		V.DREBEZOVA
-- Create date: 12/23/2014
-- Description:	return TAGS AND GROUPS
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_GROUP_TAGS]	
--DECLARE
    @ORDER_COLUMN_NAME  VARCHAR(250)	= N'TagId',
    @ORDER_DIRECTION    VARCHAR(10)		= N'ASC',
    @ITEMS_PER_PAGE	    INT				= 20,
    @PAGE_INDEX			INT				= 0,
    @FILTER_WHERE	    NVARCHAR(MAX)	= ''
AS
BEGIN
    DECLARE 
	   @startRowNumber	   bigint		  =  @PAGE_INDEX * @ITEMS_PER_PAGE + 1,
	   @endRowNumber	   bigint		  = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE,		
	   @filterCondition	   NVARCHAR(MAX) = N'';	

    IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
    BEGIN
	   SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N') '
    END	
		
    DECLARE @query NVARCHAR (MAX) = '		
	   DECLARE 
		@GROUP_COUNT INT = 0
	   SELECT @GROUP_COUNT = COUNT(TAG_GROUP_ID)
	   FROM [dbo].[F_TAG_GROUP] WITH (NOLOCK);	   

	   WITH m_data AS
	   (
		  SELECT
			 T.[TAG_ID]			AS TagId
			 ,T.[TAG_GROUP_ID]	AS GroupId
			 ,T.[TAG_CD]		AS [Name]
			 ,T.[TAG_DESC]		AS Description
			 ,tt.[TAG_TYPE_CD]	AS GroupTypes
			 ,NULL				AS IsSingle
			 ,1					AS IsTag
		  FROM [dbo].[F_TAG]		    T	WITH (NOLOCK)
		  left join [dbo].[F_TAG_GROUP]  TG	WITH (NOLOCK)
		    ON T.[TAG_GROUP_ID] = TG.[TAG_GROUP_ID]
		  left join [dbo].[C_TAG_TYPE] tt
			on t.TypeId = tt.[TAG_TYPE_ID]
	   )		
	   
	   SELECT TOP(1)
			-1			AS RowNumberID,
			-1			AS RowID,
			COUNT (TagId) + @GROUP_COUNT AS TagId,
			0			AS GroupId,
			NULL		AS [Name],
			NULL		AS Description,
			NULL		AS GroupTypes,
			NULL		AS IsSingle,
			1			AS IsTag
	   FROM m_data m
	   UNION ALL
	   SELECT
			temp.*
	   FROM
	   (		
	   SELECT
		  ROW_NUMBER() OVER(ORDER BY TT.GroupId, TT.IsTag) AS RowNumberID,
		  TT.RowID,
		  TT.TagId,	
		  TT.GroupId,
		  TT.[Name],
		  TT.Description,
		  TT.GroupTypes,
		  TT.IsSingle,
		  TT.IsTag
	   FROM
	   (
		  --SELECT TOP(1)
			 ---1			AS RowID,
			 --COUNT (TagId) + @GROUP_COUNT AS TagId,
			 --0			AS GroupId,
			 --NULL		AS [Name],
			 --NULL		AS Description,
			 --NULL		AS GroupTypes,
			 --NULL		AS IsSingle,
			 --1			AS IsTag
		  --FROM m_data m
		  --' + @filterCondition + N'
		  --UNION ALL
		  SELECT
			 t.RowID,
			 t.TagId,	
			 t.GroupId,
			 t.[Name],
			 t.Description,
			 t.GroupTypes,
			 t.IsSingle,
			 t.IsTag
		  FROM (SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				* 
			 FROM m_data m	
			 ' + @filterCondition + ') t
		  UNION ALL
		  SELECT 
			 GR.TAG_GROUP_ID * 1000 AS RowID 
			 ,NULL			 AS TagId
			 ,GR.[TAG_GROUP_ID]	 AS GroupId
			 ,GR.[TAG_GROUP_CD]	 AS [Name]
			 ,GR.[TAG_GROUP_DESC] AS [Description]
			 ,LTRIM(STUFF((
						SELECT
							'', '' + TT.[TAG_TYPE_CD] AS "data()" 
						FROM [dbo].[F_TAG_GROUP] TG 	WITH(NOLOCK)	
						INNER JOIN 	[dbo].[F_TAG_GROUP_TYPE] TGT WITH (NOLOCK)			
							ON TG.[TAG_GROUP_ID] = TGT.[TAG_GROUP_ID]
						INNER JOIN [dbo].[C_TAG_TYPE] TT WITH (NOLOCK)
							ON TGT.[TAG_TYPE_ID] = TT.[TAG_TYPE_ID]
						WHERE TG.[TAG_GROUP_ID] = GR.TAG_GROUP_ID						
						FOR XML PATH('''')),1 ,2 ,''''))
			 ,GR.[IS_SINGLE_TAG] AS IsSingle
			 ,0					 AS IsTag
		  FROM [dbo].[F_TAG_GROUP] GR WITH (NOLOCK)' +
	   ') TT 
	    ) temp
	    WHERE temp.RowNumberID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100)) + ''

	   --select @query
	   EXEC sp_executesql @query;
END
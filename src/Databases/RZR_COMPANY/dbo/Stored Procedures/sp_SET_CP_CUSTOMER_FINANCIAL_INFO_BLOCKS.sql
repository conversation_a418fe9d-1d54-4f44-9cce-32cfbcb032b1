-- =============================================
-- Author:		<PERSON>
-- Create date: 12/08/2015
-- Description:	Sets visibility of financial blocks on CP for customer
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS]
	@C_CUSTOMER_ID						BIGINT
	,@C_CP_CUSTOMER_FINANCIAL_BLOCKS	[dbo].[bigint_Bit_Array] READONLY
AS
BEGIN
	DECLARE @NOW DATETIME = GETUTCDATE()
	;WITH m_target AS
	(
		SELECT
			cfib.*
		FROM [cp].F_CUSTOMER_FINANCIAL_INFO_BLOCK	cfib
		WHERE cfib.CUSTOMER_ID = @C_CUSTOMER_ID
	)
	MERGE m_target AS target
	USING @C_CP_CUSTOMER_FINANCIAL_BLOCKS AS source
		ON	target.FINANCIAL_INFO_BLOCK_ID = source.ID
	WHEN MATCHED AND (target.IS_ENABLED <> source.VALUE) 
		THEN
			UPDATE SET 
				IS_ENABLED = SOURCE.Value
				,UPDATED_BY = N'sp_SET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS'
				,UPDATED_DT = @NOW
	WHEN NOT MATCHED BY target AND source.VALUE = 1 
		THEN
			INSERT 
			(
				FINANCIAL_INFO_BLOCK_ID
				,CUSTOMER_ID
				,IS_ENABLED
				,INSERTED_BY
				,INSERTED_DT
			)
			VALUES 
			(
				source.ID
				,@C_CUSTOMER_ID
				,SOURCE.Value
				,N'sp_SET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS'
				,@NOW
			);	
END
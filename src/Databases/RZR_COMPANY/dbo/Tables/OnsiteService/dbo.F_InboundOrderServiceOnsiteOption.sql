CREATE TABLE [dbo].[F_InboundOrderServiceOnsiteOption] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [ServiceTypeId]                             INT             NOT NULL,
    [ServiceOnsiteOptionHierarchyId]            INT             NOT NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderServiceOnsiteOption_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderServiceOnsiteOption_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceOnsiteOption_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceOnsiteOption_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderServiceOnsiteOption] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderServiceOnsiteOption_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceOnsiteOption_C_RECYCLING_ITEM_SERVICE_TYPE] FOREIGN KEY ([ServiceTypeId]) REFERENCES [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] ([SERVICE_TYPE_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceOnsiteOption_D_ServiceOnsiteOptionHierarchy] FOREIGN KEY ([ServiceOnsiteOptionHierarchyId]) REFERENCES [dbo].[D_ServiceOnsiteOptionHierarchy] ([Id]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderServiceOnsiteOption]([RecyclingOrderId])
    INCLUDE([ServiceTypeId], [ServiceOnsiteOptionHierarchyId]);
GO

CREATE NONCLUSTERED INDEX [IX_ServiceTypeId]
    ON [dbo].[F_InboundOrderServiceOnsiteOption]([ServiceTypeId])
    INCLUDE([RecyclingOrderId], [ServiceOnsiteOptionHierarchyId]);
GO

CREATE NONCLUSTERED INDEX [IX_ServiceOnsiteOptionHierarchyId]
    ON [dbo].[F_InboundOrderServiceOnsiteOption]([ServiceOnsiteOptionHierarchyId])
    INCLUDE ([RecyclingOrderId], [ServiceTypeId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Onsite option for an Service Type in Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderServiceOnsiteOption';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_ServiceTypeId_ServiceOnsiteOptionHierarchyId]
    ON [dbo].[F_InboundOrderServiceOnsiteOption]([RecyclingOrderId] ASC, [ServiceTypeId] ASC, [ServiceOnsiteOptionHierarchyId] ASC);


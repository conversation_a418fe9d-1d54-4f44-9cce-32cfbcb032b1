 
CREATE PROCEDURE [dbo].[sp_SET_MAGENTO_CUSTOMER] 
	@CUSTOMER_ID BIGINT,
	@CUSTOMER_NAME	VARCHAR(150),		
	@FIRST_NAME VARCHAR(150),
	@LAST_NAME VARCHAR(150),
	@MIDDLE_INITIAL VARCHAR(5),	
	@MAIN_EMAIL VARCHAR(50)
AS
BEGIN

	DECLARE
		@REP_ID		 INT = [dbo].[fn_bigint_GET_USER_ADMIN_ID](),
		@ST_TERMS_ID INT = 1,
		@PT_TERMS_ID INT = 2

	IF (ISNULL(@CUSTOMER_ID, -1) = -1) BEGIN
	
		INSERT INTO dbo.F_CUSTOMER 
			(CUSTOMER_NAME,
			 REP_ID,
			 ST_TERMS_ID,
			 PT_TERMS_ID,
			 IS_INACTIVE,
			 IS_FROM_MAGENTO,
			 INSERTED_BY_USER,
			 INSERTED_BY,
			 INSERTED_DT)
		VALUES ( 			
			@CUSTOMER_NAME,
			@REP_ID,
			@ST_TERMS_ID,
			@PT_TERMS_ID,
			0,
			1,
			@REP_ID,
			'sp_SET_MAGENTO_CUSTOMER',
			GETUTCDATE()
		)
		SET @CUSTOMER_ID = SCOPE_IDENTITY();

		exec [dbo].[sp_SetDefaultSlaTemplateToCustomer]
			@CustomerId				= @CUSTOMER_ID,
			@UserId					= 1,
			@UseInternalTransaction	= 0
						
	END
	ELSE BEGIN
		UPDATE dbo.F_CUSTOMER SET
			CUSTOMER_NAME = @CUSTOMER_NAME,
			REP_ID = @REP_ID,
			ST_TERMS_ID = @ST_TERMS_ID,
			PT_TERMS_ID = @PT_TERMS_ID,
			UPDATED_BY = 'sp_SET_MAGENTO_CUSTOMER',
			UPDATED_DT = GETUTCDATE()
		WHERE CUSTOMER_ID = @CUSTOMER_ID	
	END
	
	DECLARE
		@CUSTOMER_CONTACT_ID BIGINT	

	SELECT 
		@CUSTOMER_CONTACT_ID = CUSTOMER_CONTACT_ID
	FROM dbo.F_CUSTOMER_CONTACT WITH (NOLOCK) 
	WHERE FIRST_NAME = @FIRST_NAME 
	  AND LAST_NAME = @LAST_NAME 
	  AND ISNULL(MIDDLE_INITIAL, '') = ISNULL(@MIDDLE_INITIAL, '')
				
	IF (@CUSTOMER_CONTACT_ID IS NULL) BEGIN
				
		INSERT INTO dbo.F_CUSTOMER_CONTACT (
			CUSTOMER_ID, 
			FIRST_NAME,
			LAST_NAME,
			MIDDLE_INITIAL,				
			MAIN_EMAIL,			
			IS_MAIN,
			INSERTED_BY,
			INSERTED_DT)
		SELECT
			@CUSTOMER_ID,
			@FIRST_NAME,
			@LAST_NAME,
			@MIDDLE_INITIAL,			
			@MAIN_EMAIL,		
			CASE
				WHEN EXISTS(SELECT 1 FROM dbo.F_CUSTOMER_CONTACT cc WITH (NOLOCK) WHERE CUSTOMER_ID = @CUSTOMER_ID AND IS_MAIN = 1)
					THEN 0
				ELSE 1					
			END,
			'sp_SET_MAGENTO_CUSTOMER',
			GETUTCDATE()
	END		
	ELSE BEGIN
	
		UPDATE dbo.F_CUSTOMER_CONTACT SET
			CUSTOMER_ID = @CUSTOMER_ID, 
			FIRST_NAME = @FIRST_NAME,
			LAST_NAME = @LAST_NAME,
			MIDDLE_INITIAL = @MIDDLE_INITIAL,				
			MAIN_EMAIL = @MAIN_EMAIL,		
			UPDATED_BY = 'sp_SET_MAGENTO_CUSTOMER',
			UPDATED_DT = GETUTCDATE()
		WHERE CUSTOMER_CONTACT_ID =	@CUSTOMER_CONTACT_ID	
	
	END		
	
	SELECT @CUSTOMER_ID AS CUSTOMER_ID;	
END
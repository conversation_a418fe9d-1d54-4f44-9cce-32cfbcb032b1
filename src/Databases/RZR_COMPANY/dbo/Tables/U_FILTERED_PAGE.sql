CREATE TABLE [dbo].[U_FILTERED_PAGE] (
    [PAGE_ID]             INT            IDENTITY (1, 1) NOT NULL,
    [PAGE_FILE_NAME]      NVARCHAR (100) NOT NULL,
    [IS_INACTIVE]         BIT            CONSTRAINT [DF_U_FILTERED_PAGE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]          BIT            CONSTRAINT [DF_U_FILTERED_PAGE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY_USER_ID] BIGINT         NOT NULL,
    [INSERTED_DT]         DATETIME       NOT NULL,
    [UPDATED_BY_USER_ID]  BIGINT         NULL,
    [UPDATED_DT]          DATETIME       NULL,
    [DELETED_BY_USER_ID]  BIGINT         NULL,
    [DELETED_DT]          DATETIME       NULL,
    CONSTRAINT [PK_U_FILTERED_TABLE] PRIMARY KEY CLUSTERED ([PAGE_ID] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Dictionary of filter manageable pages', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'U_FILTERED_PAGE';


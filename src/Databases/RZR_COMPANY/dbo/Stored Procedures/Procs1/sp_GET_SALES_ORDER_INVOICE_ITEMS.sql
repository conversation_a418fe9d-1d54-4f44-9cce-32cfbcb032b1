CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_INVOICE_ITEMS]
    @ORDER_ID	BIGINT,
    @GROUPED	BIT	    = 0
AS
BEGIN	

    IF (ISNULL(@GROUPED, 0) = 0)
    BEGIN
	   SELECT 
		  I.SALES_ORDER_ITEM_ID											AS SALES_ORDER_ITEM_ID
		  ,i.ITEM_INVENTORY_ID											AS ITEM_INVENTORY_ID
		  ,SKU.ITEM_ID													AS SKU_ID
		  ,M.ITEM_NUMBER												AS ITEM_NUMBER
		  ,MFG.MANUFACTURER_CD											AS MANUFACTURER_CD
		  ,INV.ITEM_INVENTORY_SERIAL									AS SERIAL
		  ,INV.ITEM_INVENTORY_UNIQUE_ID									AS UNIQUE_ID
		  ,ISNULL(INV.LOCATION_ID, INV.LOCATION_PREV_ID)				AS LOCATION_ID
		  ,(CASE 
				WHEN INV.LOCATION_ID IS NOT NULL THEN dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(INV.LOCATION_ID)
				ELSE dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(L.LOCATION_ID)
			END)														AS LOCATION_NAME	
		  ,I.ITEM_PRICE													AS PRICE
		  ,(I.ITEM_PRICE * I.ITEM_QUANTITY)								AS FULL_PRICE
		  ,1															AS QTY
		  ,I.ITEM_QUANTITY												AS ITEM_QUANTITY
		  ,SKU.ITEM_DESC												AS DESCR			--	
		  ,dbo.fn_str_GET_SALES_ORDER_ITEM_TITLE(I.SALES_ORDER_ITEM_ID) AS TITLE
		  ,I.PRICE_TYPE_ID												AS PRICE_TYPE_ID
		  ,I.CONDITION_ID												AS CONDITION_ID
		  ,C.ITEM_CONDITION_DESC										AS CONDITION_DESCRIPTION
		  ,CIAT.INVENTORY_ATTRIBUTE_NAME								AS ITEM_INVENTORY_ATTRIBUTE_SET_CD
		  ,I.ITEM_NOTES													AS ITEM_NOTES
		  ,INV.ITEM_INVENTORY_NOTES										AS ITEM_INVENTORY_NOTES
		  ,a.Tag														as AssetTag
		  ,DCH.CommodityCode											as CommodityCode
		  ,m.ITEM_HARMONIZATION_CODE									as HarmonizationCode
		  ,m.ITEM_ECCN_CODE												as EccnCode
	   FROM F_SALES_ORDER_ITEM		I   WITH(NOLOCK)
	   INNER JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
		ON I.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
	   LEFT JOIN F_ITEM_INVENTORY		INV WITH(NOLOCK)
	     ON I.ITEM_INVENTORY_ID = INV.ITEM_INVENTORY_ID
	   INNER JOIN F_ITEM_MASTER		M   WITH(NOLOCK)
	     ON PM.ITEM_MASTER_ID = M.ITEM_MASTER_ID
	   INNER JOIN D_MANUFACTURER		MFG WITH(NOLOCK)
	     ON MFG.MANUFACTURER_ID = M.MANUFACTURER_ID
	   LEFT JOIN F_ITEM_MASTER_TITLE	IMT WITH(NOLOCK)
	     ON IMT.ITEM_MASTER_ID = M.ITEM_MASTER_ID
	   LEFT JOIN F_ITEM				SKU WITH(NOLOCK)
	     ON ISNULL(INV.ITEM_ID, I.ITEM_ID) = SKU.ITEM_ID
	   LEFT JOIN D_ITEM_CONDITION		C	 WITH(NOLOCK)
		 ON I.CONDITION_ID  = C.ITEM_CONDITION_ID		
		LEFT JOIN F_LOCATION				L   WITH(NOLOCK)
			ON INV.LOCATION_PREV_ID = L.LOCATION_ID		
		LEFT JOIN F_ITEM_MASTER_CATEGORY		AS FIMC WITH(NOLOCK)
			ON FIMC.ITEM_MASTER_ID = M.ITEM_MASTER_ID
			AND FIMC.IS_PRIMARY = 1
			AND FIMC.IS_DELETED = 0
			AND FIMC.IS_INACTIVE = 0
		LEFT JOIN [dbo].[D_CATEGORY_HIERARCHY]	AS DCH WITH(NOLOCK)
			ON FIMC.[CATEGORY_ID] = DCH.[CATEGORY_ID]
			AND DCH.[IS_INACTIVE] = 0
		LEFT OUTER JOIN C_INVENTORY_ATTRIBUTE_TYPE			CIAT WITH(NOLOCK)
			ON ISNULL(DCH.INVENTORY_ATTRIBUTE_TYPE_ID, M.INVENTORY_ATTRIBUTE_TYPE_ID) = CIAT.INVENTORY_ATTRIBUTE_TYPE_ID
		left join recycling.F_Asset a with (nolock)
			on INV.AssetId = a.id
	   WHERE I.SALES_ORDER_ID = @ORDER_ID 
	     AND I.ITEM_QUANTITY > 0
	   RETURN;
    END   
	
	 SELECT 
			dbo.fn_str_GET_SALES_ORDER_ITEM_TITLE(SOI.SALES_ORDER_ITEM_ID)	AS ITEM_TITLE
			,SOI.SALES_ORDER_ITEM_ID										AS ITEM_ID
		INTO #SALES_ORDER_ITEM_TITLES
		FROM F_SALES_ORDER_ITEM					SOI WITH (NOLOCK)	
		    WHERE SOI.SALES_ORDER_ID = @ORDER_ID 

    SELECT	
	   NULL																AS SKU_ID
	   ,M.ITEM_NUMBER													AS ITEM_NUMBER
	   ,MFG.MANUFACTURER_CD												AS MANUFACTURER_CD
	   ,I.ITEM_PRICE													AS PRICE
	   ,SUM(I.ITEM_PRICE * I.ITEM_QUANTITY)								AS FULL_PRICE
	   ,1																AS QTY
	   ,SUM(I.ITEM_QUANTITY)											AS ITEM_QUANTITY
		,IT.ITEM_TITLE													AS TITLE
		,I.PRICE_TYPE_ID												AS PRICE_TYPE_ID
		,I.CONDITION_ID
		,C.ITEM_CONDITION_DESC											AS CONDITION_DESCRIPTION
    FROM F_SALES_ORDER_ITEM		I   WITH(NOLOCK)
	INNER JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
		ON I.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
    INNER JOIN F_ITEM_MASTER		M   WITH(NOLOCK)
      ON PM.ITEM_MASTER_ID = M.ITEM_MASTER_ID	
    INNER JOIN D_MANUFACTURER		MFG WITH(NOLOCK)
      ON MFG.MANUFACTURER_ID = M.MANUFACTURER_ID
    INNER JOIN F_ITEM_MASTER_TITLE	IMT  WITH(NOLOCK)
      ON M.ITEM_MASTER_ID = IMT.ITEM_MASTER_ID	
	LEFT JOIN D_ITEM_CONDITION		C	 WITH(NOLOCK)
		ON I.CONDITION_ID  = C.ITEM_CONDITION_ID
	LEFT JOIN #SALES_ORDER_ITEM_TITLES IT 	WITH(NOLOCK)
		ON I.SALES_ORDER_ITEM_ID = IT.ITEM_ID
    WHERE I.SALES_ORDER_ID = @ORDER_ID 
      AND I.ITEM_QUANTITY > 0
    GROUP BY ITEM_NUMBER, ITEM_PRICE, PRICE_TYPE_ID, MANUFACTURER_CD, IT.ITEM_TITLE, I.CONDITION_ID, C.ITEM_CONDITION_DESC	

	DROP TABLE #SALES_ORDER_ITEM_TITLES
END

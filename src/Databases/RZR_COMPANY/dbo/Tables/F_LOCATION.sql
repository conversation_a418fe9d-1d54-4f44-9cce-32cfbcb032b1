CREATE TABLE [dbo].[F_LOCATION] (
    [LOCATION_ID]                   BIGINT        IDENTITY (1, 1) NOT NULL,
    [WAREHOUSE_ID]                  BIGINT        NOT NULL,
    [LOCATION_NAME]                 NVARCHAR (150) NOT NULL,
    [IS_COMPLETED]                  BIT           CONSTRAINT [DF_F_LOCATION_IS_COMPLETED] DEFAULT ((0)) NOT NULL,
    [IS_WAREHOUSE_DEFAULT_LOCATION] BIT           NULL,
    [IS_INACTIVE]                   BIT           CONSTRAINT [DF_F_LOCATION_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT           CONSTRAINT [DF_F_LOCATION_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   VARCHAR (150) NOT NULL,
    [INSERTED_DT]                   DATETIME      CONSTRAINT [DF_F_LOCATION_INSERTED_DT] DEFAULT (getdate()) NULL,
    [UPDATED_BY]                    VARCHAR (150) NULL,
    [UPDATED_DT]                    DATETIME      NULL,
    [DELETED_BY]                    VARCHAR (150) NULL,
    [DELETED_DT]                    DATETIME      NULL,
    [IS_TRANSFER_DEFAULT_LOCATION]  BIT           NULL,
    CONSTRAINT [PK_F_LOCATION] PRIMARY KEY CLUSTERED ([LOCATION_ID] ASC),
    CONSTRAINT [FK_F_LOCATION_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The locations of the system for each warehouse has reference to the F_LOCATION_DETAIL', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_LOCATION';

GO
CREATE NONCLUSTERED INDEX [IX__F_LOCATION__WAREHOUSE_ID__ACTIVE]
	ON [dbo].[F_LOCATION] ([LOCATION_ID],[WAREHOUSE_ID],[IS_INACTIVE],[IS_DELETED])
	INCLUDE ([LOCATION_NAME])

GO
CREATE NONCLUSTERED INDEX [IX_LOCATION_NAME]
    ON [dbo].[F_LOCATION]([LOCATION_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX IX__F_LOCATION_WAREHOUSE_ID_IS_WAREHOUSE_DEFAULT_LOCATION
ON [dbo].[F_LOCATION] ([WAREHOUSE_ID],[IS_WAREHOUSE_DEFAULT_LOCATION])
INCLUDE ([LOCATION_ID])


GO


GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_LOCATION_PUT_CHANGE]
    ON  [dbo].[F_LOCATION]
    AFTER INSERT, UPDATE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.LOCATION_ID, d.LOCATION_ID)
	from inserted		i
	full join deleted	d
		on i.LOCATION_ID = d.LOCATION_ID
	where isnull(i.LOCATION_NAME, '') <> isnull(d.LOCATION_NAME, '')
		or isnull(i.WAREHOUSE_ID, 0) <> isnull(d.WAREHOUSE_ID, 0)
		or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
		or isnull(i.IS_DELETED, 0) <> isnull(d.IS_DELETED, 0)
        or exists (select 1
                   from dbo.F_LOCATION_DETAIL FD_NEW with (nolock)
                            left join dbo.F_LOCATION_DETAIL FD_OLD with (nolock)
                                      on FD_OLD.INVENTORY_CAPABILITY_ID = d.LOCATION_ID
                   where FD_NEW.INVENTORY_CAPABILITY_ID = i.LOCATION_ID
                     and isnull(FD_NEW.LOCATION_DETAIL_TIED_ID, 0) <> isnull(FD_OLD.LOCATION_DETAIL_TIED_ID, 0))
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 6 --Location
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

-- =============================================
-- Author:		<V.DREBEZOVA>
-- Create date: 4/16/2014
-- Description:	<gets main information over the recyclin order, required to build the Report PDF>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INBOUND_ORDER_BY_ID_FOR_PDF_SIMPLE] 
	@ORDER_ID			BIGINT,
	@PURCHASE_ORDER_ID	BIGINT	= NULL
AS
BEGIN

	SELECT 
		 FRO.RECYCLING_ORDER_ID
		,FROI.AUTO_NAME
		,FRO.PO_NUMBER	
		,FROI.RECIEVE_DATE	
		,dbo.fn_str_GET_RECYCLING_ORDER_BOL_NUMBER_REPORT(FRO.RECYCLING_ORDER_ID) AS BOL_NUMBER				  
				
		-- ship to
		,A_SHIP.POSTAL_CODE											AS SHIP_TO_POSTAL_CODE 
		,A_SHIP.COUNTRY												AS SHIP_TO_COUNTRY		
		,A_SHIP.[STATE]												AS SHIP_TO_STATE		
		,A_SHIP.CITY												AS SHIP_TO_CITY		
		,A_SHIP.STREET_1											AS SHIP_TO_STREET_1	
		,A_SHIP.STREET_2											AS SHIP_TO_STREET_2	
		,A_SHIP.STREET_3											AS SHIP_TO_STREET_3	
		,A_SHIP.PHONE												AS SHIP_TO_PHONE
		,CTRCT.[AUTO_NAME]											AS [CONTRACT_NAME]
		,CASE 
			 WHEN A_SHIP.IS_COMPANY = 1 AND LEN(A_SHIP.COMPANY_NAME)> 0
			 THEN A_SHIP.COMPANY_NAME
			 ELSE C_SHIP.CUSTOMER_NAME
	      END														AS SHIP_TO_CUSTOMER_NAME
		,CC_SHIP.FIRST_NAME + ' ' + ISNULL(CC_SHIP.LAST_NAME, '')	AS SHIP_TO_CONTACT_NAME  	
		----

		-- bill to 
		-- A_CARRIER table was changed to REPU_DWA according to RSW-8548
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.POSTAL_CODE END
			,A_BILL_CTRCT.POSTAL_CODE
			,A_BILL.POSTAL_CODE)					AS BILL_TO_POSTAL_CODE 
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.COUNTRY END
			,A_BILL_CTRCT.COUNTRY
			,A_BILL.COUNTRY	)						AS BILL_TO_COUNTRY		
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.[STATE] END
			,A_BILL_CTRCT.[STATE]
			,A_BILL.[STATE]	)						AS BILL_TO_STATE		
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.CITY END
			,A_BILL_CTRCT.CITY
			,A_BILL.CITY)							AS BILL_TO_CITY		
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.STREET_1 END
			,A_BILL_CTRCT.STREET_1
			,A_BILL.STREET_1)						AS BILL_TO_STREET_1	
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.STREET_2 END
			,A_BILL_CTRCT.STREET_2
			,A_BILL.STREET_2)						AS BILL_TO_STREET_2	
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.STREET_3 END
			,A_BILL_CTRCT.STREET_3
			,A_BILL.STREET_3)						AS BILL_TO_STREET_3	
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.PHONE END
			,A_BILL_CTRCT.PHONE
			,A_BILL.PHONE)							AS BILL_TO_PHONE
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.VAT END
			,A_BILL_CTRCT.VAT
			,A_BILL.VAT)							AS BILL_TO_VAT
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_DWA.CommerceChamber END
			,A_BILL_CTRCT.CommerceChamber
			,A_BILL.CommerceChamber)							AS BILL_TO_CommerceChamber
		,COALESCE(CASE WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN REPU_C.CUSTOMER_NAME END
			,C_BILL.CUSTOMER_NAME
			,C_CTRCT.CUSTOMER_NAME)									AS BILL_TO_CUSTOMER_NAME
		
		
		,CASE
			-- have a PO
			WHEN FPO.PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID THEN 
				NULLIF(ISNULL(REPU.[FirstName], '')	+ ' ' +ISNULL(REPU.[LastName],''), ' ')
			ELSE
			-- a recycling SO
			CASE
					  		  
				WHEN CC_BILL_CTRCT.CUSTOMER_CONTACT_ID		IS NOT NULL
				THEN NULLIF(ISNULL(CC_BILL_CTRCT.FIRST_NAME, '')	+ ' ' +ISNULL(CC_BILL_CTRCT.LAST_NAME,''), ' ')

				WHEN CC_BILL_DEFAULT.CUSTOMER_CONTACT_ID	IS NOT NULL
				THEN NULLIF(ISNULL(CC_BILL_DEFAULT.FIRST_NAME, '')	+ ' ' +ISNULL(CC_BILL_DEFAULT.LAST_NAME,''), ' ')

			END		
		END									AS BILL_TO_CONTACT_NAME	
		----	
		
		-- pick up location
		,A_PICK.POSTAL_CODE					AS PICKUP_POSTAL_CODE	
		,A_PICK.COUNTRY						AS PICKUP_COUNTRY		
		,A_PICK.[STATE]						AS PICKUP_STATE	
		,A_PICK.CITY						AS PICKUP_CITY		
		,A_PICK.STREET_1					AS PICKUP_STREET_1	
		,A_PICK.STREET_2					AS PICKUP_STREET_2	
		,A_PICK.STREET_3					AS PICKUP_STREET_3	
		,A_PICK.PHONE						AS PICKUP_PHONE		
		,CASE
			 WHEN A_PICK.IS_COMPANY = 1 AND LEN(A_PICK.COMPANY_NAME) > 0 THEN A_PICK.COMPANY_NAME
			 ELSE C_PICK.CUSTOMER_NAME
		  END								AS PICKUP_CUSTOMER_NAME


		,CASE
			WHEN CC_PICKUP.CUSTOMER_CONTACT_ID			IS NOT NULL
			THEN NULLIF(ISNULL(CC_PICKUP.FIRST_NAME, '')		+ ' ' +ISNULL(CC_PICKUP.LAST_NAME,''), ' ')
		  		  
			WHEN CC_PICKUP_ALT.CUSTOMER_CONTACT_ID		IS NOT NULL
			THEN NULLIF(ISNULL(CC_PICKUP_ALT.FIRST_NAME, '')	+ ' ' +ISNULL(CC_PICKUP_ALT.LAST_NAME,''), ' ')
		  		  
			ELSE CC_PICK_DEFAULT.FIRST_NAME +' '+ ISNULL(CC_PICK_DEFAULT.LAST_NAME, '')	
		END									AS PICKUP_CONTACT_NAME 	
		,[dbo].fn_str_GET_DEFAULT_STATEMENT_TERM_BY_TYPE(3) AS RECYCLING_CERT_TERM

	-- Recycling Order
	FROM		F_RECYCLING_ORDER				  FRO				WITH(NOLOCK)
	INNER JOIN  F_RECYCLING_ORDER_INBOUND		  FROI				WITH(NOLOCK)
		ON FRO.RECYCLING_ORDER_ID = FROI.RECYCLING_ORDER_ID	
	INNER JOIN	F_CUSTOMER						  C_BILL			WITH(NOLOCK)
		ON FRO.CUSTOMER_ID = C_BILL.CUSTOMER_ID
    LEFT JOIN	F_CUSTOMER_CONTACT				  CC_BILL_DEFAULT	WITH(NOLOCK)
		ON CC_BILL_DEFAULT.CUSTOMER_ID = C_BILL.CUSTOMER_ID
	    AND CC_BILL_DEFAULT.IS_MAIN = 1
	LEFT JOIN	F_CUSTOMER					      C_SHIP			WITH(NOLOCK)
		ON FROI.SHIP_TO_CUSTOMER_ID = C_SHIP.CUSTOMER_ID
	LEFT JOIN	F_CUSTOMER_CONTACT				  CC_SHIP			WITH(NOLOCK)
		ON CC_SHIP.CUSTOMER_ID = FROI.SHIP_TO_CUSTOMER_ID
		AND CC_SHIP.IS_MAIN = 1
	LEFT JOIN	F_CUSTOMER_ADDRESS				  A_SHIP			WITH(NOLOCK)
		ON FROI.SHIP_TO_ADDRESS_ID = A_SHIP.CUSTOMER_ADDRESS_ID
	LEFT JOIN	F_CUSTOMER_ADDRESS				  A_BILL			WITH(NOLOCK)
		ON  FRO.CUSTOMER_ID = A_BILL.CUSTOMER_ID
	    AND A_BILL.CUSTOMER_ADDRESS_TYPE_ID = 3
	    AND A_BILL.IS_MAIN = 1 -- "3" is "BILL TO" in C_CUSTOMER_ADDRESS_TYPE
	LEFT JOIN F_RECYCLING_ORDER_CONTRACT			ROC				WITH(NOLOCK)
		ON ROC.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
		AND ROC.IsFirstApplied = 1
	-- Contract customer contact and addresses
	LEFT JOIN [dbo].[F_CONTRACT]				  CTRCT				WITH (NOLOCK)
		ON ROC.CONTRACT_ID = CTRCT.CONTRACT_ID
	LEFT JOIN	F_CUSTOMER						  C_CTRCT			WITH(NOLOCK)
		ON CTRCT.CUSTOMER_ID = C_CTRCT.CUSTOMER_ID
	LEFT JOIN F_CUSTOMER_ADDRESS				  A_BILL_CTRCT		WITH (NOLOCK)
		ON CTRCT.BILLING_ADDRESS_ID = A_BILL_CTRCT.CUSTOMER_ADDRESS_ID
	LEFT JOIN F_CUSTOMER_CONTACT				  CC_BILL_CTRCT		WITH (NOLOCK)
		ON CTRCT.BILLING_CONTACT_ID = CC_BILL_CTRCT.CUSTOMER_CONTACT_ID
	
	-- Order Pickup location owner customer contact and addresses
	LEFT JOIN	F_CUSTOMER_ADDRESS				  A_PICK			WITH(NOLOCK)
		ON FROI.CUSTOMER_ADDRESS_ID = A_PICK.CUSTOMER_ADDRESS_ID
	LEFT JOIN F_CUSTOMER						  C_PICK			WITH(NOLOCK)
		ON A_PICK.CUSTOMER_ID = C_PICK.CUSTOMER_ID	
	
	LEFT JOIN F_CUSTOMER_CONTACT				  CC_PICKUP			WITH(NOLOCK)
	    ON CC_PICKUP.CUSTOMER_CONTACT_ID = FROI.CUSTOMER_CONTACT_ID
    LEFT JOIN F_CUSTOMER_CONTACT				  CC_PICKUP_ALT		WITH(NOLOCK)
	    ON CC_PICKUP_ALT.CUSTOMER_CONTACT_ID = FROI.CUSTOMER_ALTERNATIVE_CONTACT_ID
	
	LEFT JOIN F_CUSTOMER_CONTACT				  CC_PICK_DEFAULT	WITH(NOLOCK)
		ON A_PICK.CUSTOMER_ID = CC_PICK_DEFAULT.CUSTOMER_ID
	    AND CC_PICK_DEFAULT.IS_MAIN = 1
	
	-- Freight carrier customer 
	--left join (
	--	select top(1)
	--		ffc.[RecyclingOrderId],
	--		ffc.[CUSTOMER_ID],
	--		ffc.CUSTOMER_CONTACT_ID,
	--		ffc.CUSTOMER_ADDRESS_ID
	--	from [dbo].[F_FREIGHT_CARRIER]	ffc with(nolock)
	--	where ffc.[RecyclingOrderId] = @ORDER_ID
	--	order by [FREIGHT_CARRIER_ID]
	--)	FREIGHT
	--	on FREIGHT.[RecyclingOrderId] = @ORDER_ID
	--LEFT JOIN	F_CUSTOMER						  C_CARRIER			WITH(NOLOCK)
	--	ON FREIGHT.CUSTOMER_ID = C_CARRIER.CUSTOMER_ID
	--LEFT JOIN	F_CUSTOMER_ADDRESS				  A_CARRIER			WITH(NOLOCK)
	--	ON FREIGHT.CUSTOMER_ADDRESS_ID = A_CARRIER.CUSTOMER_ADDRESS_ID
	--LEFT JOIN F_CUSTOMER_CONTACT				  CC_CARRIER		WITH (NOLOCK)		
	--	ON FREIGHT.CUSTOMER_CONTACT_ID = CC_CARRIER.CUSTOMER_CONTACT_ID
	
	-- PO of the carrier for this order
	LEFT JOIN [dbo].[F_PURCHASE_ORDER]			  FPO				WITH(NOLOCK)
		ON FRO.[RECYCLING_ORDER_ID] = FPO.[RECYCLING_ORDER_ID]
		--AND FPO.CUSTOMER_ID = C_CARRIER.CUSTOMER_ID according to RSW-8548

	-- RSW-8548 Get rep user default location address
	LEFT JOIN [dbo].[tb_User]					REPU				WITH(NOLOCK)
		ON FRO.[USER_ID] = REPU.[UserID]
	LEFT JOIN	F_CUSTOMER_ADDRESS				REPU_DWA			WITH(NOLOCK)
		ON REPU.[DEFAULT_WAREHOUSE_ID] = REPU_DWA.[WAREHOUSE_ID]
	LEFT JOIN	F_CUSTOMER						REPU_C			WITH(NOLOCK)
		ON REPU_DWA.CUSTOMER_ID = REPU_C.CUSTOMER_ID

	WHERE FRO.RECYCLING_ORDER_ID = @ORDER_ID

END
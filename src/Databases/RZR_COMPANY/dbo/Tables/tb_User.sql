CREATE TABLE [dbo].[tb_User] (
    [UserID]                        BIGINT         IDENTITY (1, 1) NOT NULL,
    [MANAGER_ID]                    BIGINT         NULL,
    [UserName]                      NVARCHAR (32)  NOT NULL,
    [Password]                      NVARCHAR (128) NOT NULL,
    [TITLE]                         NVARCHAR (50)  NULL,
    [JOB_TITLE]                     NVARCHAR (128) NULL,
    [FirstName]                     NVARCHAR (128) NOT NULL,
    [Initials]                      NVARCHAR (8)   NULL,
    [LastName]                      NVARCHAR (128) NOT NULL,
    [TIME_ZONE_ID]                  NVARCHAR (150) CONSTRAINT [DF_tb_User_TIME_ZONE_ID] DEFAULT (N'Eastern Standard Time') NULL,
    [Email]                         NVARCHAR (128) NULL,
    [EMAIL_SECONDARY]               NVARCHAR (128) NULL,
    [PHONE_MAIN]                    NVARCHAR (20)  NULL,
    [PHONE_MOBILE]                  NVARCHAR (20)  NULL,
    [PHONE_HOME]                    NVARCHAR (20)  NULL,
    [PHONE_OTHER]                   NVARCHAR (20)  NULL,
    [FAX]                           NVARCHAR (20)  NULL,
    [IsActive]                      BIT            CONSTRAINT [DF_tb_User_IsActive] DEFAULT ((1)) NOT NULL,
    [IS_REMOTE_AGENT]               BIT            CONSTRAINT [DF_tb_User_IsActive1] DEFAULT ((0)) NOT NULL,
    [IS_PASSWORD_SENT]              BIT            CONSTRAINT [DF_tb_User_IS_PASSWORD_SENT] DEFAULT ((0)) NOT NULL,
    [DEFAULT_WAREHOUSE_ID]          BIGINT         NULL,
    [PERMISSION_CATEGORY_ENTITY_ID] INT            CONSTRAINT [DF_tb_User_PERMISSION_CATEGORY_ENTITY] DEFAULT ((23)) NOT NULL,
    [WEIGHT_MEASURE_SYSTEM_ID]      INT            CONSTRAINT [DF_WEIGHT_MEASURE_SYSTEM_ID] DEFAULT ((1)) NULL,
    [MAX_CONNECTIONS]               INT            CONSTRAINT [DF__tb_User__MAX_CON__7FE04210] DEFAULT ((0)) NULL,
    [IS_INACTIVE]                   BIT            CONSTRAINT [DF__tb_User__IS_INAC__5DF47309] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT            CONSTRAINT [DF_tb_User_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                   NVARCHAR (150) CONSTRAINT [DF__tb_User__INSERTE__5EE89742] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                   DATETIME       CONSTRAINT [DF__tb_User__INSERTE__5FDCBB7B] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                    NVARCHAR (150) NULL,
    [UPDATED_DT]                    DATETIME       NULL,
    [DELETED_BY]                    NVARCHAR (150) NULL,
    [DELETED_DT]                    DATETIME       NULL,
    [SELF_ID]                       AS             ([UserId]),
    [SELF_NAME]                     AS             ([UserName]),
    [SELF_CREATE_DATE]              AS             (isnull([INSERTED_DT],'2000-01-01')),
    [PARENT_ID]                     AS             (CONVERT([bigint],isnull(NULL,(-1)),(0))),
    [LengthMeasuringSystemId]       BIGINT         NULL,
    [ProviderId]                    INT            NULL,
    [ShippingMethodId]              INT            NULL,
    [API_KEY_TOKEN]                 NVARCHAR(500)  NULL, 
    [DateFormat]                    INT            NULL,
    [TimeFormat]                    INT            NULL,
    [IsPortalUser]                  BIT            CONSTRAINT [DF_tb_User_IsPortalUser] DEFAULT ((0)) NOT NULL,
    [MfaType]                       INT            CONSTRAINT [DF_tb_User_MfaType] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_tb_Users] PRIMARY KEY CLUSTERED ([UserID] ASC),
    CONSTRAINT [FK_tb_User_C_LengthMeasuringSystem] FOREIGN KEY ([LengthMeasuringSystemId]) REFERENCES [dbo].[C_LengthMeasuringSystem] ([Id]),
    CONSTRAINT [FK_tb_User_C_WEIGHT_MEASURE_SYSTEM] FOREIGN KEY ([WEIGHT_MEASURE_SYSTEM_ID]) REFERENCES [dbo].[C_WEIGHT_MEASURE_SYSTEM] ([WEIGHT_MEASURE_SYSTEM_ID]),
    CONSTRAINT [FK_tb_User_D_PERMISSION_CATEGORY_ENTITY] FOREIGN KEY ([PERMISSION_CATEGORY_ENTITY_ID]) REFERENCES [dbo].[D_PERMISSION_CATEGORY_ENTITY] ([PERMISSION_CATEGORY_ENTITY_ID]),
    CONSTRAINT [FK_tb_User_D_WAREHOUSE] FOREIGN KEY ([DEFAULT_WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]),
    CONSTRAINT [FK_tb_User_tb_User] FOREIGN KEY ([MANAGER_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);



GO
CREATE NONCLUSTERED INDEX [IX_USER_NAME]
    ON [dbo].[tb_User]([UserName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WAREHOUSE]
    ON [dbo].[tb_User]([DEFAULT_WAREHOUSE_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_USER_NAME_IS_DELETED]
    ON [dbo].[tb_User]([UserName] ASC, [IS_DELETED] ASC, [DEFAULT_WAREHOUSE_ID] ASC, [FirstName] ASC, [Initials] ASC, [LastName] ASC)
    INCLUDE([UserID], [TITLE], [TIME_ZONE_ID], [IsActive], [Email]);




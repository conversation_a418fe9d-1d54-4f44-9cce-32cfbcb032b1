CREATE TABLE [dbo].[F_ITEM_MASTER_ADD_ON] (
    [ITEM_MASTER_ADD_ON_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_PARENT_ID] BIGINT        NOT NULL,
    [ITEM_MASTER_CHILD_ID]  BIGINT        NOT NULL,
    [INSERTED_BY]           VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_ADD_ONS_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]           DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_ADD_ONS_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]            VARCHAR (150) NULL,
    [UPDATED_DT]            DATETIME      NULL,
    [DELETED_BY]            VARCHAR (150) NULL,
    [DELETED_DT]            DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_ADD_ONS] PRIMARY KEY CLUSTERED ([ITEM_MASTER_ADD_ON_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_ADD_ON_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_PARENT_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_ADD_ON_F_ITEM_MASTER1] FOREIGN KEY ([ITEM_MASTER_CHILD_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Parent child relations, so this is addon for parent model.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_ADD_ON';


CREATE TABLE [dbo].[F_USER_ROLE] (
    [USER_ROLE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [USER_ID]      BIGINT        NOT NULL,
    [ROLE_ID]      INT           NOT NULL,
    [INSERTED_BY]  VARCHAR (250) CONSTRAINT [DF_F_USER_ROLES_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]  DATETIME      CONSTRAINT [DF_F_USER_ROLES_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_F_USER_ROLES] PRIMARY KEY CLUSTERED ([USER_ROLE_ID] ASC),
    CONSTRAINT [FK_F_USER_ROLES_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_USER_ROLES_tb_UserRole] FOREIGN KEY ([ROLE_ID]) REFERENCES [dbo].[tb_UserRole] ([UserRoleID]) ON DELETE CASCADE ON UPDATE CASCADE
);






GO
CREATE NONCLUSTERED INDEX [IX_USER_ID_ROLE_ID]
    ON [dbo].[F_USER_ROLE]([USER_ID] ASC, [ROLE_ID] ASC);


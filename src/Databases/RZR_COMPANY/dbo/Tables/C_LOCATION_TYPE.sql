CREATE TABLE [dbo].[C_LOCATION_TYPE] (
    [LOCATION_TYPE_ID]   BIGINT        NOT NULL,
    [LOCATION_TYPE_CD]   VARCHAR (50)  NOT NULL,
    [LOCATION_TYPE_DESC] VARCHAR (250) NULL,
    [IS_INACTIVE]        BIT           CONSTRAINT [DF_C_LOCATION_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]         BIT           CONSTRAINT [DF_C_LOCATION_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]        VARCHAR (250) CONSTRAINT [DF_C_LOCATION_TYPE_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]        DATETIME      CONSTRAINT [DF_C_LOCATION_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]         VARCHAR (250) NULL,
    [UPDATED_DT]         DATETIME      NULL,
    [DELETED_BY]         VARCHAR (250) NULL,
    [DELETED_DT]         DATETIME      NULL,
    CONSTRAINT [PK_C_LOCATION_TYPE] PRIMARY KEY CLUSTERED ([LOCATION_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Types of location', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_LOCATION_TYPE';


CREATE PROCEDURE [dbo].[sp_SetInvoiceCreatedDate]
	@InvoiceId		BIGINT,
	@CreatedDate	DATETIME
AS
BEGIN
	DECLARE
		@UtcNow		DATETIME		= GETUTCDATE()
		,@SpName	NVARCHAR(250)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
		,@StatusId	INT
		,@IsPaid	BIT
		,@IsVoided  BIT

	SELECT
		@StatusId = [STATUS_ID],
		@IsPaid = IIF ([PAID_AMOUNT] > 0, 1, 0),
		@IsVoided = IS_VOIDED
	FROM dbo.F_INVOICE WITH(NOLOCK)
	WHERE INVOICE_ID = @InvoiceId

	IF (@StatusId <> 2 OR @IsPaid = 1 OR @IsVoided = 1)
	BEGIN
		SELECT N'Invoice not in open status, is voided or has payment received. Created date cannot be changed.';
		RETURN;
	END

	UPDATE dbo.F_INVOICE WITH(ROWLOCK) SET
			DATE_CREATED = @CreatedDate,
			UPDATED_BY = @SpName,
			UPDATED_DT = @UtcNow				
	WHERE INVOICE_ID = @InvoiceId
END


CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_RECYCLING_ITEMS]
--declare
	@SALES_ORDER_ID		BIGINT = 19,	
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'SalesOrderItemId',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(MAX)	= ''		
AS
BEGIN

	SELECT @ORDER_COLUMN_NAME = ISNULL(@ORDER_COLUMN_NAME, 'SalesOrderItemId')
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		
	DECLARE @query NVARCHAR (MAX) = 	
			' WITH m_data AS
					(
						SELECT 
							soi.SALES_ORDER_ITEM_ID					AS SalesOrderItemId,
							CASE WHEN soi.RECYCLING_ORDER_ITEM_ID IS NOT NULL
								THEN 1
								ELSE 0
							END										AS ItemType,
							ISNULL(soi.RECYCLING_ORDER_ITEM_ID, PM.SERVICE_MASTER_ID) 
																	AS ItemMasterId,
							CASE
								WHEN soi.RECYCLING_ORDER_ITEM_ID IS NULL THEN
									CASE
										WHEN PM.SERVICE_MASTER_ID IS NULL THEN
											CASE
												WHEN ROIM.[CommodityID] IS NULL THEN IMR.ITEM_NUMBER
												ELSE (SELECT RECYCLING_ITEM_MASTER_NAME FROM dbo.F_RECYCLING_ITEM_MASTER WITH (NOLOCK) WHERE RECYCLING_ITEM_MASTER_ID = ROIM.[CommodityId])
											END
										ELSE
											(SELECT st.SERVICE_TYPE_CD FROM dbo.C_RECYCLING_ITEM_SERVICE_TYPE st WITH (NOLOCK) WHERE st.SERVICE_TYPE_ID = PM.SERVICE_MASTER_ID)
									END
								ELSE im.RECYCLING_ITEM_MASTER_NAME
							END										AS ItemNumber,
							M.MANUFACTURER_ID						AS MfgId,
							M.MANUFACTURER_CD						AS Mfg,
							NULL									AS ConditionId,
							NULL									AS ConditionCd,							
							soi.RECEIVED_PRICE						AS PriceRecv,
							soi.ITEM_QUANTITY						AS Qty,
							soi.RECEIVE_STATUS_ID					AS StatusId,
							rs.ORDER_RECEIVE_STATUS_CD				AS StatusCd,
							soi.ITEM_ID								AS Sku,
							soi.ITEM_NOTES AS DESCR,
							dbo.fn_str_GET_SALES_ORDER_ITEM_TITLE(soi.SALES_ORDER_ITEM_ID)  AS DESCRIPTION,
							PT.PRICE_TYPE_DESC						AS PriceTypeDesc,
							CASE
								WHEN soi.RECYCLING_ORDER_ITEM_ID IS NULL THEN 1
								ELSE 0
							END										AS IsService,
							soi.IS_FLAT_FEE							AS IsFlatFee,
							PT.PRICE_TYPE_ID						AS PriceTypeId,
							ISNULL((CASE
								WHEN (EXISTS (
									SELECT TOP(1) 1 
									FROM F_INVOICE	IV WITH(NOLOCK)
									WHERE IV.INVOICE_TYPE_ID = 1 
									  AND IV.ORDER_ID = ' + CAST(@SALES_ORDER_ID AS VARCHAR(100)) + '
									  AND IV.IS_VOIDED = 0
									  AND IV.PAID_DATE IS NOT NULL)) 
								THEN so.TAX
								ELSE 
									dbo.fn_float_GET_TAX_RATE(so.TAX_ID, 1)
							END), 0.0)								AS TaxPc,
							soi.APPLY_TAX							AS TaxOn
						FROM dbo.F_SALES_ORDER_ITEM soi WITH (NOLOCK)
						LEFT JOIN F_SALES_ORDER		so	WITH (NOLOCK)
							ON so.SALES_ORDER_ID = soi.SALES_ORDER_ID
						LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM oi WITH (NOLOCK)
							ON soi.RECYCLING_ORDER_ITEM_ID = oi.RECYCLING_ORDER_ITEM_ID
						LEFT JOIN 	dbo.F_RECYCLING_ITEM_MASTER	im WITH (NOLOCK)
							ON oi.RECYCLING_ITEM_MASTER_ID = im.RECYCLING_ITEM_MASTER_ID
						INNER JOIN dbo.C_PURCHASE_ORDER_RECEIVE_STATUS rs WITH (NOLOCK) 
							ON soi.RECEIVE_STATUS_ID = rs.PURCHASE_ORDER_RECEIVE_STATUS_ID
						LEFT JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
							ON SOI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
						LEFT JOIN F_ITEM_MASTER IMR WITH (NOLOCK)
							ON PM.[PRODUCT_MASTER_TYPE_ID] = 1 AND PM.ITEM_MASTER_ID = IMR.ITEM_MASTER_ID
						LEFT JOIN [dbo].[D_MANUFACTURER] M WITH (NOLOCK)
							ON IMR.[MANUFACTURER_ID] = M.MANUFACTURER_ID
						LEFT JOIN 	dbo.C_RECYCLING_PRICE_TYPE PT WITH (NOLOCK)																									
							ON soi.PRICE_TYPE_ID = PT.PRICE_TYPE_ID
						LEFT JOIN [recycling].[F_CommodityRule] ROIM WITH (NOLOCK) 
							ON soi.RECYCLING_ORDER_ITEM_MASTER_ID = ROIM.[Id]
						WHERE soi.SALES_ORDER_ID =' + CAST(@SALES_ORDER_ID AS VARCHAR(100)) + '
					)
				SELECT TOP(1)
					-1										AS RowID,
					COUNT (SalesOrderItemId)				AS SalesOrderItemId,	
					NULL									AS ItemType,
					NULL									AS ItemMasterId,
					NULL									AS ItemNumber,
					NULL									AS MfgId,
					NULL									AS Mfg,
					NULL									AS ConditionId,
					NULL									AS ConditionCd,					
					NULL									AS PriceRecv,
					NULL									AS Qty,
					NULL									AS StatusId,
					NULL									AS StatusCd,
					NULL									AS Sku,
					NULL									AS Descr,	
					NULL									AS Description,
					NULL									AS PriceTypeDesc,				
					0										AS IsService,
					NULL									AS IsFlatFee,
					NULL									AS PriceTypeId,
					0										AS TaxPc,
					0										AS TaxOn
				FROM m_data
				' + @filterCondition + '
				UNION ALL
				SELECT
					t.RowID,
					t.SalesOrderItemId,
					t.ItemType,
					t.ItemMasterId,
					t.ItemNumber,
					t.MfgId,
					t.Mfg,
					t.ConditionId,
					t.ConditionCd,					
					t.PriceRecv,
					t.Qty,
					t.StatusId,
					t.StatusCd,
					t.Sku,
					t.Descr,		
					t.Description,
					T.PriceTypeDesc,			
					0 AS IsService,
					t.IsFlatFee,
					t.PriceTypeId,
					t.TaxPc,
					t.TaxOn
				FROM (SELECT 
							ROW_NUMBER() OVER (ORDER BY ItemType, '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
							* 
						FROM m_data	
						' + @filterCondition + ') t	
				WHERE 
					RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))													
	
	--select @query
	EXEC sp_executesql @query;
	
END
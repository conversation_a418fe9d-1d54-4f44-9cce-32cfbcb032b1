CREATE TABLE [dbo].[D_EBAY_CATEGORY_HIERARCHY] (
    [EBAY_CATEGORY_HIERARCHY_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [CATEGORY_ID]                BIGINT        NOT NULL,
    [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]               VARCHAR (512) NOT NULL,
    [EBAY_CATEGORY_FULL_PATH]    VARCHAR (MAX) NULL,
    [PARENT_ID]                  INT           NULL,
    [MAPPED_CATEGORY_ID]         INT           NULL,
    [CATEGORY_KEY]               VARCHAR (MAX) NULL,
    [CATEGORY_LEVEL]             INT           NULL,
    [RECENTLY_USED_DT]           DATETIME      NULL,
    [IS_INACTIVE]                BIT           CONSTRAINT [DF_D_EBAY_CATEGORY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                VARCHAR (150) CONSTRAINT [DF_D_EBAY_CATEGORY_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                DATETIME      CONSTRAINT [DF_D_EBAY_CATEGORY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 VARCHAR (150) NULL,
    [UPDATED_DT]                 DATETIME      NULL,
    [DELETED_BY]                 VARCHAR (150) NULL,
    [DELETED_DT]                 DATETIME      NULL,
    CONSTRAINT [PK_D_EBAY_CATEGORY_HIERARCHY] PRIMARY KEY CLUSTERED ([EBAY_CATEGORY_HIERARCHY_ID] ASC),
    CONSTRAINT [FK_D_EBAY_CATEGORY_HIERARCHY_D_CATEGORY_HIERARCHY] FOREIGN KEY ([MAPPED_CATEGORY_ID]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID])
);




GO
CREATE NONCLUSTERED INDEX [IX_PARENT_ID]
    ON [dbo].[D_EBAY_CATEGORY_HIERARCHY]([PARENT_ID] ASC)
    INCLUDE([CATEGORY_KEY]);


GO
CREATE NONCLUSTERED INDEX [IX_MAPPED_CATEGORY_ID]
    ON [dbo].[D_EBAY_CATEGORY_HIERARCHY]([MAPPED_CATEGORY_ID] ASC)
    INCLUDE([CATEGORY_KEY]);


GO
CREATE NONCLUSTERED INDEX [IX_LEVEL]
    ON [dbo].[D_EBAY_CATEGORY_HIERARCHY]([CATEGORY_LEVEL] ASC)
    INCLUDE([CATEGORY_KEY]);


GO
CREATE NONCLUSTERED INDEX [IX_CATEGORY_ID]
    ON [dbo].[D_EBAY_CATEGORY_HIERARCHY]([CATEGORY_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Imported ebay categories. They are mapped with system categories', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_EBAY_CATEGORY_HIERARCHY';


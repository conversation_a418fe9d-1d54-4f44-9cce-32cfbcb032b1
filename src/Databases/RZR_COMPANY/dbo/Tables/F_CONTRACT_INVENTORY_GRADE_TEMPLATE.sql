CREATE TABLE [dbo].[F_CONTRACT_INVENTORY_GRADE_TEMPLATE] (
    [CONTRACT_INVENTORY_GRADE_TEMPLATE_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [INVENTORY_GRADE_TEMPLATE_ID]          BIGINT         NOT NULL,
    [CONTRACT_ID]                          BIGINT         NOT NULL,
    [IS_DELETED]                           BIT            CONSTRAINT [DF_COTRACT_INVENTORY_GRADE_TEMPLATE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                          NVARCHAR (150) NOT NULL,
    [INSERTED_DT]                          DATETIME       NOT NULL,
    [DELETED_BY]                           NVARCHAR (150) NULL,
    [DELETED_DT]                           DATETIME       NULL,
    CONSTRAINT [PK_COTRACT_INVENTORY_GRADE_TEMPLATE] PRIMARY KEY CLUSTERED ([CONTRACT_INVENTORY_GRADE_TEMPLATE_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_INVENTORY_GRADE_TEMPLATE_F_CONTRACT] FOREIGN KEY ([CONTRACT_ID]) REFERENCES [dbo].[F_CONTRACT] ([CONTRACT_ID]),
    CONSTRAINT [FK_F_CONTRACT_INVENTORY_GRADE_TEMPLATE_F_GradeTemplate] FOREIGN KEY ([INVENTORY_GRADE_TEMPLATE_ID]) REFERENCES [recycling].[F_GradeTemplate] ([Id])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The mapping table, which stores assignment of template for contract', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_INVENTORY_GRADE_TEMPLATE';


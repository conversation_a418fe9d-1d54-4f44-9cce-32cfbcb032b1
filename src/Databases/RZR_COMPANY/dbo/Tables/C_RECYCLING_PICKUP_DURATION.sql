CREATE TABLE [dbo].[C_RECYCLING_PICKUP_DURATION] (
    [DURATION_TYPE_ID] INT           NOT NULL,
    [DURATION]         BIGINT        NOT NULL,
    [DURATION_CD]      NVARCHAR (50) NOT NULL,
    [IS_INACTIVE]      BIT           CONSTRAINT [DF_C_RECYCLING_PICKUP_DURATION_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]       BIT           CONSTRAINT [DF_C_RECYCLING_PICKUP_DURATION_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]      VARCHAR (250) CONSTRAINT [DF_C_RECYCLING_PICKUP_DURATION_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]      DATETIME      CONSTRAINT [DF_C_RECYCLING_PICKUP_DURATION_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]       VARCHAR (250) NULL,
    [UPDATED_DT]       DATETIME      NULL,
    [DELETED_BY]       VARCHAR (250) NULL,
    [DELETED_DT]       DATETIME      NULL,
    CONSTRAINT [PK_C_RECYCLING_PICKUP_DURATION] PRIMARY KEY CLUSTERED ([DURATION_TYPE_ID] ASC)
);




GO
CREATE NONCLUSTERED INDEX [IX_DURATION]
    ON [dbo].[C_RECYCLING_PICKUP_DURATION]([DURATION] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is used in recycling inbound modal and when we generate route. This is the onsite time', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_PICKUP_DURATION';


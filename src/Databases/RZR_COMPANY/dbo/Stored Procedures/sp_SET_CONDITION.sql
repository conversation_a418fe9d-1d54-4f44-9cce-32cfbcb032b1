-- =============================================
-- Author:		O.Evseev
-- Create date:	05/29/2015
-- Description:	creates or updates a condition
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CONDITION]		
    @CONDITION_ID	BIGINT = NULL,
    @NAME			NVARCHAR(50),
    @DESCR		NVARCHAR(250),
    @USER_ID		BIGINT,  
    @IS_ECOMMERCE	BIT = NULL
AS
BEGIN
    DECLARE @CODE INT = 0;
    
	select 
		@NAME = LTRIM(RTRIM(REPLACE(REPLACE(@NAME, CHAR(13),' '), CHAR(10),' '))),
		@DESCR = LTRIM(RTRIM(REPLACE(REPLACE(@DESCR, CHAR(13),' '), CHAR(10),' ')))

    SET XACT_ABORT ON
    BEGIN TRAN
	   IF (EXISTS(
		  SELECT TOP(1) 1
		  FROM D_ITEM_CONDITION C
		  WHERE (@CONDITION_ID IS NULL OR C.ITEM_CONDITION_ID != @CONDITION_ID)
		    AND (C.ITEM_CONDITION_CD = @NAME OR C.ITEM_CONDITION_DESC = @DESCR)))
	   BEGIN
		  SET @CODE = 1
	   END ELSE
	   IF (EXISTS(
		
		  SELECT TOP(1) 1
		  FROM D_ITEM_CONDITION C
		  WHERE C.ITEM_CONDITION_ID = @CONDITION_ID
		    AND C.IS_DEFAULT = 1))
	   BEGIN
		  SET @CODE = 3
	   END ELSE	   
	   IF (EXISTS(SELECT TOP(1) 1 FROM D_ITEM_CONDITION C WHERE C.ITEM_CONDITION_ID = @CONDITION_ID))
	   BEGIN
		  UPDATE D_ITEM_CONDITION SET			 
			 ITEM_CONDITION_CD		= @NAME,
			 ITEM_CONDITION_DESC	= @DESCR,
			 IS_ECOMMERCE			= ISNULL(@IS_ECOMMERCE, IS_ECOMMERCE),
			 UPDATED_BY				= @USER_ID,
			 UPDATED_DT				= GETUTCDATE()
		  WHERE ITEM_CONDITION_ID	= @CONDITION_ID
	   END ELSE
	   BEGIN
		  INSERT INTO D_ITEM_CONDITION(ITEM_CONDITION_CD, ITEM_CONDITION_DESC, ITEM_CONDITION_ORDER_BY, IS_ECOMMERCE, IS_DEFAULT, INSERTED_BY, INSERTED_DT)
		  VALUES(
					@NAME,
					@DESCR,
					0, 
					ISNULL(@IS_ECOMMERCE, 0), 
					0, 
					@USER_ID, 
					GETUTCDATE()
				)
	   END
    COMMIT TRAN

    SELECT @CODE AS RESULT_CODE;

END
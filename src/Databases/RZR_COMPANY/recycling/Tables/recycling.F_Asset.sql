CREATE TABLE [recycling].[F_Asset] (
    [Id]                        BIGINT         IDENTITY (1, 1) NOT NULL,
    [AuditSessionId]            BIGINT         NULL,
    [RecyclingOrderItemId]      BIGINT         NULL,
    [Reference]                 VARCHAR (100)  NULL,
    [ItemNumber]                NVARCHAR (256) NULL,
    [UniqueId]                  VARCHAR (250)  NULL,
    [SerialNumber]              VARCHAR (50)   NULL,
    [ManufacturerId]            BIGINT         NULL,
    [ManufacturerName]          VARCHAR (50)   NULL,
    [AuditDate]                 DATETIME       NOT NULL,
    [LocationId]                BIGINT         NULL,
    [Tag]                       NVARCHAR (512) NULL,
    [Weight]                    FLOAT (53)     CONSTRAINT [DF_F_Asset_Weight] DEFAULT ((0)) NULL,
    [Quantity]                  INT            NULL,
    [Notes]                     NVARCHAR (MAX) NULL,
    [ItemPrice]                 FLOAT (53)     NULL,
    [PriceTypeId]               INT            CONSTRAINT [DF_F_Asset_PRICE_TYPE_ID] DEFAULT ((2)) NULL,
    [ItemCount]                 INT            CONSTRAINT [DF_F_Asset_ITEM_COUNT] DEFAULT ((1)) NULL,
    [CategoryId]                INT            NULL,
    [CategoryName]              VARCHAR (250)  NULL,
    [InventoryAttributeTypeId]  INT            NULL,
    [CosmeticsId]               BIGINT         NULL,
    [ConditionId]               BIGINT         NULL,
    [IsGetAfterSettle]          BIT            CONSTRAINT [DF_F_Asset_IsGetAfterSettle] DEFAULT ((0)) NULL,
    [WorkflowStepId]            INT            NULL,
    [RecyclingItemMasterId]     BIGINT         NULL,
    [IsInactive]                BIT            CONSTRAINT [DF_F_Asset_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IsDeleted]                 BIT            CONSTRAINT [DF_F_Asset_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [InsertedByUserIp]          BIGINT         NULL,
    [InsertedBy]                VARCHAR (250)  CONSTRAINT [DF_F_Asset_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [InsertedDate]              DATETIME       CONSTRAINT [DF_F_Asset_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UpdatedByUserId]           BIGINT         NULL,
    [UpdatedByUserIp]           BIGINT         NULL,
    [UpdatedBy]                 VARCHAR (250)  NULL,
    [UpdatedDate]               DATETIME       NULL,
    [DeletedBy]                 VARCHAR (250)  NULL,
    [DeletedDate]               DATETIME       NULL,
    [WEIGHT_KG]                 AS             ([Weight]*(0.45359237)) PERSISTED,
    [InsertedByUserId]          BIGINT         CONSTRAINT [DF_F_Asset_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [DeletedByUserId]           BIGINT         NULL,
    [DeletedByUserIp]           BIGINT         NULL,
    [IsUniqueDefect]            BIT            CONSTRAINT [DF_F_Asset_IsUniqueDefect] DEFAULT ((0)) NOT NULL,
    [CustomerId]                BIGINT         NULL,
    [ItemMasterId]              BIGINT         NULL,
    [FMVPrice]                  FLOAT (53)     NULL,
    [AssetWorkflowStepId]       BIGINT         CONSTRAINT [DF_F_Asset_AssetWorkflowStepId] DEFAULT ((0)) NOT NULL,
    [ItemPriceCalc]             FLOAT (53)     NULL,
    [IsPriceChanged]            BIT            CONSTRAINT [DF_F_Asset_IsPriceChanged] DEFAULT ((0)) NULL,
    [ProcessedDate]             DATETIME       NULL,
    [DataDestructionState]      TINYINT        NULL,
    [IsReconciled]              BIT            CONSTRAINT [DF_F_Asset_IsReconciled] DEFAULT ((0)) NULL,
    [InternalCost]              FLOAT (53)     NULL,
    [ShippingCost]              FLOAT (53)     NULL,
    [ExpectedSellPriceCustomer] FLOAT (53)     NULL,
    [ExpectedSellPriceRep]      FLOAT (53)     NULL,
    [AuditToolReportId]         BIGINT         NULL,
    [InitiallySoldAssetId]      BIGINT         NULL,
    [ProcessedUserId]           BIGINT         NULL,
    [DistributedCost]           FLOAT (53)     NULL,
    [ProductCodeIdHeci]         BIGINT         NULL,
    [StateProgramId]            BIGINT         NULL,
	[FloorPrice]				FLOAT		   NULL,
    CONSTRAINT [PK_F_Asset] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_Asset_C_AssetWorkflowStepId] FOREIGN KEY ([AssetWorkflowStepId]) REFERENCES [recycling].[C_AssetWorkflowStep] ([Id]),
    CONSTRAINT [FK_F_Asset_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([InventoryAttributeTypeId]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_F_Asset_C_RECYCLING_PRICE_TYPE] FOREIGN KEY ([PriceTypeId]) REFERENCES [dbo].[C_RECYCLING_PRICE_TYPE] ([PRICE_TYPE_ID]),
    CONSTRAINT [FK_F_Asset_C_RECYCLING_WORKFLOW_TYPE] FOREIGN KEY ([WorkflowStepId]) REFERENCES [dbo].[C_RECYCLING_WORKFLOW_TYPE] ([WORKFLOW_TYPE_ID]),
    CONSTRAINT [FK_F_Asset_D_CATEGORY_HIERARCHY] FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID]) ON DELETE SET NULL,
    CONSTRAINT [FK_F_Asset_D_INVENTORY_CAPABILITY] FOREIGN KEY ([CosmeticsId]) REFERENCES [dbo].[D_INVENTORY_CAPABILITY] ([INVENTORY_CAPABILITY_ID]),
    CONSTRAINT [FK_F_Asset_D_ITEM_CONDITION] FOREIGN KEY ([ConditionId]) REFERENCES [dbo].[D_ITEM_CONDITION] ([ITEM_CONDITION_ID]),
    CONSTRAINT [FK_F_Asset_D_MANUFACTURER] FOREIGN KEY ([ManufacturerId]) REFERENCES [dbo].[D_MANUFACTURER] ([MANUFACTURER_ID]),
    CONSTRAINT [FK_F_Asset_F_CUSTOMER] FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_Asset_F_IntegrationAsset] FOREIGN KEY ([AuditToolReportId]) REFERENCES [integration].[F_IntegrationAsset] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_F_Asset_F_ITEM_MASTER] FOREIGN KEY ([ItemMasterId]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_Asset_F_LOCATION] FOREIGN KEY ([LocationId]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]),
    CONSTRAINT [FK_F_Asset_F_RECYCLING_ITEM_MASTER] FOREIGN KEY ([RecyclingItemMasterId]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_Asset_F_RECYCLING_ORDER_AUDIT_HEADER] FOREIGN KEY ([AuditSessionId]) REFERENCES [recycling].[F_AuditSession] ([Id]),
    CONSTRAINT [FK_F_Asset_F_RECYCLING_ORDER_ITEM] FOREIGN KEY ([RecyclingOrderItemId]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_Asset_tb_USER_DELETED_BY_USER] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_Asset_tb_USER_INSERTED_BY_USER] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_Asset_tb_USER_UPDATED_BY_USER] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);
GO
CREATE NONCLUSTERED INDEX [IX_recycling_F_Asset_to_settle]
	ON [recycling].[F_Asset] (
		[RecyclingOrderItemId],
		[AssetWorkflowStepId],
		[WorkflowStepId],
		[RecyclingItemMasterId]
	)
	INCLUDE (
		[Weight]
	)
GO

CREATE NONCLUSTERED INDEX [IX__recycling_F_Asset__RecyclingOrderItemId__IsGetAfterSettle__INCLUDE]
  ON [recycling].[F_Asset] (
    [RecyclingOrderItemId],
	[IsGetAfterSettle]
  )
  INCLUDE (
    [Id], [ItemNumber], [UniqueId], [SerialNumber], [ManufacturerName], [Tag], [Quantity], [Notes], [CategoryName], [WEIGHT_KG], [ItemMasterId], [AssetWorkflowStepId], [ProcessedDate], [AuditSessionId], [Weight], [ItemPrice], [PriceTypeId]
  )
  
GO

CREATE NONCLUSTERED INDEX [IX__recycling_F_Asset__UniqueId__LocationIdINCLUDE__Id__SerialNumber__InventoryAttributeTypeId]
  ON [recycling].[F_Asset] (
    [Id], 
	[UniqueId], 
	[LocationId]
  )
  INCLUDE (
    [SerialNumber], [InventoryAttributeTypeId]
  )
  
GO

CREATE NONCLUSTERED INDEX [IX__recycling_F_Asset__UniqueId__IsInactive__IsDeleted__INCLUDE]
  ON [recycling].[F_Asset] (
    [UniqueId], 
	[IsInactive], 
	[IsDeleted]
  )
  INCLUDE (
	[Id],
	[RecyclingOrderItemId],
	[SerialNumber],
	[InsertedDate],
	[ItemMasterId],
	[ItemNumber],
	[ManufacturerId],
	[ManufacturerName]
  )
  
GO

CREATE unique NONCLUSTERED INDEX [UIX__recycling_F_Asset__IsDeleted__AssetWorkflowStepId__INCLUDE]
  ON [recycling].[F_Asset] (
    [IsDeleted], 
	[AssetWorkflowStepId],
    [Id], 
	[RecyclingOrderItemId]
  )
  INCLUDE (
	[LocationId], 
	[CategoryId], 
	[CategoryName]
  )
  
GO

CREATE NONCLUSTERED INDEX [IX__F_Asset__RecyclingOrderItemId__IsDeleted__include]
	ON [recycling].[F_Asset] (
		[RecyclingOrderItemId],
		[IsDeleted]
	)
	INCLUDE (
		[Id]
		,[Reference]
		,[ItemNumber]
		,[UniqueId]
		,[SerialNumber]
		,[ManufacturerId]
		,[ManufacturerName]
		,[LocationId]
		,[Tag]
		,[Weight]
		,[Quantity]
		,[Notes]
		,[CategoryId]
		,[CategoryName]
		,[ConditionId]
		,[WorkflowStepId]
		,[InsertedDate]
		,[InsertedByUserId]
		,[ItemMasterId]
		,[AssetWorkflowStepId]
		,[DataDestructionState]
	)
	
GO

CREATE NONCLUSTERED INDEX [ix__F_Asset__AuditSessionId__RecyclingOrderItemId__IsDeleted__AssetWorkflowStepId]
	ON [recycling].[F_Asset] ([AuditSessionId],[RecyclingOrderItemId],[IsDeleted],[AssetWorkflowStepId])
	INCLUDE ([Id],[Weight])
	
GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderItemId]
    ON [recycling].F_Asset([RecyclingOrderItemId] ASC)
    INCLUDE([AuditSessionId], Id)
	
GO


--used by recycling.sp_GetAssets
CREATE NONCLUSTERED INDEX [IX_RecyclingAuditOrderID]
    ON [recycling].F_Asset([AuditSessionId] ASC, [IsDeleted], [IsInactive], [IsGetAfterSettle])
	INCLUDE(
		[RecyclingOrderItemId],
		Id, 
		[Weight], 
		ItemNumber, 
		UniqueId, 
		SerialNumber, 
		ManufacturerId, 
		ManufacturerName,
		AuditDate, 
		LocationId, 
		Tag,  
		Notes, 
		ItemPrice, 
		PriceTypeId, 
		CategoryId, 
		CategoryName, 
		ConditionId, 
		RecyclingItemMasterId,
		ItemMasterId, 
		Quantity
	)
	
GO

CREATE NONCLUSTERED INDEX [IX_SETTLE]
    ON [recycling].F_Asset([RecyclingOrderItemId] ASC, [PriceTypeId] ASC, [ItemPrice] ASC, [Weight] ASC)
	
GO

CREATE NONCLUSTERED INDEX [IX_LOCATION_ID]
    ON [recycling].F_Asset([LocationId] ASC)
	
GO


EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'it contains audit items information, audit adds items to receive inventory', @level0type = N'SCHEMA', @level0name = N'recycling', @level1type = N'TABLE', @level1name = N'F_Asset';

GO


CREATE NONCLUSTERED INDEX [IX_F_ASSET__CosmeticsId]
    ON [recycling].[F_Asset] ([CosmeticsId])

GO

-- used in [recycling].[sp_GetInformationForReturnAssetBySerial]
CREATE NONCLUSTERED INDEX [IX_recycling__F_Asset__UniqueId__Id]
	ON [recycling].[F_Asset] ([UniqueId],[Id],[IsDeleted])
GO

CREATE NONCLUSTERED INDEX [IX_recycling__F_Asset__SerialNumber__Id]
	ON [recycling].[F_Asset] ([SerialNumber],[Id],[IsDeleted])
	
GO
-- used in [recycling].[sp_GetInformationForReturnAssetBySerial]
CREATE NONCLUSTERED INDEX [IX_recycling__F_Asset__InitiallySoldAssetId__INCLUDE__UniqueId]
	ON [recycling].[F_Asset] ([InitiallySoldAssetId])
	INCLUDE ([UniqueId])
	
GO

-- used in [dbo].[sp_GetInventoryAuditedReport]
CREATE NONCLUSTERED INDEX [IX_F_Asset_IsGetAfterSettle]
    ON [recycling].[F_Asset] ([IsGetAfterSettle])
    INCLUDE ([Id],[RecyclingOrderItemId],[AssetWorkflowStepId])

GO

-- =============================================

-- =============================================
CREATE TRIGGER [recycling].[trg_ASSET_AFTER_UPDATE]
	ON [recycling].[F_Asset]
	AFTER UPDATE, INSERT
AS
BEGIN

	declare @insertedBy nvarchar(128) = isnull(object_schema_name(@@procid) + N'.', N'') + object_name(@@procid);

	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	);

	INSERT INTO @logData
	SELECT
		NEW.[SOURCE],
		NEW.[USER_ID],
		NEW.[USER_IP],
		NEW.[OPERATION_NAME],
		17, -- Asset
		NEW.[ENTITY_KEY_VALUE],
		NEW.[ENTITY_AUTO_NAME],
		[dbo].[fn_str_STRIP_XML_TAGS]((
			SELECT new.[UniqueId], new.[ItemNumber], new.[SerialNumber], new.[Manufacturer],
				new.[Location], new.[Weight], new.[Quantity], new.[Category], new.[InventoryAttribute],
				new.[Condition], new.[ScapCommodity], new.[ItemMaster], new.[AssetWorkflow], new.ItemPrice, new.FMVPrice
			FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT
		)) AS [CHANGES]
	FROM (
		SELECT -- Fixed columns
			SUBSTRING(ISNULL(I.[UpdatedBy], I.[InsertedBy]) + N' [' + @insertedBy + N']', 1, 250)	AS [SOURCE],
			CASE
				WHEN D.[Id] IS NULL THEN N'Inserted'
				WHEN I.[IsDeleted] = 1 THEN N'Deleted'
				ELSE N'Updated'
			END															AS [OPERATION_NAME],
			ISNULL(I.[UpdatedByUserId], I.[InsertedByUserId])			AS [USER_ID],
			ISNULL(I.[UpdatedByUserIp], I.[InsertedByUserIp])			AS [USER_IP],
			I.[Id]														AS [ENTITY_KEY_VALUE],
			ISNULL(d.[UniqueId], i.[UniqueId])							AS [ENTITY_AUTO_NAME],
			-- XML columns
			CASE
				WHEN I.[IsDeleted] = 1 THEN N'Asset with Unique Id ' + I.[UniqueId] + N' is deleted'
				WHEN ISNULL(D.[UniqueId], '') <> ISNULL(I.[UniqueId], '')
				THEN N'Unique Id was "' + ISNULL(D.[UniqueId], N'') + N'" is "' + ISNULL(I.[UniqueId], N'') + N'"'
				ELSE NULL
			END															AS [UniqueId],
			CASE
				WHEN ISNULL(D.[ItemNumber], N'') <> ISNULL(I.[ItemNumber], N'')
				THEN N'Item Number was "' + ISNULL(D.[ItemNumber], N'') + N'" is "' + ISNULL(I.[ItemNumber], N'') + N'"'
				ELSE NULL
			END															AS [ItemNumber],
			CASE
				WHEN ISNULL(D.[SerialNumber], '') <> ISNULL(I.[SerialNumber], '')
				THEN N'Serial Number was "' + ISNULL(D.[SerialNumber], N'') + N'" is "' + ISNULL(I.[SerialNumber], N'') + N'"'
				ELSE NULL
			END															AS [SerialNumber],
			CASE
				WHEN ISNULL(D.[ManufacturerId], 0) <> ISNULL(I.[ManufacturerId], 0)
				THEN N'Manufacturer was "' + ISNULL(md.[MANUFACTURER_CD], N'') + N'" is "' + ISNULL(mi.[MANUFACTURER_CD], N'') + N'"'
				ELSE NULL
			END															AS [Manufacturer],
			CASE
				WHEN ISNULL(D.[LocationId], 0) <> ISNULL(I.[LocationId], 0)
				THEN N'Location was "' + ISNULL(ld.[LOCATION_NAME], N'') + N'" is "' + ISNULL(li.[LOCATION_NAME], N'') + N'"'
				ELSE NULL
			END															AS [Location],
			CASE
				WHEN ISNULL(D.[Weight], 0) <> ISNULL(I.[Weight], 0)
				THEN N'Weight was "' + ISNULL(CAST(D.[Weight] AS NVARCHAR), N'') + N'" is "' + ISNULL(CAST(I.[Weight] AS NVARCHAR), N'') + N'"'
				ELSE NULL
			END															AS [Weight],
			CASE
				WHEN ISNULL(D.[Quantity], 0) <> ISNULL(I.[Quantity], 0)
				THEN N'Quantity was "' + ISNULL(CAST(D.[Quantity] AS NVARCHAR), N'') + N'" is "' + ISNULL(CAST(I.[Quantity] AS NVARCHAR), N'') + N'"'
				ELSE NULL
			END															AS [Quantity],
			CASE
				WHEN ISNULL(D.[CategoryId], N'') <> ISNULL(I.[CategoryId], N'')
				THEN N'Category was "' + ISNULL(CD.[ITEM_CATEGORY_FULL_PATH], N'') + N'" is "' + ISNULL(CI.[ITEM_CATEGORY_FULL_PATH], N'') + N'"'
				ELSE NULL
			END															AS [Category],
			CASE
				WHEN ISNULL(D.[InventoryAttributeTypeId], 0) <> ISNULL(I.[InventoryAttributeTypeId], 0)
				THEN N'Inventory Attribute Set was "' + ISNULL(DAT.[INVENTORY_ATTRIBUTE_NAME], N'') + N'" is "' + ISNULL(IAT.[INVENTORY_ATTRIBUTE_NAME], N'') + N'"'
				ELSE NULL
			END															AS [InventoryAttribute],
			CASE
				WHEN ISNULL(D.[ConditionId], 0) <> ISNULL(I.[ConditionId], 0)
				THEN N'Condition was "' + ISNULL(COND.[ITEM_CONDITION_CD], N'') + N'" is "' + ISNULL(CONI.[ITEM_CONDITION_CD], N'') + N'"'
				ELSE NULL
			END															AS [Condition],
			CASE
				WHEN ISNULL(D.[RecyclingItemMasterId], 0) <> ISNULL(I.[RecyclingItemMasterId], 0)
				THEN N'Scrap commodity was "' + ISNULL(RIMD.[RECYCLING_ITEM_MASTER_NAME], N'') + N'" is "' + ISNULL(RIMI.[RECYCLING_ITEM_MASTER_NAME], N'') + N'"'
				ELSE NULL
			END															AS [ScapCommodity],
			CASE
				WHEN ISNULL(D.[ItemMasterId], 0) <> ISNULL(I.[ItemMasterId], 0)
				THEN N'Part number was "' + ISNULL(IMD.[ITEM_NUMBER], N'') + N'" is "' + ISNULL(IMI.[ITEM_NUMBER], N'') + N'"'
				ELSE NULL
			END															AS [ItemMaster],
			CASE
				WHEN ISNULL(D.[AssetWorkflowStepId], 0) <> ISNULL(I.[AssetWorkflowStepId], 0)
				THEN N'Workflow was "' + ISNULL(AWD.[Label], N'') + N'" is "' + ISNULL(AWI.[Label], N'') + N'"'
				ELSE NULL
			END															AS [AssetWorkflow],
			CASE
				WHEN ISNULL(D.[ItemPrice], 0) <> ISNULL(I.[ItemPrice], 0)
				THEN N'Item Price was "' + ISNULL(CAST(D.[ItemPrice] as nvarchar), N'') + N'" is "' + ISNULL(cast(I.[ItemPrice] as nvarchar), N'') + N'"'
				ELSE NULL
			END															AS [ItemPrice],
			CASE
				WHEN ISNULL(D.FMVPrice, 0) <> ISNULL(I.FMVPrice, 0)
				THEN N'FMV Price was "' + ISNULL(cast(D.FMVPrice as nvarchar), N'') + N'" is "' + ISNULL(cast(I.FMVPrice as nvarchar), N'') + N'"'
				ELSE NULL
			END															AS FMVPrice
		FROM INSERTED									AS I
		LEFT JOIN DELETED								AS D
			ON I.[Id] = D.[Id]
		left join [dbo].[D_MANUFACTURER]				as mi  with (nolock)
			on i.ManufacturerId = mi.MANUFACTURER_ID
		left join [dbo].[D_MANUFACTURER]				as md  with (nolock)
			on d.ManufacturerId = md.MANUFACTURER_ID	
		left join [dbo].[F_LOCATION]					as li  with (nolock)
			on  i.LocationId = li.LOCATION_ID
		left join [dbo].[F_LOCATION]					as ld  with (nolock)
			on  d.LocationId = ld.LOCATION_ID
		left join [dbo].[D_CATEGORY_HIERARCHY]			as ci  with (nolock)
			on i.CategoryId = ci.CATEGORY_ID
		left join [dbo].[D_CATEGORY_HIERARCHY]			as cd  with (nolock)
			on d.CategoryId = cd.CATEGORY_ID
		left join [dbo].[C_INVENTORY_ATTRIBUTE_TYPE]	as iat with (nolock)
			on i.InventoryAttributeTypeId = iat.INVENTORY_ATTRIBUTE_TYPE_ID
		left join [dbo].[C_INVENTORY_ATTRIBUTE_TYPE]	as dat with (nolock)
			on d.InventoryAttributeTypeId = dat.INVENTORY_ATTRIBUTE_TYPE_ID
		left join [dbo].[D_ITEM_CONDITION]				as coni with (nolock)
			on i.ConditionId = coni.ITEM_CONDITION_ID
		left join [dbo].[D_ITEM_CONDITION]				as cond with (nolock)
			on d.ConditionId = cond.ITEM_CONDITION_ID
		left join [dbo].[F_RECYCLING_ITEM_MASTER]		as rimi  with (nolock)
			on i.RecyclingItemMasterId = rimi.RECYCLING_ITEM_MASTER_ID
		left join [dbo].[F_RECYCLING_ITEM_MASTER]		as rimd  with (nolock)
			on d.RecyclingItemMasterId = rimd.RECYCLING_ITEM_MASTER_ID
		left join [dbo].[F_ITEM_MASTER]					as imi with (nolock)
			on i.ItemMasterId = imi.ITEM_MASTER_ID
		left join [dbo].[F_ITEM_MASTER]					as imd with (nolock)
			on d.ItemMasterId = imd.ITEM_MASTER_ID
		left join [recycling].[C_AssetWorkflowStep]		as awi with (nolock)
			on i.AssetWorkflowStepId = awi.Id
		left join [recycling].[C_AssetWorkflowStep]		as awd with (nolock)
			on d.AssetWorkflowStepId = awd.Id
		where (isnull(I.[ItemNumber], '') <> isnull(D.[ItemNumber], '')
			or isnull(I.[UniqueId], '') <> isnull(D.[UniqueId], '')
			or isnull(I.[SerialNumber], '') <> isnull(D.[SerialNumber], '')
			or isnull(I.[ManufacturerId], 0) <> isnull(D.[ManufacturerId], 0)
			or isnull(I.[ManufacturerName], '') <> isnull(D.[ManufacturerName], '')
			or isnull(I.[LocationId], 0) <> isnull(D.[LocationId], 0)
			or isnull(I.[Weight], 0) <> isnull(D.[Weight], 0)
			or isnull(I.[Quantity], 0) <> isnull(D.[Quantity], 0)
			or isnull(I.[CategoryId], 0) <> isnull(D.[CategoryId], 0)
			or isnull(I.[InventoryAttributeTypeId], 0) <> isnull(D.[InventoryAttributeTypeId], 0)
			or isnull(I.[ConditionId], 0) <> isnull(D.[ConditionId], 0)
			or isnull(I.[RecyclingItemMasterId], 0) <> isnull(D.[RecyclingItemMasterId], 0)
			or isnull(I.[ItemMasterId], 0) <> isnull(D.[ItemMasterId], 0)
			or isnull(I.[AssetWorkflowStepId], 0) <> isnull(D.[AssetWorkflowStepId], 0)
			or isnull(I.[IsDeleted], 0) <> isnull(D.[IsDeleted], 0)
			or isnull(I.[ItemPrice], 0) <> isnull(D.[ItemPrice], 0)
			or isnull(I.FMVPrice, 0) <> isnull(D.FMVPrice, 0)
			)
	) AS NEW;

	INSERT INTO [dbo].[F_LOG_DATA] WITH(ROWLOCK) (
		[SOURCE],
		[USER_ID],
		[USER_IP],
		[OPERATION_NAME],
		[ENTITY_TYPE_ID],
		[ENTITY_KEY_VALUE],
		[ENTITY_AUTO_NAME],
		[CHANGES]
	)
	SELECT
		[SOURCE],
		[USER_ID],
		[USER_IP],
		[OPERATION_NAME],
		[ENTITY_TYPE_ID],
		[ENTITY_KEY_VALUE],
		[ENTITY_AUTO_NAME],
		ISNULL([CHANGES], N'')
	FROM @logData;

	INSERT INTO [dbo].[F_LOG_ACTION_DATA] WITH(ROWLOCK) (
		[ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
	   ,[CHANED_PROPERTY_PREV_ID]
	   ,[CHANED_PROPERTY_PREV_VALUE]
	   ,[CHANED_PROPERTY_CURRENT_ID]
	   ,[CHANED_PROPERTY_CURRENT_VALUE]
       ,[USER_ID]
       ,[USER_IP]
       ,[SOURCE])
	SELECT
		25												AS [ACTION_ID]		-- Workflow Changed
	    ,12												AS [ENTITY_TYPE_ID]	-- Asset
		,I.Id											AS [ENTITY_KEY_VALUE]
		,I.[UniqueId]									AS [ENTITY_AUTO_NAME]		
		,d.AssetWorkflowStepId							as [CHANED_PROPERTY_PREV_ID]
		,awd.Label										as [CHANED_PROPERTY_PREV_VALUE]
		,i.AssetWorkflowStepId							as [CHANED_PROPERTY_CURRENT_ID]
		,aw.Label										as [CHANED_PROPERTY_CURRENT_VALUE]
		,ISNULL(I.[UpdatedByUserId], I.[InsertedByUserId])	AS [USER_ID]
		,ISNULL(I.[UpdatedByUserIp], I.[InsertedByUserIp])	AS [USER_IP]
		,SUBSTRING(ISNULL(I.[UpdatedBy], I.[InsertedBy]) + N' [' + @insertedBy + N']', 1, 250)	AS [SOURCE]
	FROM INSERTED										AS I
	LEFT JOIN DELETED									AS D
		ON I.[Id] = D.[Id]
	left join recycling.C_AssetWorkflowStep				as aw with (nolock)
		on i.AssetWorkflowStepId = aw.Id
	left join recycling.C_AssetWorkflowStep				as awd with (nolock)
		on d.AssetWorkflowStepId = awd.Id
	where (isnull(I.[AssetWorkflowStepId], -1) <> isnull(D.[AssetWorkflowStepId], -1) or D.[AssetWorkflowStepId] is null)

END

GO
CREATE TRIGGER [recycling].[trg_Asset_PUT_CHANGE]
    ON  [recycling].[F_Asset]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select distinct
		Id
	from 
	(
		select
			isnull(i.Id, d.Id) as Id
		from inserted		i
		full join deleted	d
			on i.Id = d.Id	
		where isnull(i.ExpectedSellPriceCustomer, 0) <> isnull(d.ExpectedSellPriceCustomer, 0)
			or isnull(i.ExpectedSellPriceRep, 0)	 <> isnull(d.ExpectedSellPriceRep, 0)	
			or isnull(i.AssetWorkflowStepId, 0)	     <> isnull(d.AssetWorkflowStepId, 0)	
			or isnull(i.Notes, '')					 <> isnull(d.Notes, '')	
			or i.IsInactive							 <> d.IsInactive
			or i.IsDeleted							 <> d.IsDeleted
	)	i

	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 13
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO

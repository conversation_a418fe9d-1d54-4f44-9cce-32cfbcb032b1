-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RMA_WORKFLOW_DATA] 
	@RMAID BIGINT
AS
BEGIN
	SELECT
		fsor.RMA_ID as RmaId, 
		fso.SALES_ORDER_NUMBER	AS SoNumber,
		fso.SALES_ORDER_ID      as SalesOrderId,
		drs.RMA_STATUS_CD		AS [Status],
		drt.RMA_TYPE_CD			AS RmaType,
		fc.CUSTOMER_NAME		AS CompanyName,
		OU.UserName				AS OpenedBy,
		fsor.OPENED_DT			as OpenedDate,
		fsor.RECEIVED_BY		as ReceivedBy,
		fsor.RECEIVED_DT		as ReceivedDate,
		VU.UserName				AS VerifiedBy,
		fsor.VERIFIED_DT		as VerifiedDate,
		fsor.[DESCRIPTION]		as [Description],
		fsor.INTERNAL_COMMENT	as InternalComment,
		fsor.EMAIL				as Email,
		fsos.SHIPPING_METHOD_ID	as ShippingMethodId,
		fsos.TRACKING_NO		as TrackingNo,	
		totals.Tax				as Tax,
		totals.ShippingCost		as ShippingCost,
		totals.HandingFee		as MiscCharge,		
		cm.Id					as CreditMemoId,
		i.INVOICE_ID			as InvoiceId
	FROM F_SALES_ORDER_RMA	fsor WITH(NOLOCK)
	INNER JOIN F_SALES_ORDER fso WITH(NOLOCK)
		ON fso.SALES_ORDER_ID = fsor.SALES_ORDER_ID
	INNER JOIN [F_SHIPPING] fsos WITH(NOLOCK)
		ON fsos.SALES_ORDER_ID = fso.SALES_ORDER_ID
	INNER JOIN D_RMA_STATUS drs WITH(NOLOCK)
		ON drs.RMA_STATUS_ID = fsor.RMA_STATUS_ID	
	INNER JOIN D_RMA_TYPE drt WITH(NOLOCK)
		ON drt.RMA_TYPE_ID = fsor.RMA_TYPE_ID
	INNER JOIN F_CUSTOMER fc WITH(NOLOCK)
		ON fc.CUSTOMER_ID = fso.CUSTOMER_ID
	LEFT JOIN [dbo].[F_INVOICE]	i WITH(NOLOCK)
		ON i.INVOICE_TYPE_ID = 1 and i.ORDER_ID = fsor.SALES_ORDER_ID	
		AND i.IS_VOIDED = 0 and i.STATUS_ID >= 3
	LEFT JOIN tb_User	OU WITH(NOLOCK)
		ON OU.UserID = fsor.OPENED_BY_ID
	LEFT JOIN tb_User	VU WITH(NOLOCK)
		ON VU.UserID = fsor.VERIFIED_BY_ID
	left join dbo.F_CreditMemo cm with (nolock)
		on fsor.RMA_ID = cm.RmaId 	
	outer apply [dbo].[tvf_GET_SALES_ORDER_TOTALS] (fso.SALES_ORDER_ID) totals
	WHERE fsor.RMA_ID = @RMAID

END
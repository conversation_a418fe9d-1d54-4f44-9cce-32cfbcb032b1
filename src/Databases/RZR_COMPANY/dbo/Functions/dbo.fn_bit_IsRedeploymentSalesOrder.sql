-- =============================================
-- Create date: 11/14/2022
-- Description:	Checks if sales order is redeployment
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IsRedeploymentSalesOrder]
(
	@SalesOrderId					BIGINT
)
RETURNS BIT
AS
BEGIN
	DECLARE @result BIT = 0
	DECLARE @redeploymentOrderType BIGINT = 22;
	DECLARE @charityOrderType BIGINT = 23;

	IF (EXISTS(SELECT TOP(1) 1 
		FROM dbo.F_ORDER_ORDER_SUBJECT_TYPE			OOST WITH(NOLOCK)
		WHERE OOST.ORDER_ID = @SalesOrderId
			AND OOST.[ENTITY_SUBJECT_TYPE_ID] in (@redeploymentOrderType, @charityOrderType)
			AND OOST.[ENTITY_TYPE_ID] = 1))
	BEGIN
		SELECT @result = 1
	END
	
	RETURN 
		@result
END
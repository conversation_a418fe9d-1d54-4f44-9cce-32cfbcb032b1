 
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_CAPABILITY] 
	@INVENTORY_CAPABILITY_TYPE_ID INT,
	@USER_ID BIGINT,
	@MANUFACTURE_CD VARCHAR(150) = NULL
AS
BEGIN
	DECLARE @INVENTORY_PARENT_CAPABILITY_ID INT = NULL;
	-- Getting id of the capability type "<PERSON><PERSON><PERSON>"	
	
	-- If has Admin role
	--IF (EXISTS(SELECT TOP(1) 1 FROM F_USER_ROLE WHERE USER_ID = @USER_ID AND ROLE_ID = 1)) BEGIN
		SELECT 
			INVENTORY_CAPABILITY_ID,
			INVENTORY_CAPABILITY_VALUE,
			INVENTORY_CAPABILITY_TYPE_ID,
			INVENTORY_PARENT_CAPABILITY_ID
		FROM dbo.D_INVENTORY_CAPABILITY	
		WHERE @INVENTORY_CAPABILITY_TYPE_ID IS NULL OR			
			INVENTORY_CAPABILITY_TYPE_ID = @INVENTORY_CAPABILITY_TYPE_ID
		ORDER BY INVENTORY_CAPABILITY_VALUE			
	--END
	--ELSE BEGIN
	--	SELECT 
	--		INVENTORY_CAPABILITY_ID,
	--		INVENTORY_CAPABILITY_VALUE,
	--		INVENTORY_CAPABILITY_TYPE_ID,
	--		INVENTORY_PARENT_CAPABILITY_ID
	--	FROM dbo.D_INVENTORY_CAPABILITY	
	--	WHERE @INVENTORY_CAPABILITY_TYPE_ID IS NULL OR			
	--		INVENTORY_CAPABILITY_TYPE_ID = @INVENTORY_CAPABILITY_TYPE_ID
	--	ORDER BY INVENTORY_CAPABILITY_VALUE	
	--END
	
END
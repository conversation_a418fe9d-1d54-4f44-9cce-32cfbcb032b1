-- =============================================
-- Author:		<Oleg Evseev>
-- Create date: <02/25/2014>
-- Description:	<Lists files attached to the outbound recycling order>
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_INBOUND_ORDER_FILES]
	@RECYCLING_ORDER_ID BIGINT,
	@IS_IMAGES		   BIT = NULL,
	@IS_BOL			   BIT = 0,
	@IS_SHARED		   BIT = NULL
AS
BEGIN	
	SELECT 
		F.RECYCLING_INBOUND_FILE_ID AS Id,
		F.[FILE_NAME]				  AS [Name],
		F.BYTE_COUNT				  AS [Length],
		ISNULL(F.IS_SHARED, 0)		  AS [IsShared],
		F.IS_BOL					  as  IsBol
	FROM F_RECYCLING_INBOUND_FILE	F	WITH(NOLOCK)	  
	WHERE F.RECYCLING_ORDER_ID  = @RECYCLING_ORDER_ID 
		and F.[IS_DELETED] = 0
		and (@IS_IMAGES is null or ISNULL(F.IS_IMAGE_LOAD, 0) = @IS_IMAGES)
		and (ISNULL(F.IS_BOL, 0) = isnull(@IS_BOL, 0))
		and (@IS_SHARED is null or ISNULL(F.IS_SHARED, 0) = @IS_SHARED)
	ORDER BY 
		IS_IMAGE_LOAD, POSITION_INDEX ASC
END
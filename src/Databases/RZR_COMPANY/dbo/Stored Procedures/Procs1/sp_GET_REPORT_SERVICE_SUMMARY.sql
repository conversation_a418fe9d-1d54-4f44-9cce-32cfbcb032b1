CREATE PROCEDURE [dbo].[sp_GET_REPORT_SERVICE_SUMMARY]
    @DATE_FROM			DATETIME		= '1900-01-01',
    @DATE_TO			DATETIME		= '2100-01-01',
	@DateFilter			NVARCHAR(150)	= N'PickupDate',
	@WAREHOUSE_IDS		XML				=  NULL,
	@CUSTOMER_IDS		XML				=  NULL,
	@LABEL_IDS			XML				=  NULL,
	@IS_TRANSFER_INCLUDE BIT				= 0,
    @ORDER_COLUMN_NAME	VARCHAR(150)	= N'DateCreated',
    @ORDER_DIRECTION	VARCHAR(20)		= N'DESC',
    @ITEMS_PER_PAGE		INT				= 20,
    @PAGE_INDEX			INT				= 0,
    @FILTER_WHERE		VARCHAR(2000)	= N'',
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN
	
	 SET @DATE_FROM	= ISNULL(@DATE_FROM, '1900-01-01')
	 SET @DATE_TO	= ISNULL(@DATE_TO,'2100-01-01')
	DECLARE		
	   @filterCondition VARCHAR(2006) =CASE
		  WHEN @FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL THEN N' WHERE ('+ @FILTER_WHERE +N')'
		  ELSE N''
	   END,
	  
	   @dateWhere NVARCHAR(2000) =  CASE
		  WHEN @DATE_FROM IS NOT NULL 
		  THEN CASE @DateFilter 
				WHEN 'PickupDate'		THEN N' DateCreated >= @DateFrom AND DateCreated <= @DateTo'
				WHEN 'ItemCreateDate'	THEN N' ItemDateCreated >= @DateFrom AND ItemDateCreated <= @DateTo'
				WHEN 'ShippedDate'		THEN N' ShipDate >= @DateFrom AND ShipDate <= @DateTo'
				WHEN 'ReceiveDate'		THEN N' DateCreated >= @DateFrom AND DateCreated <= @DateTo'
				WHEN 'SettleDate'		THEN N' SettlementDate >= @DateFrom AND SettlementDate <= @DateTo'
				ELSE N' DateCreated >= @DateFrom AND DateCreated <= @DateTo' 
			END
		  ELSE N''
	   END,
	   @warehousesWhere  VARCHAR(2000)	 = '',
	   @customersWhere	 VARCHAR(MAX)	 = '',
	   @labelsWhere		 VARCHAR(MAX)	 = ''

    SET @filterCondition =CASE
	   WHEN LEN(@dateWhere) = 0 THEN @filterCondition
	   WHEN LEN(@dateWhere) > 0 AND LEN(@filterCondition) = 0 THEN N' WHERE '+ @dateWhere
	   WHEN LEN(@dateWhere) > 0 AND LEN(@filterCondition) > 0 THEN @filterCondition + N' AND ' + @dateWhere
	   ELSE N''
    END

	DECLARE @idoc INT
	EXEC sp_xml_preparedocument @idoc OUTPUT, @WAREHOUSE_IDS
	CREATE TABLE #WAREHOUSES  (ID BIGINT)
	INSERT INTO #WAREHOUSES (ID)
		SELECT -- select from xml doc as table
			x.ID
		FROM OPENXML (@idoc, '/Root/Items', 1) 
		WITH ( 
			ID INT ) AS x		         
	EXEC sp_xml_removedocument @idoc

	EXEC sp_xml_preparedocument @idoc OUTPUT, @CUSTOMER_IDS
	CREATE TABLE #CUSTOMERS (ID BIGINT)
	INSERT INTO #CUSTOMERS (ID)
		SELECT -- select from xml doc as table
			x.ID
		FROM OPENXML (@idoc, '/Root/Items', 1) 
		WITH ( 
			ID INT ) AS x		         
	EXEC sp_xml_removedocument @idoc

	EXEC sp_xml_preparedocument @idoc OUTPUT, @LABEL_IDS
	CREATE  TABLE #LABELS  (ID BIGINT)
	INSERT INTO #LABELS (ID)
		SELECT -- select from xml doc as table
			x.ID
		FROM OPENXML (@idoc, '/Root/Items', 1) 
		WITH ( 
			ID INT ) AS x		         
	EXEC sp_xml_removedocument @idoc

	DECLARE @HAS_WAREHOUSES BIT = 0;
	DECLARE @HAS_CUSTOMERS  BIT = 0;
	IF (EXISTS(SELECT ID FROM #CUSTOMERS))
	BEGIN
		SET @customersWhere = N' RO.CUSTOMER_ID IN (SELECT ID FROM #CUSTOMERS)'
		SET @HAS_CUSTOMERS = 1
	END
	ELSE
	BEGIN
		SET @customersWhere = N' 1=1'
	END
	
	IF (EXISTS(SELECT ID FROM #WAREHOUSES))
	BEGIN
		SET @warehousesWhere = N' AND RO.WAREHOUSE_ID IN (SELECT ID FROM #WAREHOUSES)'
		SET @HAS_WAREHOUSES = 1
	END	

	IF (EXISTS(SELECT ID FROM #LABELS))
	BEGIN
		SET @labelsWhere = N'
		WHERE EXISTS(SELECT TOP(1) 1
			FROM F_ENTITY_TAG		ET WITH(NOLOCK)
			INNER JOIN #LABELS	L
				ON L.ID = ET.TAG_ID
			WHERE ET.TAG_TYPE_ID = 2 AND ET.ENTITY_ID = C.CUSTOMER_ID 
			   OR ET.TAG_TYPE_ID = 1 AND ET.ENTITY_ID = frim.RECYCLING_ITEM_MASTER_ID)'
	END	

	IF OBJECT_ID('tempdb..#F_ENTITY_TAG') IS NOT NULL
	DROP TABLE #F_ENTITY_TAG
		SELECT
		[dbo].[fn_str_AUTO_NAME_TAG](ET.TAG_ID) as 	TAG_NAME,
		ET.ENTITY_ID,
		ET.TAG_TYPE_ID
		INTO #F_ENTITY_TAG
		FROM F_ENTITY_TAG		 ET	  WITH(NOLOCK)
		WHERE ET.TAG_TYPE_ID IN(1,2)
	CREATE CLUSTERED INDEX IDX_F_ENTITY_PK ON #F_ENTITY_TAG (ENTITY_ID,TAG_TYPE_ID) 


	IF OBJECT_ID('tempdb..#t_LOTS') IS NOT NULL 
	DROP TABLE #t_LOTS

	SELECT * 
	into #t_LOTS
	FROM [recycling].vw_InboundLots il
	WHERE il.ITEM_INSERTED_DT BETWEEN @DATE_FROM AND @DATE_TO
		AND (@HAS_WAREHOUSES = 0 OR il.WAREHOUSE_ID IN (SELECT ID FROM #WAREHOUSES))
		AND (@HAS_CUSTOMERS  = 0 OR il.CUSTOMER_ID IN (SELECT ID FROM #CUSTOMERS))
	UNION ALL
	SELECT * 
	FROM [recycling].vw_OutboundLots ol
	WHERE ol.ITEM_EXPORTED_DT BETWEEN @DATE_FROM AND @DATE_TO
		AND (@HAS_WAREHOUSES = 0 OR ol.WAREHOUSE_ID IN (SELECT ID FROM #WAREHOUSES))
		AND (@HAS_CUSTOMERS  = 0 OR ol.CUSTOMER_ID IN (SELECT ID FROM #CUSTOMERS))
		and (@IS_TRANSFER_INCLUDE = 1 or ol.IS_TRANSFER = 0)
	UNION ALL
	SELECT *
	FROM [recycling].vw_InboundTransferredLots il
	WHERE @IS_TRANSFER_INCLUDE = 1 
		and il.ITEM_INSERTED_DT BETWEEN @DATE_FROM AND @DATE_TO
		AND (@HAS_WAREHOUSES = 0 OR il.WAREHOUSE_ID IN (SELECT ID FROM #WAREHOUSES))
		AND (@HAS_CUSTOMERS  = 0 OR il.CUSTOMER_ID IN (SELECT ID FROM #CUSTOMERS))
	



	CREATE NONCLUSTERED INDEX idx_ORDER_ITEM_OUTBOUND_ORDER_ID 	ON [dbo].[#t_LOTS] ([OUTBOUND_ORDER_ID])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[AUTO_NAME],ITEM_INSERTED_DT,ITEM_EXPORTED_DT,[WEIGHT_RECEIVED],[WEIGHT_TARE],[ORDER_NET],[WEIGHT_REMAIN],[ITEM_COUNT],[RECYCLING_ITEM_MASTER_ID])

	CREATE NONCLUSTERED INDEX idx_ORDER_ITEM_RECYCLING_ORDER_ID ON [dbo].[#t_LOTS] ([RECYCLING_ORDER_ID])
	INCLUDE ([RECYCLING_ORDER_ITEM_ID],[AUTO_NAME],ITEM_INSERTED_DT,ITEM_EXPORTED_DT,[WEIGHT_RECEIVED],[WEIGHT_TARE],[ORDER_NET],[WEIGHT_REMAIN],[ITEM_COUNT],[RECYCLING_ITEM_MASTER_ID])

	DECLARE @q_inbound nvarchar(max) = N'
		SELECT DISTINCT
			FROI.RECYCLING_ORDER_ITEM_ID	AS OrderItemId
			,RO.PICKUP_START_DATE			AS DateCreated --"Receive start" for Inbound
			,''INBOUND''					AS OrderType
			,FROIN.AUTO_NAME				AS OrderNumber
			,FROI.AUTO_NAME					AS OrderItemNumber
			,FROI.ITEM_INSERTED_DT			AS ItemDateCreated
			,RO.RECYCLING_ORDER_ID			AS RECYCLING_ORDER_ID
			,FROI.RECYCLING_ITEM_MASTER_ID	AS RECYCLING_ITEM_MASTER_ID
			,RO.WAREHOUSE_ID				AS WAREHOUSE_ID
			,FROIN.CUSTOMER_ADDRESS_ID		AS PICKUP_ADDRESS_ID
			,FROIN.SHIP_TO_ADDRESS_ID		AS SHIP_TO_ADDRESS_ID
			,IOS.Name						AS STATUS_CD
			,RO.CUSTOMER_ID
			,ST.[SERVICE_TYPE_CD]											AS [ServiceTypeCd]
			,ISNULL(SI.ITEM_SERVICE_DESCRIPTION, ST.[SERVICE_TYPE_DESCR])	AS [Descr]
			,ROUND(SI.[ITEM_SERVICE_COUNT], 2)								AS [Qty]
			,ROUND(ISNULL(SI.[ITEM_SERVICE_COUNT_CALCULATED], 0), 2)		AS [QtyCalculated]
			,SI.[ITEM_SERVICE_PRICE_FOR_ONE]								AS [Price]
			,SI.[NOTES]														AS [Notes]
			,[dbo].[fn_str_GET_USER_AUTO_NAME](RO.[USER_ID], 0)				AS RepUser
			,RO.SETTLE_DATE													AS SettlementDate
		FROM		#t_LOTS								FROI
		INNER JOIN dbo.F_RECYCLING_ORDER				RO		WITH(NOLOCK)
			ON  FROI.IS_INBOUND = 1
			AND RO.RECYCLING_ORDER_ID = FROI.RECYCLING_ORDER_ID
		INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND		FROIN   WITH(NOLOCK)
			ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID
			AND FROIN.StatusId != 7 /*Canceled*/
		LEFT JOIN [dbo].[F_RECYCLING_ORDER_ITEM_SERVICE]	AS SI
			ON SI.RECYCLING_ORDER_ID =  RO.RECYCLING_ORDER_ID
		INNER JOIN [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE]	AS ST	WITH(NOLOCK)
				ON SI.[ITEM_SERVICE_TYPE_ID] = ST.[SERVICE_TYPE_ID]
		INNER JOIN recycling.C_InboundOrderStatus IOS WITH(NOLOCK)
			ON IOS.ID = FROIN.StatusId
		WHERE ' + @customersWhere  + ' 
		   '	+ @warehousesWhere
	DECLARE @q_outbound nvarchar(max) = N'
	SELECT DISTINCT
		FROI.RECYCLING_ORDER_ITEM_ID			AS OrderItemId
		,FROU.LOADING_DT						AS DateCreated --"Order date" for Outbound
		,''OUTBOUND''							AS OrderType
		,FROU.AUTO_NAME							AS OrderNumber
		,FROI.AUTO_NAME							AS OrderItemNumber
		,FROI.ITEM_INSERTED_DT						AS ItemDateCreated
		,RO.RECYCLING_ORDER_ID					AS RECYCLING_ORDER_ID
		,FROI.RECYCLING_ITEM_MASTER_ID			AS RECYCLING_ITEM_MASTER_ID
		,RO.WAREHOUSE_ID						AS WAREHOUSE_ID
		,CASE
			-- Truck: "Our Trucking", "Common Carrier", "Client Scheduled"
			WHEN (FROU.[IS_TRUCKING] = 1) THEN T.PICKUP_LOCATION_ID 
			-- Container: "Container" ("Client Scheduled" doesn''t have pickup location)
			WHEN (FROU.[IS_TRUCKING] = 0 AND C.[SHIPPING_METHOD_ID] = 4) THEN C.[SHIP_FROM_ADDRESS_ID]
			ELSE NULL
		 END									AS PICKUP_ADDRESS_ID
		,CASE
			-- Truck: "Our Trucking", "Common Carrier" ("Client Scheduled" doesn''t have ship to location)
			WHEN (FROU.[IS_TRUCKING] = 1 AND (T.[SHIPPING_METHOD_ID] = 1 OR T.[SHIPPING_METHOD_ID] = 2)) THEN T.SHIPTO_LOCATION_ID 
			-- Container: "Container" ("Client Scheduled" doesn''t have pickup location)
			WHEN (FROU.[IS_TRUCKING] = 0 AND C.[SHIPPING_METHOD_ID] = 4) THEN C.[SHIP_TO_ADDRESS_ID]
			ELSE NULL
		 END									AS SHIP_TO_ADDRESS_ID
		 ,ROS.[STATUS_CD]						AS STATUS_CD
		 ,RO.CUSTOMER_ID
		 ,null									AS [ServiceTypeCd]
		 ,null									AS [Descr]
		 ,null									AS [Qty]
		 ,null									AS [QtyCalculated]
		 ,null									AS [Price]
		 ,null									AS [Notes]
		 ,[dbo].[fn_str_GET_USER_AUTO_NAME](RO.[USER_ID], 0)				AS RepUser
		 ,RO.SETTLE_DATE													AS SettlementDate


	FROM #t_LOTS									FROI
	INNER JOIN dbo.F_RECYCLING_ORDER				RO	    WITH(NOLOCK)
		ON FROI.OUTBOUND_ORDER_ID = RO.RECYCLING_ORDER_ID
		AND FROI.IS_INBOUND = 0
	INNER JOIN dbo.F_RECYCLING_ORDER_OUTBOUND		FROU	WITH (NOLOCK)
		ON RO.RECYCLING_ORDER_ID = FROU.RECYCLING_ORDER_ID
	INNER JOIN dbo.C_RECYCLING_ORDER_STATUS			ROS		WITH (NOLOCK)
		ON ROS.RECYCLING_ORDER_STATUS_ID = RO.RECYCLING_ORDER_STATUS_ID

	LEFT JOIN dbo.F_RECYCLING_OUTBOUND_TRUCKING		T		WITH (NOLOCK)
		ON  /*FROU.IS_TRUCKING = 1
		AND */FROU.RECYCLING_OUTBOUND_TRUCKING_ID = T.RECYCLING_OUTBOUND_TRUCKING_ID	
	LEFT JOIN dbo.F_RECYCLING_OUTBOUND_CONTAINER	C		WITH (NOLOCK)
		ON  ISNULL(FROU.IS_TRUCKING, 0) = 0
		AND FROU.RECYCLING_OUTBOUND_CONTAINER_ID = C.RECYCLING_OUTBOUND_CONTAINER_ID
	
	WHERE ' + @customersWhere  + ' 
		'	+ @warehousesWhere

	DECLARE @q_cte nvarchar(max) = N'
	;WITH cte_inbound AS
	(
	' + @q_inbound + N'
	), cte_outbound AS 
	(
	'  + @q_outbound + N' 
	), m_data AS
	(
		SELECT
			ROW_NUMBER() OVER( ORDER BY ' + @ORDER_COLUMN_NAME + N' ' + @ORDER_DIRECTION + N', OrderItemId) RowID,
			TT.*
		FROM (
			SELECT 				
				t.OrderItemId,
				t.DateCreated,			   
				t.OrderType,
				t.OrderNumber,
				t.OrderItemNumber,
				t.ItemDateCreated,
				t.RECYCLING_ORDER_ID,
				T.STATUS_CD								AS StatusCD,
				T.ServiceTypeCd							AS ServiceTypeCd,
				T.Descr									AS Descr,
				T.Qty									AS Qty,
				T.QtyCalculated							AS QtyCalculated,
				T.Price									AS Price,
				T.Notes									AS Notes,
				C.CUSTOMER_ID							AS CUSTOMER_ID,
				C.CUSTOMER_NAME							AS CustomerName,
				dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME_WITH_COUNTY(T.PICKUP_ADDRESS_ID)	AS PickupLocation,
				PUCA.LOCATION_NAME						AS PickUpLocationName,
				PUCAT.CUSTOMER_ADDRESS_TYPE_NAME		AS PickUpAddressType,
				PUCA.STREET_1							AS PickUpStreet1,
				PUCA.CITY								AS PickUpCity,
				PUCA.COUNTY								AS PickUpCounty,
				PUCA.STATE								AS PickUpState,
				PUCA.POSTAL_CODE						AS PickUpZipCode,
				PUCA.COUNTRY							AS PickUpCountry,
				dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME_WITH_COUNTY(T.SHIP_TO_ADDRESS_ID)	AS ShipToLocation,
				STCA.LOCATION_NAME						AS ShipToLocationName,
				STCAT.[CUSTOMER_ADDRESS_TYPE_NAME]		AS ShipToAddressType,
				STCA.STREET_1							AS ShipToStreet1,
				STCA.CITY								AS ShipToCity,
				STCA.COUNTY								AS ShipToCounty,
				STCA.STATE								AS ShipToState,
				STCA.POSTAL_CODE						AS ShipToZipCode,
				STCA.COUNTRY							AS ShipToCountry,
				WW.[WAREHOUSE_CD]						AS Warehouse,
				frim.RECYCLING_ITEM_MASTER_ID			AS [ITEM_TYPE_ID],
				frim.RECYCLING_ITEM_MASTER_NAME			AS ItemTypeCd,
				ch.CATEGORYNAME							AS Category,
				T.RepUser								AS RepUser,
				T.SettlementDate						AS SettlementDate
			
			FROM (
				SELECT * FROM cte_inbound
				UNION
				SELECT * FROM cte_outbound
			) T
			INNER JOIN dbo.F_CUSTOMER					C		WITH(NOLOCK)
				ON T.CUSTOMER_ID = C.CUSTOMER_ID
			LEFT JOIN dbo.F_CUSTOMER_ADDRESS					PUCA		WITH(NOLOCK)
				ON T.PICKUP_ADDRESS_ID = PUCA.CUSTOMER_ADDRESS_ID
			LEFT JOIN dbo.C_CUSTOMER_ADDRESS_TYPE					PUCAT		WITH(NOLOCK)
				ON PUCAT.CUSTOMER_ADDRESS_TYPE_ID = PUCA.CUSTOMER_ADDRESS_TYPE_ID
			LEFT JOIN dbo.F_CUSTOMER_ADDRESS					STCA		WITH(NOLOCK)
				ON T.SHIP_TO_ADDRESS_ID = STCA.CUSTOMER_ADDRESS_ID
			LEFT JOIN dbo.C_CUSTOMER_ADDRESS_TYPE					STCAT		WITH(NOLOCK)
				ON STCAT.CUSTOMER_ADDRESS_TYPE_ID = STCA.CUSTOMER_ADDRESS_TYPE_ID
			INNER JOIN F_RECYCLING_ITEM_MASTER			frim    WITH (NOLOCK)
				ON T.RECYCLING_ITEM_MASTER_ID = frim.RECYCLING_ITEM_MASTER_ID
			LEFT JOIN dbo.D_WAREHOUSE					WW		WITH (NOLOCK)
				ON T.WAREHOUSE_ID = WW.WAREHOUSE_ID			
			LEFT JOIN dbo.D_CATEGORY_HIERARCHY			ch 		WITH (NOLOCK)
				ON  CH.IS_INACTIVE = 0
				AND frim.CATEGORY_ID = ch.CATEGORY_ID
			OUTER APPLY(
				SELECT  RTRIM(LTRIM(REPLACE(STUFF((SELECT '', '' + TAG_NAME  as ''data()''
				FROM #F_ENTITY_TAG	ET
				WHERE ET.ENTITY_ID = CASE TAG_TYPE_ID
					WHEN 1 THEN frim.RECYCLING_ITEM_MASTER_ID
					WHEN 2 THEN C.CUSTOMER_ID
					END
				FOR XML PATH(''''), type).value(''(./text())[1]'',''varchar(max)''), 1, 1, '''' ), '' ,'', '', ''))) AS Labels
			) t_LABELS
			' + @labelsWhere + N'
		) TT
		' + @filterCondition + N'
	)';

	DECLARE @q_main NVARCHAR (MAX) = N'
	SELECT TOP(1)
		-1										AS RowID
		,COUNT(OrderItemId)						AS OrderItemId
		,CONVERT(datetime, ''1900-01-01'')		AS DateCreated
		,NULL									AS OrderType
		,NULL									AS OrderNumber
		,NULL									AS OrderItemNumber
		,0										AS RECYCLING_ORDER_ID
		,NULL									AS ItemDateCreated
		,NULL									AS StatusCD
		,NULL									AS ServiceTypeCd
		,NULL									AS Descr
		,NULL									AS Qty
		,NULL									AS QtyCalculated
		,NULL									AS Price
		,NULL									AS Notes
		,NULL									AS CustomerName
		,NULL									AS PickupLocation
		,NULL									AS PickUpLocationName
		,NULL									AS PickUpAddressType
		,NULL									AS PickUpStreet1
		,NULL									AS PickUpCity
		,NULL									AS PickUpCounty
		,NULL									AS PickUpState
		,NULL									AS PickUpZipCode
		,NULL									AS PickUpCountry
		,NULL									AS ShipToLocation
		,NULL									AS ShipToLocationName
		,NULL									AS ShipToAddressType
		,NULL									AS ShipToStreet1
		,NULL									AS ShipToCity
		,NULL									AS ShipToCounty
		,NULL									AS ShipToState
		,NULL									AS ShipToZipCode
		,NULL									AS ShipToCountry
		,NULL									AS Warehouse
		,0										AS ITEM_TYPE_ID
		,NULL									AS ItemTypeCd
		,NULL									AS Category
		,NULL									AS RepUser
		,NULL									AS SettlementDate

		

	FROM m_data
	UNION
	SELECT
		RowID
		,OrderItemId
		,DateCreated
		,OrderType
		,OrderNumber
		,OrderItemNumber
		,RECYCLING_ORDER_ID
		,ItemDateCreated
		,StatusCD
		,ServiceTypeCd
		,Descr
		,Qty
		,QtyCalculated
		,Price
		,Notes
		,CustomerName
		,PickupLocation
		,PickUpLocationName
		,PickUpAddressType
		,PickUpStreet1
		,PickUpCity
		,PickUpCounty
		,PickUpState
		,PickUpZipCode
		,PickUpCountry
		,ShipToLocation
		,ShipToLocationName
		,ShipToAddressType
		,ShipToStreet1
		,ShipToCity
		,ShipToCounty
		,ShipToState
		,ShipToZipCode
		,ShipToCountry
		,Warehouse
		,ITEM_TYPE_ID
		,ItemTypeCd
		,Category
		,RepUser
		,SettlementDate
		

	FROM (
		SELECT *
		FROM m_data
		ORDER BY RowID
		OFFSET ' + CAST(@PAGE_INDEX * @ITEMS_PER_PAGE AS VARCHAR(20)) + N' ROWS
		FETCH NEXT ' + CAST(@ITEMS_PER_PAGE AS VARCHAR(20)) + N' ROWS ONLY
	) t';

	SET @q_main = @q_cte + @q_main

	IF (@C_IS_DEBUG = 1)
		print(cast(@q_main AS ntext))
	
	EXEC sp_executesql @q_main, N'
		@DateFrom			DATETIME,
		@DateTo				DATETIME',
		@DateFrom			= @DATE_FROM,
		@DateTo				= @DATE_TO;

	IF OBJECT_ID('tempdb.. #LABELS') IS NOT NULL DROP TABLE #LABELS

END
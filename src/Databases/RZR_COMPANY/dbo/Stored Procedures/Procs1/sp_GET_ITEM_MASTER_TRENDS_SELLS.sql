-- =============================================
-- Author:		<VERONICA DREBEZOVA>
-- Create date: <07/14/2014>
-- Description:	<>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ITEM_MASTER_TRENDS_SELLS] 
	@MASTER_ITEM_ID		BIGINT,
	@ORDER_COLUMN_NAME	VARCHAR(150)	= N'INSERTED_DT',
	@ORDER_DIRECTION	VARCHAR(20)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(2000)	= N''
AS
BEGIN		

	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition varchar(2006) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		
	
	DECLARE @query NVARCHAR (max) = 
		N'WITH m_data AS (
			SELECT 
				SO.INSERTED_DT,
				SO.QTY,
				C.CUSTOMER_NAME,
				SO.PRICE,
				INV.INVOICE_ID,
				SOI.SALES_ORDER_NUMBER,
				SO.SALES_ORDER_ID
			FROM
			(
				SELECT 		
					SI.SALES_ORDER_ID,					
					SI.ITEM_PRICE AS PRICE,
					MAX(AT.INSERTED_DT) AS INSERTED_DT,
					SUM(SI.ITEM_QUANTITY)	AS QTY
				FROM [dbo].[F_SALES_ORDER_ITEM]				SI	WITH(NOLOCK)
				INNER JOIN [dbo].[F_ITEM_INVENTORY]			II	WITH (NOLOCK)
					ON SI.ITEM_INVENTORY_ID = II.ITEM_INVENTORY_ID	
				INNER JOIN		 
				(
					SELECT 
						ATI.ITEM_INVENTORY_ID,
						MAX(AT.INSERTED_DT) AS INSERTED_DT
					FROM dbo.F_ACCOUNT_TRANSACTION_ITEM	ATI WITH (NOLOCK)			
					INNER JOIN F_ACCOUNT_TRANSACTION AT	WITH (NOLOCK)
						ON AT.ACCOUNT_TRANSACTION_ID = ATI.ACCOUNT_TRANSACTION_ID
					WHERE 	AT.ACCOUNT_TRANSACTION_TYPE_ID IN (3, 5)
					GROUP BY ATI.ITEM_INVENTORY_ID	
				) AT		
						ON AT.ITEM_INVENTORY_ID = II.ITEM_INVENTORY_ID	
				WHERE II.ITEM_MASTER_ID = '+ CONVERT(NVARCHAR(20), @MASTER_ITEM_ID) +'
					AND II.ITEM_STATUS_ID = 3 			
				GROUP BY SI.SALES_ORDER_ID, SI.ITEM_PRICE
			) SO
			INNER JOIN [dbo].[F_SALES_ORDER] SOI WITH (NOLOCK)
				ON SO.SALES_ORDER_ID = SOI.SALES_ORDER_ID
			INNER JOIN 
			(
				SELECT
					SOI.ORDER_ID AS SALES_ORDER_ID,
					MAX(SOI.INVOICE_ID) AS INVOICE_ID
				FROM dbo.F_INVOICE SOI	WITH (NOLOCK)
				WHERE SOI.INVOICE_TYPE_ID = 1 AND ISNULL(SOI.IS_VOIDED, 0) = 0
				GROUP BY SOI.ORDER_ID
			) INV
				ON SO.SALES_ORDER_ID = INV.SALES_ORDER_ID
			INNER JOIN [dbo].[F_CUSTOMER]				C	WITH (NOLOCK)
				ON SOI.CUSTOMER_ID = C.CUSTOMER_ID			
		)
		SELECT 
			-1		AS RowID,
			(SELECT COUNT(SALES_ORDER_ID) FROM m_data)
					AS SALES_ORDER_ID,
			NULL	AS [INSERTED_DT],
			0		AS [QTY],
			0		AS [PRICE],
			NULL	AS [CUSTOMER_NAME],
			0		AS [INVOICE_ID],
			NULL	AS [SALES_ORDER_NUMBER]
		UNION ALL
		SELECT
			t.RowID,			
			t.SALES_ORDER_ID,
			t.[INSERTED_DT],
			t.[QTY],
			t.[PRICE],
			t.[CUSTOMER_NAME],
			t.[INVOICE_ID],
			t.[SALES_ORDER_NUMBER]
		FROM (
			SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				*
			FROM m_data
			' + @filterCondition + ') t
		WHERE 
			RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))		
		
		--select @query
		exec sp_executeSQL @query		
END
-- =============================================
-- Author:		
-- Create date: 
-- Description:
-- =============================================
CREATE function [dbo].[fn_bit_IsCustomerInternational]
	(@CUSTOMER_ID			BIGINT)	
RETURNS bit
AS
BEGIN	
		
	declare @country nvarchar(100) = null,
		@customerCountry nvarchar(100) = null

	select @country = COUNTRY
	from U_COMPANY_ADDRESS	WITH(NOLOCK)		

	select @customerCountry = COUNTRY
	from F_CUSTOMER_ADDRESS	WITH(NOLOCK)	
	where CUSTOMER_ID = @CUSTOMER_ID
		and CUSTOMER_ADDRESS_TYPE_ID in (1,2)
		and COUNTRY is not null	
		and IS_MAIN = 1
	order by CUSTOMER_ADDRESS_TYPE_ID desc

	RETURN (		
			SELECT iif(@country is null or @customerCountry is null or 
				@country = @customerCountry or 
					@country like '[US]%' and  @customerCountry like '[US]%', 0, 1)
	)
END
CREATE TABLE [dbo].[F_RECYCLING_ORDER_ITEM_TRANSFER] (
    [RECYCLING_ORDER_ITEM_TRANSFER_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [RECYCLING_ORDER_ITEM_ID]          BIGINT        NOT NULL,
    [OUTBOUND_ORDER_ID]                BIGINT        NULL,
    [INBOUND_ORDER_ID]                 BIGINT        NULL,
    [INSERTED_BY]                      VARCHAR (250) NOT NULL,
    [INSERTED_DT]                      DATETIME      NOT NULL,
    [UPDATED_BY]                       VARCHAR (250) NULL,
    [UPDATED_DT]                       DATETIME      NULL,
    [DELETED_BY]                       VARCHAR (250) NULL,
    [DELETED_DT]                       DATETIME      NULL,
    CONSTRAINT [PK_F_RECYCLING_ORDER_ITEM_TRANSFER] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_ITEM_TRANSFER_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_TRANSFER_F_RECYCLING_ORDER] FOREIGN KEY ([INBOUND_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_TRANSFER_F_RECYCLING_ORDER_ITEM] FOREIGN KEY ([RECYCLING_ORDER_ITEM_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_ITEM_TRANSFER_F_RECYCLING_ORDER1] FOREIGN KEY ([OUTBOUND_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID])
);
GO
CREATE INDEX IDX_F_RECYCLING_ORDER_ITEM_TRANSFER_RECYCLING_ORDER_ITEM_ID
    ON dbo.F_RECYCLING_ORDER_ITEM_TRANSFER(
        RECYCLING_ORDER_ITEM_ID
    ) 
    INCLUDE(
        INBOUND_ORDER_ID, 
        OUTBOUND_ORDER_ID,
        RECYCLING_ORDER_ITEM_TRANSFER_ID
    )
GO
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_TRANSFER_AFTER_INS_UPD_RAW_ACTION_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM_TRANSFER]
    AFTER INSERT, UPDATE
AS 
BEGIN
	IF (ROWCOUNT_BIG() = 0)
		RETURN;

	DECLARE	@utc datetime = getutcdate();
	DECLARE @currentTransactionId bigint = CURRENT_TRANSACTION_ID();
								
    INSERT INTO [dbo].[F_LotActionLogRaw] (
		[ActualLotId],
		[PreviousTransferredFromOutboundOrderId],
		[PreviousTransferredToInboundOrderId],
		[ActualTransferredFromOutboundOrderId],
        [ActualTransferredToInboundOrderId],
		[CurrentTransactionId]
    )
	SELECT
		I.RECYCLING_ORDER_ITEM_ID		AS [ActualLotId],
        D.OUTBOUND_ORDER_ID				AS [PreviousTransferredFromOutboundOrderId],
		D.INBOUND_ORDER_ID				AS [PreviousTransferredToInboundOrderId],
		I.OUTBOUND_ORDER_ID				AS [ActualTransferredFromOutboundOrderId],
		I.INBOUND_ORDER_ID				AS [ActualTransferredToInboundOrderId],
		@currentTransactionId
	FROM INSERTED									I
	LEFT JOIN DELETED								D
		ON D.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_ID
	LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM			L WITH(NOLOCK)
		ON I.RECYCLING_ORDER_ITEM_ID = L.RECYCLING_ORDER_ITEM_ID
	WHERE L.[NotForRawActionLog] = 0
		AND (
			ISNULL(D.OUTBOUND_ORDER_ID, 0) != ISNULL(I.OUTBOUND_ORDER_ID, 0)
			OR ISNULL(D.INBOUND_ORDER_ID, 0) != ISNULL(I.INBOUND_ORDER_ID, 0)
		)

END

GO
CREATE TRIGGER [dbo].[trg_RECYCLING_ORDER_ITEM_TRANSFER_AFTER_DEL_RAW_ACTION_LOG]
    ON  [dbo].[F_RECYCLING_ORDER_ITEM_TRANSFER]
    AFTER DELETE
AS 
BEGIN
	IF (ROWCOUNT_BIG() = 0)
		RETURN;

	DECLARE	@utc datetime = getutcdate();
	DECLARE @currentTransactionId bigint = CURRENT_TRANSACTION_ID();
								
    INSERT INTO [dbo].[F_LotActionLogRaw] (
		[ActualLotId],
		[PreviousTransferredFromOutboundOrderId],
		[PreviousTransferredToInboundOrderId],
		[ActualTransferredFromOutboundOrderId],
        [ActualTransferredToInboundOrderId],
		[CurrentTransactionId]
    )
	SELECT
		D.RECYCLING_ORDER_ITEM_ID		AS [ActualLotId],
        D.OUTBOUND_ORDER_ID				AS [PreviousTransferredFromOutboundOrderId],
		D.INBOUND_ORDER_ID				AS [PreviousTransferredToInboundOrderId],
		I.OUTBOUND_ORDER_ID				AS [ActualTransferredFromOutboundOrderId],
		I.INBOUND_ORDER_ID				AS [ActualTransferredToInboundOrderId],
		@currentTransactionId
	FROM DELETED									D
	LEFT JOIN INSERTED								I
		ON D.RECYCLING_ORDER_ITEM_ID = I.RECYCLING_ORDER_ITEM_ID
	LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM			L WITH(NOLOCK)
		ON D.RECYCLING_ORDER_ITEM_ID = L.RECYCLING_ORDER_ITEM_ID
	WHERE L.[NotForRawActionLog] = 0
		AND (
			ISNULL(D.OUTBOUND_ORDER_ID, 0) != ISNULL(I.OUTBOUND_ORDER_ID, 0)
			OR ISNULL(D.INBOUND_ORDER_ID, 0) != ISNULL(I.INBOUND_ORDER_ID, 0)
		)

END

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'item can be sent form one warehouse to another, this is done by transfer outbound order creation, the history of transfer we store here', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_RECYCLING_ORDER_ITEM_TRANSFER';

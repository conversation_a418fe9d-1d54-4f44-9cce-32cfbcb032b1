-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE function [dbo].[fn_bit_IsSalesOrderCanBeDeleted]
(
	@SalesOrderId		BIGINT
)
RETURNS bit
AS
BEGIN
	DECLARE @result bit = 1

	declare @creditmemoIds [dbo].[bigint_ID_ARRAY]
	insert into  @creditmemoIds
	select cm.Id            
	from [dbo].[F_CreditMemo] cm with (nolock)
	inner join dbo.F_SALES_ORDER_RMA rma WITH(nolock)
		on cm.RmaId = rma.RMA_ID
	where rma.SALES_ORDER_ID = @SalesOrderId
	union
	select cm.Id            
	from [dbo].[F_CreditMemo] cm with (nolock)
	inner join dbo.F_INVOICE i WITH(nolock)
		on cm.InvoiceId = i.INVOICE_ID
	where i.INVOICE_TYPE_ID = 1 and i.ORDER_ID = @SalesOrderId

	if (exists(
		select top(1) 1
		from [dbo].[F_CreditMemo] cm with (nolock)
		inner join @creditmemoIds ids
			on cm.Id = ids.ID
		inner join [dbo].[F_CUSTOMER_CREDIT] cc with (nolock)
			on cm.CustomerCreditId = cc.CUSTOMER_CREDIT_ID and cc.IS_DELETED = 0
		left join [dbo].[F_CREDIT_PAYMENT] cp with (nolock) --used credit/credit memo or not
			on cc.CUSTOMER_CREDIT_ID = cp.CREDIT_ID	
		left join [dbo].[F_INVOICE] i with (nolock)
			on i.INVOICE_TYPE_ID = 3 and cm.Id = i.[ORDER_ID] and i.IS_VOIDED = 0
		left join [dbo].[F_INVOICE_PAYMENT] ipt with (nolock) --credit memo AP invoice is paied or not
			on i.INVOICE_ID = ipt.[INVOICE_ID] and ipt.IS_DELETED = 0
		where cp.CREDIT_ID is not null or ipt.[INVOICE_ID] is not null
	))
	begin
		select @result = 0
	end
	
	RETURN 
		@result
END
-- =============================================
-- Author:		V.DREBEZOVA
-- Create date: 02/04/2014
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_PRESET_SERVICE]
	@RECYCLING_ORDER_ID BIGINT
AS
BEGIN
	RAISERROR('REDUNDANT', 16, 1);
	RETURN;
	--DECLARE 
	--	@IS_OFFSITE BIT,		
	--	@ESTIMATED_COUNT INT,
	--	@ITEMS_COUNT INT,
	--	@PRICE_PER_UNIT MONEY,
		
	--	@IS_SERVICE_FLAT_FEE_PRICE BIT,
		
	--	@IS_FLAT_DISCOUNT BIT,
	--	@DISCOUNT MONEY,
	--	@TOTAL_PRICE MONEY
		
	--SELECT 
	--	@IS_SERVICE_FLAT_FEE_PRICE = IS_SERVICE_FLAT_FEE_PRICE,
	--	@IS_FLAT_DISCOUNT = IS_FLAT_DISCOUNT,
	--	@DISCOUNT = DISCOUNT,
	--	@TOTAL_PRICE = TOTAL_PRICE
	--FROM dbo.F_RECYCLING_ORDER_INBOUND WITH(NOLOCK)
	--WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	
	--SELECT @ITEMS_COUNT = COUNT(*) 
	--FROM dbo.F_RECYCLING_ORDER_ITEM_SERVICE
	--WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

	--if(@ITEMS_COUNT = 0)
	--BEGIN			
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_HARD_DRIVE_SHRED WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK)
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 1 AND IS_PROVIDED = 1)
	--		) BEGIN
													
	--		SELECT 
	--			@IS_OFFSITE = IS_OFFSITE,
	--			@PRICE_PER_UNIT = PRICE_PER_UNIT,
	--			@ESTIMATED_COUNT = ESTIMATED_COUNT
	--		FROM dbo.F_RECYCLING_HARD_DRIVE_SHRED WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
				
	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			CASE
	--				WHEN @IS_OFFSITE = 1 THEN 2
	--				ELSE 1
	--			END,
	--			ISNULL(@ESTIMATED_COUNT, 1),			
	--			CASE
	--				WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@PRICE_PER_UNIT
	--				ELSE 0
	--			END
	--	END

	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_TAPE_DRIVE_SHRED WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK)
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 2 AND IS_PROVIDED = 1)
	--		) BEGIN
					
	--		SELECT 
	--			@IS_OFFSITE = IS_OFFSITE,
	--			@PRICE_PER_UNIT = PRICE_PER_UNIT,
	--			@ESTIMATED_COUNT = ESTIMATED_COUNT
	--		FROM dbo.F_RECYCLING_TAPE_DRIVE_SHRED WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
				
	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			CASE
	--				WHEN @IS_OFFSITE = 1 THEN 4
	--				ELSE 3
	--			END,
	--			ISNULL(@ESTIMATED_COUNT, 1),			
	--			CASE
	--				WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@PRICE_PER_UNIT
	--				ELSE 0
	--			END
	--	END
		
	--	DECLARE 
	--		@IS_SERIALS_REQUIRED BIT,
	--		@IS_MNFR_REQUIRED BIT,
	--		@IS_MODEL_REQUIRED BIT,
	--		@IS_SERIAL_NUMBER_DRIVE_REMOVED BIT
			
		
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_EQUIPMENT_COLLECTION WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK)
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 3 AND IS_PROVIDED = 1)
	--		) BEGIN
					
	--		SELECT 
	--			@IS_SERIALS_REQUIRED = IS_SERIALS_REQUIRED,
	--			@IS_MNFR_REQUIRED = IS_MNFR_REQUIRED,
	--			@IS_MODEL_REQUIRED = IS_MODEL_REQUIRED,
	--			@IS_SERIAL_NUMBER_DRIVE_REMOVED = IS_SERIAL_NUMBER_DRIVE_REMOVED,
	--			@PRICE_PER_UNIT = PRICE
	--		FROM dbo.F_RECYCLING_EQUIPMENT_COLLECTION WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE 
	--			(
	--				RECYCLING_ORDER_ID, 
	--				ITEM_SERVICE_TYPE_ID, 
	--				ITEM_SERVICE_COUNT, 
	--				ITEM_SERVICE_PRICE_FOR_ONE,
	--				ITEM_SERVICE_DESCRIPTION
	--			)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			5,		
	--			1,			
	--			CASE
	--				WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@PRICE_PER_UNIT
	--				ELSE 0
	--			END,
	--			'Equipment Data Collection ' +
	--				CASE
	--					WHEN @IS_SERIALS_REQUIRED = 1 THEN 'SERIAL'
	--					ELSE ''
	--				END +
	--				CASE
	--					WHEN @IS_MNFR_REQUIRED = 1 THEN ', MNFR'
	--					ELSE ''
	--				END +	
	--				CASE
	--					WHEN @IS_MODEL_REQUIRED = 1 THEN ', MODEL'
	--					ELSE ''
	--				END +
	--				CASE
	--					WHEN @IS_SERIAL_NUMBER_DRIVE_REMOVED = 1 THEN ', SERIAL DRIVE REMOVED'
	--					ELSE ''
	--				END														
			
	--	END
		
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_DATA_SECURITY_COLLECTION WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK)
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 4 AND IS_PROVIDED = 1)
	--		) BEGIN
					
	--		SELECT 
	--			@IS_SERIALS_REQUIRED = IS_SERIALS_REQUIRED,
	--			@IS_MNFR_REQUIRED = IS_MNFR_REQUIRED,
	--			@IS_MODEL_REQUIRED = IS_MODEL_REQUIRED,
	--			@IS_SERIAL_NUMBER_DRIVE_REMOVED = IS_SERIAL_NUMBER_DRIVE_REMOVED,
	--			@PRICE_PER_UNIT = PRICE
	--		FROM dbo.F_RECYCLING_DATA_SECURITY_COLLECTION WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE 
	--			(
	--				RECYCLING_ORDER_ID, 
	--				ITEM_SERVICE_TYPE_ID, 
	--				ITEM_SERVICE_COUNT, 
	--				ITEM_SERVICE_PRICE_FOR_ONE,
	--				ITEM_SERVICE_DESCRIPTION
	--			)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			9,				
	--			1,	
	--			CASE
	--				WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@PRICE_PER_UNIT
	--				ELSE 0
	--			END,
	--			'Security Data Collection ' +
	--				CASE
	--					WHEN @IS_SERIALS_REQUIRED = 1 THEN 'SERIAL'
	--					ELSE ''
	--				END +
	--				CASE
	--					WHEN @IS_MNFR_REQUIRED = 1 THEN ', MNFR'
	--					ELSE ''
	--				END +	
	--				CASE
	--					WHEN @IS_MODEL_REQUIRED = 1 THEN ', MODEL'
	--					ELSE ''
	--				END +
	--				CASE
	--					WHEN @IS_SERIAL_NUMBER_DRIVE_REMOVED = 1 THEN ', SERIAL DRIVE REMOVED'
	--					ELSE ''
	--				END																
			
	--	END
		
	--	DECLARE
	--		@IS_RECYCLING_CERTIFICATE_REQUIRED BIT,
	--		@RECYCLING_CERTIFICATE_PRICE MONEY,
	--		@IS_DESTRICTION_CERTIFICATE_REQUIRED BIT,
	--		@DESTRICTION_CERTIFICATE_PRICE MONEY
			
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_CUSTOMER_CERTIFICATES WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 5 AND IS_PROVIDED = 1)
	--		) BEGIN
		
	--		SELECT 
	--			@IS_RECYCLING_CERTIFICATE_REQUIRED = IS_RECYCLING_CERTIFICATE_REQUIRED,
	--			@RECYCLING_CERTIFICATE_PRICE = RECYCLING_CERTIFICATE_PRICE,
	--			@IS_DESTRICTION_CERTIFICATE_REQUIRED = IS_DESTRICTION_CERTIFICATE_REQUIRED,
	--			@DESTRICTION_CERTIFICATE_PRICE = DESTRICTION_CERTIFICATE_PRICE			
	--		FROM dbo.F_RECYCLING_CUSTOMER_CERTIFICATES WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
			
	--		IF (@IS_RECYCLING_CERTIFICATE_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				13,					
	--				1,
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@RECYCLING_CERTIFICATE_PRICE
	--					ELSE 0
	--				END
	--		END		
			
	--		IF (@IS_DESTRICTION_CERTIFICATE_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				14,		
	--				1,			
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@DESTRICTION_CERTIFICATE_PRICE
	--					ELSE 0
	--				END
	--		END		
		
	--	END		
		
	--	DECLARE
	--		@IS_MASS_BALANCE_REQUIRED BIT,
	--		@MASS_BALANCE_PRICE MONEY,
	--		@IS_AUDIT_REQUIRED BIT,
	--		@AUDIT_PRICE MONEY,
	--		@IS_RECEIVING_REQUIRED BIT,
	--		@RECEIVING_PRICE MONEY,
	--		@IS_DATA_SECURITY_REQUIRED BIT,
	--		@DATA_SECURITY_PRICE MONEY
			
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_CUSTOMER_REPORTS WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 6 AND IS_PROVIDED = 1)
	--		) BEGIN
		
	--		SELECT 
	--			@IS_MASS_BALANCE_REQUIRED = IS_MASS_BALANCE_REQUIRED,
	--			@MASS_BALANCE_PRICE = MASS_BALANCE_PRICE,
	--			@IS_AUDIT_REQUIRED = IS_AUDIT_REQUIRED,
	--			@AUDIT_PRICE = AUDIT_PRICE,
	--			@IS_RECEIVING_REQUIRED = IS_RECEIVING_REQUIRED,
	--			@RECEIVING_PRICE = RECEIVING_PRICE,
	--			@IS_DATA_SECURITY_REQUIRED = IS_DATA_SECURITY_REQUIRED,
	--			@DATA_SECURITY_PRICE = DATA_SECURITY_PRICE
	--		FROM dbo.F_RECYCLING_CUSTOMER_REPORTS WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
			
	--		IF (@IS_MASS_BALANCE_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				15,				
	--				1,	
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@MASS_BALANCE_PRICE
	--					ELSE 0
	--				END
	--		END		
			
	--		IF (@IS_RECEIVING_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				16,					
	--				1,
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@RECEIVING_PRICE
	--					ELSE 0
	--				END
	--		END	
			
	--		IF (@IS_AUDIT_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				17,		
	--				1,			
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@AUDIT_PRICE
	--					ELSE 0
	--				END
	--		END		
			
	--		IF (@IS_DATA_SECURITY_REQUIRED = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				18,	
	--				1,				
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@DATA_SECURITY_PRICE
	--					ELSE 0
	--				END
	--		END		
		
	--	END	
		
	--	DECLARE
	--		@IS_FREGHT_CHARGE BIT,
	--		@FREGHT_PRICE MONEY,
	--		@IS_AFTER_HOURS_CHARGE BIT,
	--		@AFTER_HOURS_PRICE MONEY,
	--		@IS_HOURLY_RATE_CHARGE BIT,
	--		@HOURLY_RATE_PRICE MONEY,
	--		@IS_MILEAGE_CHARGE BIT,
	--		@MILEAGE_PRICE MONEY
			
	--	IF (
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_OTHER_SERVICES WITH (NOLOCK) 
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--			AND 
	--			EXISTS(SELECT TOP(1) 1 FROM dbo.F_RECYCLING_ORDER_SERVICE WITH (NOLOCK)
	--				WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID AND RECYCLING_SERVICE_ID = 7 AND IS_PROVIDED = 1)
	--		) BEGIN
		
	--		SELECT 
	--			@IS_FREGHT_CHARGE = IS_FREGHT_CHARGE,
	--			@FREGHT_PRICE = FREGHT_PRICE,
	--			@IS_AFTER_HOURS_CHARGE = IS_AFTER_HOURS_CHARGE,
	--			@AFTER_HOURS_PRICE = AFTER_HOURS_PRICE,
	--			@IS_HOURLY_RATE_CHARGE = IS_HOURLY_RATE_CHARGE,
	--			@HOURLY_RATE_PRICE = HOURLY_RATE_PRICE,
	--			@IS_MILEAGE_CHARGE = IS_MILEAGE_CHARGE,
	--			@MILEAGE_PRICE = MILEAGE_PRICE
	--		FROM dbo.F_RECYCLING_OTHER_SERVICES WITH (NOLOCK) 
	--		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
			
	--		IF (@IS_FREGHT_CHARGE = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				19,	
	--				1,				
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@FREGHT_PRICE
	--					ELSE 0
	--				END
	--		END		
			
	--		IF (@IS_AFTER_HOURS_CHARGE = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				21,			
	--				1,		
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@AFTER_HOURS_PRICE
	--					ELSE 0
	--				END
	--		END	
			
	--		IF (@IS_HOURLY_RATE_CHARGE = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				20,			
	--				1,		
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@HOURLY_RATE_PRICE
	--					ELSE 0
	--				END
	--		END		
			
	--		IF (@IS_MILEAGE_CHARGE = 1) BEGIN
	--			INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--			SELECT 
	--				@RECYCLING_ORDER_ID, 
	--				22,			
	--				1,		
	--				CASE
	--					WHEN @IS_SERVICE_FLAT_FEE_PRICE = 0 THEN -@MILEAGE_PRICE
	--					ELSE 0
	--				END
	--		END		
		
	--	END	
		
	--	DECLARE @DISCOUNT_VALUE MONEY = 0
	--	IF (@DISCOUNT > 0) BEGIN
	--		SELECT @DISCOUNT_VALUE = CASE
	--				WHEN @IS_FLAT_DISCOUNT = 1 THEN @DISCOUNT
	--				ELSE @TOTAL_PRICE * @DISCOUNT / (100 - @DISCOUNT)
	--			END	
			
	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			CASE
	--				WHEN @IS_FLAT_DISCOUNT = 1 THEN 23
	--				ELSE 24
	--			END,
	--			1,				
	--			@DISCOUNT_VALUE	
	--	END		
		
	--	IF (@IS_SERVICE_FLAT_FEE_PRICE = 1) BEGIN
	--		INSERT INTO dbo.F_RECYCLING_ORDER_ITEM_SERVICE (RECYCLING_ORDER_ID, ITEM_SERVICE_TYPE_ID, ITEM_SERVICE_COUNT, ITEM_SERVICE_PRICE_FOR_ONE)
	--		SELECT 
	--			@RECYCLING_ORDER_ID, 
	--			25,
	--			1,				
	--			-(@TOTAL_PRICE + @DISCOUNT_VALUE)
	--	END		
		
	--	UPDATE dbo.F_RECYCLING_ORDER_INBOUND SET
	--		AMOUNT_OWED = (SELECT SUM(ISNULL(ITEM_SERVICE_PRICE_FOR_ONE, 0.0) * ITEM_SERVICE_COUNT)
	--						FROM dbo.F_RECYCLING_ORDER_ITEM_SERVICE WITH (NOLOCK)
	--						WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
	--	WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID					
			
	--END
END
CREATE FUNCTION [dbo].[fn_nvarchar_GetFormattedExchangeRate]
(
	@foreignCurrencyAbbreviation	nvarchar(8),
	@homeCurrencyAbbreviation		nvarchar(8),
	@foreignToHome					decimal(16,10)
)
RETURNS NVARCHAR(50)
AS
BEGIN
	IF (@foreignCurrencyAbbreviation IS NULL OR @homeCurrencyAbbreviation IS NULL OR @foreignToHome IS NULL)
	BEGIN
		RETURN '';
	END

	RETURN '1 ' + @foreignCurrencyAbbreviation + ' = ' + cast(CONVERT(DOUBLE PRECISION, @foreignToHome) as nvarchar(32)) + ' ' + @homeCurrencyAbbreviation;
END

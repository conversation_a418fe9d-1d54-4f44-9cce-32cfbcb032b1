-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT * FROM RZR_TBS.dbo.F_ITEM_INVENTORY_CAPABILITY WHERE ITEM_INVENTORY_ID = 1000
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('2-20|3-549|4-29|5-34|6-44|7-255|9-199|12-219|13-116|14-103', 4)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('2-13|3-239|4-28|5-249|6-106|7-54|9-213|12-228|13-644|14-265', 4)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('16-85548|27-85549', 27)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('16-85548|27-85549', 6)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('16-85548|27-85549', 16)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE('27-85110|46-85553', 46)
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE(NULL, 1)
CREATE FUNCTION [dbo].[fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE]
(
	@ITEM_ID BIGINT,
	@C_ITEM_MASTER_SKU_ATTRB_CD varchar(max)
	,@C_INVENTORY_CAPABILITY_TYPE_ID bigint
)
RETURNS varchar(max)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @C_RETURN_VAL varchar(max) = ''

	DECLARE @C_INVENTORY_CAPABILITY_TYPE_CD varchar(20) = CONVERT(varchar(19), @C_INVENTORY_CAPABILITY_TYPE_ID) + '-'
	DECLARE @C_ITEM_CAPABILITY_TYPE_ID varchar(20)
		
	IF (CHARINDEX('|' + @C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) > 0
		OR CHARINDEX(CONVERT(varchar(19), @C_INVENTORY_CAPABILITY_TYPE_ID) + '-', @C_ITEM_MASTER_SKU_ATTRB_CD) > 0 
			AND CHARINDEX(CONVERT(varchar(19), @C_INVENTORY_CAPABILITY_TYPE_ID) + '-', @C_ITEM_MASTER_SKU_ATTRB_CD) = 1
	)
	BEGIN
	
		SET
			@C_ITEM_CAPABILITY_TYPE_ID =
			--CONVERT(bigint,
				SUBSTRING(@C_ITEM_MASTER_SKU_ATTRB_CD
						, CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) 
							+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD)
						, LEN(@C_ITEM_MASTER_SKU_ATTRB_CD) - 
							(
								CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) 
								+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD) - 1
							)
					)			
			--)

		IF (CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) > 0)
			SET
				@C_ITEM_CAPABILITY_TYPE_ID 
				=
				SUBSTRING(@C_ITEM_CAPABILITY_TYPE_ID
							, 1
							, CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) - 1
						)			
		SELECT
			@C_RETURN_VAL = (
				CASE 
					WHEN C.INVENTORY_CAPABILITY_ID IS NULL THEN dic.INVENTORY_CAPABILITY_VALUE
					ELSE C.INVENTORY_CAPABILITY_VALUE
				END)
		FROM D_INVENTORY_CAPABILITY			dic	WITH(NOLOCK)
		LEFT JOIN F_ITEM_CAPABILITY			L	WITH(NOLOCK)
			ON L.CAPABILITY_TYPE_ID = dic.INVENTORY_CAPABILITY_TYPE_ID
			AND L.IS_LAST_INSERTED = 1
			AND L.ITEM_ID = @ITEM_ID
		LEFT JOIN D_INVENTORY_CAPABILITY	C	WITH(NOLOCK)
			ON L.INVENTORY_CAPABILITY_ID = C.INVENTORY_CAPABILITY_ID			
		WHERE
			dic.INVENTORY_CAPABILITY_ID = CONVERT(bigint, @C_ITEM_CAPABILITY_TYPE_ID)
	END
	
	--IF (@C_RETURN_VAL = '') SET @C_RETURN_VAL = NULL
	
	-- Return the result of the function
	RETURN @C_RETURN_VAL

END

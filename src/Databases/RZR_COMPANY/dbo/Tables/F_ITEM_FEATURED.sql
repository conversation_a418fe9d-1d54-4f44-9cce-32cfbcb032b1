CREATE TABLE [dbo].[F_ITEM_FEATURED] (
    [ITEM_FEATURED_ID]                    BIGINT          IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]                      BIGINT          NOT NULL,
    [ITEM_NUMBER]                         NVARCHAR (256)  NOT NULL,
    [ITEM_IS_FEATURED]                    BIT             NOT NULL,
    [ITEM_IS_DEAL_10_PERC_OFF_EBAY]       BIT             CONSTRAINT [DF_F_ITEM_FEATURED_ITEM_IS_DEAL_10_PERC_OFF_EBAY] DEFAULT ((0)) NOT NULL,
    [ITEM_IS_DEAL_10_PERC_OFF_EBAY_PRICE] DECIMAL (18, 2) CONSTRAINT [DF_F_ITEM_FEATURED_ITEM_IS_DEAL_10_PERC_OFF_EBAY_PRICE] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]                         BIT             NOT NULL,
    [IS_DELETED]                          BIT             NOT NULL,
    [INSERTED_BY]                         VARCHAR (150)   NOT NULL,
    [INSERTED_DT]                         DATETIME        NOT NULL,
    [UPDATED_BY]                          VARCHAR (150)   NULL,
    [UPDATED_DT]                          DATETIME        NULL,
    [DELETED_BY]                          VARCHAR (150)   NULL,
    [DELETED_DT]                          DATETIME        NULL,
    CONSTRAINT [PK_F_ITEM_FEATURED] PRIMARY KEY CLUSTERED ([ITEM_FEATURED_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_FEATURED';


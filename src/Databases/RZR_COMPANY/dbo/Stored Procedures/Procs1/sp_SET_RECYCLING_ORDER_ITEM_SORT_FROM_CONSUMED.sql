-- =============================================
-- Author:		<V.DREBEZOVA>
-- Create date: <07/09/2014>
-- Description:	Moves the weight from consumed lots
-- =============================================
/*
exec [dbo].[sp_SET_RECYCLING_ORDER_ITEM_SORT_FROM_CONSUMED]
	 @WAREHOUSE_ID              = 6
	,@RECYCLING_ORDER_ITEM_IDS  = NULL
	,@WEIGHT                    = 14700000
	,@RECEIVED_ORDER_ITEM_ID    = 59397
	,@ITEM_TYPE_ID              = 362
	,@USER_ID                   = 45
	,@IP                        = 1
	,@IS_DEBUG					= 1
*/
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_SORT_FROM_CONSUMED]		
	@WAREHOUSE_ID				BIGINT
	,@RECYCLING_ORDER_ITEM_IDS	XML
	,@WEIGHT					FLOAT
	,@RECEIVED_ORDER_ITEM_ID	BIGINT
	,@ITEM_TYPE_ID				BIGINT
	,@USER_ID					BIGINT
	,@IP						BIGINT
	,@IS_DEBUG					BIT = 0
AS
BEGIN
	SET XACT_ABORT ON
	
	DECLARE @idoc INT
	DECLARE @PARENTS table (ID BIGINT)
	EXEC sp_xml_preparedocument @idoc OUTPUT, @RECYCLING_ORDER_ITEM_IDS		
	INSERT INTO @PARENTS (ID)
	SELECT 	x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( ID BIGINT ) AS x		         
	EXEC sp_xml_removedocument @idoc
		
	DECLARE
		@consumedWorkflowTypeId int = 9, -- select * from C_RECYCLING_WORKFLOW_TYPE
		@spName					nvarchar(64) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID),
		@utcNow					datetime = getutcdate();

	DECLARE 	
		@wtToCollect		FLOAT = ISNULL(@WEIGHT, 0),	
		@wtCollectedFromLot	FLOAT = 0.0,
		@fromLotId			BIGINT,		
		@wtRemainInLot		FLOAT = 0.0,
		@wtRemainCurrentInLot FLOAT = 0.0;

	DECLARE Items CURSOR LOCAL FORWARD_ONLY FOR
		SELECT
			RECYCLING_ORDER_ITEM_ID,
			WEIGHT_REMAIN		
		FROM dbo.F_RECYCLING_ORDER_ITEM			i	WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER			RO	WITH(NOLOCK)
			ON I.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
			AND I.RECYCLING_ORDER_ITEM_ID != @RECEIVED_ORDER_ITEM_ID
		LEFT JOIN [dbo].[F_LOCATION]			L	WITH(NOLOCK)
			ON i.LOCATION_ID = L.LOCATION_ID
		LEFT JOIN F_RECYCLING_ORDER				OO	WITH(NOLOCK)
			ON OO.RECYCLING_ORDER_ID = I.OUTBOUND_ORDER_ID
			AND OO.RECYCLING_ORDER_STATUS_ID != 4 -- !canceled -- select * from C_RECYCLING_ORDER_STATUS
		WHERE I.WORKFLOW_STEP_ID = @consumedWorkflowTypeId
			AND I.RECYCLING_ORDER_ITEM_MERGED_ID IS NULL
			AND I.WEIGHT_REMAIN > 0
			AND (I.OUTBOUND_ORDER_ID IS NULL       OR OO.RECYCLING_ORDER_ID IS NULL)		
			AND (@RECYCLING_ORDER_ITEM_IDS IS NULL OR RECYCLING_ORDER_ITEM_ID IN (SELECT ID FROM @PARENTS))
			AND coalesce(i.CONSUMED_IN_WAREHOUSE_ID, L.WAREHOUSE_ID, RO.WAREHOUSE_ID) = @WAREHOUSE_ID
		ORDER BY WEIGHT_REMAIN asc
			
	OPEN Items		
	FETCH NEXT FROM Items INTO @fromLotId, @wtRemainInLot
	WHILE @@FETCH_STATUS = 0
	BEGIN

		IF (@wtToCollect <= 0)
			BREAK;

		SET @wtCollectedFromLot = IIF(@wtRemainInLot <= @wtToCollect, @wtRemainInLot, @wtToCollect);
		SET @wtToCollect = @wtToCollect - @wtCollectedFromLot

		IF (@IS_DEBUG = 1)
		BEGIN
			SELECT 
				'Consumed collected'							AS [WHAT]
				,RECYCLING_ORDER_ITEM_ID						AS [@fromLotId]
				,ROUND(WEIGHT_REMAIN - @wtCollectedFromLot, 2)	AS [@wtRemainInLot]
				,@wtCollectedFromLot							AS [@wtCollectedFromLot]   
				,ROUND(@wtToCollect - @wtCollectedFromLot, 2)	AS [@wtToCollect]	
			FROM F_RECYCLING_ORDER_ITEM
			WHERE RECYCLING_ORDER_ITEM_ID = @fromLotId		
		END
		ELSE
		BEGIN
			BEGIN TRANSACTION
					
				UPDATE F_RECYCLING_ORDER_ITEM SET
					WEIGHT_REMAIN	= ROUND(WEIGHT_REMAIN - @wtCollectedFromLot, 2),
					UPDATED_BY		= @spName,
					UPDATED_DT		= @utcNow,
					UPDATED_BY_USER = @USER_ID,
					UPDATED_BY_IP	= @IP
				WHERE RECYCLING_ORDER_ITEM_ID = @fromLotId					
				
				select @wtRemainCurrentInLot = @wtRemainInLot - @wtCollectedFromLot
				EXEC [recycling].[sp_SetLotConsumed]
					@LotId					= @RECEIVED_ORDER_ITEM_ID,
					@ParentConsumedLot		= @fromLotId,
					@GetWeight				= @wtCollectedFromLot,
					@RemainWeight			= @wtRemainCurrentInLot
				
			COMMIT TRANSACTION
		END																			

		FETCH NEXT FROM Items INTO @fromLotId, @wtRemainInLot
	END
	CLOSE Items
	DEALLOCATE Items	

	
	IF (@wtToCollect > 0)
	BEGIN

		SELECT top(1)
			@fromLotId		= RECYCLING_ORDER_ITEM_ID,
			@wtRemainInLot	= WEIGHT_REMAIN		
		FROM dbo.F_RECYCLING_ORDER_ITEM			i	WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER			RO	WITH(NOLOCK)
			ON I.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
			AND I.RECYCLING_ORDER_ITEM_ID != @RECEIVED_ORDER_ITEM_ID
		LEFT JOIN [dbo].[F_LOCATION]			L	WITH(NOLOCK)
			ON i.LOCATION_ID = L.LOCATION_ID
		LEFT JOIN F_RECYCLING_ORDER				OO	WITH(NOLOCK)
			ON OO.RECYCLING_ORDER_ID = I.OUTBOUND_ORDER_ID
			AND OO.RECYCLING_ORDER_STATUS_ID != 4 -- !canceled -- select * from C_RECYCLING_ORDER_STATUS
		WHERE I.WORKFLOW_STEP_ID = @consumedWorkflowTypeId
			AND I.RECYCLING_ORDER_ITEM_MERGED_ID IS NULL
			--AND I.WEIGHT_REMAIN > 0 -- must pick the weight that does not exist in the warehouse (RSW-12968)
			AND (I.OUTBOUND_ORDER_ID IS NULL       OR OO.RECYCLING_ORDER_ID IS NULL)		
			AND (@RECYCLING_ORDER_ITEM_IDS IS NULL OR RECYCLING_ORDER_ITEM_ID IN (SELECT ID FROM @PARENTS))
			AND coalesce(i.CONSUMED_IN_WAREHOUSE_ID, L.WAREHOUSE_ID, RO.WAREHOUSE_ID) = @WAREHOUSE_ID
		ORDER BY WEIGHT_REMAIN asc -- from the least weight as there are no lots with positive weights left

		IF (@fromLotId IS NULL)
		BEGIN
			DECLARE
				@recyclingOrderId bigint = null
				,@packagingTypeId int    = (select top(1) RECYCLING_PACKAGING_TYPE_ID from [dbo].[D_RECYCLING_PACKAGING_TYPE] order by IS_DELETED asc, IS_SYSTEM_PACKAGING desc)
				,@locationId      bigint = (select top(1) location_id from F_LOCATION where IS_DELETED = 0 and IS_INACTIVE = 0 order by IS_WAREHOUSE_DEFAULT_LOCATION desc)

			EXEC sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID
				@WAREHOUSE_ID = @WAREHOUSE_ID
				,@RECYCLING_ORDER_ID = @recyclingOrderId OUTPUT

			declare @tIds [dbo].[bigint_ID_ARRAY];			
			insert into @tIds(ID)
			exec [dbo].[sp_SET_RECYCLING_ORDER_ITEM]
				@RECYCLING_ORDER_ID		 	= @recyclingOrderId
				,@RECYCLING_ORDER_ITEM_ID 	= null
				,@ITEM_TYPE_ID			 	= @ITEM_TYPE_ID
				,@WEIGHT_RECIEVED		 	= 0
				,@WEIGHT_TARE			 	= 0
				,@PACKAGING_TYPE_ID		 	= @packagingTypeId
				,@WORKFLOW_TYPE_ID		 	= @consumedWorkflowTypeId
				,@ITEM_COUNT				= 0
				,@LOCATION_ID			 	= @locationId
				,@NOTES					 	= 'A seed consumed lot'
				,@RECIEVE_DATE			 	= @utcNow
				,@USER_ID				 	= @USER_ID
				,@warehouseId			 	= @WAREHOUSE_ID
				,@IP						= @IP
				,@IS_SELECT_RESULT		 	= 1

			select @fromLotId = ID from @tIds;
		END


		IF (@IS_DEBUG = 1)
			SELECT
				'Spooky weight created'					AS [WHAT]
				,@fromLotId								AS [@fromLotId]
				,round(@wtRemainInLot - @wtToCollect, 2)AS [@wtRemainInLot]
				,@wtToCollect							AS [@wtCollectedFromLot]   
				,0										AS [@wtToCollect]	 
		BEGIN

			BEGIN TRANSACTION					
				UPDATE F_RECYCLING_ORDER_ITEM SET
					WEIGHT_REMAIN	= ROUND(WEIGHT_REMAIN - @wtToCollect, 2),
					UPDATED_BY		= @spName,
					UPDATED_DT		= @utcNow,
					UPDATED_BY_USER = @USER_ID,
					UPDATED_BY_IP	= @IP
				WHERE RECYCLING_ORDER_ITEM_ID = @fromLotId					

				select @wtRemainCurrentInLot = @wtRemainInLot - @wtCollectedFromLot
				EXEC [recycling].[sp_SetLotConsumed]
					@LotId					= @RECEIVED_ORDER_ITEM_ID,
					@ParentConsumedLot		= @fromLotId,
					@GetWeight				= @wtToCollect,
					@RemainWeight			= @wtRemainCurrentInLot
						
			COMMIT TRANSACTION
		END 
	END


	--replace previous select with new function, that should calculate this in the correct weight
	SELECT
		[dbo].[fn_float_GET_RECYCLING_INVENTORY_CONSUMED_WEIGHT](@WAREHOUSE_ID, 0, NULL) 
			- iif(@IS_DEBUG = 1, @WEIGHT- @wtToCollect, 0.0) AS [WEIGHT]		
END
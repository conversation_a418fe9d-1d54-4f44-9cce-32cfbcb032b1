CREATE TABLE [dbo].[D_WAREHOUSE_STORAGE_UNIT] (
    [STORAGE_UNIT_TYPE_ID] INT            NOT NULL,
    [STORAGE_UNIT_NAME]    NVARCHAR (128) NOT NULL,
    [STORAGE_UNIT_CODE]    NVARCHAR (128) NOT NULL,
    [IS_FIXED]             BIT            CONSTRAINT [DF_D_WAREHOUSE_STORAGE_UNIT_IS_FIXED] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_D_WAREHOUSE_STORAGE_UNIT] PRIMARY KEY CLUSTERED ([STORAGE_UNIT_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is used to set the warehouse structure. The page in the system module', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_WAREHOUSE_STORAGE_UNIT';


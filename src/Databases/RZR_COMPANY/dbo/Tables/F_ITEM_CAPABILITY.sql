CREATE TABLE [dbo].[F_ITEM_CAPABILITY] (
    [ITEM_CAPABILITY_ID]      BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_ID]                 BIGINT        NOT NULL,
    [CAPABILITY_TYPE_ID]      INT           NOT NULL,
    [INVENTORY_CAPABILITY_ID] BIGINT        NOT NULL,
    [USER_ID]                 BIGINT        NOT NULL,
    [IS_LAST_INSERTED]        BIT           CONSTRAINT [DF_F_ITEM_CAPABILITY_IS_LAST_INSERTED] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]             BIT           CONSTRAINT [DF_F_ITEM_CAPABILITY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]              BIT           CONSTRAINT [DF_F_ITEM_CAPABILITY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]             VARCHAR (150) CONSTRAINT [DF_F_ITEM_CAPABILITY_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]             DATETIME      CONSTRAINT [DF_F_ITEM_CAPABILITY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]              VARCHAR (150) NULL,
    [UPDATED_DT]              DATETIME      NULL,
    [DELETED_BY]              VARCHAR (150) NULL,
    [DELETED_DT]              DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_CAPABILITY] PRIMARY KEY CLUSTERED ([ITEM_CAPABILITY_ID] ASC),
    CONSTRAINT [FK_F_ITEM_CAPABILITY_C_INVENTORY_CAPABILITY_TYPE] FOREIGN KEY ([CAPABILITY_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_TYPE_ID]),
    CONSTRAINT [FK_F_ITEM_CAPABILITY_D_INVENTORY_CAPABILITY] FOREIGN KEY ([INVENTORY_CAPABILITY_ID]) REFERENCES [dbo].[D_INVENTORY_CAPABILITY] ([INVENTORY_CAPABILITY_ID]),
    CONSTRAINT [FK_F_ITEM_CAPABILITY_F_ITEM] FOREIGN KEY ([ITEM_ID]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_F_ITEM_CAPABILITY_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON DELETE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The item (SKU) edit page has capabilities which are tied to SKU, those capabilities’ values can be changed.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_CAPABILITY';


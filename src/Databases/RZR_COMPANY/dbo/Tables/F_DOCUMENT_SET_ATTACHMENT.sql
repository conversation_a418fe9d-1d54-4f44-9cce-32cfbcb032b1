CREATE TABLE [dbo].[F_DOCUMENT_SET_ATTACHMENT] (
    [DOCUMENT_SET_ATTACHMENT_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [DOCUMENT_SET_ID]            BIGINT         NOT NULL,
    [POSITION_INDEX]             INT            NULL,
    [NAME]                       NVARCHAR (256) NOT NULL,
    [FILE_BYTE_COUNT]            BIGINT         NULL,
    [IS_DELETED]                 BIT            CONSTRAINT [DF_F_DOCUMENT_SET_ATTACHMENT_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                NVARCHAR (150) CONSTRAINT [DF_F_DOCUMENT_SET_ATTACHMENT_INSERTED_BY] DEFAULT ((1)) NOT NULL,
    [INSERTED_DT]                DATETIME       CONSTRAINT [DF_F_DOCUMENT_SET_ATTACHMENT_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 NVARCHAR (150) NULL,
    [UPDATED_DT]                 DATETIME       NULL,
    [DELETED_BY]                 NVARCHAR (150) NULL,
    [DELETED_DT]                 DATETIME       NULL,
    CONSTRAINT [PK_F_DOCUMENT_SET_ATTACHMENT] PRIMARY KEY CLUSTERED ([DOCUMENT_SET_ATTACHMENT_ID] ASC),
    CONSTRAINT [FK_F_DOCUMENT_SET_ATTACHMENT_F_DOCUMENT_SET] FOREIGN KEY ([DOCUMENT_SET_ID]) REFERENCES [dbo].[F_DOCUMENT_SET] ([DOCUMENT_SET_ID]) ON DELETE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of documents for crm -> document management', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_DOCUMENT_SET_ATTACHMENT';


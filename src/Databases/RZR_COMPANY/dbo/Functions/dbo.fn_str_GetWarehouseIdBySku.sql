-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
create function [dbo].[fn_str_GetWarehouseIdBySku]
(
	@SKU bigint
)
RETURNS varchar(200)
AS
BEGIN
	
	DECLARE @C_RETURN_VAL bigint = null

	declare 			
		@warehouseCapability		bigint

	select
		@warehouseCapability = [INVENTORY_CAPABILITY_TYPE_ID]
	from [dbo].[C_INVENTORY_CAPABILITY_TYPE] with (nolock)
	where [INVENTORY_CAPABILITY_VALUE] = 'warehouse'

	select @C_RETURN_VAL = w.WAREHOUSE_ID
	FROM F_ITEM AS fi with (nolock)			
	inner join [dbo].[F_ITEM_MASTER_SKU_ATTRB] imsa with (nolock)
		on fi.[ITEM_MASTER_SKU_ATTRB_ID] = imsa.[ITEM_MASTER_SKU_ATTRB_ID]
	inner join [dbo].[D_WAREHOUSE] w  with (nolock)		
		on [dbo].[fn_str_GetInventoryCapabilityValueByCapabilityTypeId](imsa.[ITEM_MASTER_SKU_ATTRB_CD], @warehouseCapability) = w.WAREHOUSE_CD
	where fi.ITEM_ID = @SKU			
	
	RETURN @C_RETURN_VAL

END
-- =============================================
-- Author:	 <Oleg K. Evseev>
-- Create date: <09/15/2013>
-- Description: <Returns inventory items data to build inventory labelS>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEMS_FOR_PRINTING] 	
    @ITEM_IDs XML	
AS
BEGIN
    DECLARE @idoc INT
    DECLARE @ids TABLE (ITEM_ID BIGINT)
    
    -- read xml list into "@ids"
    EXEC sp_xml_preparedocument @idoc OUTPUT, @ITEM_IDs
    
    INSERT INTO @ids (ITEM_ID)
    SELECT x.ID
    FROM OPENXML (@idoc, '/Root/Items', 1)
    WITH (ID BIGINT) AS x
    
    EXEC sp_xml_removedocument @idoc


    SELECT DISTINCT
	   I.ITEM_INVENTORY_ID
	   ,MI.ITEM_NUMBER	
	   ,I.ITEM_INVENTORY_SERIAL
	   ,MT.ITEM_TITLE				AS [DESCRIPTION]
	   ,D.INVENTORY_CAPABILITY_VALUE	AS DISPOSITOIN
	   ,M.MANUFACTURER_CD
	   ,UR.UserName				AS AUTHOR_USER
	   ,I.ITEM_INVENTORY_UNIQUE_ID
	   ,CU.CUSTOMER_NAME
	   ,L.LOCATION_NAME				AS LOCATION
	   ,CD.ITEM_CONDITION_DESC		AS CONDITION
    FROM @ids						IDS
    INNER JOIN F_ITEM_INVENTORY		I   WITH(NOLOCK)
      ON IDS.ITEM_ID = I.ITEM_INVENTORY_ID
    LEFT JOIN F_ITEM_MASTER			MI  WITH(NOLOCK)
      ON I.ITEM_MASTER_ID = MI.ITEM_MASTER_ID
    INNER JOIN F_ITEM_MASTER_TITLE		MT  WITH(NOLOCK)
      ON I.ITEM_MASTER_ID	= MT.ITEM_MASTER_ID
    INNER JOIN D_MANUFACTURER			M   WITH(NOLOCK)
      ON MI.MANUFACTURER_ID = M.MANUFACTURER_ID
    LEFT JOIN D_ITEM_CONDITION		CD  WITH(NOLOCK)
      ON I.CONDITION_ID = CD.ITEM_CONDITION_ID
    -- LOCATION
    LEFT JOIN F_LOCATION				L   WITH(NOLOCK)
      ON  L.LOCATION_ID = I.LOCATION_ID
    -- DISPOSITOIN
    LEFT JOIN F_ITEM_INVENTORY_CAPABILITY C  WITH(NOLOCK)
	 ON I.ITEM_INVENTORY_ID = C.ITEM_INVENTORY_ID
    LEFT JOIN D_INVENTORY_CAPABILITY	D   WITH(NOLOCK)
	 ON  C.ITEM_INVENTORY_CAPABILITY_ID = D.INVENTORY_CAPABILITY_ID
	 AND D.INVENTORY_CAPABILITY_TYPE_ID = 13 
    LEFT JOIN tb_User				UR  WITH(NOLOCK)
      ON I.AUTHOR_USER_ID	=	UR.UserID
    LEFT JOIN F_CUSTOMER				CU  WITH(NOLOCK)
	 ON I.CUSTOMER_ID = CU.CUSTOMER_ID
END
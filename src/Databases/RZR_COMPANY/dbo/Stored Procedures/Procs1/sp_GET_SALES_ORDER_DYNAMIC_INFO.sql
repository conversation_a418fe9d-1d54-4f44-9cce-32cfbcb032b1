CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_DYNAMIC_INFO] 
--declare
	@ORDER_ID	BIGINT,
	@IS_RECEIVE	BIT
AS
BEGIN

	DECLARE 
		@STATUS_ID INT,
		@AMOUNT MONEY = 0,
		@IS_INBOUND		BIT,
		@ITEM_COUNT BIGINT,
		@ITEM_COUNT_IN_TRANSIT BIGINT,
		@RECYCLING_ORDER_ID		BIGINT,
		@TAX					MONEY = dbo.sp_CALCULATE_SALES_ORDER_TAX(@ORDER_ID)

	SELECT
		@RECYCLING_ORDER_ID = [RECYCLING_ORDER_ID]		
	FROM F_SALES_ORDER WITH (NOLOCK)
	WHERE SALES_ORDER_ID = @ORDER_ID

	SELECT 				
		@IS_INBOUND		= IS_INBOUND		
	FROM dbo.F_RECYCLING_ORDER WITH (NOLOCK) 
	WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
		
	SELECT 
		@AMOUNT = SUM(
						CASE WHEN IS_FLAT_FEE = 0 THEN SOI.RECEIVED_PRICE
							 ELSE SOI.ITEM_PRICE
						END *
						CASE
							WHEN SOI.IS_FLAT_FEE = 0 THEN SOI.ITEM_QUANTITY
							ELSE 1							
						END
					),
		@ITEM_COUNT = COUNT(SOI.SALES_ORDER_ID),
		@ITEM_COUNT_IN_TRANSIT = COUNT(CASE
			WHEN SOI.RECEIVE_STATUS_ID = 1 THEN SOI.SALES_ORDER_ID						
		END)
	FROM F_SALES_ORDER_ITEM SOI WITH (NOLOCK)	
	WHERE SOI.SALES_ORDER_ID = @ORDER_ID				
		
	SELECT
		3						AS StatusId
		,@AMOUNT				AS Subtotal	
		,@TAX					AS Tax
		,CASE
			WHEN (NOT EXISTS(
				SELECT TOP(1) 1 
				FROM F_INVOICE WITH (NOLOCK)
				WHERE INVOICE_TYPE_ID = 1 AND ORDER_ID = @ORDER_ID)) 
			THEN 0

			WHEN EXISTS(
				SELECT TOP(1) 1 
				FROM dbo.F_INVOICE  WITH (NOLOCK) 
				WHERE INVOICE_TYPE_ID = 1 AND ORDER_ID = @ORDER_ID 
				  AND STATUS_ID = 5
				  AND IS_VOIDED = 0) 
			THEN 1	
					
			ELSE 0
		END						AS PaymentsApplied
	,@ITEM_COUNT				AS ItemCount
	,@ITEM_COUNT_IN_TRANSIT		AS InTransitCount
	,(SELECT SUM(PAID_AMOUNT) 		
				FROM dbo.F_INVOICE	FI	WITH (NOLOCK) -- There is always 1 active invoice
			WHERE INVOICE_TYPE_ID = 1 and ORDER_ID = @ORDER_ID
			  AND IS_VOIDED = 0	
			  GROUP BY FI.ORDER_ID
		) as PaidAmount

END
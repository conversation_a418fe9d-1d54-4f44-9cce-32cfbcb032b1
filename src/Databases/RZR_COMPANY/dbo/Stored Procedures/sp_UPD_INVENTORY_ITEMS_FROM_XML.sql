-- =============================================
-- Author:		
-- Create date: 
-- Description: Update assets prices from XML
-- =============================================
CREATE PROCEDURE [dbo].[sp_UPD_INVENTORY_ITEMS_FROM_XML]
	 @ORDER_ID		BIGINT
	,@ITEMS_DATA	[AssetImport] READONLY
	,@USER_ID		BIGINT
	,@IP			BIGINT
AS
BEGIN
	DECLARE
		 @SP_NAME NVARCHAR(50)	= N'sp_UPD_INVENTORY_ITEMS_FROM_XML'
		,@UTC_NOW DATETIME		= GETUTCDATE()

	DECLARE @T_RESULT TABLE([AssetId] BIGINT, Price money, FairMarketPrice money)

	begin transaction

		UPDATE asset SET
			asset.[ItemPrice]	 	 = a.[ItemPrice]
			,asset.[FMVPrice]		 = a.[FMVPrice]	
			,asset.IsPriceChanged	 = 
				case
					when a.[ItemPrice] = isnull(asset.ItemPriceCalc, 0) then 0 
					else 1
				end			
			,asset.[UpdatedDate]	 = @UTC_NOW
			,asset.[UpdatedBy]		 = @SP_NAME
			,asset.[UpdatedByUserId] = @USER_ID
			,asset.[UpdatedByUserIp] = @IP
		OUTPUT INSERTED.[Id], INSERTED.ItemPrice, INSERTED.FMVPrice INTO @T_RESULT
		FROM 
			(
				select a.Id, 
					iif(nullif(id.[FairMarketPrice], 0) is null or nullif(id.Price, 0) is not null,
						isnull(id.Price, A.[ItemPrice]),
						iif(isnull(ro.[RevenueShareSplit], 0) = 0, 1, ro.[RevenueShareSplit]) *
							iif(id.[FairMarketPrice] * ((100 - ag.[TotalDevaluationValue]) / 100) - ag.[AbsoluteDevaluationValue]  < 0,
								0,
								id.[FairMarketPrice] * ((100 - ag.[TotalDevaluationValue]) / 100) - ag.[AbsoluteDevaluationValue])
					) as [ItemPrice],
					isnull(id.[FairMarketPrice], A.[FMVPrice])	as [FMVPrice]
				from @ITEMS_DATA								id
				inner join [recycling].F_Asset					a WITH(ROWLOCK)
					on (ID.SERIAL = A.[SerialNumber] or ID.UniqueId = A.UniqueId)
				inner join [recycling].[F_AuditSession]			s WITH(ROWLOCK)
					on a.[AuditSessionId] = s.Id
					and s.[RecyclingOrderId] = @ORDER_ID
				left join [recycling].[F_AssetGrading]			ag with (nolock)
					on a.Id = ag.AssetId
				left join [dbo].[F_RECYCLING_ORDER]				ro WITH(NOLOCK)
					on s.[RecyclingOrderId] = ro.RECYCLING_ORDER_ID
			) a				
		inner join [recycling].F_Asset	AS asset
			on asset.Id = a.Id				
	
		UPDATE II SET
			II.ITEM_INVENTORY_UNIT_COST_ORIGINAL	= r.Price,
			II.ITEM_INVENTORY_UNIT_COST				= r.Price,
			II.ITEM_INVENTORY_UNIT_COST_FAIR_MARKET = isnull(r.FairMarketPrice, II.ITEM_INVENTORY_UNIT_COST_FAIR_MARKET),		
			II.UPDATED_DT							= @UTC_NOW,
			II.UPDATED_BY							= @SP_NAME,
			II.UPDATED_BY_IP						= @IP,
			II.MODIFIER_USER_ID						= @USER_ID
		FROM F_ITEM_INVENTORY  II WITH(ROWLOCK)
		INNER JOIN @T_RESULT r
			ON r.AssetId = II.AssetId				

		SELECT @@ROWCOUNT AS UpdateRowCount

	commit transaction

END
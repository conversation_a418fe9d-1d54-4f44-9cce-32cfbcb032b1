CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDERS_BY_INVOICE_CONSOLIDATED]
	@C_INVOICE_CONSOLIDATED_ID BIGINT
AS
BEGIN

	SELECT 
		I.ORDER_ID				AS SalesOrderId
		,I.ORDER_AUTO_NAME		AS SalesOrderNumber
		,I.INVOICE_ID			AS InvoiceId
		,I.AUTO_NAME			AS InvoiceAutoName 
		,I.ORIGINAL_AMOUNT		AS Amount
		,I.PAID_AMOUNT			AS PaidAmount
		,I.AMOUNT_DUE			AS AmountDue
		,I.DATE_CREATED			AS DateCreate
		,I.DATE_DUE				AS DateDue
		,I.IS_RECYCLING			AS IsRecycling
	FROM dbo.[vw_AR_INVOICE] I WITH(NOLOCK)
	WHERE I.CONSOLIDATED_ID = @C_INVOICE_CONSOLIDATED_ID
	  AND I.IS_INVOICE_CONSOLIDATED = 0

END
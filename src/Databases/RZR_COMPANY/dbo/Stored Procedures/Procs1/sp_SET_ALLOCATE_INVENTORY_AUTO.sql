-- =============================================
-- Author:	 <Author,,Name>
-- Create date: <Create Date,,>
-- Description: <Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_ALLOCATE_INVENTORY_AUTO] 
     @SALES_ORDER_ITEM_ID			BIGINT
    ,@ITEM_MASTER_ID				BIGINT
    ,@CONDITION_ID					BIGINT
    ,@SKU							BIGINT	= NULL
	,@PRODUCT_CODE_ID				BIGINT	= NULL
	,@REVISION_ID					BIGINT	= NULL
    ,@USE_SUBS						BIT		= 0	
	,@USER_ID						BIGINT
	,@USER_IP						BIGINT
	,@C_IS_DEBUG					BIT		= 0
	,@C_USE_INTERNAL_TRANSACTION	BIT		= 1
AS
BEGIN

	IF (@C_USE_INTERNAL_TRANSACTION = 1)
	BEGIN
		SET XACT_ABORT ON;
		BEGIN TRAN AllocateInventoryAuto;
	END

	-- Collect items to allocate
	DECLARE @inventoryToAllocate bigint_PARE_ARRAY;
	
	DECLARE @IS_LOCATION_AND_CUSTOMER_GROUPING	BIT = ISNULL((SELECT TOP(1) USS.[IS_EXTRA_COLUMNS_FOR_UNALLOCATED] FROM [dbo].[U_SYSTEM_SETTINGS] USS WITH(NOLOCK)), 0);

	-- Qty to allocate - from the salesOrder item
	DECLARE
		@QTY_ORDERED	BIGINT
		,@CUSTOMER_ID	BIGINT
		,@LOCATION_ID	BIGINT
		,@IsKit			bit = 0;
		
	SELECT
		@QTY_ORDERED = soi.[ITEM_QUANTITY]
		,@CUSTOMER_ID = IIF(@IS_LOCATION_AND_CUSTOMER_GROUPING = 1, soi.[CUSTOMER_ID], NULL)
		,@LOCATION_ID = IIF(@IS_LOCATION_AND_CUSTOMER_GROUPING = 1, soi.[LOCATION_ID], NULL)
		,@IsKit		  = case when iikh.ItemId is null then 0 else 1 end
	FROM [dbo].[F_SALES_ORDER_ITEM] soi WITH (NOLOCK)
	left join [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)
		on soi.ITEM_ID = iikh.ItemId
	WHERE soi.[SALES_ORDER_ITEM_ID] = @SALES_ORDER_ITEM_ID;


	-- Collect models that can be used for allocation
	CREATE TABLE #MODELS (ID BIGINT NOT NULL, SORT_ORDER INT NOT NULL);

	-- source model
	INSERT INTO #MODELS (ID, SORT_ORDER) 
	VALUES (@ITEM_MASTER_ID, 1);

	select @USE_SUBS = iif(@IsKit = 0, @USE_SUBS, 0)

	-- can take substitute models
	IF (@USE_SUBS = 1)
	BEGIN
		INSERT INTO #MODELS
		SELECT
				[SUBSTITUTE_ITEM_MASTER_ID]
			,2
		FROM [dbo].[vw_F_ITEM_MASTER_SUBSTITUTES_ALL]
		WHERE [ITEM_MASTER_ID] = @ITEM_MASTER_ID;
	END

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT
			'#MODELS' as WHAT,
			*
		FROM #MODELS;
	END

	-- models from bundles ---------
	INSERT INTO #MODELS
	SELECT
			I.ITEM_MASTER_ID
		,3
	FROM [dbo].[F_ITEM_LOT]		IL WITH (NOLOCK)
	INNER JOIN [dbo].[F_ITEM]	I WITH (NOLOCK)
		ON IL.[ITEM_ID_MASTER] = I.[ITEM_ID]
	LEFT JOIN #MODELS		M
		ON I.[ITEM_MASTER_ID] = M.ID
	WHERE M.[ID] IS NULL
		AND IL.[ITEM_ID] = @SKU;

		
	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT
			'#MODELS + bundles' as WHAT,
			*
		FROM #MODELS;
	END

	CREATE TABLE #SKUS (ITEM_ID BIGINT);
	INSERT INTO #SKUS(ITEM_ID)	
	SELECT @SKU
	UNION ALL
	SELECT 
		IL.ITEM_ID_MASTER
	FROM F_ITEM			    I  WITH (NOLOCK)
	INNER JOIN dbo.F_ITEM_LOT IL WITH (NOLOCK)
		ON I.ITEM_ID = IL.ITEM_ID
	WHERE I.ITEM_ID = @SKU;
	--------------------------------

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT
			'#SKUS' as WHAT
			,@QTY_ORDERED AS QTY_ORDERED
			,*
		FROM #SKUS;
	END


	-- Select all available inventory suitable for allocation (having model from #MODELS)
	CREATE TABLE #T_ITEM_INVENTORY_AVAILABLE (
		[ITEM_INVENTORY_ID] BIGINT NOT NULL,
		[QTY_UNALLOCATED] BIGINT NOT NULL,
		[ITEM_ID] BIGINT,
		[CONDITION_ID] BIGINT,
		[SORT_ORDER] INT NOT NULL
	);

	INSERT INTO #T_ITEM_INVENTORY_AVAILABLE
	SELECT
		FII.[ITEM_INVENTORY_ID],			  
		ISNULL(FII.[ITEM_INVENTORY_CHILD_QTY_UNALLOCATED], 1),
		FII.[ITEM_ID],
		FII.[CONDITION_ID],
		case when iikh.ItemInventoryId is null then 0 else m.[SORT_ORDER] end as SORT_ORDER	-- not kits inventory will be at first 
	FROM [dbo].[vw_F_ITEM_INVENTORY_AVAILABLE_FOR_SALE]		AS FII WITH (NOLOCK)
	INNER JOIN #MODELS								AS M WITH (NOLOCK)
		ON FII.[ITEM_MASTER_ID] = M.[ID] AND FII.[IS_VIRTUAL] = 0
	left join [dbo].[F_ItemInventoryKit] iik with (nolock)
		on fii.ITEM_INVENTORY_ID = iik.ItemInventoryId
	left join [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)
		on fii.ITEM_INVENTORY_ID = iikh.ItemInventoryId
	WHERE iik.ItemInventoryId is null and
		((@CUSTOMER_ID     IS NULL OR FII.[CUSTOMER_ID] = @CUSTOMER_ID) AND
		(@LOCATION_ID     IS NULL OR FII.[LOCATION_ID] = @LOCATION_ID) AND
		(@PRODUCT_CODE_ID IS NULL OR FII.[PRODUCT_CODE_ID_HECI] = @PRODUCT_CODE_ID) AND
		(@REVISION_ID     IS NULL OR FII.[ITEM_INVENTORY_REVISION_ID] = @REVISION_ID) AND
		((@SKU IS NULL OR @USE_SUBS = 1) AND FII.[CONDITION_ID] = @CONDITION_ID		-- SKU not set or can use substitutes - take from any SKU
			OR fii.[ITEM_ID] IN (SELECT [ITEM_ID] FROM #SKUS)))						-- SKU set - take from the filled ones
	order by iikh.ItemInventoryId

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT '#T_ITEM_INVENTORY_AVAILABLE' as WHAT, *
		FROM #T_ITEM_INVENTORY_AVAILABLE;
	END

		
	-- Collect items to allocate		
	DECLARE @quantityToAllocate BIGINT = @QTY_ORDERED;
	DECLARE @inventoryId BIGINT, @quantity BIGINT;
	
	DECLARE InventoryCursor CURSOR LOCAL FORWARD_ONLY FOR
	SELECT [ITEM_INVENTORY_ID], [QTY_UNALLOCATED]
	FROM #T_ITEM_INVENTORY_AVAILABLE
	ORDER BY
		-- If SKU specified - than first of all take inventory from that SKU, after it we can take invetory items from any other SKU if could @USE_SUBS
		IIF(@SKU IS NULL AND [CONDITION_ID] = @CONDITION_ID OR [ITEM_ID] IN (SELECT [ITEM_ID] FROM #SKUS), 0, 1),
		SORT_ORDER,
		[QTY_UNALLOCATED],	-- First take the items having QTY = 1
		ITEM_INVENTORY_ID;	-- oldest Inventory first

	OPEN InventoryCursor;
	FETCH NEXT FROM InventoryCursor INTO @inventoryId, @quantity;
	WHILE (@@FETCH_STATUS = 0 AND @quantityToAllocate > 0)
	BEGIN
		INSERT INTO @inventoryToAllocate
		VALUES (
			@inventoryId,
			IIF(@quantity < @quantityToAllocate, @quantity, @quantityToAllocate)
		);

		SET @quantityToAllocate = @quantityToAllocate - @quantity;
		FETCH NEXT FROM InventoryCursor INTO @inventoryId, @quantity;
	END;
	CLOSE InventoryCursor;
	DEALLOCATE InventoryCursor;

	IF (@C_IS_DEBUG = 1)
	BEGIN
		SELECT '@inventoryToAllocate' as WHAT, *
		FROM @inventoryToAllocate;
	END
				     
	    
	-- SELECT the count here to suppress the results of inner selects of sp_SET_SALES_ORDER_ITEMS_ALLOCATED
		DECLARE @qtyAllocated BIGINT = ISNULL((
			SELECT SUM([VALUE])
			FROM @inventoryToAllocate
		), 0);

	SELECT @qtyAllocated AS QTY_ALLOCATED;


	IF (@C_IS_DEBUG != 1)
	BEGIN		
		EXEC [dbo].[sp_SET_SALES_ORDER_ITEMS_ALLOCATED]
			@SALES_ORDER_ITEM_ID	= @SALES_ORDER_ITEM_ID
			,@ITEMS_DATA			= @inventoryToAllocate
			,@USER_ID				= @USER_ID
			,@USER_IP				= @USER_IP;
	END

	DROP TABLE #T_ITEM_INVENTORY_AVAILABLE;
	DROP TABLE #MODELS;
	DROP TABLE #SKUS;
	
	IF (@C_USE_INTERNAL_TRANSACTION = 1)
	BEGIN		
		COMMIT TRAN AllocateInventoryAuto;
	END

END

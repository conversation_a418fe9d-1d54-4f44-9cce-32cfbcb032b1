CREATE TABLE [dbo].[D_ELEMENT_CATEGORY] (
    [ELEMENT_CATEGORY_ID]   INT           IDENTITY (1, 1) NOT NULL,
    [ELEMENT_CATEGORY_CD]   VARCHAR (50)  NOT NULL,
    [ELEMENT_CATEGORY_DESC] VARCHAR (250) NOT NULL,
    [IS_INACTIVE]           BIT           CONSTRAINT [DF_D_ELEMENT_CATEGORY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]            BIT           CONSTRAINT [DF_D_ELEMENT_CATEGORY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]           VARCHAR (150) NOT NULL,
    [INSERTED_DT]           DATETIME      NOT NULL,
    [UPDATED_BY]            VARCHAR (150) NULL,
    [UPDATED_DT]            DATETIME      NULL,
    [DELETED_BY]            VARCHAR (150) NULL,
    [DELETED_DT]            DATETIME      NULL,
    CONSTRAINT [PK_D_ELEMENT_CATEGORY] PRIMARY KEY CLUSTERED ([ELEMENT_CATEGORY_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ELEMENT_CATEGORY';


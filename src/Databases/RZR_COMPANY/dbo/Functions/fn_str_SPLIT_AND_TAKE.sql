-- =============================================
-- Author:	O.Evseev, stackoverflow
-- Create date: 07 aug 2017
-- Description:	Splits the provided string by @C_SEPARATOR entries and takes the @C_PART_IDX_0_BASED-st part
-- created in relation with RSW-8632
-- =============================================
CREATE FUNCTION dbo.fn_str_SPLIT_AND_TAKE (
	 @C_STR					NVARCHAR(MAX) 
	,@C_SEPARATOR			NVARCHAR(MAX)
	,@C_PART_IDX_0_BASED	INT
)
RETURNS NVARCHAR(MAX)
AS
BEGIN

	IF (@C_STR IS NULL OR @C_SEPARATOR IS NULL)
		RETURN NULL;

	DECLARE
		@STR_PART			NVARCHAR(MAX),
		@PART_CHAR_IDX		INT = 0,
		@PART_IDX			INT = 0

	SET @C_SEPARATOR = '%'+ @C_SEPARATOR + '%'

	SET @PART_CHAR_IDX = PATINDEX(@C_SEPARATOR, @C_STR);
	WHILE @PART_CHAR_IDX > 0
	BEGIN

		SET @STR_PART =  SUBSTRING(@C_STR, 1, @PART_CHAR_IDX - 1)						-- take the current part from left
		IF (@PART_IDX = @C_PART_IDX_0_BASED)
			RETURN @STR_PART;

		SET @C_STR = SUBSTRING(@C_STR, @PART_CHAR_IDX+1, LEN(@C_STR)-@PART_CHAR_IDX)	-- remove @STR_PART from the string
		SET @PART_CHAR_IDX = PATINDEX(@C_SEPARATOR, @C_STR);							-- next index of @C_SEPARATOR
			
		SET @PART_IDX = @PART_IDX + 1
	END

	IF (@PART_IDX = @C_PART_IDX_0_BASED)
		RETURN @C_STR

	RETURN NULL;
END
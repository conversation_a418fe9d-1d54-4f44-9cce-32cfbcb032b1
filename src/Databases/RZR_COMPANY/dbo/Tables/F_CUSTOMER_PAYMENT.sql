CREATE TABLE [dbo].[F_CUSTOMER_PAYMENT] (
    [CUSTOMER_PAYMENT_ID]		BIGINT			IDENTITY (1, 1) NOT NULL,
    [TYPE_BY_ACCOUNT_TYPE_ID]	INT				CONSTRAINT [DF_F_CUSTOMER_PAYMENT_TYPE_BY_ACCOUNT_TYPE_ID] DEFAULT (NULL) NULL,
    [CUSTOMER_ID]				BIGINT			CONSTRAINT [DF_F_CUSTOMER_PAYMENT_CUSTOMER_ID] DEFAULT (NULL) NOT NULL,
    [AMOUNT]					MONEY			NOT NULL,
    [DISCOUNT]					MONEY			NULL,
    [PAYMENT_TYPE_ID]			INT				NOT NULL,
    [CHECK_NUMBER]				NVARCHAR (128)	NULL,
    [REFERENCE_NUMBER]			NVARCHAR (128)	NULL,
    [PAYMENT_DATE]				DATETIME		NOT NULL,
    [ACCOUNT_ID]				BIGINT			NULL,
    [IS_DELETED]				BIT				CONSTRAINT [DF_F_INVOICE_PAYMENT_IS_VOIDED] DEFAULT ((0)) NOT NULL,
	[DELETING_REFERENCE_NUMBER] NVARCHAR (128)	NULL,
    [INSERTED_BY_ID]			BIGINT			NULL,
    [INSERTED_BY_IP]			BIGINT			NULL,
    [UPDATED_BY_ID]				BIGINT			NULL,
    [UPDATED_BY_IP]				BIGINT			NULL,
    [DELETED_BY_ID]				BIGINT			NULL,
    [DELETED_BY_IP]				BIGINT			NULL,
    [INSERTED_BY]				VARCHAR (150)	NOT NULL,
    [INSERTED_DT]				DATETIME		NOT NULL,
    [UPDATED_BY]				VARCHAR (150)	NULL,
    [UPDATED_DT]				DATETIME		NULL,
    [DELETED_BY]				VARCHAR (150)	NULL,
    [DELETED_DT]				DATETIME		NULL,
    [SELF_ID]					AS				([CUSTOMER_PAYMENT_ID]),
    [SELF_NAME]					AS				(CONVERT([nvarchar](20),[CUSTOMER_PAYMENT_ID],(0))),
    [SELF_CREATE_DATE]			AS				(isnull([PAYMENT_DATE],'2000-01-01')),
    [PARENT_ID]					AS				(CONVERT([bigint],isnull(NULL,(-1)),(0))),
	[CurrencyExchangeId]		BIGINT			NULL,
    CONSTRAINT [PK_F_CUSTOMER_PAYMENT] PRIMARY KEY CLUSTERED ([CUSTOMER_PAYMENT_ID] ASC),
	CONSTRAINT [FK_F_CUSTOMER_PAYMENT_CurrencyExchange] FOREIGN KEY ([CurrencyExchangeId]) REFERENCES [dbo].[D_CurrencyExchange] ([Id]),
    CONSTRAINT [CK__F_CUSTOMER_PAYMENT__TYPE_BY_ACCOUNT_TYPE_ID] CHECK ([TYPE_BY_ACCOUNT_TYPE_ID]=(5) OR [TYPE_BY_ACCOUNT_TYPE_ID]=(2)),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_C_INVOICE_PAYMENT_TYPE] FOREIGN KEY ([PAYMENT_TYPE_ID]) REFERENCES [dbo].[C_INVOICE_PAYMENT_TYPE] ([INVOICE_PAYMENT_TYPE_ID]),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_D_ACCOUNT_TYPE] FOREIGN KEY ([TYPE_BY_ACCOUNT_TYPE_ID]) REFERENCES [dbo].[D_ACCOUNT_TYPE] ([ACCOUNT_TYPE_ID]),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_F_ACCOUNT] FOREIGN KEY ([ACCOUNT_ID]) REFERENCES [dbo].[F_ACCOUNT] ([ACCOUNT_ID]) ON UPDATE CASCADE,
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_tb_User__DELETED_BY_ID] FOREIGN KEY ([DELETED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_tb_User__INSERTED_BY_ID] FOREIGN KEY ([INSERTED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_CUSTOMER_PAYMENT_tb_User__UPDATED_BY_ID] FOREIGN KEY ([UPDATED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);


GO
CREATE NONCLUSTERED INDEX [IX_F_CUSTOMER_PAYMENT__TYPE_BY_ACCOUNT_TYPE_ID__INCLUDE__IDS__AMOUNT__REFERENCE]
ON [dbo].[F_CUSTOMER_PAYMENT] ([TYPE_BY_ACCOUNT_TYPE_ID])
INCLUDE ([CUSTOMER_PAYMENT_ID],[CUSTOMER_ID],[AMOUNT],[DISCOUNT],[REFERENCE_NUMBER])

GO
CREATE NONCLUSTERED INDEX [IX__F_CUSTOMER_PAYMENT__AUTO_TXN_NUMBER]
	ON [dbo].[F_CUSTOMER_PAYMENT] ([TYPE_BY_ACCOUNT_TYPE_ID],[ACCOUNT_ID],[IS_DELETED])
	INCLUDE ([CHECK_NUMBER],[REFERENCE_NUMBER])
GO

GO
CREATE TRIGGER [dbo].[tgr_CUSTOMER_PAYMENT_LOG] ON [dbo].[F_CUSTOMER_PAYMENT]
	AFTER INSERT, UPDATE
AS
BEGIN
	--LOG ABOUT CREATE PAYMENT
	INSERT INTO F_LOG_DATA(	
	   [SOURCE],   
	   [USER_ID],
	   USER_IP,
	   OPERATION_NAME,
	   ENTITY_TYPE_ID,
	   ENTITY_KEY_VALUE,
	   ENTITY_AUTO_NAME,	   
	   [CHANGES]	   
    )
	SELECT 
		'tgr_CUSTOMER_PAYMENT_LOG' as [SOURCE]
		,[DATA].[USER_ID]
		,[DATA].USER_IP
		,IIF([DATA].IS_DELETED = 1, [DATA].DELETED_ACTION, [Data].[Action]) as OPERATION_NAME
		,14 as ENTITY_TYPE_ID /*Payment*/
		, [DATA].ENTITY_KEY_VALUE
		, [DATA].ENTITY_AUTO_NAME
		,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT
		  IIF([DATA].IS_DELETED = 1, [DATA].DELETED_ACTION, [Data].[Action]) as [Action],
		  [DATA].[AMOUNT],
		  [DATA].[DISCOUNT],
		  [DATA].[PAYMENT_DATE],
		  [DATA].PAYMENT_TYPE,
		  [DATA].[DELETED_ACTION]
	   FOR XML
	   PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
	FROM 
	(SELECT 
		IIF(I.IS_DELETED = 1, I.DELETED_BY_ID, ISNULL(I.UPDATED_BY_ID, i.INSERTED_BY_ID))		AS [USER_ID],
		IIF(I.IS_DELETED = 1, I.DELETED_BY_IP, ISNULL(I.UPDATED_BY_IP, I.[INSERTED_BY_IP]))		AS USER_IP,
		I.[CUSTOMER_PAYMENT_ID]	AS ENTITY_KEY_VALUE,
		I.[REFERENCE_NUMBER]	AS ENTITY_AUTO_NAME,
		-- XML columns
		'Payment ' + ISNULL(LTRIM(I.[REFERENCE_NUMBER]), '') + IIF(u.[CUSTOMER_PAYMENT_ID] IS NULL,
			' is create at ' + CAST(I.[INSERTED_DT] as nvarchar(20)), 
			' was ' + IIF(I.IS_DELETED = 1, 'deleted at ' + CAST(ISNULL(I.DELETED_DT, getutcdate()) as nvarchar(20)),
			'changed at '  + CAST(ISNULL(I.[UPDATED_DT], getutcdate()) as nvarchar(20)))) as [Action],
		'On amount ' + CAST(i.[AMOUNT] as nvarchar(50)) [AMOUNT],
		'On discount ' + CAST(ISNULL(i.DISCOUNT,0) as nvarchar(50)) as [DISCOUNT],
 		'Payment date: ' + CAST(i.[PAYMENT_DATE] as nvarchar(20)) as  [PAYMENT_DATE],
		'Payment type: ' + pt.INVOICE_PAYMENT_TYPE_NAME as PAYMENT_TYPE,
		i.IS_DELETED,
		IIF(i.IS_DELETED = 1, 'Payment was deleted at '+ CAST(I.[DELETED_DT] as nvarchar(20)) + ' by ' + CAST(I.[DELETED_BY_ID] as nvarchar(20)), null) as DELETED_ACTION 
		FROM inserted						I 
		LEFT JOIN deleted					u
			ON u.[CUSTOMER_PAYMENT_ID] = i.[CUSTOMER_PAYMENT_ID] 
		INNER JOIN [C_INVOICE_PAYMENT_TYPE] pt WITH(NOLOCK)
			ON pt.INVOICE_PAYMENT_TYPE_ID = i.[PAYMENT_TYPE_ID]

	) [DATA]
		
END







GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The payment information. Which customer did which payment type for which amount and is the discount was used or not (DISCOUNT)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CUSTOMER_PAYMENT';


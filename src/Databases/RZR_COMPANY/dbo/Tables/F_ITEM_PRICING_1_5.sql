CREATE TABLE [dbo].[F_ITEM_PRICING_1_5] (
    [ITEM_PRICING_ID]               BIGINT          NOT NULL,
    [ITEM_ID]                       BIGINT          NOT NULL,
    [ITEM_NUMBER]                   VARCHAR (50)    NOT NULL,
    [SOURCE_SYS_ID]                 BIGINT          NOT NULL,
    [ITEM_LIST_COMMON_CATEGORY]     VARCHAR (150)   NULL,
    [SALES_PRICE_AVG]               DECIMAL (19, 2) NULL,
    [SALES_SELL_THRU_RATE]          DECIMAL (19, 2) NULL,
    [SALES_TOTAL]                   DECIMAL (19, 2) NULL,
    [SALES_START_PRICE]             DECIMAL (19, 2) NULL,
    [SALES_SHIPPING_COST_AVG]       DECIMAL (19, 2) NULL,
    [QTY_SOLD]                      DECIMAL (19, 2) NULL,
    [QTY_LISTED]                    DECIMAL (19, 2) NULL,
    [QTY_BID]                       DECIMAL (19, 2) NULL,
    [QTY_BID_AVG]                   DECIMAL (19, 2) NULL,
    [PRICING_HIST_DAYS_WINDOW]      DECIMAL (19, 2) NOT NULL,
    [IS_INACTIVE]                   BIT             NOT NULL,
    [IS_DELETED]                    BIT             NOT NULL,
    [INSERTED_BY]                   VARCHAR (150)   NOT NULL,
    [INSERTED_DT]                   DATETIME        NOT NULL,
    [UPDATED_BY]                    VARCHAR (150)   NULL,
    [UPDATED_DT]                    DATETIME        NULL,
    [DELETED_BY]                    VARCHAR (150)   NULL,
    [DELETED_DT]                    DATETIME        NULL,
    [ITEM_LIST_CATEGORY_OPTIONS_ID] BIGINT          NULL
);


CREATE TABLE [dbo].[F_PermissionEntityToSystemSettings] (
    [Id]              BIGINT         NOT NULL,
    [RelativeURL]     NVARCHAR (256) NOT NULL,
    [ItemMenuCaption] NVARCHAR (128) NULL,
    [ItemType]        NVARCHAR (128) NULL,
    [ItemIsHidden]    BIT            CONSTRAINT [DF_F_PermissionEntityToSystemSettings_ItemIsHidden] DEFAULT ((0)) NOT NULL,
    [Icon]            NVARCHAR (128) NULL,
	[IsSystem]        BIT            CONSTRAINT [DF_F_PermissionEntityToSystemSettings_IsSystem]     DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK__F_PermissionEntityToSystemSettings] PRIMARY KEY CLUSTERED ([Id] ASC)
);
GO
CREATE NONCLUSTERED INDEX [IX__PermissionEntityToSystemSettings__Id__ItemIsHidden__IsSystem]
    ON [dbo].[F_PermissionEntityToSystemSettings](Id ASC, ItemIsHidden ASC, IsSystem ASC);
GO
CREATE NONCLUSTERED INDEX [IX__PermissionEntityToSystemSettings__ItemIsHidden__IsSystem]
    ON [dbo].[F_PermissionEntityToSystemSettings](ItemIsHidden ASC, IsSystem ASC);
GO

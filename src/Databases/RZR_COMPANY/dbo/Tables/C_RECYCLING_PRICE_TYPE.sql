CREATE TABLE [dbo].[C_RECYCLING_PRICE_TYPE] (
    [PRICE_TYPE_ID]   INT            NOT NULL,
    [PRICE_TYPE_DESC] NVARCHAR (256) NOT NULL,
    CONSTRAINT [PK_C_RECYCLING_PRICE_TYPE] PRIMARY KEY CLUSTERED ([PRICE_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The price type is used in many calculations. Depends on price type the calculation of total amount is different', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_PRICE_TYPE';


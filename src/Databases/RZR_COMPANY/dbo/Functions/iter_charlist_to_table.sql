--SELECT * FROM iter_charlist_to_table('''SOCIAL_USER_CONNECTION'', ''DEFAULT'', ''N'', ''N'', ''2014-07-15'', ''2014-07-30'', 0, 0, 3, 0, 0, 346599458, 0', ',')
CREATE FUNCTION [dbo].[iter_charlist_to_table]
                    (@list      nvarchar(max),
                     @delimiter nchar(1) = N',')
	returns table
AS
return (
with tokens(p, a, b) AS (
    select 
        cast(1 as bigint), 
        cast(1 as bigint), 
        charindex(@delimiter, @list)
    union all
    select
        p + 1, 
        b + 1, 
        charindex(@delimiter, @list, b + 1)
    from tokens
    where b > 0
)
select
    p as listpos,
    ltrim(rtrim(substring(
        @list, 
        a, 
        case when b > 0 then b-a ELSE LEN(@list) end)))
    AS nstr
from tokens
);
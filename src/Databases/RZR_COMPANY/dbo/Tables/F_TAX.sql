CREATE TABLE [dbo].[F_TAX] (
    [TAX_ID]           BIGINT        IDENTITY (1, 1) NOT NULL,
    [TAX_NAME]         NVARCHAR (150) NOT NULL,
    [TAX_DESCRIPTION]  NVARCHAR (350) NULL,
    [TAX_RATE]         FLOAT (53)    NULL,
    [TAX_AGENCY]       BIGINT        NULL,
    [IS_GROUP]         BIT           CONSTRAINT [DF_F_TAX_IS_GROUP] DEFAULT ((0)) NULL,
    [IS_INACTIVE]      BIT           CONSTRAINT [DF_F_TAX_IS_INACTIVE1] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]       BIT           CONSTRAINT [DF_F_TAX_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]      VARCHAR (150) CONSTRAINT [DF_F_TAX_INSERTED_BY] DEFAULT (isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid)) NOT NULL,
    [INSERTED_DT]      DATETIME      CONSTRAINT [DF_F_TAX_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]       VARCHAR (150) NULL,
    [UPDATED_DT]       DATETIME      NULL,
    [DELETED_BY]       VARCHAR (150) NULL,
    [DELETED_DT]       DATETIME      NULL,
    [SELF_ID]          AS            ([TAX_ID]),
    [SELF_NAME]        AS            ([TAX_NAME]),
    [SELF_CREATE_DATE] AS            (isnull([INSERTED_DT],'2000-01-01')),
    [PARENT_ID]        AS            (CONVERT([bigint],isnull(NULL,(-1)),0)),
    [IsEditable]       bit           constraint [DF__F_TAX__IsEditable] default ((1)) not null,
    CONSTRAINT [PK_F_TAX] PRIMARY KEY CLUSTERED ([TAX_ID] ASC),
    CONSTRAINT [FK_F_CUSTOMER_F_TAX] FOREIGN KEY ([TAX_AGENCY]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID])
);






CREATE TABLE [dbo].[F_INVENTORY_AUDIT_SESSION] (
    [SESSION_ID]  BIGINT        IDENTITY (1, 1) NOT NULL,
    [LOCATION_ID] BIGINT        NULL,
    [USER_ID]     BIGINT        NOT NULL,
    [IS_COMPLETE] BIT           CONSTRAINT [DF_F_ITEM_AUDIT_SESSION_IS_COMPLETE] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE] BIT           CONSTRAINT [DF_F_ITEM_AUDIT_SESSION_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]  BIT           CONSTRAINT [DF_F_ITEM_AUDIT_SESSION_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY] VARCHAR (150) CONSTRAINT [DF_F_ITEM_AUDIT_SESSION_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT] DATETIME      CONSTRAINT [DF_F_ITEM_AUDIT_SESSION_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]  VARCHAR (150) NULL,
    [UPDATED_DT]  DATETIME      NULL,
    [DELETED_BY]  VARCHAR (150) NULL,
    [DELETED_DT]  DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_AUDIT_SESSION] PRIMARY KEY CLUSTERED ([SESSION_ID] ASC),
    CONSTRAINT [FK_F_ITEM_AUDIT_SESSION_D_INVENTORY_CAPABILITY] FOREIGN KEY ([LOCATION_ID]) REFERENCES [dbo].[F_LOCATION] ([LOCATION_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_AUDIT_SESSION_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]) ON UPDATE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The location audit page. Stored details of audit', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_INVENTORY_AUDIT_SESSION';


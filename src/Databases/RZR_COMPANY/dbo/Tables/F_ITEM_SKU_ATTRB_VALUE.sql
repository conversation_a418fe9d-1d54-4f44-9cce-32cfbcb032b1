CREATE TABLE [dbo].[F_ITEM_SKU_ATTRB_VALUE] (
    [ITEM_SKU_ATTRB_VALUE_ID]      BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]               BIGINT        NOT NULL,
    [ITEM_MASTER_SKU_ATTRB_ID]     BIGINT        NOT NULL,
    [ITEM_ID]                      BIGINT        NOT NULL,
    [INVENTORY_CAPABILITY_TYPE_ID] BIGINT        NOT NULL,
    [INVENTORY_CAPABILITY_ID]      BIGINT        NOT NULL,
    [INVENTORY_CAPABILITY_VALUE]   VARCHAR (1000) NOT NULL,
    [IS_INACTIVE]                  BIT           CONSTRAINT [DF_F_ITEM_SKU_ATTRB_VALUE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                   BIT           CONSTRAINT [DF_F_ITEM_SKU_ATTRB_VALUE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                  VARCHAR (150) CONSTRAINT [DF_F_ITEM_SKU_ATTRB_VALUE_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                  DATETIME      CONSTRAINT [DF_F_ITEM_SKU_ATTRB_VALUE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                   VARCHAR (150) NULL,
    [UPDATED_DT]                   DATETIME      NULL,
    [DELETED_BY]                   VARCHAR (150) NULL,
    [DELETED_DT]                   DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_SKU_ATTRB_VALUE] PRIMARY KEY CLUSTERED ([ITEM_SKU_ATTRB_VALUE_ID] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX__dbo_F_ITEM_SKU_ATTRB_VALUE__ITEM_MASTER_SKU_ATTRB_ID__INVENTORY_CAPABILITY_TYPE_ID__INCLUDE]
  ON [dbo].[F_ITEM_SKU_ATTRB_VALUE] (
    [ITEM_MASTER_SKU_ATTRB_ID], [INVENTORY_CAPABILITY_TYPE_ID]
  )
  INCLUDE (
    [ITEM_MASTER_ID], [INVENTORY_CAPABILITY_VALUE]
  )
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_SKU_ATTRB_VALUE_INVENTORY_CAPABILITY_TYPE_ID]
    ON [dbo].[F_ITEM_SKU_ATTRB_VALUE] ([INVENTORY_CAPABILITY_TYPE_ID])
INCLUDE ([ITEM_ID],[INVENTORY_CAPABILITY_VALUE])


GO
CREATE NONCLUSTERED INDEX [IX_ITEM_ID_CAPABILITY_TYPE_ID]
    ON [dbo].[F_ITEM_SKU_ATTRB_VALUE]([ITEM_ID] ASC, [INVENTORY_CAPABILITY_TYPE_ID] ASC)
    INCLUDE([INVENTORY_CAPABILITY_VALUE]);


GO
CREATE NONCLUSTERED INDEX [IX__SKU_CAPABILITY_VALUE]
    ON [dbo].[F_ITEM_SKU_ATTRB_VALUE] ([INVENTORY_CAPABILITY_TYPE_ID])
    INCLUDE ([ITEM_MASTER_ID],[ITEM_MASTER_SKU_ATTRB_ID],[INVENTORY_CAPABILITY_VALUE])
GO

CREATE NONCLUSTERED INDEX IX__ITEM_MASTER_ID_ITEM_MASTER_SKU_ATTRB_ID_ITEM_ID_INVENTORY_CAPABILITY_TYPE_ID_INVENTORY_CAPABILITY_ID
ON [dbo].[F_ITEM_SKU_ATTRB_VALUE]
(
	[ITEM_MASTER_ID] ASC,
	[ITEM_MASTER_SKU_ATTRB_ID] ASC,
	[ITEM_ID] ASC,
	[INVENTORY_CAPABILITY_TYPE_ID] ASC,
	[INVENTORY_CAPABILITY_ID] ASC
)
INCLUDE (ITEM_SKU_ATTRB_VALUE_ID,
	[INVENTORY_CAPABILITY_VALUE],
	[IS_INACTIVE],
	[IS_DELETED],
	[INSERTED_BY],
	[INSERTED_DT],

	[UPDATED_BY],
	[UPDATED_DT],
	[DELETED_BY],
	[DELETED_DT])

GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The item (SKU) edit page has capabilities which are tied to SKU, those capabilities’ values can be changed.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_SKU_ATTRB_VALUE';


CREATE TABLE [dbo].[C_USER_SETTING_TYPE] (
    [USER_SETTING_TYPE_ID]   INT           NOT NULL,
    [USER_SETTING_TYPE_CD]   VARCHAR (50)  NOT NULL,
    [USER_SETTING_TYPE_DESC] VARCHAR (100) NULL,
    [IS_INACTIVE]            BIT           CONSTRAINT [DF_tb_USER_SETTING_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]             BIT           CONSTRAINT [DF_tb_USER_SETTING_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]            VARCHAR (150) NOT NULL,
    [INSERTED_DT]            DATETIME      NOT NULL,
    [UPDATED_BY]             VARCHAR (150) NULL,
    [UPDATED_DT]             DATETIME      NULL,
    [DELETED_BY]             VARCHAR (150) NULL,
    [DELETED_DT]             DATETIME      NULL,
    CONSTRAINT [PK_tb_USER_SETTING_TYPE] PRIMARY KEY CLUSTERED ([USER_SETTING_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems it is not', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_USER_SETTING_TYPE';


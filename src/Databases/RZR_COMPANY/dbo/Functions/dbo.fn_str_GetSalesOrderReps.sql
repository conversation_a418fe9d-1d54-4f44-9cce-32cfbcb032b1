CREATE FUNCTION [dbo].[fn_str_GetSalesOrderReps]
(
	@SalesOrderId		BIGINT,
	@UseUserName		BIT = 0,
	@AddUserName		BIT = 0
)
RETURNS NVARCHAR(1024)
BEGIN

	set @UseUserName = isnull(@UseUserName, 0);
	set @AddUserName = isnull(@AddUserName, 0);

	declare @reps nvarchar(max);

	select @reps = coalesce(@reps + N', ' + r.UserName, r.UserName)
	from (
		select distinct iif(@UseUserName = 1, TU.UserName, TU.FirstName + N' ' + TU.LastName + iif(@AddUserName = 1, N' ('+ TU.UserName + ')', N'')) as UserName
		from [dbo].[F_SalesOrderRepUser]	SOREP with (nolock)
		inner join [dbo].[tb_User]			TU with (nolock)
			on SOREP.RepUserId = TU.UserID
		where SOREP.SalesOrderId = @SalesOrderId
	) r;

	return @reps;

END
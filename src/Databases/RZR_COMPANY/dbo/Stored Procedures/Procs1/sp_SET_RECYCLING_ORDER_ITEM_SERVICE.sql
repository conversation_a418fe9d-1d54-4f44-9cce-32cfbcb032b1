-- =============================================
-- Author:		<O. Evseev>
-- Create date: <02/03/2014>
-- Description:	<creates or updates a service item into the recycling order>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_SERVICE]
	@RECYCLING_ORDER_ID		 BIGINT,
	@RECYCLING_ORDER_ITEM_ID BIGINT,
	@SERVICE_TYPE_ID		 INT,
	@DESC					 VARCHAR(500),
	@QTY					 FLOAT,
	@PRICE_FOR_ONE			 MONEY,
	@PriceTypeId			 INT,
	@NOTES					 NVARCHAR(MAX),
	@COMMENT				 NVARCHAR(MAX) = NULL,
	@USER_ID				 BIGINT,
	@IP						 BIGINT
AS
BEGIN

	DECLARE @spName nvarchar(128)		= isnull(object_schema_name(@@procid) + N'.', N'') + object_name(@@procid);
	DECLARE @utcNow datetime			= getutcdate();
    DECLARE @PricingType INT;
    DECLARE @SinglePrice MONEY;
    DECLARE @CurrentPrice MONEY;
    DECLARE @MatrixPrice MONEY;
    DECLARE @CurrentQty FLOAT;
    DECLARE @Distance FLOAT;

	IF (EXISTS(SELECT TOP 1 1 FROM F_RECYCLING_ORDER_ITEM_SERVICE WITH (NOLOCK) 
		WHERE RECYCLING_ORDER_ITEM_SERVICE_ID = @RECYCLING_ORDER_ITEM_ID))
	BEGIN

		SET @SERVICE_TYPE_ID = (SELECT TOP(1) ITEM_SERVICE_TYPE_ID FROM F_RECYCLING_ORDER_ITEM_SERVICE WHERE RECYCLING_ORDER_ITEM_SERVICE_ID = @RECYCLING_ORDER_ITEM_ID)

        SELECT TOP 1
            @PricingType = PricingType,
            @SinglePrice = SinglePrice
        FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] WITH (NOLOCK)
        WHERE SERVICE_TYPE_ID = @SERVICE_TYPE_ID;
        
        SELECT TOP 1 
            @Distance = ISNULL(Distance, 0)
        FROM [dbo].[F_RECYCLING_ORDER_INBOUND] WITH (NOLOCK)
        WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID;
        
        SELECT TOP 1
            @CurrentQty = ITEM_SERVICE_COUNT,
            @CurrentPrice = ITEM_SERVICE_PRICE_FOR_ONE
        FROM [dbo].[F_RECYCLING_ORDER_ITEM_SERVICE] WITH (NOLOCK)
        WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
          AND ITEM_SERVICE_TYPE_ID = @SERVICE_TYPE_ID;
        
        IF ISNULL(@QTY, 0) <> ISNULL(@CurrentQty, 0)
        BEGIN
            IF @PricingType = 1
            BEGIN
                SET @PRICE_FOR_ONE = ISNULL(-1 * @SinglePrice, @CurrentPrice);
            END
            
            ELSE IF @PricingType = 2
            BEGIN
                SELECT @MatrixPrice = [dbo].[fn_money_GET_RECYCLING_SERVICE_PRICE_MATRIX_VALUE](@SERVICE_TYPE_ID, @Distance, @QTY);
                SET @PRICE_FOR_ONE = ISNULL(@MatrixPrice, @CurrentPrice);
            END
        END 

		UPDATE F_RECYCLING_ORDER_ITEM_SERVICE SET
			[ITEM_SERVICE_COUNT]			  = @QTY,
			[ITEM_SERVICE_PRICE_FOR_ONE]	  = @PRICE_FOR_ONE,
			[NOTES]							  = RTRIM(LTRIM(@NOTES)),
			[PRICE_CHANGE_COMMENT]			  = @COMMENT,
			[ITEM_SERVICE_DESCRIPTION]		  = RTRIM(LTRIM(@DESC)),
			[PriceTypeId]					  = ISNULL(@PriceTypeId, 2),
			[UPDATED_BY]					  = N'sp_SET_RECYCLING_ORDER_ITEM_SERVICE',
			[UPDATED_DT]					  = GETUTCDATE()
		WHERE RECYCLING_ORDER_ITEM_SERVICE_ID = @RECYCLING_ORDER_ITEM_ID

	END ELSE
	BEGIN

		DECLARE 
			@SAVED_RECYCLING_ORDER_ITEM_ID BIGINT
			
		SELECT 
			@SAVED_RECYCLING_ORDER_ITEM_ID	= RECYCLING_ORDER_ITEM_SERVICE_ID
		FROM F_RECYCLING_ORDER_ITEM_SERVICE WITH (NOLOCK) 
		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID 
			AND	 ITEM_SERVICE_TYPE_ID = @SERVICE_TYPE_ID 
			AND ITEM_SERVICE_PRICE_FOR_ONE = @PRICE_FOR_ONE 
			AND NOTES = RTRIM(LTRIM(@NOTES))
		
		IF (@SAVED_RECYCLING_ORDER_ITEM_ID IS NULL) BEGIN


            SELECT
                @PricingType = PricingType,
                @SinglePrice = SinglePrice
            FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] WITH (NOLOCK)
            WHERE SERVICE_TYPE_ID = @SERVICE_TYPE_ID;
        
            SELECT @Distance = ISNULL(Distance, 0)
            FROM [dbo].[F_RECYCLING_ORDER_INBOUND] WITH (NOLOCK)
            WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID;
        
        
            IF ISNULL(@PRICE_FOR_ONE, 0) = 0
            BEGIN
                IF @PricingType = 1
                BEGIN
                    SET @PRICE_FOR_ONE = ISNULL(-1 * @SinglePrice, @PRICE_FOR_ONE);
                END
            
                ELSE IF @PricingType = 2
                BEGIN
                    SELECT @MatrixPrice = [dbo].[fn_money_GET_RECYCLING_SERVICE_PRICE_MATRIX_VALUE](@SERVICE_TYPE_ID, @Distance, @QTY);
                    SET @PRICE_FOR_ONE = ISNULL(@MatrixPrice, @CurrentPrice);
                END
            END 


			INSERT INTO F_RECYCLING_ORDER_ITEM_SERVICE (
				[RECYCLING_ORDER_ID],
				[ITEM_SERVICE_TYPE_ID],
				[ITEM_SERVICE_COUNT],
				[ITEM_SERVICE_PRICE_FOR_ONE],
				[PriceTypeId],
				[NOTES],
				[PRICE_CHANGE_COMMENT],
				[INSERTED_BY],
				[INSERTED_DT]
			)VALUES(
				@RECYCLING_ORDER_ID,
				@SERVICE_TYPE_ID,
				@QTY,
				@PRICE_FOR_ONE,
				ISNULL(@PriceTypeId, 2),
				@NOTES,
				@COMMENT,
				N'sp_SET_RECYCLING_ORDER_ITEM_SERVICE',
				GETUTCDATE()
			)


            IF (select top(1) ApplyType from [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] where SERVICE_TYPE_ID = @SERVICE_TYPE_ID) = 6
            BEGIN
				-- Insert Qty for Onsite Service by Category to InboundOrder Service
                delete from [dbo].[F_InboundOrderServiceCategories] where RecyclingOrderId = @RECYCLING_ORDER_ID and ServiceTypeId = @SERVICE_TYPE_ID
                insert into [dbo].[F_InboundOrderServiceCategories] with(rowlock) (
                    RecyclingOrderId,
                    ServiceTypeId,
                    CategoryId,
                    ActualQty,
                    InsertedBy,
                    InsertedDate,
                    InsertedByUserId,
                    InsertedByUserIp)
                select
                    @RECYCLING_ORDER_ID,
                    @SERVICE_TYPE_ID,
                    RSTC.CategoryId,
                    0,
                    @spName,
                    @utcNow,
                    @USER_ID,
                    @IP
                from [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE]				RIS with(nolock)
                inner join [dbo].[D_RecyclingServiceTypeCategory]        RSTC with(nolock)
                    on RIS.SERVICE_TYPE_ID = RSTC.ServiceTypeId
                where RIS.ApplyType = 6 and RIS.SERVICE_TYPE_ID = @SERVICE_TYPE_ID -- OnsiteType

                -- Insert Qty for Onsite Service by Label to InboundOrder Service
                delete from [dbo].[F_InboundOrderServiceLabels] where RecyclingOrderId = @RECYCLING_ORDER_ID and ServiceTypeId = @SERVICE_TYPE_ID
                insert into [dbo].[F_InboundOrderServiceLabels] with (rowlock) (
                    RecyclingOrderId,
                    ServiceTypeId,
                    LabelId,
                    ActualQty,
                    InsertedBy,
                    InsertedDate,
                    InsertedByUserId,
                    InsertedByUserIp)
                select
                    @RECYCLING_ORDER_ID,
                    @SERVICE_TYPE_ID,
                    RSTL.LabelId,
                    0,
                    @spName,
                    @utcNow,
                    @USER_ID,
                    @IP         
                from [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE]    RIS with(nolock)
                inner join [dbo].[D_RecyclingServiceTypeLabel]        RSTL with(nolock)
                    on RIS.SERVICE_TYPE_ID = RSTL.ServiceTypeId
                where RIS.ApplyType = 6 and RIS.SERVICE_TYPE_ID = @SERVICE_TYPE_ID -- OnsiteType

                INSERT INTO [dbo].[F_InboundOrderOnsiteCategories] WITH (ROWLOCK)
                (
                    RecyclingOrderId,
                    OnsiteTypeId,
                    CategoryId,
                    FinalScannedQty,
                    ManualQty,
                    InsertedBy,
                    InsertedDate,
                    InsertedByUserId,
                    InsertedByUserIp
                )
                SELECT DISTINCT
                    @RECYCLING_ORDER_ID,
                    RIS.OnsiteTypeId,
                    RSTC.CategoryId,
                    0,
                    0,
                    @spName,
                    @utcNow,
                    @USER_ID,
                    @IP
                FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] RIS WITH (NOLOCK)
                    INNER JOIN [dbo].[D_RecyclingServiceTypeCategory] RSTC WITH (NOLOCK)
                ON RSTC.ServiceTypeId = RIS.SERVICE_TYPE_ID
                    LEFT JOIN [dbo].[F_InboundOrderOnsiteCategories] EC WITH (NOLOCK) -- existing categories
                ON EC.RecyclingOrderId = @RECYCLING_ORDER_ID
                    AND EC.OnsiteTypeId = RIS.OnsiteTypeId
                    AND EC.CategoryId = RSTC.CategoryId
                WHERE RIS.ApplyType = 6 AND RIS.SERVICE_TYPE_ID = @SERVICE_TYPE_ID
                    AND EC.Id IS NULL

                INSERT INTO [dbo].[F_InboundOrderOnsiteLabels] WITH (ROWLOCK)
                (
                    RecyclingOrderId,
                    OnsiteTypeId,
                    LabelId,
                    FinalScannedQty,
                    ManualQty,
                    InsertedBy,
                    InsertedDate,
                    InsertedByUserId,
                    InsertedByUserIp
                )
                SELECT DISTINCT
                    @RECYCLING_ORDER_ID,
                    RIS.OnsiteTypeId,
                    RSTL.LabelId,
                    0,
                    0,
                    @spName,
                    @utcNow,
                    @USER_ID,
                    @IP
                FROM [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] RIS WITH (NOLOCK)
                    INNER JOIN [dbo].[D_RecyclingServiceTypeLabel] RSTL WITH (NOLOCK)
                ON RSTL.ServiceTypeId = RIS.SERVICE_TYPE_ID
                    LEFT JOIN [dbo].[F_InboundOrderOnsiteLabels] EL WITH (NOLOCK) -- existing categories
                ON EL.RecyclingOrderId = @RECYCLING_ORDER_ID
                    AND EL.OnsiteTypeId = RIS.OnsiteTypeId
                    AND EL.LabelId = RSTL.LabelId
                WHERE RIS.ApplyType = 6 AND RIS.SERVICE_TYPE_ID = @SERVICE_TYPE_ID
                    AND EL.Id IS NULL
            END
		
		END
		ELSE BEGIN
		
			UPDATE F_RECYCLING_ORDER_ITEM_SERVICE SET
				[ITEM_SERVICE_COUNT]	= @QTY + [ITEM_SERVICE_COUNT],
				[PRICE_CHANGE_COMMENT]	= @COMMENT,
				[UPDATED_BY]			= N'sp_SET_RECYCLING_ORDER_ITEM_SERVICE',
				[UPDATED_DT]			= GETUTCDATE()
			WHERE RECYCLING_ORDER_ITEM_SERVICE_ID = @SAVED_RECYCLING_ORDER_ITEM_ID
		
		END

		SET @RECYCLING_ORDER_ITEM_ID = SCOPE_IDENTITY()
	END

	UPDATE dbo.F_RECYCLING_ORDER_INBOUND SET
		AMOUNT_OWED		= (SELECT dbo.fn_money_GET_RECYCLING_AMOUNT_OWED(@RECYCLING_ORDER_ID)),
		UPDATED_BY_USER	= @USER_ID,
		UPDATED_BY_IP	= @IP
	WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID	

	SELECT @RECYCLING_ORDER_ITEM_ID AS SERVICE_ITEM_ID
END
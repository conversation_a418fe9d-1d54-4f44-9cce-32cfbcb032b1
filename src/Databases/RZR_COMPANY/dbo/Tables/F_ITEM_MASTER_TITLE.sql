CREATE TABLE [dbo].[F_ITEM_MASTER_TITLE] (
    [ITEM_MASTER_ID]       BIGINT        NOT NULL,
    [ITEM_TITLE]           NVARCHAR (200) NOT NULL,
    [ITEM_DESC]            NVARCHAR (MAX) NULL,
    [ITEM_QUALIFIED_TITLE] NVARCHAR (150) NULL,
    [IS_INACTIVE]          BIT           CONSTRAINT [DF_F_ITEM_MASTER_TITLE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]           BIT           CONSTRAINT [DF_F_ITEM_MASTER_TITLE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]          VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_TITLE_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]          DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_TITLE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]           VARCHAR (150) NULL,
    [UPDATED_DT]           DATETIME      NULL,
    [DELETED_BY]           VARCHAR (150) NULL,
    [DELETED_DT]           DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_TITLE] PRIMARY KEY CLUSTERED ([ITEM_MASTER_ID] ASC),
    CONSTRAINT [UC_ITEM_MASTER_ID] UNIQUE NONCLUSTERED ([ITEM_MASTER_ID] ASC)
);
GO


CREATE NONCLUSTERED INDEX [IX__dbo_F_ITEM_MASTER_TITLE__ID__TITLE]
  ON [dbo].[F_ITEM_MASTER_TITLE] (
    [ITEM_MASTER_ID], [ITEM_TITLE]
  )

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'the title, description information for model', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_TITLE';


GO

-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_MASTER_TITLE_PUT_CHANGE]
    ON  [dbo].[F_ITEM_MASTER_TITLE]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_MASTER_ID, d.ITEM_MASTER_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_MASTER_ID = d.ITEM_MASTER_ID	
	where isnull(i.ITEM_TITLE, '') <> isnull(d.ITEM_TITLE, '')
		or isnull(i.ITEM_QUALIFIED_TITLE, '') <> isnull(d.ITEM_QUALIFIED_TITLE, '')
		or isnull(i.ITEM_DESC, '') <> isnull(d.ITEM_DESC, '')
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 3 --MasterItem
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

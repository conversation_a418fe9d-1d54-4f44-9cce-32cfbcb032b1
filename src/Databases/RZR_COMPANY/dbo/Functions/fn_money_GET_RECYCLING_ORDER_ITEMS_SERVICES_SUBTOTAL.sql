-- Warnings:
-- Medium: The created function returns a dummy value. The result script should be modified manually.

CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_ORDER_ITEMS_SERVICES_SUBTOTAL](@ORDER_ID BIGINT)
<PERSON><PERSON><PERSON><PERSON> MONEY
AS
BEGIN

	RETURN ISNULL((
		SELECT
			SUM([dbo].[fn_money_GET_ITEM_PRICE_BY_TYPE_RECYCLING](
				OS.ITEM_SERVICE_PRICE_FOR_ONE, 
				OS.PriceTypeId, 
				OS.ITEM_SERVICE_COUNT, 
				OS.ITEM_SERVICE_COUNT, 
				0))
		FROM F_RECYCLING_ORDER_ITEM_SERVICE OS WITH(NOLOCK)
		WHERE OS.RECYCLING_ORDER_ID = @ORDER_ID
	), 0.0);

END
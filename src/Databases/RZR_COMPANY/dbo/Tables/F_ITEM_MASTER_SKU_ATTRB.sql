CREATE TABLE [dbo].[F_ITEM_MASTER_SKU_ATTRB] (
    [ITEM_MASTER_SKU_ATTRB_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]           BIGINT        NOT NULL,
    [ITEM_MASTER_SKU_ATTRB_CD] NVARCHAR (900) NOT NULL,
    [IS_INACTIVE]              BIT           CONSTRAINT [DF_F_ITEM_MASTER_SKU_ATTRB_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]               BIT           CONSTRAINT [DF_F_ITEM_MASTER_SKU_ATTRB_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]              VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_SKU_ATTRB_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]              DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_SKU_ATTRB_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]               VARCHAR (150) NULL,
    [UPDATED_DT]               DATETIME      NULL,
    [DELETED_BY]               VARCHAR (150) NULL,
    [DELETED_DT]               DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_SKU_ATTRB] PRIMARY KEY CLUSTERED ([ITEM_MASTER_SKU_ATTRB_ID] ASC)
);





GO





GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It is built in the background', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_SKU_ATTRB';


GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_SKU_ATTRB_ITEM_MASTER_ID]
    ON [dbo].[F_ITEM_MASTER_SKU_ATTRB]([ITEM_MASTER_ID] ASC, [ITEM_MASTER_SKU_ATTRB_CD] ASC)
    INCLUDE([ITEM_MASTER_SKU_ATTRB_ID]);
GO


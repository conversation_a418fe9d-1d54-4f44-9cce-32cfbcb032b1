-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_GET_SALES_ORDER_ITEM_TITLE](@C_SALES_ORDER_ITEM_ID BIGINT)
RETURNS NVARCHAR(250)
AS
BEGIN

	RETURN (
		SELECT TOP(1)
			COALESCE(FSOI.ITEM_UNALLOCATED_TITLE, FSOI.ITEM_TITLE, FI.ITEM_DESC, FIMT.ITEM_TITLE, FRIM.RECYCLING_ITEM_MASTER_DESC, FSOI.ITEM_NOTES, FRIST.SERVICE_TYPE_DESCR)
		FROM F_SALES_ORDER_ITEM					FSOI	WITH(NOLOCK)
		-- A normal sales order
		LEFT JOIN dbo.F_ITEM					FI		WITH(NOLOCK)
			on FI.ITEM_ID = FSOI.ITEM_ID
		LEFT JOIN F_PRODUCT_MASTER				FPM		WITH(NOLOCK)
			ON FSOI.PRODUCT_MASTER_ID = FPM.PRODUCT_MASTER_ID
		LEFT JOIN F_ITEM_MASTER					FIM		WITH(NOLOCK)
			ON FPM.ITEM_MASTER_ID = FIM.ITEM_MASTER_ID
			AND FPM.PRODUCT_MASTER_TYPE_ID = 1
		LEFT JOIN F_ITEM_MASTER_TITLE			FIMT	WITH(NOLOCK)
			ON FIM.ITEM_MASTER_ID = FIMT.ITEM_MASTER_ID
		-- A recycling sales order
		LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM	FROI	WITH(NOLOCK)
			ON FSOI.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
		LEFT JOIN dbo.F_RECYCLING_ITEM_MASTER	FRIM	WITH(NOLOCK)
			ON FROI.RECYCLING_ITEM_MASTER_ID = FRIM.RECYCLING_ITEM_MASTER_ID
		LEFT JOIN C_RECYCLING_ITEM_SERVICE_TYPE	FRIST	WITH(NOLOCK)
			ON FRIST.SERVICE_TYPE_ID = FPM.SERVICE_MASTER_ID
			AND FPM.PRODUCT_MASTER_TYPE_ID = 2
		WHERE FSOI.SALES_ORDER_ITEM_ID = @C_SALES_ORDER_ITEM_ID
	);
END

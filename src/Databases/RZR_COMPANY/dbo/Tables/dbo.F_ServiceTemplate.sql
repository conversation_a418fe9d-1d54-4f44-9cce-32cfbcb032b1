CREATE TABLE [dbo].[F_ServiceTemplate] (
    [Id]               BIGINT         IDENTITY (1, 1) NOT NULL,
    [Name]             NVARCHAR (250) NOT NULL,
    [CustomerId]       BIGINT         NULL,
    [IsDeleted]        BIT            CONSTRAINT [DF_F_ServiceTemplate_IsDeleted] DEFAULT ((0)) NOT NULL,
    [InsertedBy]       NVARCHAR (150) NOT NULL,
    [InsertedDate]     DATETIME       CONSTRAINT [DF_D_ServiceTemplate_InsetedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId] BIGINT         NULL,
    [InsertedByUserIp] BIGINT         NULL,
    [UpdatedBy]        NVARCHAR (150) NULL,
    [UpdatedDate]      DATETIME       NULL,
    [UpdatedByUserId]  BIGINT         NULL,
    [UpdatedByUserIp]  BIGINT         NULL,
    [DeletedBy]        NVARCHAR (150) NULL,
    [DeletedDate]      DATETIME       NULL,
    [DeletedByUserId]  BIGINT         NULL,
    [DeletedByUserIp]  BIGINT         NULL,
    CONSTRAINT [PK_dbo.F_ServiceTemplate] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_dbo.F_ServiceTemplate_DeletedByUserId] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_dbo.F_ServiceTemplate_F_CUSTOMER] FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_dbo.F_ServiceTemplate_InsertedByUserId] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_dbo.F_ServiceTemplate_UpdatedByUserId] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);




GO
CREATE NONCLUSTERED INDEX [idx_CustomerId]
    ON [dbo].[F_ServiceTemplate]([CustomerId] ASC);


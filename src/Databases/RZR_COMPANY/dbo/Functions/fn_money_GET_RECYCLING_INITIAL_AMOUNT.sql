-- =============================================
-- Author:		<<PERSON>>
-- Create date: <>
-- Description:	<>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_INITIAL_AMOUNT]
(
	@ORDER_ID BIGINT
)
RETURNS FLOAT
BEGIN

	DECLARE @PRICE FLOAT;
	
	IF (
		EXISTS(
			SELECT TOP (1) 1
			FROM [recycling].[F_CommodityRule] WITH (NOLOCK)
			WHERE [RecyclingOrderId] = @ORDER_ID
		) OR EXISTS(
			SELECT TOP (1) 1
			FROM [dbo].[F_RECYCLING_ORDER_ITEM_SERVICE] WITH (NOLOCK)
			WHERE [RECYCLING_ORDER_ID] = @ORDER_ID
		))
	BEGIN

		SELECT @PRICE =
			ISNULL((
				SELECT SUM(ISNULL([ITEM_SERVICE_PRICE_FOR_ONE], 0.0) * [ITEM_SERVICE_COUNT])
				FROM [dbo].[F_RECYCLING_ORDER_ITEM_SERVICE] WITH (NOLOCK)
				WHERE [RECYCLING_ORDER_ID] = @ORDER_ID
			), 0) + ISNULL((
				SELECT SUM([Price] *
					CASE
						WHEN [PriceTypeId] = 2 THEN [DefaultQuantity]
						WHEN [PriceTypeId] = 3 THEN 1
						ELSE [DefaultWeight]
					END)
				FROM [recycling].[F_CommodityRule] WITH (NOLOCK)
				WHERE [RecyclingOrderId] = @ORDER_ID
			), 0);

	END
	ELSE
	BEGIN

		SELECT @PRICE = ISNULL((
			SELECT SUM(CM.[Price] *
				CASE
					WHEN CM.[PriceTypeId] = 2 THEN CM.[DefaultQuantity]
					WHEN CM.[PriceTypeId] = 3 THEN 1
					ELSE CM.[DefaultWeight]
				END)
			FROM [dbo].[F_RECYCLING_ORDER_CONTRACT] AS FROC WITH (NOLOCK)
			INNER JOIN [recycling].[F_CommodityRule] CM WITH (NOLOCK)
				ON CM.[ContractId] = FROC.[CONTRACT_ID]
			WHERE FROC.[RECYCLING_ORDER_ID] = @ORDER_ID
		), 0);

	END

	RETURN @PRICE;

END
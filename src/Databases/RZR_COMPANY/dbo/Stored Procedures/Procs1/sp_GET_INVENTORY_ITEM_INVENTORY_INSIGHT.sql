-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_INVENTORY_INSIGHT]
	@C_ITEM_ID BIGINT = -1
	
AS
BEGIN
	SELECT TOP(1) 
		I.QUANTITY_AVAILABLE as QtyAvailable,
		I.QUANTITY_AVAILABLE_FOR_ECOMMERCE as QtyAvailableForEcom,
		ISNULL(I.PRICE_CHANGED_DATE, I.QUALIFIED_DT)	as LastPricingUpdate,
		OI.LastTimeSold,
		dbo.fn_str_GET_SALES_ORDERS_FOR_LOCKED(I.ITEM_ID) as SalesOrders
	FROM dbo.F_ITEM I with (nolock)
		OUTER APPLY (SELECT MAX(OI.INSERTED_DT) as LastTimeSold  FROM dbo.F_SALES_ORDER_ITEM OI WHERE OI.ITEM_ID = I.ITEM_ID) OI	
	WHERE I.ITEM_ID = @C_ITEM_ID
		
END
CREATE TABLE [dbo].[C_LOGGED_ENTITY_TYPE] (
    [ENTITY_TYPE_ID]   INT            NOT NULL,
    [ENTITY_TYPE_NAME] NVARCHAR (256) NULL,
    [IS_INACTIVE]      BIT            CONSTRAINT [DF_C_LOGGED_ENTITY_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_C_LOGGED_ENTITY_TYPE] PRIMARY KEY CLUSTERED ([ENTITY_TYPE_ID] ASC)
);

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is used for log entities', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_LOGGED_ENTITY_TYPE';
GO
CREATE INDEX IX_C_LOGGED_ENTITY_TYPE_IS_INACTIVE ON [C_LOGGED_ENTITY_TYPE] ([IS_INACTIVE])
INCLUDE ([ENTITY_TYPE_ID], [ENTITY_TYPE_NAME])
GO
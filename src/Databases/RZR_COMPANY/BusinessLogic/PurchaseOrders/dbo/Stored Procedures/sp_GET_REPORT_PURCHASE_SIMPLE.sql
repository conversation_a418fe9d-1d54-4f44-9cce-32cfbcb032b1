-- =============================================
-- Author:		<<PERSON>>
-- Create date: <02/19/2015>
-- Description:	<Recycling Purchases report SP>
-- =============================================
/*
declare @cIds bigint_id_array;
insert into @cIds(id) values (373)

exec sp_GET_REPORT_PURCHASE_SIMPLE
	@CUSTOMER_IDS = @cIds
	,@DATE_FROM	= N'2018-01-01'
	,@DATE_TO	= N'2020-01-01'
	,@C_IS_DEBUG = 1
*/
CREATE PROCEDURE [dbo].[sp_GET_REPORT_PURCHASE_SIMPLE]
	@DATE_FROM			DATETIME				= '2015-10-01',
	@DATE_TO			DATETIME				= '2015-11-10',
	@CUSTOMER_IDS		bigint_ID_ARRAY READONLY,
	@INVOICE_IDS		bigint_ID_ARRAY READONLY,
	@ORDER_COLUMN_NAME	NVARCHAR(150)			= N'SettleDate',
	@ORDER_DIRECTION	NVARCHAR(20)			= N'DESC',
	@ITEMS_PER_PAGE		INT						= 20,
	@PAGE_INDEX			INT						= 0,
	@FILTER_WHERE		VARCHAR(2000)			= N'',
	@WAREHOUSE_IDS		bigint_ID_ARRAY READONLY,
	@C_IS_DEBUG			BIT						= 0
AS
BEGIN

    DECLARE	
	   @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1,
	   @endRowNumber   bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE,	
	   @filterCondition VARCHAR(MAX) = CASE
		  WHEN @FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL THEN N' WHERE ('+ @FILTER_WHERE +N')'
		  ELSE N''
	   END,
	   @dateWhere	    VARCHAR(2000) = CASE
		  WHEN @DATE_FROM IS NOT NULL 
		  THEN CASE @ORDER_COLUMN_NAME 
				WHEN 'SettleDate' THEN N'WHERE SETTLE_DATE >= '''+		CONVERT(VARCHAR, @DATE_FROM) + N''' AND SETTLE_DATE <= '''+		CONVERT(VARCHAR, @DATE_TO) + N''''
				WHEN 'CompletedDate' THEN N'WHERE COMPLETED_DT >= '''+ CONVERT(VARCHAR, @DATE_FROM) + N''' AND COMPLETED_DT <= '''+	CONVERT(VARCHAR, @DATE_TO) + N''''
				ELSE N'WHERE [RECIEVE_DATE] >= '''+ CONVERT(VARCHAR, @DATE_FROM) + N''' AND [RECIEVE_DATE] <= '''+	CONVERT(VARCHAR, @DATE_TO) + N''''
			END
		  ELSE N''
	   END,
	   @TABLE_NAME NVARCHAR(50) = --'F_CUSTOMER'
	   CASE
		  WHEN (EXISTS (SELECT TOP(1) 1 FROM @CUSTOMER_IDS)) THEN '#T_CUSTOMERS'
		  ELSE 'F_CUSTOMER'
	   END,
	   @warehousesWhere	VARCHAR(200) = N'',
	   @invoceWhere nvarchar(1000) = N''


    SELECT ID AS CUSTOMER_ID
    INTO #T_CUSTOMERS
    FROM @CUSTOMER_IDS
        
    SELECT ID AS INVOICE_ID
    INTO #T_INVOICES
    FROM @INVOICE_IDS	

    SELECT ID AS WAREHOUSE_ID
    INTO #T_WAREHOUSES
    FROM @WAREHOUSE_IDS	
	
	IF (EXISTS(SELECT WAREHOUSE_ID FROM #T_WAREHOUSES))
	BEGIN
		SET @warehousesWhere = N' AND WHO.WAREHOUSE_ID IN (SELECT WAREHOUSE_ID FROM #T_WAREHOUSES)'
	END
		ELSE SET @warehousesWhere = 'AND 1=1'
	IF (EXISTS (SELECT TOP(1) 1 FROM @INVOICE_IDS))
	BEGIN
		SET @invoceWhere = 'AND (POI.PURCHASE_ORDER_ID IS NOT NULL AND I.INVOICE_ID IN (SELECT INVOICE_ID FROM #T_INVOICES) OR POI.PURCHASE_ORDER_ID IS NULL AND II.INVOICE_ID IN (SELECT INVOICE_ID FROM #T_INVOICES)) '
	END
		ElSE SET @invoceWhere = 'AND 1=1'
	
	IF (LEN(@dateWhere) = 0) BEGIN SET @dateWhere = N'WHERE 1 = 1' END
    DECLARE @query1 NVARCHAR (MAX) = N'
	   WITH m_data AS (
		  SELECT				
			 froi.RECYCLING_ORDER_ITEM_ID											AS RecyclingOrderItemId
			 ,froi.SETTLE_DATE														AS SettleDate
			 ,dbo.fn_str_GET_USER_AUTO_NAME(froi.USER_ID, 0)						AS InboundRep
			 ,froi.[RECIEVE_DATE]													AS ReceivedDate
			 ,froi.COMPLETED_DT														AS CompletedDate
			 ,froi.INBOUND_ORDER_AUTO_NAME											AS OrderName
			 ,froi.LOT_AUTO_NAME													AS ItemAutoName
			 ,ISNULL(I.AUTO_NAME, II.AUTO_NAME)										AS InvoiceId					
			 ,ISNULL(PO.AUTO_NAME, SO.SALES_ORDER_NUMBER)							AS InvoiceAutoName
			 ,dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME (CAD.CUSTOMER_ADDRESS_ID)	AS Warehouse
			 ,C.CUSTOMER_NAME														AS CustomerName			 
			 ,rep.FirstName + '' '' + ISNULL(rep.LastName, '''')					AS RepName	
			 ,frim.RECYCLING_ITEM_MASTER_NAME										AS ItemTypeCd
			 ,ISNULL(REPLACE(ch.ITEM_CATEGORY_FULL_PATH, ''|'', '' > ''), '''')		AS CategoryName
			 ,froi.WEIGHT_RECEIVED													AS WeightReceived
			 ,froi.WEIGHT_TARE														AS WeightTare
			 ,froi.WEIGHT_RECEIVED - froi.WEIGHT_TARE								AS WeightNet																								
			 ,froi.ITEM_COUNT														AS ItemCount
			 ,CASE
					   WHEN froi.PRICE_TYPE_ID = 1 THEN  ''Weight'' -- Weight
					   WHEN froi.PRICE_TYPE_ID = 2 THEN  ''Unit''	-- Unit						
				END																	AS WeightType
			 ,froi.ITEM_PRICE														AS ItemPrice 
			 ,ISNULL(froi.ITEM_PRICE, 0.0) *
				    CASE
					   WHEN froi.PRICE_TYPE_ID = 3 THEN 1		  -- Flat Fee
					   WHEN froi.PRICE_TYPE_ID = 1 THEN ISNULL(froi.WEIGHT_RECEIVED, 0.0) - ISNULL(froi.WEIGHT_TARE, 0.0)  -- Weight
					   WHEN froi.PRICE_TYPE_ID = 2 THEN ISNULL(froi.ITEM_COUNT, 0)								   -- Unit
					   ELSE 0.0
				    END																AS ExtPrice								
			 ,froi.NOTES															AS Notes
			 
			 ,cgr.COMMODITY_GROUP_NAME												AS CommodityGroup
			 ,dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(froi.LOCATION_ID) + ISNULL('' - '' + WH.WAREHOUSE_CD, '''') 	AS Location
			 ,WHO.WAREHOUSE_CD														AS ActualWarehouse
			 ,LT.RECYCLING_LOGISTIC_TYPE_CD											AS Trucking
			 ,froi.BOL_NUMBER														AS BolNumber
			 ,froi.PO_NUMBER														AS PoNumber
			 ,FA.ACCOUNT_NAME														AS AccountName
			 ,froi.StateProgramName													AS StateProgramName
		  FROM recycling.vw_PurchasedLots				froi
		  INNER JOIN dbo.F_CUSTOMER_ADDRESS				CAD		WITH(NOLOCK)
			 ON froi.CUSTOMER_ADDRESS_ID = CAD.CUSTOMER_ADDRESS_ID'
		 
	DECLARE @query2 NVARCHAR(MAX) = N'
		  INNER JOIN '+ @TABLE_NAME +'					CL
			 ON CL.CUSTOMER_ID = CAD.CUSTOMER_ID
		  INNER JOIN 	dbo.F_CUSTOMER					C		WITH(NOLOCK)
			 ON C.CUSTOMER_ID = CL.CUSTOMER_ID		   
		  INNER JOIN 	F_RECYCLING_ITEM_MASTER			frim	WITH(NOLOCK)	
			 ON froi.RECYCLING_ITEM_MASTER_ID = frim.RECYCLING_ITEM_MASTER_ID						  
		  INNER JOIN 	dbo.tb_User						rep	    WITH(NOLOCK)
			 ON C.REP_ID = rep.UserID
		  
		  LEFT JOIN D_CATEGORY_HIERARCHY				ch		WITH(NOLOCK)
			 ON  ch.IS_INACTIVE = 0
			 AND ch.CATEGORY_ID = frim.CATEGORY_ID 
		  LEFT JOIN dbo.F_PURCHASE_ORDER				PO		WITH(NOLOCK) 
			 ON froi.RECYCLING_ORDER_ID = PO.RECYCLING_ORDER_ID
			 AND froi.CUSTOMER_ID = PO.CUSTOMER_ID -- Skip carrier POs
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM			POI		WITH(NOLOCK) 
			 ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
			 AND FROI.RECYCLING_ORDER_ITEM_ID = POI.RECYCLING_ORDER_ITEM_ID 		  
		  LEFT JOIN dbo.F_INVOICE						I		WITH(NOLOCK) 
			 ON I.INVOICE_TYPE_ID = 2
			 AND I.ORDER_ID = PO.PURCHASE_ORDER_ID
			 AND I.IS_VOIDED = 0
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM_INVOICE	POII	WITH(NOLOCK)
			 ON POI.PURCHASE_ORDER_ITEM_ID = POII.PURCHASE_ORDER_ITEM_ID
			 AND I.INVOICE_ID = POII.PURCHASE_ORDER_INVOICE_ID

		  LEFT JOIN F_COMMODITY_GROUP					cgr		WITH(NOLOCK)
			ON cgr.COMMODITY_GROUP_ID = frim.COMMODITY_GROUP_ID
		  LEFT JOIN 	dbo.F_LOCATION					loc	    WITH(NOLOCK)	
			ON froi.LOCATION_ID = loc.LOCATION_ID
		  LEFT JOIN D_WAREHOUSE							WH		WITH(NOLOCK)
			ON WH.WAREHOUSE_ID = loc.WAREHOUSE_ID
		  LEFT JOIN D_WAREHOUSE							WHO		WITH(NOLOCK)
			ON WHO.WAREHOUSE_ID = froi.WAREHOUSE_ID
		  LEFT JOIN C_RECYCLING_LOGISTIC_TYPE			LT		WITH(NOLOCK)
			ON LT.RECYCLING_LOGISTIC_TYPE_ID = froi.LOGISTIC_TYPE_ID
		  LEFT JOIN F_PRODUCT_MASTER					FPM		WITH(NOLOCK)
		 	ON FPM.MODULE_MASTER_ID = frim.RECYCLING_ITEM_MASTER_ID
		 	AND FPM.PRODUCT_MASTER_TYPE_ID = 3
			AND FPM.IS_INACTIVE = 0
			AND FPM.IS_DELETED = 0 
		  LEFT JOIN F_WAREHOUSE_ACCOUNT					FWA		WITH(NOLOCK)
		 	ON FWA.WAREHOUSE_ID = WHO.WAREHOUSE_ID
		 	AND FWA.PRODUCT_MASTER_ID = FPM.PRODUCT_MASTER_ID
		  LEFT JOIN F_ACCOUNT							FA		WITH(NOLOCK)
		 	ON FA.ACCOUNT_ID = FWA.ACCOUNT_ID

		  LEFT JOIN dbo.F_SALES_ORDER					SO		WITH(NOLOCK) 
			 ON froi.RECYCLING_ORDER_ID = SO.RECYCLING_ORDER_ID
		  LEFT JOIN dbo.F_SALES_ORDER_ITEM				SOI		WITH(NOLOCK) 
			 ON SO.SALES_ORDER_ID = SOI.SALES_ORDER_ID
			 AND FROI.RECYCLING_ORDER_ITEM_ID = SOI.RECYCLING_ORDER_ITEM_ID		  
		  LEFT JOIN dbo.F_INVOICE						II		WITH(NOLOCK) 
			 ON II.INVOICE_TYPE_ID = 1
			 AND II.ORDER_ID = SO.SALES_ORDER_ID 
			 ' + @dateWhere + N' '
			 + @invoceWhere + N' '
			 + @warehousesWhere +N'  
			 
		    AND froi.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
		  '

	DECLARE @query1_2 NVARCHAR (MAX) = N'
		UNION ALL	 
		  SELECT				
			 OI.Id													AS RecyclingOrderItemId
			 ,RO.SETTLE_DATE										AS SettleDate
			 ,dbo.fn_str_GET_USER_AUTO_NAME(RO.USER_ID, 0)			AS InboundRep
			 ,FROIN.[RECIEVE_DATE]									AS ReceivedDate
			 ,FROIN.COMPLETED_DT									AS CompletedDate
			 ,FROIN.AUTO_NAME										AS OrderName
			 ,OI.UniqueId											AS ItemAutoName
			 ,ISNULL(I.AUTO_NAME, II.AUTO_NAME)						AS InvoiceId
			 ,ISNULL(PO.AUTO_NAME, SO.SALES_ORDER_NUMBER)			AS InvoiceAutoName
			 ,dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME (CAD.CUSTOMER_ADDRESS_ID)	AS Warehouse
			 ,C.CUSTOMER_NAME										AS CustomerName			 
			 ,rep.FirstName + '' '' + ISNULL(rep.LastName, '''')	AS RepName	
			 ,im.ITEM_NUMBER										AS ItemTypeCd
			 ,ISNULL(vw.CategoryFullPathToDisplay, '''')			AS CategoryName
			 ,OI.[Weight]											AS WeightReceived
			 ,0														AS WeightTare
			 ,OI.[Weight]											AS WeightNet
			 ,OI.ItemCount											AS ItemCount
			 ,CASE
					   WHEN OI.PriceTypeId = 1 THEN  ''Weight''   -- Weight
					   WHEN OI.PriceTypeId = 2 THEN  ''Unit''		-- Unit						
				END													AS WeightType
			 ,OI.ItemPrice											AS ItemPrice 
			 ,ISNULL(OI.ItemPrice, 0.0) *
				    CASE
					   WHEN OI.PriceTypeId = 3 THEN 1		  -- Flat Fee
					   WHEN OI.PriceTypeId = 1 THEN ISNULL(OI.[Weight], 0.0)  -- Weight
					   WHEN OI.PriceTypeId = 2 THEN ISNULL(OI.[ItemCount], 0)	-- Unit
					   ELSE 0.0
				    END												AS ExtPrice								
			 ,OI.Notes												AS Notes
			 ,NULL													AS CommodityGroup
			 ,dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(OI.LocationId) + ISNULL('' - '' + WH.WAREHOUSE_CD, '''') AS Location
			 ,WHO.WAREHOUSE_CD														AS ActualWarehouse
			 ,LT.RECYCLING_LOGISTIC_TYPE_CD							AS Trucking
			 ,RO.BOL_NUMBER											AS BolNumber
			 ,RO.PO_NUMBER											AS PoNumber
			 ,NULL													AS AccountName
			 ,FSP.[Name]											AS StateProgramName
		  FROM recycling.F_Asset					OI		WITH(NOLOCK)	
		  INNER JOIN dbo.F_ITEM_INVENTORY						IINV	WITH(NOLOCK)
			ON OI.Id = IINV.AssetId
		  INNER JOIN dbo.F_RECYCLING_ORDER_ITEM		ROI		WITH(NOLOCK)
		    ON OI.RecyclingOrderItemId = ROI.RECYCLING_ORDER_ITEM_ID
			AND ROI.IS_DELETED = 0
			AND ROI.NO_EXTERNAL_LOTS_MERGED_INTO = 1
		  INNER JOIN [recycling].[F_AuditSession]				AH		WITH(NOLOCK)
			ON OI.AuditSessionId = AH.Id
		  INNER JOIN dbo.F_RECYCLING_ORDER						RO		WITH(NOLOCK)
			ON (RO.RECYCLING_SETTLEMENT_STATE_ID = 3
			  OR RO.RECYCLING_SETTLEMENT_STATE_ID = 4
			  OR RO.RECYCLING_SETTLEMENT_STATE_ID = 5)
			AND AH.RecyclingOrderId = RO.RECYCLING_ORDER_ID
			
		  INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND				FROIN  WITH(NOLOCK)
			 ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID	
			 AND FROIN.StatusId = 6 /*Settlement Complete*/
		  INNER JOIN dbo.F_CUSTOMER_ADDRESS						CAD		WITH(NOLOCK)
			 ON FROIN.CUSTOMER_ADDRESS_ID = CAD.CUSTOMER_ADDRESS_ID
		  INNER JOIN '+ @TABLE_NAME +'							CL
			 ON CL.CUSTOMER_ID = CAD.CUSTOMER_ID
		  INNER JOIN 	dbo.F_CUSTOMER							C	    WITH(NOLOCK)
			 ON C.CUSTOMER_ID = CL.CUSTOMER_ID		   
		  INNER JOIN 	F_ITEM_MASTER							im		WITH(NOLOCK)	
			 ON IINV.ITEM_MASTER_ID = im.ITEM_MASTER_ID						  
		  INNER JOIN 	dbo.tb_User								rep		WITH(NOLOCK)
			 ON C.REP_ID = rep.UserID
		  LEFT JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY]	vw WITH(NOLOCK)
			ON vw.ItemMasterId = im.ITEM_MASTER_ID
			AND vw.CategoryIsPrimary = 1		
		  LEFT JOIN 	dbo.F_LOCATION							loc	    WITH(NOLOCK)	
			ON OI.LocationId = loc.LOCATION_ID
		  LEFT JOIN D_WAREHOUSE									WH		WITH(NOLOCK)
			ON WH.WAREHOUSE_ID = loc.WAREHOUSE_ID
		  LEFT JOIN D_WAREHOUSE								WHO		WITH(NOLOCK)
			ON WHO.WAREHOUSE_ID = RO.WAREHOUSE_ID
		  LEFT JOIN C_RECYCLING_LOGISTIC_TYPE					LT		WITH(NOLOCK)
			ON LT.RECYCLING_LOGISTIC_TYPE_ID = FROIN.LOGISTIC_TYPE_ID'

	DECLARE @query2_2 NVARCHAR(MAX) = N'
		  LEFT JOIN dbo.F_PURCHASE_ORDER						PO		WITH(NOLOCK) 
			 ON RO.RECYCLING_ORDER_ID = PO.RECYCLING_ORDER_ID
			 AND RO.CUSTOMER_ID = PO.CUSTOMER_ID -- Skip carrier POs
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM					POI		WITH(NOLOCK) 
			 ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
			 AND IINV.ITEM_INVENTORY_ID = POI.INVENTORY_ITEM_ID 		  
		  LEFT JOIN dbo.F_INVOICE								I		WITH(NOLOCK) 
			 ON I.INVOICE_TYPE_ID = 2
			 AND I.ORDER_ID = PO.PURCHASE_ORDER_ID
			 AND I.IS_VOIDED = 0
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM_INVOICE			POII	WITH(NOLOCK)
			 ON POI.PURCHASE_ORDER_ITEM_ID = POII.PURCHASE_ORDER_ITEM_ID
			 AND I.INVOICE_ID = POII.PURCHASE_ORDER_INVOICE_ID
			 
		  LEFT JOIN dbo.F_SALES_ORDER							SO		WITH(NOLOCK) 
			 ON RO.RECYCLING_ORDER_ID = SO.RECYCLING_ORDER_ID
		  LEFT JOIN dbo.F_SALES_ORDER_ITEM						SOI		WITH(NOLOCK) 
			 ON SO.SALES_ORDER_ID = SOI.SALES_ORDER_ID
			 AND IINV.ITEM_INVENTORY_ID = SOI.ITEM_INVENTORY_ID	  
		  LEFT JOIN dbo.F_INVOICE								II		WITH(NOLOCK) 
			 ON II.INVOICE_TYPE_ID = 1
			 AND II.ORDER_ID = SO.SALES_ORDER_ID 			
		  LEFT JOIN [recycling].[F_StateProgram]				FSP		WITH(NOLOCK)
		     ON OI.[StateProgramId] = FSP.[Id]
			 ' + @dateWhere + N' '
			 + @invoceWhere + N' '
			 + @warehousesWhere +N''

	DECLARE 
	   @query3 NVARCHAR (MAX) = N'    
		  UNION ALL
		  SELECT DISTINCT
			 froi.RECYCLING_ORDER_ITEM_SERVICE_ID	AS RecyclingOrderItemId					
			 ,RO.SETTLE_DATE						AS SettleDate
			 ,dbo.fn_str_GET_USER_AUTO_NAME(RO.USER_ID, 0)	AS InboundRep
			 ,FROIN.[RECIEVE_DATE]					AS ReceivedDate
			 ,FROIN.COMPLETED_DT					AS CompletedDate
			 ,FROIN.AUTO_NAME						AS OrderName
			 ,NULL									AS ItemAutoName
			 ,CASE
				WHEN POI.PURCHASE_ORDER_ID IS NULL THEN II.AUTO_NAME
				ELSE I.AUTO_NAME
			 END									AS InvoiceId
			 ,CASE
				WHEN POI.PURCHASE_ORDER_ID IS NULL THEN SO.SALES_ORDER_NUMBER
				ELSE PO.AUTO_NAME
			 END									AS InvoiceAutoName
			 ,dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME (CAD.CUSTOMER_ADDRESS_ID)	AS Warehouse
			 ,C.CUSTOMER_NAME														AS CustomerName			
			 ,rep.FirstName + '' '' + ISNULL(rep.LastName, '''')					AS RepName	
			 ,ST.SERVICE_TYPE_CD					AS ItemTypeCd
			 ,''''									AS CategoryName
			 ,NULL									AS WeightReceived
			 ,NULL									AS WeightTare
			 ,NULL									AS WeightNet													
			 ,froi.ITEM_SERVICE_COUNT				AS ItemCount
			 ,''unit''								AS WeightType
			 ,froi.ITEM_SERVICE_PRICE_FOR_ONE		AS ItemPrice 
			 ,ISNULL(froi.ITEM_SERVICE_COUNT, 0) * ISNULL(froi.ITEM_SERVICE_PRICE_FOR_ONE, 0.0)	AS ExtPrice								
			 ,froi.NOTES																		AS Notes	
			 
			 ,NULL									AS CommodityGroup
			 ,NULL									AS Location
			 ,WHO.WAREHOUSE_CD						AS ActualWarehouse
			 ,LT.RECYCLING_LOGISTIC_TYPE_CD			AS Trucking
			 ,RO.BOL_NUMBER							AS BolNumber
			 ,RO.PO_NUMBER							AS PoNumber			 	
			 ,NULL									AS AccountName
			 ,NULL									AS StateProgramName
		  FROM F_RECYCLING_ORDER_ITEM_SERVICE			froi	WITH(NOLOCK)
		  INNER JOIN dbo.F_RECYCLING_ORDER				RO	    WITH(NOLOCK)
			 ON froi.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID						  
		  INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND		FROIN	WITH (NOLOCK)
			 ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID
			 AND FROIN.IS_QUOTE = 0		
			 AND FROIN.StatusId = 6 /*Settlement Complete*/
		  INNER JOIN C_RECYCLING_ITEM_SERVICE_TYPE		ST		WITH(NOLOCK)
			 ON ST.SERVICE_TYPE_ID = froi.ITEM_SERVICE_TYPE_ID
		  INNER JOIN dbo.F_PRODUCT_MASTER				PM		WITH(NOLOCK)					
			 ON PM.PRODUCT_MASTER_TYPE_ID = 2
			 AND ST.SERVICE_TYPE_ID = PM.SERVICE_MASTER_ID
			 AND PM.IS_INACTIVE = 0
			 AND PM.IS_DELETED = 0 
		  INNER JOIN dbo.F_CUSTOMER_ADDRESS				CAD		WITH(NOLOCK)
			 ON FROIN.CUSTOMER_ADDRESS_ID = CAD.CUSTOMER_ADDRESS_ID
		  INNER JOIN '+ @TABLE_NAME +'					CL
			 ON CL.CUSTOMER_ID = CAD.CUSTOMER_ID
		  INNER JOIN 	dbo.F_CUSTOMER					C	    WITH(NOLOCK)
			 ON C.CUSTOMER_ID = CL.CUSTOMER_ID		  
		  INNER JOIN 	dbo.tb_User						rep		WITH(NOLOCK)
			 ON C.REP_ID = rep.UserID		  
		  LEFT JOIN dbo.F_PURCHASE_ORDER				PO		WITH(NOLOCK) 
			ON RO.RECYCLING_ORDER_ID = PO.RECYCLING_ORDER_ID
			AND RO.CUSTOMER_ID = PO.CUSTOMER_ID -- Skip carrier POs
		  LEFT JOIN dbo.F_INVOICE						I		WITH(NOLOCK) 
			 ON I.INVOICE_TYPE_ID = 2
			 AND I.ORDER_ID = PO.PURCHASE_ORDER_ID
			 AND I.IS_VOIDED = 0 '
	DECLARE 
	   @query4 NVARCHAR (MAX) = N'    
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM			POI		WITH(NOLOCK) 
			ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID 
			AND POI.PRODUCT_MASTER_TYPE_ID = 2
			AND PM.PRODUCT_MASTER_ID = POI.PRODUCT_MASTER_ID
		  LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM_INVOICE	POII	WITH(NOLOCK)
			 ON POI.PURCHASE_ORDER_ITEM_ID = POII.PURCHASE_ORDER_ITEM_ID
			 AND I.INVOICE_ID = POII.PURCHASE_ORDER_INVOICE_ID 		 		  
		  LEFT JOIN dbo.F_SALES_ORDER					SO		WITH(NOLOCK) 
			 ON RO.RECYCLING_ORDER_ID = SO.RECYCLING_ORDER_ID
		  LEFT JOIN dbo.F_INVOICE						II		WITH(NOLOCK) 
			 ON II.INVOICE_TYPE_ID = 1
			 AND II.ORDER_ID = SO.SALES_ORDER_ID			 	  
		  LEFT JOIN dbo.F_SALES_ORDER_ITEM				SOI		WITH(NOLOCK) 
			ON SO.SALES_ORDER_ID = SOI.SALES_ORDER_ID
			AND SOI.PRODUCT_MASTER_TYPE_ID = 2
			AND PM.PRODUCT_MASTER_ID = SOI.PRODUCT_MASTER_ID 
		  LEFT JOIN (select 0 as WAREHOUSE_ID) WH
			ON WH.WAREHOUSE_ID = 0
		  LEFT JOIN D_WAREHOUSE								WHO		WITH(NOLOCK)
			ON WHO.WAREHOUSE_ID = RO.WAREHOUSE_ID
		  LEFT JOIN C_RECYCLING_LOGISTIC_TYPE			LT		WITH(NOLOCK)
			ON LT.RECYCLING_LOGISTIC_TYPE_ID = FROIN.LOGISTIC_TYPE_ID
			 ' + @dateWhere + N' '
			 + @invoceWhere + N' '
			 + @warehousesWhere +N''

     DECLARE @query5 NVARCHAR(MAX) = N'	
		  UNION ALL	 
		  SELECT TOP(1)
			 froi.RECYCLING_ORDER_ITEM_SERVICE_ID	AS RecyclingOrderItemId						
			 ,RO.SETTLE_DATE						AS SettleDate
			 ,dbo.fn_str_GET_USER_AUTO_NAME(RO.USER_ID, 0)		AS InboundRep
			 ,FROIN.[RECIEVE_DATE]					AS ReceivedDate
			 ,FROIN.COMPLETED_DT					AS CompletedDate
			 ,FROIN.AUTO_NAME						AS OrderName
			 ,''Freight Cost''						AS ItemAutoName
			 ,NULL									AS InvoiceId
			 ,NULL									AS InvoiceAutoName
			 ,dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME (CAD.CUSTOMER_ADDRESS_ID)	AS Warehouse
			 ,C.CUSTOMER_NAME														AS CustomerName
			 ,rep.FirstName + '' '' + ISNULL(rep.LastName, '''')					AS RepName	
			 ,''Freight Cost''						AS ItemTypeCd
			 ,''''									AS CategoryName
			 ,NULL									AS WeightReceived
			 ,NULL									AS WeightTare
			 ,NULL									AS WeightNet														
			 ,1										AS ItemCount
			 ,''unit''								AS WeightType
			 ,FC.[QuoteRate]						AS ItemPrice 
			 ,FC.[QuoteRate]						AS ExtPrice	
			 ,NULL									AS Notes	

			 ,NULL									AS CommodityGroup
			 ,NULL									AS Location
			 ,WHO.WAREHOUSE_CD						AS ActualWarehouse
			 ,LT.RECYCLING_LOGISTIC_TYPE_CD			AS Trucking
			 ,RO.BOL_NUMBER							AS BolNumber
			 ,RO.PO_NUMBER							AS PoNumber
			 ,NULL									AS AccountName
			 ,NULL									AS StateProgramName
		  FROM F_RECYCLING_ORDER_ITEM_SERVICE				froi	WITH(NOLOCK)
		  INNER JOIN C_RECYCLING_ITEM_SERVICE_TYPE			ST		WITH(NOLOCK)
			 ON ST.SERVICE_TYPE_ID = froi.ITEM_SERVICE_TYPE_ID
			 AND ST.SERVICE_TYPE_ID = 19
		  INNER JOIN dbo.F_RECYCLING_ORDER					RO	    WITH(NOLOCK)
			 ON  froi.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID				
		  INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND			FROIN  WITH(NOLOCK)
			 ON RO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID
			 AND FROIN.IS_QUOTE = 0		
			 AND FROIN.StatusId = 6 /*Settlement Complete*/
		  INNER JOIN dbo.F_PRODUCT_MASTER					PM		WITH(NOLOCK)					
			 ON PM.PRODUCT_MASTER_TYPE_ID = 2
			 AND ST.SERVICE_TYPE_ID = PM.SERVICE_MASTER_ID
			 AND PM.IS_INACTIVE = 0
			 AND PM.IS_DELETED = 0 
		  INNER JOIN dbo.F_CUSTOMER_ADDRESS					CAD		WITH(NOLOCK)
			 ON FROIN.CUSTOMER_ADDRESS_ID = CAD.CUSTOMER_ADDRESS_ID
		  INNER JOIN '+ @TABLE_NAME +'						CL
			 ON CL.CUSTOMER_ID = CAD.CUSTOMER_ID
		  INNER JOIN 	dbo.F_CUSTOMER						C	    WITH(NOLOCK)
			 ON C.CUSTOMER_ID = CL.CUSTOMER_ID
		  INNER JOIN 	dbo.tb_User							rep		WITH(NOLOCK)
			 ON C.REP_ID = rep.UserID 
		  LEFT JOIN C_RECYCLING_LOGISTIC_TYPE				LT		WITH(NOLOCK)
			ON LT.RECYCLING_LOGISTIC_TYPE_ID = FROIN.LOGISTIC_TYPE_ID
		  LEFT JOIN (select 0 as WAREHOUSE_ID) WH
			ON WH.WAREHOUSE_ID = 0
		  LEFT JOIN D_WAREHOUSE								WHO		WITH(NOLOCK)
			ON WHO.WAREHOUSE_ID = RO.WAREHOUSE_ID
		  left join [recycling].[fn_table_GetRecyclingOrderFreightQuoteRate]() fc
			on fc.[RecyclingOrderId] = RO.RECYCLING_ORDER_ID
			' + @dateWhere + N' '
			  +@warehousesWhere+ N' '
			  + 'AND NOT EXISTS(SELECT TOP(1) 1 FROM #T_INVOICES)' + N')'

    DECLARE @query6 NVARCHAR(MAX) = N'
	   SELECT TOP(1)
		  -1							AS RowID
		  ,COUNT(RecyclingOrderItemId)	AS RecyclingOrderItemId		
		  ,CONVERT(datetime, ''1900-01-01'')  AS SettleDate
		  ,NULL							AS InboundRep
		  ,NULL							AS ReceivedDate
		  ,NULL							AS CompletedDate
		  ,NULL							AS OrderName
		  ,NULL							AS ItemAutoName
		  ,NULL							AS InvoiceId
		  ,NULL							AS InvoiceAutoName
		  ,NULL							as Warehouse
		  ,NULL							AS CustomerName
		  ,NULL							AS RepName	
		  ,NULL							AS ItemTypeCd
		  ,NULL 						AS CategoryName
		  ,0 							AS WeightReceived
		  ,0 							AS WeightTare
		  ,0 							AS WeightNet			
		  ,0 							AS ItemCount
		  ,NULL 						AS WeightType
		  ,0 							AS ItemPrice 
		  ,0 							AS ExtPrice	
		  ,NULL 						AS Notes
		  ,NULL							AS CommodityGroup
		  ,NULL							AS Location
		  ,NULL							AS ActualWarehouse
		  ,NULL							AS Trucking
		  ,NULL							AS BolNumber
		  ,NULL							AS PoNumber	
		  ,NULL							AS AccountName
		  ,NULL							AS StateProgramName
	   FROM m_data
	   ' + @filterCondition + '
	   UNION ALL
	   SELECT
		   TT.RowID
		  ,TT.RecyclingOrderItemId
		  ,TT.SettleDate
		  ,TT.InboundRep
		  ,TT.ReceivedDate
		  ,TT.CompletedDate
		  ,TT.OrderName
		  ,TT.ItemAutoName
		  ,TT.InvoiceId
		  ,TT.InvoiceAutoName
		  ,TT.Warehouse
		  ,TT.CustomerName
		  ,TT.RepName		
		  ,TT.ItemTypeCd
		  ,TT.CategoryName
		  ,TT.WeightReceived
		  ,TT.WeightTare
		  ,TT.WeightNet		
		  ,TT.ItemCount
		  ,TT.WeightType
		  ,TT.ItemPrice 
		  ,TT.ExtPrice	
		  ,TT.Notes
		  ,TT.CommodityGroup
		  ,TT.Location
		  ,TT.ActualWarehouse
		  ,TT.Trucking
		  ,TT.BolNumber
		  ,TT.PoNumber
		  ,TT.AccountName
		  ,TT.StateProgramName
	   FROM 
	   (
		  SELECT *
		  FROM
			 (SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowId,
				* 
			 FROM m_data	
			 ' + @filterCondition + ') t	
		  WHERE RowId BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100)) + '
	   ) TT';	

    SET @query1 = @query1 + @query2 + @query1_2 + @query2_2 + @query3 + @query4 + @query5 + @query6;
	
	IF (@C_IS_DEBUG = 1)
	begin
		print(cast(@query1 as ntext))		
	end

	EXEC sp_executesql @query1;

    DROP TABLE #T_CUSTOMERS
    DROP TABLE #T_INVOICES
	DROP TABLE #T_WAREHOUSES

END
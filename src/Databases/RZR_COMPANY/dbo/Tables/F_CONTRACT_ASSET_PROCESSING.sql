CREATE TABLE [dbo].[F_CONTRACT_ASSET_PROCESSING] (
    [CONTRACT_ASSET_PROCESSING_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [CONTRACT_ASSET_ID]            BIGINT        NOT NULL,
    [PROCESSING_REQUIREMENTS_ID]   BIGINT        NOT NULL,
    [PRICE]                        MONEY         NOT NULL,
    [INSERTED_BY]                  VARCHAR (250) NOT NULL,
    [INSERTED_DT]                  DATETIME      NOT NULL,
    CONSTRAINT [PK_F_CONTRACT_ASSET_PROCESSING] PRIMARY KEY CLUSTERED ([CONTRACT_ASSET_PROCESSING_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_ASSET_PROCESSING_C_PROCESSING_REQUIREMENTS] FOREIGN KEY ([PROCESSING_REQUIREMENTS_ID]) REFERENCES [dbo].[C_PROCESSING_REQUIREMENTS] ([PROCESSING_REQUIREMENTS_ID]),
    CONSTRAINT [FK_F_CONTRACT_ASSET_PROCESSING_F_CONTRACT_ASSET] FOREIGN KEY ([CONTRACT_ASSET_ID]) REFERENCES [dbo].[F_CONTRACT_ASSET] ([CONTRACT_ASSET_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The contract has ITAD asset tab. It has several groups of options. This is one of them. Many of them are used just for info and not in logic', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_ASSET_PROCESSING';


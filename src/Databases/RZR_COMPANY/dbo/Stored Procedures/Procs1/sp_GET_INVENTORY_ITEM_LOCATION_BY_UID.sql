-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <08/07/2013>
-- Description:	<Counts the inventory items in location being checked during the session>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ITEM_LOCATION_BY_UID]
	(@UID NVARCHAR(MAX))
AS
BEGIN
	SET @UID = RTRIM(LTRIM(@UID));
	SELECT
		FII.LOCATION_ID
		,FII.AUDIT_SESSION_ID
		,FII.ITEM_INVENTORY_ID
		,FII.AUDIT_STATUS_ID
		,DW.WAREHOUSE_CD
		,FL.LOCATION_NAME
		,FII.ITEM_STATUS_ID
		,IIS.STATUS_CD
	FROM F_ITEM_INVENTORY								FII		WITH(NOLOCK)
	LEFT JOIN F_LOCATION								FL		WITH(NOLOCK)
		ON FL.LOCATION_ID = FII.LOCATION_ID
	LEFT JOIN D_WAREHOUSE								DW		WITH(NOLOCK)
		ON DW.WAREHOUSE_ID = FL.WAREHOUSE_ID
	LEFT JOIN D_ITEM_INVENTORY_STATUS					IIS		WITH (NOLOCK)
		ON FII.ITEM_STATUS_ID = IIS.ITEM_INVENTORY_STATUS_ID
	LEFT JOIN F_PURCHASE_ORDER_ITEM						FPOI	WITH (NOLOCK)
		ON FII.ITEM_INVENTORY_ID = FPOI.INVENTORY_ITEM_ID
	LEFT JOIN F_PURCHASE_ORDER							FPO		WITH (NOLOCK)
		ON FPOI.PURCHASE_ORDER_ID = FPO.PURCHASE_ORDER_ID
	LEFT JOIN F_ORDER_ORDER_SUBJECT_TYPE				POT		WITH (NOLOCK)
		ON POT.ORDER_ID = FPO.PURCHASE_ORDER_ID
		AND POT.ENTITY_TYPE_ID = 3
	LEFT JOIN F_PURCHASE_ORDER							FPOR	WITH (NOLOCK)
		ON fii.PurchaseOrderRepairId = FPOR.PURCHASE_ORDER_ID
	LEFT JOIN F_ORDER_ORDER_SUBJECT_TYPE				PORT	WITH (NOLOCK)
		ON PORT.ORDER_ID = FPOR.PURCHASE_ORDER_ID
		AND PORT.ENTITY_TYPE_ID = 3	
	WHERE ITEM_STATUS_ID IN (1, 2, 8, 14)
		AND FII.IS_DELETED = 0 
		AND FII.IS_VIRTUAL = 0
		AND FII.IS_DROP_SHIP_ITEM = 0
		AND (PORT.[ENTITY_SUBJECT_TYPE_ID] IS NULL and POT.[ENTITY_SUBJECT_TYPE_ID] IS NULL OR ISNULL(PORT.[ENTITY_SUBJECT_TYPE_ID], POT.[ENTITY_SUBJECT_TYPE_ID]) != 8)		-- 8 - Drop Ship
		AND (RTRIM(LTRIM(ITEM_INVENTORY_SERIAL))    = @UID
	    OR RTRIM(LTRIM(ITEM_INVENTORY_UNIQUE_ID)) = @UID)
END
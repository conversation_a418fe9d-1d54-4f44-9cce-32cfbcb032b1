--This function is parsed <Root><Items ID="1946987" /><Items ID="1946988" /></Root>
--SELECT * FROM [fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY]('<Root><Items ID="1946987" /><Items ID="1946988" /></Root>', 'Items') 
CREATE FUNCTION [dbo].[fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY](@C_IDS XML, @C_NODE_NAME nvarchar(250)) 
	RETURNS	@T_IDS TABLE(ID BIGINT)
BEGIN
	DECLARE @ROOT_ITEM nvarchar(50)= 'Root'
	IF NULLIF(@C_NODE_NAME, '') = ''	
		SET @C_NODE_NAME = 'Item'
	
	INSERT INTO @T_IDS
	SELECT ItemId 
		FROM (
	select id.value('@ID', 'bigint') as ItemId 
	from @C_IDS.nodes('/*[local-name(.)=sql:variable("@ROOT_ITEM")]/*[local-name(.)=sql:variable("@C_NODE_NAME")]') col(id)
	UNION
	select id.value('@Id', 'bigint') as ItemId 
	from @C_IDS.nodes('/*[local-name(.)=sql:variable("@ROOT_ITEM")]/*[local-name(.)=sql:variable("@C_NODE_NAME")]') col(id)
	) t
	WHERE  t.ItemId IS NOT NULL
	RETURN
END
CREATE TABLE [dbo].[F_ZEBRA_PRINTER_PARAMS] (
    [PARAMS_ID]    BIGINT         IDENTITY (1, 1) NOT NULL,
    [PRINTER_ID]   BIGINT         NOT NULL,
    [PRINTER_IP]   NVARCHAR (128) NOT NULL,
    [PRINTER_PORT] INT            NOT NULL,
    [INSERTED_BY]  VARCHAR (150)  CONSTRAINT [DF_F_ZEBRA_PRINTER_PARAMS_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]  DATETIME       CONSTRAINT [DF_F_ZEBRA_PRINTER_PARAMS_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]   VARCHAR (150)  NULL,
    [UPDATED_DT]   DATETIME       NULL,
    [DELETED_BY]   VARCHAR (150)  NULL,
    [DELETED_DT]   DATETIME       NULL,
    CONSTRAINT [PK_F_ZEBRA_PRINTER_PARAMS] PRIMARY KEY CLUSTERED ([PARAMS_ID] ASC),
    CONSTRAINT [FK_F_ZEBRA_PRINTER_PARAMS_U_PRINTER] FOREIGN KEY ([PRINTER_ID]) REFERENCES [dbo].[U_PRINTER] ([PRINTER_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);




CREATE function [dbo].[fn_str_LaborHoursRepresentation](
	@LaborHours float
)
RETURNS VARCHAR(256)
AS
BEGIN

	DECLARE 
		@minutes float,
		@hrs     float
	         
    select @minutes = round(iif(@LaborHours >= 1, abs(@LaborHours) - abs(floor(@LaborHours)), @LaborHours) * 60, 0)

    if (@LaborHours >= 1)
		select @hrs = Floor(@LaborHours);   

     return iif(@hrs > 0, cast(@hrs as varchar(5)) + 'h ', '') + iif(@minutes > 0, cast(@minutes as varchar(5)) + 'm', '');
	

END
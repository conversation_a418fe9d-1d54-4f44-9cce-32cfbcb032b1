-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE FUNCTION [dbo].[tvf_CALCULATE_LOCATION_RANK]
(		
	@MODEL_LENGTH						FLOAT,
	@MODEL_WIDTH						FLOAT,
	@MODEL_HEIGHT						FLOAT,
	@MODEL_WEIGHT_LBS					FLOAT,
	@INVENTORY_CAPABILITY_ID			BIGINT,
	@ITEM_MASTER_ID						BIGINT,
	@QTY								BIGINT = 1
)
RETURNS @t TABLE (SortColumn FLOAT, AdditionalSort FLOAT)
 
AS
BEGIN
	DECLARE @LOCATION_DEPTH				FLOAT,
				@LOCATION_WIDTH				FLOAT,
				@LOCATION_HEIGHT			FLOAT,
				@LOCATION_MAX_WEIGHT		FLOAT,
				@LOCATION_CAPACITY			BIGINT,
				@LOCATION_TYPE_ID			BIGINT,
				@LOCATION_ALLOWED_ITEM_ID	INT,
				@LOCATION_DETAIL_ID			BIGINT,
				@NOT_SUGGESTED				BIT
	SELECT 
		@LOCATION_DEPTH				 = LOCATION_DETAIL_DEPTH
		,@LOCATION_WIDTH			 = LOCATION_DETAIL_WIDTH
		,@LOCATION_HEIGHT			 = LOCATION_DETAIL_HEIGHT
		,@LOCATION_MAX_WEIGHT		 = LOCATION_DETAIL_HEIGHT
		,@LOCATION_CAPACITY			 = LOCATION_DETAIL_CAPACITY
		,@LOCATION_TYPE_ID			 = LOCATION_TYPE_ID
		,@LOCATION_ALLOWED_ITEM_ID	 = LOCATION_ALLOWED_ITEM_ID
		,@LOCATION_DETAIL_ID		 = LOCATION_DETAIL_ID
		,@NOT_SUGGESTED				 = LOCATION_DETAIL_NOT_SUGGESTED
	FROM dbo.F_LOCATION_DETAIL ld WITH(NOLOCK)
	WHERE INVENTORY_CAPABILITY_ID = @INVENTORY_CAPABILITY_ID	
	

	IF (@NOT_SUGGESTED = 1) BEGIN
		INSERT INTO @t
		SELECT -1, -1
		
		RETURN
	END			
	
		
			
	DECLARE 
		@PERCENT_CONST				FLOAT = 0.9,
		@LOCATION_VOLUME			FLOAT,
		@CURRENT_ITEMS_VOLUME		FLOAT,
		@CURRENT_ITEMS_WEIGHT_LBS	FLOAT,
		@CURRENT_MODEL_ITEMS_COUNT	BIGINT = 0,
		@CURRENT_ITEMS_COUNT		BIGINT,
		
		@FREE_VOLUME				FLOAT = 0,
		@FREE_WEIGHT				FLOAT = 0,		
				
		@MODEL_WEIGHT				FLOAT,
		
		@MAX_WEIGHT_CONST			INT = 4000,		
		@WEIGHT_CONST				INT = 80
					
	--VOLUME		
				
	SELECT @LOCATION_VOLUME = (@LOCATION_DEPTH * @LOCATION_WIDTH * @LOCATION_HEIGHT) * @PERCENT_CONST
	
	SELECT 
		@CURRENT_ITEMS_COUNT = COUNT(ii.LOCATION_ID),
		@CURRENT_ITEMS_VOLUME = 
			SUM(imd.DIMENSION_LENGTH * imd.DIMENSION_WIDTH * imd.DIMENSION_HEIGHT),
		@CURRENT_ITEMS_WEIGHT_LBS = SUM(imd.DIMENSION_WEIGHT)						
	FROM dbo.F_ITEM_INVENTORY	ii	WITH (NOLOCK)
	LEFT JOIN dbo.F_ITEM		FI	WITH(NOLOCK)
		ON FI.ITEM_ID = II.ITEM_ID
	LEFT JOIN dbo.F_DIMENSION imd WITH (NOLOCK)
		ON fi.DIMENSION_ID = imd.DIMENSION_ID
	WHERE ii.LOCATION_ID = @INVENTORY_CAPABILITY_ID
	
	SELECT 
		@CURRENT_ITEMS_COUNT		= ISNULL(@CURRENT_ITEMS_COUNT, 0), 
		@CURRENT_ITEMS_VOLUME		= ISNULL(@CURRENT_ITEMS_VOLUME, 0),
		@CURRENT_ITEMS_WEIGHT_LBS	= ISNULL(@CURRENT_ITEMS_WEIGHT_LBS, 0)
	
	SELECT @FREE_VOLUME = (@LOCATION_VOLUME - (@CURRENT_ITEMS_VOLUME + @QTY * (@MODEL_LENGTH * @MODEL_WIDTH * @MODEL_HEIGHT)))
	
	--VOLUME
	
	IF (@FREE_VOLUME < 0 OR 
			(@QTY  = 1 AND (@MODEL_LENGTH > @LOCATION_DEPTH OR @MODEL_WIDTH > @LOCATION_WIDTH OR @MODEL_HEIGHT > @LOCATION_HEIGHT)) OR
			(@QTY > 1 AND @QTY * @MODEL_LENGTH > @LOCATION_DEPTH AND @QTY * @MODEL_WIDTH > @LOCATION_WIDTH AND @QTY * @MODEL_HEIGHT > @LOCATION_HEIGHT)
		) BEGIN
		INSERT INTO @t
		SELECT -1, -1
		
		RETURN
	END
	ELSE IF (@LOCATION_TYPE_ID = 2 AND @LOCATION_CAPACITY > 0 AND @LOCATION_CAPACITY < @CURRENT_ITEMS_COUNT + @QTY) BEGIN
		INSERT INTO @t
		SELECT -1, -1
		
		RETURN
	END
	
	--WEIGHT
	
	SELECT @MODEL_WEIGHT = @MODEL_WEIGHT_LBS * 16		
	
	IF (@LOCATION_MAX_WEIGHT > 0) BEGIN			
		SELECT @LOCATION_MAX_WEIGHT = @LOCATION_MAX_WEIGHT * 16 * @PERCENT_CONST						
		
		SELECT @FREE_WEIGHT = (@LOCATION_MAX_WEIGHT - ((@CURRENT_ITEMS_WEIGHT_LBS * 16) + @QTY * @MODEL_WEIGHT))
		
		--WEIGHT
		
		IF (@FREE_WEIGHT < 0) BEGIN
			INSERT INTO @t
			SELECT -1, -1
		
			RETURN
		END
		
	END	
	ELSE BEGIN
		
		SELECT @FREE_WEIGHT = @MAX_WEIGHT_CONST
		
	END	
		
	SELECT
		@CURRENT_MODEL_ITEMS_COUNT = COUNT(mii.LOCATION_ID) 
	FROM dbo.F_ITEM_INVENTORY mii WITH (NOLOCK)
	WHERE mii.LOCATION_ID    = @INVENTORY_CAPABILITY_ID
	  AND mii.ITEM_MASTER_ID = @ITEM_MASTER_ID
	
	DECLARE @ADDITIONAL_SORT INT
	
	IF (ISNULL(@MODEL_WEIGHT, 0) < @WEIGHT_CONST) BEGIN	
		SELECT @ADDITIONAL_SORT = CASE
					WHEN @LOCATION_TYPE_ID = 2 THEN 4
					WHEN @LOCATION_TYPE_ID = 1 AND @LOCATION_ALLOWED_ITEM_ID IN (1, 2) THEN 3					
					WHEN @LOCATION_TYPE_ID = 3 THEN 2
					WHEN @LOCATION_TYPE_ID = 1 AND @LOCATION_ALLOWED_ITEM_ID = 3 THEN 1
				END
	END
	ELSE BEGIN
		SELECT @ADDITIONAL_SORT = CASE
					WHEN @LOCATION_TYPE_ID = 1 AND @LOCATION_ALLOWED_ITEM_ID IN (1, 2) THEN 4	
					WHEN @LOCATION_TYPE_ID = 3 THEN 3
					WHEN @LOCATION_TYPE_ID = 2 THEN 2														
					WHEN @LOCATION_TYPE_ID = 1 AND @LOCATION_ALLOWED_ITEM_ID = 3 THEN 1
				END
	END

	DECLARE @MAX_COUNT_IN_ANY_LOC BIGINT = 100
	--(	
	--	SELECT MAX(t0.ITEMS_COUNT) 
	--		FROM 
	--		(
	--		SELECT COUNT(mii.LOCATION_ID) ITEMS_COUNT
	--		FROM dbo.F_ITEM_INVENTORY mii WITH (NOLOCK)
	--	    WHERE mii.LOCATION_ID IS NOT NULL
	--		GROUP BY mii.LOCATION_ID, mii.ITEM_MASTER_ID
	--		) t0
	--)
	DECLARE @MAX_VOLUME_IN_ANY_LOC FLOAT = 900000,
		@MAX_WEIGHT_IN_ANY_LOC FLOAT = 200000
    
	--SELECT 
	--	@MAX_VOLUME_IN_ANY_LOC = MAX(fld.LOCATION_DETAIL_DEPTH * fld.LOCATION_DETAIL_WIDTH * fld.LOCATION_DETAIL_HEIGHT) 
	--	, @MAX_WEIGHT_IN_ANY_LOC = MAX (LOCATION_DETAIL_MAX_WEIGHT) * 16 
	--FROM dbo.F_LOCATION_DETAIL fld WITH (NOLOCK)

	--IF (@MAX_WEIGHT_IN_ANY_LOC < 4000)
	--BEGIN
	--	SET @MAX_WEIGHT_IN_ANY_LOC = 4000
	--END

	DECLARE 
		@FREE_VOLUME_PERC FLOAT = @FREE_VOLUME				 * 100 / @MAX_VOLUME_IN_ANY_LOC,
		@FREE_WEIGHT_PERC FLOAT = @FREE_WEIGHT				 * 100 / @MAX_WEIGHT_IN_ANY_LOC,
		@ENAGAGED_COUNT   FLOAT = @CURRENT_MODEL_ITEMS_COUNT * 100 / @MAX_COUNT_IN_ANY_LOC

	DECLARE @CATEGORY_COUNT INT
	SELECT @CATEGORY_COUNT = COUNT(ldc.LOCATION_DETAIL_CATEGORY_ID)
	FROM dbo.F_LOCATION_DETAIL_CATEGORY ldc WITH (NOLOCK)
	WHERE ldc.LOCATION_DETAIL_ID = @LOCATION_DETAIL_ID	
	
	IF (@CATEGORY_COUNT > 0)
	BEGIN
		
		SELECT
			@CATEGORY_COUNT = 2 * COUNT(imc.ITEM_MASTER_CATEGORY_ID)
		FROM dbo.F_ITEM_MASTER_CATEGORY				imc WITH (NOLOCK)
		INNER JOIN dbo.F_LOCATION_DETAIL_CATEGORY	ldc WITH (NOLOCK)
			ON imc.ITEM_MASTER_ID = @ITEM_MASTER_ID
			AND imc.CATEGORY_ID = ldc.CATEGORY_ID
		WHERE ldc.LOCATION_DETAIL_ID = @LOCATION_DETAIL_ID	
		
	END	
	ELSE BEGIN
	
		SELECT @CATEGORY_COUNT = 1
	
	END		
		
	DECLARE @RANK_VALUE FLOAT = ISNULL(@FREE_VOLUME_PERC + @FREE_WEIGHT_PERC + @ENAGAGED_COUNT * 10 + @CATEGORY_COUNT * 100, -1) 
		
	INSERT INTO @t
	SELECT 		
		@RANK_VALUE, 
		@ADDITIONAL_SORT		
	
	RETURN				
	
END
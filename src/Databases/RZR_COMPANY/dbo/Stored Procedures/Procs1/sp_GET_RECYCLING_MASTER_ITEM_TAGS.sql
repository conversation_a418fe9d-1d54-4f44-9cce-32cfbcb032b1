CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_MASTER_ITEM_TAGS]
    @MASTER_ITEM_ID	BIGINT
AS
BEGIN
    SELECT
	   ET.TAG_ID								AS value
	   ,[dbo].[fn_str_AUTO_NAME_TAG](ET.TAG_ID)	AS label
	   ,0										AS [state]
	   ,ET.TAG_TYPE_ID							AS typeId
	   ,TG.IS_SINGLE_TAG						AS isSingle
	   ,TG.TAG_GROUP_ID							AS groupId
    FROM F_ENTITY_TAG   ET WITH(NOLOCK)
	LEFT JOIN F_TAG		  T  WITH(NOLOCK)
		ON T.TAG_ID = ET.TAG_ID
	LEFT JOIN F_TAG_GROUP TG WITH(NOLOCK)
		ON TG.TAG_GROUP_ID = T.TAG_GROUP_ID
    WHERE ET.TAG_TYPE_ID = 1 -- Commodity
      AND ET.ENTITY_ID = @MASTER_ITEM_ID
END

-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 08/14/2023
-- Description: is client portal notification allowed
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IS_CLIENT_PORTAL_NOTIFICATION_ALLOWED] 
(
	@NOTIFICATION_TYPE_ID BIGINT
)
RETURNS BIT
AS
BEGIN
	IF(EXISTS(
		SELECT TOP(1) N.NOTIFICATION_ID
		FROM F_NOTIFICATION_SET				NS  WITH(NOLOCK)
		INNER JOIN F_NOTIFICATION_SET_GROUP	NSG WITH(NOLOCK)
			ON NSG.NOTIFICATION_SET_ID = NS.NOTIFICATION_SET_ID
		INNER JOIN F_NOTIFICATION			N	WITH(NOLOCK)
			ON N.NOTIFICATION_GROUP_ID = NSG.NOTIFICATION_GROUP_ID
		WHERE NS.IS_ACTIVE = 1
			AND NSG.IS_ACTIVE = 1
			AND N.NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
			AND N.IS_ACTIVE = 1
			AND N.NOTIFY_BY_EMAIL = 1
	))
    BEGIN
	   RETURN 1;
    END
    RETURN 0;
END
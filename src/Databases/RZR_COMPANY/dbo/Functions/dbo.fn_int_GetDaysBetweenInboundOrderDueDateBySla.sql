create function [dbo].[fn_int_GetDaysBetweenInboundOrderDueDateBySla]
(
	@SlaTypeId			bigint,
	@OrderDate			datetime,
	@ReceiptDate		datetime,
	@CompletedDate		datetime,
	@DeliveryDate		datetime,
	@SlaDays			int
)
returns int
as
begin
	declare @utcNow datetime = getutcdate();

	if (@ReceiptDate is null)
	begin
		set @ReceiptDate = @OrderDate;
	end

	if (@DeliveryDate is null)
	begin
		set @DeliveryDate = @ReceiptDate;
	end

	return case @SlaTypeId
		when 0 then @SlaDays - datediff(dd, @OrderDate, @utcNow)
		when 1 then @SlaDays - datediff(dd, @ReceiptDate, @utcNow)
		when 2 then [dbo].[fn_int_GetNumberOfWorkingDaysBetweenTwoDates](@utcNow, [dbo].[fn_datetime_GetDateAfterNWorkdays](@OrderDate, @SlaDays))
		when 3 then [dbo].[fn_int_GetNumberOfWorkingDaysBetweenTwoDates](@utcNow, [dbo].[fn_datetime_GetDateAfterNWorkdays](@ReceiptDate, @SlaDays))
		when 4 then @SlaDays - datediff(dd, @CompletedDate, @utcNow)
		when 5 then @SlaDays - datediff(dd, @DeliveryDate, @utcNow)
	end

end
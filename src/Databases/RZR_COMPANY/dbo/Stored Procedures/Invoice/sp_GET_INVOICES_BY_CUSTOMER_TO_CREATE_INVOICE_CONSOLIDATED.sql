--Exec sp_GET_INVOICES_BY_CUSTOMER_TO_CREATE_INVOICE_CONSOLIDATED @C_CUSTOMER_ID = 503
CREATE PROCEDURE [dbo].sp_GET_INVOICES_BY_CUSTOMER_TO_CREATE_INVOICE_CONSOLIDATED
	@C_CUSTOMER_ID BIGINT,
	@C_ORDER_COLUMN_NAME	VARCHAR(250) = N'InvoiceId',
	@C_ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@C_ITEMS_PER_PAGE		INT			= 20,
	@C_PAGE_INDEX			INT			= 0
AS
BEGIN
	
	;WITH CTE AS
	(
		SELECT 
			I.INVOICE_ID							AS InvoiceId,
			I.AUTO_NAME								AS InvoiceAutoName,
			I.ORDER_ID								AS SalesOrderId,
			I.ORDER_AUTO_NAME						AS SalesOrderNumber,
			I.DATE_CREATED							AS DateCreated,
			I.DATE_DUE								AS DateDue,
			i.ORIGINAL_AMOUNT						AS AmountOriginal,
			ISNULL(I.AMOUNT_DUE, 0)					AS AmoutDue,
			ISNULL(I.PAID_AMOUNT, 0)				AS AmountPaid
		
		FROM dbo.vw_AR_INVOICE	I WITH(NOLOCK)
		WHERE I.CUSTOMER_ID = @C_CUSTOMER_ID
			AND I.IS_INVOICE_CONSOLIDATED = 0
			AND I.CONSOLIDATED_ID IS NULL
			AND I.[IS_VOIDED] = 0
			AND I.PAID_AMOUNT = 0
			AND I.AMOUNT_DUE  > 0
			AND I.STATUS_ID IN (3) /*Final*/
	), cte_data as
	(
		SELECT 
		 ROW_NUMBER() OVER (ORDER BY 
			CASE WHEN ISNULL(@C_ORDER_COLUMN_NAME, 'InvoiceAutoName') = 'InvoiceAutoName' AND ISNULL(@C_ORDER_DIRECTION,'ASC') = 'ASC' THEN InvoiceAutoName END ASC,
			CASE WHEN ISNULL(@C_ORDER_COLUMN_NAME, 'InvoiceAutoName') = 'InvoiceAutoName' AND @C_ORDER_DIRECTION = 'DESC' THEN InvoiceAutoName END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'SalesOrderNumber' AND @C_ORDER_DIRECTION = 'ASC' THEN SalesOrderNumber END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'SalesOrderNumber' AND @C_ORDER_DIRECTION = 'DESC' THEN SalesOrderNumber END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'DateCreated' AND @C_ORDER_DIRECTION = 'ASC' THEN DateCreated END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'DateCreated' AND @C_ORDER_DIRECTION = 'DESC' THEN DateCreated END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'DateDue' AND @C_ORDER_DIRECTION = 'ASC' THEN DateDue END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'DateDue' AND @C_ORDER_DIRECTION = 'DESC' THEN DateDue END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmountOriginal' AND @C_ORDER_DIRECTION = 'ASC' THEN AmountOriginal END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmountOriginal' AND @C_ORDER_DIRECTION = 'DESC' THEN AmountOriginal END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmoutDue' AND @C_ORDER_DIRECTION = 'ASC' THEN AmoutDue END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmoutDue' AND @C_ORDER_DIRECTION = 'DESC' THEN AmoutDue END DESC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmountPaid' AND @C_ORDER_DIRECTION = 'ASC' THEN AmountPaid END ASC,
			CASE WHEN @C_ORDER_COLUMN_NAME = 'AmountPaid' AND @C_ORDER_DIRECTION = 'DESC' THEN AmountPaid END DESC) as nRow,
		InvoiceId,
		InvoiceAutoName,
		SalesOrderId,
		SalesOrderNumber,
		DateCreated,
		DateDue,
		AmountOriginal,
		AmoutDue,
		AmountPaid 
		FROM CTE
	)


	select  
	-1					as nRow,
	COUNT(InvoiceId)	AS InvoiceId,
	NULL				AS InvoiceAutoName,
	null				AS SalesOrderId,
	null				AS SalesOrderNumber,
	null				AS DateCreated,
	null				AS DateDue,
	null				AS AmountOriginal,
	null				AS AmoutDue,
	null				AS AmountPaid
	FROM cte_data
	UNION all
	SELECT * FROM 
	(
		SELECT * FROm cte_data
		ORDER BY nRow
		OFFSET((@C_PAGE_INDEX)* @C_ITEMS_PER_PAGE) ROWS
		FETCH NEXT @C_ITEMS_PER_PAGE ROWS ONLY
	) s
END

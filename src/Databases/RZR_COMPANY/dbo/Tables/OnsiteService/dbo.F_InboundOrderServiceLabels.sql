CREATE TABLE [dbo].[F_InboundOrderServiceLabels] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [ServiceTypeId]                             INT             NOT NULL,
    [LabelId]                                   INT             NOT NULL,
    [ActualQty]                                 INT             NULL,
    [ExpectedQty]                               INT             NULL,
    [Notes]                                     NVARCHAR (MAX)  NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderServiceLabels_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderServiceLabels_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceLabels_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceLabels_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderServiceLabels] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderServiceLabels_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceLabels_C_RECYCLING_ITEM_SERVICE_TYPE] FOREIGN KEY ([ServiceTypeId]) REFERENCES [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] ([SERVICE_TYPE_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceLabels_D_RecyclingServiceLabel] FOREIGN KEY ([LabelId]) REFERENCES [dbo].[D_RecyclingServiceLabel] ([Id]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderServiceLabels]([RecyclingOrderId])
    INCLUDE([ServiceTypeId], [LabelId]);
GO

CREATE NONCLUSTERED INDEX [IX_ServiceTypeId]
    ON [dbo].[F_InboundOrderServiceLabels]([ServiceTypeId])
    INCLUDE([RecyclingOrderId], [LabelId]);
GO

CREATE NONCLUSTERED INDEX [IX_CategoryId]
    ON [dbo].[F_InboundOrderServiceLabels]([LabelId])
    INCLUDE ([RecyclingOrderId], [ServiceTypeId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Labels for an Service Type in Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderServiceLabels';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_ServiceTypeId_LabelId]
    ON [dbo].[F_InboundOrderServiceLabels]([RecyclingOrderId] ASC, [ServiceTypeId] ASC, [LabelId] ASC);
       
GO
CREATE UNIQUE NONCLUSTERED INDEX [UX_RecyclingOrderId_ServiceTypeId_LabelId]
    ON [dbo].[F_InboundOrderServiceLabels] ([RecyclingOrderId], [ServiceTypeId], [LabelId]);


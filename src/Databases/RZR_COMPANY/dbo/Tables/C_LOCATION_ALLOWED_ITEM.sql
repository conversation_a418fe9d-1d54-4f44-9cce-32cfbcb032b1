CREATE TABLE [dbo].[C_LOCATION_ALLOWED_ITEM] (
    [LOCATION_ALLOWED_ITEM_ID]   INT           NOT NULL,
    [LOCATION_ALLOWED_ITEM_CD]   VARCHAR (50)  NOT NULL,
    [LOCATION_ALLOWED_ITEM_DESC] VARCHAR (250) NULL,
    [IS_INACTIVE]                BIT           CONSTRAINT [DF_C_LOCATION_ALLOWED_ITEM_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                 BIT           CONSTRAINT [DF_C_LOCATION_ALLOWED_ITEM_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                VARCHAR (250) CONSTRAINT [DF_C_LOCATION_ALLOWED_ITEM_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]                DATETIME      CONSTRAINT [DF_C_LOCATION_ALLOWED_ITEM_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 VARCHAR (250) NULL,
    [UPDATED_DT]                 DATETIME      NULL,
    [DELETED_BY]                 VARCHAR (250) NULL,
    [DELETED_DT]                 DATETIME      NULL,
    CONSTRAINT [PK_LOCATION_ALLOWED_ITEM] PRIMARY KEY CLUSTERED ([LOCATION_ALLOWED_ITEM_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Which types of locations is allowed for this type. The reference in F_LOCATION_DETAIL', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_LOCATION_ALLOWED_ITEM';


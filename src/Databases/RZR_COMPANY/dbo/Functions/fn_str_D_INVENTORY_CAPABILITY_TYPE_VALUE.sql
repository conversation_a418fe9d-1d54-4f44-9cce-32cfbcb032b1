-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT * FROM RZR_TBS.dbo.F_ITEM_INVENTORY_CAPABILITY WHERE ITEM_INVENTORY_ID = 1000
-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_VALUE(5494521, 46)
CREATE FUNCTION [dbo].[fn_str_D_INVENTORY_CAPABILITY_TYPE_VALUE]
(
	@C_ITEM_INVENTORY_ID bigint
	,@C_INVENTORY_CAPABILITY_TYPE_ID bigint
)
RETURNS varchar(max)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @C_RETURN_VAL varchar(max) = ''

	SELECT 
		@C_RETURN_VAL = dic.INVENTORY_CAPABILITY_VALUE
	FROM F_ITEM_INVENTORY_CAPABILITY			fiic WITH(NOLOCK)
	INNER JOIN dbo.C_INVENTORY_CAPABILITY_TYPE	cict WITH(NOLOCK)
		ON fiic.INVENTORY_CAPABILITY_TYPE_ID = cict.INVENTORY_CAPABILITY_TYPE_ID
	INNER JOIN dbo.D_INVENTORY_CAPABILITY		dic  WITH(NOLOCK)
		ON fiic.INVENTORY_CAPABILITY_ID = dic.INVENTORY_CAPABILITY_ID
	WHERE 
		ITEM_INVENTORY_ID = @C_ITEM_INVENTORY_ID
		AND cict.INVENTORY_CAPABILITY_TYPE_ID = @C_INVENTORY_CAPABILITY_TYPE_ID
	
	--IF (@C_RETURN_VAL = '') SET @C_RETURN_VAL = NULL
	
	-- Return the result of the function
	RETURN @C_RETURN_VAL

END

	--DECLARE @C_ITEM_MASTER_SKU_ATTRB_CD varchar(max) = '2-20|3-549|4-29|5-34|6-44|7-255|9-199|12-219|13-116|14-103'
	--DECLARE @C_INVENTORY_CAPABILITY_TYPE_ID bigint = 2
	
	
	--DECLARE @C_INVENTORY_CAPABILITY_TYPE_CD varchar(20) = CONVERT(varchar(19), @C_INVENTORY_CAPABILITY_TYPE_ID) + '-'
	--DECLARE @C_ITEM_CAPABILITY_TYPE_ID varchar(20)
		
	--IF (CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) > 0)
	--BEGIN
	
	--	SET
	--		@C_ITEM_CAPABILITY_TYPE_ID 
	--		=
	--		--CONVERT(bigint,
	--		SUBSTRING(@C_ITEM_MASTER_SKU_ATTRB_CD
	--					, CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) 
	--						+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD)
	--					, LEN(@C_ITEM_MASTER_SKU_ATTRB_CD) - 
	--						(
	--							CHARINDEX(@C_INVENTORY_CAPABILITY_TYPE_CD, @C_ITEM_MASTER_SKU_ATTRB_CD) 
	--							+ LEN(@C_INVENTORY_CAPABILITY_TYPE_CD)
	--						)
	--				)			
	--		--)

	--	IF (CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) > 0)
	--	SET
	--		@C_ITEM_CAPABILITY_TYPE_ID 
	--		=
	--		--CONVERT(bigint,
	--		SUBSTRING(@C_ITEM_CAPABILITY_TYPE_ID
	--					, 1
	--					, CHARINDEX('|', @C_ITEM_CAPABILITY_TYPE_ID) - 1
	--				)			
	--		--)

	--END
	--ELSE SELECT NULL

	--SELECT @C_ITEM_CAPABILITY_TYPE_ID
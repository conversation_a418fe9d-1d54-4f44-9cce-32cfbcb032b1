-- =============================================
-- Author:		<Author,,Name>
-- Create date: 07.20.2016
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE sp_GET_LOG_INFO_BY_ID
	@C_LOG_ID BIGINT
AS
BEGIN
	SELECT
		FSL.ENTRY_ID		AS LogId,
		FSL.EVENT_LEVEL		AS EventLevel,
		FSL.STACK_TRACE		AS StackTrace,
		FSL.CLASS			AS Class,
		FSL.EXCEPTION		AS Exception,
		FSL.LOGGER_NAME		AS LoggerName,
		FSL.METHOD			AS Method,
		FSL.[MESSAGE]		AS [Message],
		FSL.[URL]			AS [Url],
		FSL.THREAD			AS Thread,
		FSL.INSERTED_DT		AS LoggedDate
	FROM F_SYSTEM_LOG	FSL WITH(NOLOCK)
	WHERE FSL.ENTRY_ID = @C_LOG_ID
END
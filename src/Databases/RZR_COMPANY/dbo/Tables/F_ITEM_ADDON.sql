CREATE TABLE [dbo].[F_ITEM_ADDON] (
    [ITEM_ADDON_ID]     BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_ID]           BIGINT        NOT NULL,
    [ITEM_INVENTORY_ID] BIGINT        NOT NULL,
    [INSERTED_BY]       VARCHAR (150) CONSTRAINT [DF_F_ITEM_ADDON_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]       DATETIME      CONSTRAINT [DF_F_ITEM_ADDON_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_F_ITEM_ADDON] PRIMARY KEY CLUSTERED ([ITEM_ADDON_ID] ASC),
    CONSTRAINT [FK_F_ITEM_ADDON_F_ITEM] FOREIGN KEY ([ITEM_ID]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]),
    CONSTRAINT [FK_F_ITEM_ADDON_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Which inventory goes with which SKU.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_ADDON';


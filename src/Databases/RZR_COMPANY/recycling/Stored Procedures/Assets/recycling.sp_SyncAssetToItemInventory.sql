CREATE procedure [recycling].[sp_SyncAssetToItemInventory]
	@sourceAssetId				bigint
	,@capabilites				[dbo].[udtt_INVENTORY_CAPABILITY] READONLY
	,@isNoRebuildForInventory	bit = 0
	,@isPartialSyncAllowed		bit = 0
	,@outItemInventoryId		bigint = null output
	,@outerSpName				nvarchar(250) = null
	,@useInnerTransaction		bit = 1
	,@userId					bigint
	,@userIp					bigint
as
begin

	--TODO: think about async synchronization
	declare
		@INVENTORY_RECV_ID			bigint
		,@INVENTORY_RECV_UNIQUE_ID	varchar(250)
		,@INVENTORY_RECV_SERIAL		varchar(250)
		,@INVENTORY_RECV_NOTES		varchar(MAX)	= N''
		,@INVENTORY_CAPABILITIES	dbo.udtt_INVENTORY_CAPABILITY
		,@CUSTOMER_ID				bigint
		,@ITEM_MASTER_ID			bigint
		,@CONDITION_ID				bigint
		,@QTY_UNALLOCATED			bigint
		,@UNIT_COST_ORIGINAL		money			= 0.0
		,@priceTypeId				int
		,@WAREHOUSE_ID				bigint
		,@PRODUCT_CODE_ID			BIGINT		
		--,@PURCHASE_ORDER_ID		BIGINT TODO: check can we take from this point or not
		--,@C_CUSTOMER_CODE_ID		BIGINT
		--,@C_IS_CUSTOMER_CODE_ACT	BIT
		--,@C_IS_VIRTUAL				BIT
		--,@C_IS_SELECT				BIT
		,@C_LOCATION_ID				bigint
		,@C_LOCATION_NAME			varchar (150)
		,@C_IS_CONSIGNMENT			BIT
		--,@C_IS_DROP_SHIP_ITEM		BIT
		--,@O_ITEM_INVENTORY_ID		bigint

		,@inventoryAttributeTypeId	bigint
		,@IsUniqueDefect			bit
		,@weight					float
		,@auditStatusId				int
		,@isSkuGenerate				bit = 0
		,@recyclingOrderItemId		bigint
		,@isPartAdded				bit = 0
		,@spName					nvarchar(250) = isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid) + isnull(N' (' + @outerSpName + ')', '')
		,@now						datetime = getutcdate()

	--collect data from asset
	select
		@INVENTORY_RECV_ID			= isnull(ii.ITEM_INVENTORY_ID, -1)
		,@INVENTORY_RECV_UNIQUE_ID	= a.[UniqueId]
		,@INVENTORY_RECV_SERIAL		= isnull(a.[SerialNumber], '')
		,@INVENTORY_RECV_NOTES		= a.[Notes]
		,@CUSTOMER_ID				= a.[CustomerId]
		,@ITEM_MASTER_ID			= a.[ItemMasterId]
		,@CONDITION_ID				= a.[ConditionId]
		,@QTY_UNALLOCATED			= a.[Quantity]
		,@UNIT_COST_ORIGINAL		= a.[ItemPrice]
		,@priceTypeId				= a.[PriceTypeId]
		,@WAREHOUSE_ID				= l.[WAREHOUSE_ID]
		,@PRODUCT_CODE_ID			= a.[ProductCodeIdHeci]
		,@C_LOCATION_ID				= a.[LocationId]
		,@C_LOCATION_NAME			= l.[LOCATION_NAME]
		,@inventoryAttributeTypeId  = a.InventoryAttributeTypeId
		,@IsUniqueDefect			= a.[IsUniqueDefect]
		,@weight					= a.[Weight]
		,@recyclingOrderItemId		= a.[RecyclingOrderItemId]
		,@auditStatusId				= case a.[AssetWorkflowStepId] -- from this point we should update audit status cause this way is the most common for most of clients
			when 102 then 2
			when 103 then 3
			when 104 then 4
			else 1
		end
		,@isPartAdded				= iif(an.NestedAssetId is null, 0, 1)
		,@isSkuGenerate				= (aw.IsSkuGenerate & ~aw.IsFinal)
	from		recycling.F_Asset			as a	with(nolock)
	left join	dbo.F_ITEM_INVENTORY		as ii	with(nolock)
		on ii.AssetId = a.Id
	left join	dbo.F_LOCATION				as l	with(nolock)
		on a.LocationId = l.LOCATION_ID
	left join recycling.C_AssetWorkflowStep as aw	with(nolock)
		on a.AssetWorkflowStepId = aw.Id
	left join recycling.F_AssetNesting		as an	with(nolock)
		on a.Id = an.NestedAssetId
		and an.[State] = 1
	where a.Id = @sourceAssetId


	if (@useInnerTransaction = 1)
	begin
		begin transaction
		set xact_abort on
	end
		
		if (@isPartialSyncAllowed = 0)
		begin
			
			if (not exists(select top(1) 1 from @capabilites))
			begin
				--collect data from capabilities
				insert into @INVENTORY_CAPABILITIES (
					INVENTORY_CAPABILITY_TYPE_ID
					,INVENTORY_CAPABILITY_VALUE
				)
				select
					ic.INVENTORY_CAPABILITY_TYPE_ID
					,ic.INVENTORY_CAPABILITY_VALUE
				from	dbo.F_ITEM_INVENTORY				as i	with (nolock)
				inner join dbo.F_ITEM_INVENTORY_CAPABILITY	as iic	with(nolock)
					on i.ITEM_INVENTORY_ID = iic.ITEM_INVENTORY_ID
				inner join	dbo.D_INVENTORY_CAPABILITY		as ic	with(nolock)
					on iic.INVENTORY_CAPABILITY_ID = ic.INVENTORY_CAPABILITY_ID
				where i.AssetId = @sourceAssetId
					and iic.IS_DELETED = 0
			end
			else
				insert into @INVENTORY_CAPABILITIES
				select * from @capabilites

			--collect additional data
			--collect data abgout consignment
			set @C_IS_CONSIGNMENT = dbo.fn_bit_IS_RECYCLING_ORDER_ITEM_ID_IN_CONSIGMENT(@RecyclingOrderItemId);

			--collect data about UniqueDefect
			if (@IsUniqueDefect = 1)
			begin
				insert into @INVENTORY_CAPABILITIES (
					INVENTORY_CAPABILITY_TYPE_ID
					,INVENTORY_CAPABILITY_VALUE
				)
				values (
					53
					,ISNULL(NULLIF(LTRIM(@INVENTORY_RECV_SERIAL), ''), @INVENTORY_RECV_UNIQUE_ID)
				)
			end
			else
			begin
				delete from @INVENTORY_CAPABILITIES
				where INVENTORY_CAPABILITY_TYPE_ID = 53
			end

			--call [dbo].[sp_SET_INVENTORY_RECV] for basic syncing
			exec [dbo].[sp_SET_INVENTORY_RECV]
				@INVENTORY_RECV_ID			= @INVENTORY_RECV_ID
				,@INVENTORY_RECV_UNIQUE_ID	= @INVENTORY_RECV_UNIQUE_ID
				,@INVENTORY_RECV_SERIAL		= @INVENTORY_RECV_SERIAL
				,@INVENTORY_RECV_NOTES		= @INVENTORY_RECV_NOTES
				,@INVENTORY_CAPABILITIES	= @INVENTORY_CAPABILITIES
				,@CUSTOMER_ID				= @CUSTOMER_ID
				,@ITEM_MASTER_ID			= @ITEM_MASTER_ID
				,@CONDITION_ID				= @CONDITION_ID
				,@QTY_UNALLOCATED			= @QTY_UNALLOCATED
				,@UNIT_COST_ORIGINAL		= @UNIT_COST_ORIGINAL
				,@WAREHOUSE_ID				= @WAREHOUSE_ID
				,@PRODUCT_CODE_ID			= @PRODUCT_CODE_ID
				--,@PURCHASE_ORDER_ID		= @PURCHASE_ORDER_ID
				--,@C_CUSTOMER_CODE_ID		= @C_CUSTOMER_CODE_ID
				--,@C_IS_CUSTOMER_CODE_ACT	= @C_IS_CUSTOMER_CODE_ACT
				--,@C_IS_VIRTUAL			= @C_IS_VIRTUAL
				,@C_IS_SELECT				= 0
				,@C_LOCATION_ID				= @C_LOCATION_ID
				,@C_LOCATION_NAME			= @C_LOCATION_NAME
				,@C_IS_CONSIGNMENT_PO		= @C_IS_CONSIGNMENT
				--,@C_IS_DROP_SHIP_ITEM		= @C_IS_DROP_SHIP_ITEM
				,@O_ITEM_INVENTORY_ID		= @outItemInventoryId output
				,@SYNC_WITH_ASSET			= 2
				,@USER_ID					= @userId
				,@USER_IP					= @userIp
				,@AUDIT_STATUS_ID			= @auditStatusId
				,@IS_WORKFLOW_SKU_GENERATE	= @isSkuGenerate
		end

		--but after that we will sync additional fields
		--TODO: someday this should be refactored
		update ii_t set
			ii_t.AssetId							= @sourceAssetId
			,ii_t.ITEM_STATUS_ID					= case
				when ii_t.ITEM_STATUS_ID = 1 and @auditStatusId = 2 and @isPartAdded = 1
					then 12 --if unallocated and final step is resale and part added, set 12
				when ii_t.ITEM_STATUS_ID = 9 and @auditStatusId = 2
					then 1 -- if Consumed and final step is resale set unallocated
				when ii_t.ITEM_STATUS_ID in (1, 8, 14)
					then iif(@isSkuGenerate = 1, 14, iif(@auditStatusId = 2, 1, 8)) -- 14 - SKU_GENERATE_ItemStatus
				else ii_t.ITEM_STATUS_ID
			end
			,ii_t.AUDIT_STATUS_ID					= @auditStatusId
			,ii_t.RECYCLING_ORDER_ITEM_ID			= @RecyclingOrderItemId
			,ii_t.[WEIGHT]							= @Weight
			,ii_t.PRICE_TYPE_ID						= @priceTypeId
			,ii_t.UPDATED_BY						= substring(@spName, 1, 150)
			,ii_t.UPDATED_DT						= @now
			,ii_t.UPDATED_BY_IP						= @userIp
			,ii_t.MODIFIER_USER_ID					= @userId
		from dbo.F_ITEM_INVENTORY	as ii_t		with(rowlock)
		where ii_t.ITEM_INVENTORY_ID = @outItemInventoryId

	if (@useInnerTransaction = 1)
	begin
		commit transaction
	end

end

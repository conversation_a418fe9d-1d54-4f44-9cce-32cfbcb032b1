-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_FROM_BASE64]
(
	@BASE64_STRING VARCHAR(MAX)
)
RETURNS VARCHAR(MAX)
AS
BEGIN
	RETURN (
		SELECT 
			CAST(
				CAST(N'' AS XML).value('xs:base64Binary(sql:variable("@BASE64_STRING"))', 'VARBINARY(MAX)') 
				AS VARCHAR(MAX)
			)   UTF8Encoding
	)
END --'xs:base64Binary("VGVzdERhdGE=")'
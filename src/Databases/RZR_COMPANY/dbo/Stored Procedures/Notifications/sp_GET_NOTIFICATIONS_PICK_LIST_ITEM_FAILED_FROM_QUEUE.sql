CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_PICK_LIST_ITEM_FAILED_FROM_QUEUE]
	@C_COUNT INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID		BIGINT = 14
	DECLARE @IS_ALLOWED					BIT = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID)

	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY = N'sp_GET_NOTIFICATIONS_PICK_LIST_ITEM_FAILED_FROM_QUEUE',
			UPDATED_DT = GETUTCDATE()
		WHERE IS_INACTIVE = 0 AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID		
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP (@C_COUNT)
		  NQ.NotificationQueueId			as [NOTIFICATION_QUEUE_ID]
			,DU.*
			,PL.Id							as PICK_LIST_ID
			,PL.Name						as PICK_LIST_NAME
			,II.ITEM_INVENTORY_SERIAL		as PICK_LIST_ITEM_SERIAL
			,II.ITEM_INVENTORY_UNIQUE_ID	as PICK_LIST_ITEM_UNIQUE_ID
			,IIS.STATUS_CD as PICK_LIST_ITEM_STATUS_CD
			,PLI.FailedNote as PICK_LIST_ITEM_REASON
		  ,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(14, QP.REP_ID, NULL, NULL, NQ.NotificationSetId, NQ.CustomerId) AS EMAILS
		from @tNotifyItemWithNotifySet					NQ
		cross apply	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
		cross apply [dbo].[tvf_GET_SNIPPET_DATA_USER]						(QP.REP_ID)						    DU
		left join F_PickListAllocatedItem			PLI with (nolock)
			on PLI.ItemInventoryId  = QP.PICK_LIST_ITEM_ID
		left join F_ITEM_INVENTORY					II with (nolock)
			on II.ITEM_INVENTORY_ID = PLI.ItemInventoryId
		left JOIN D_ITEM_INVENTORY_STATUS			IIS with (nolock)
			on IIS.ITEM_INVENTORY_STATUS_ID = II.ITEM_STATUS_ID
		left JOIN F_PickList						PL with (nolock)
			on PL.Id = QP.PICK_LIST_ID
	   where NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
	   and QP.PICK_LIST_ITEM_ID IS NOT NULL
	
END
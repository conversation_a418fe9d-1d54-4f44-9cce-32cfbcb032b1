-- =============================================
-- Author:	 <Maks<PERSON>>
-- Create date: <2020-11-10>
-- Description:
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_ORDER_OUTBOUND]
(
	@RECYCLING_ORDER_ID	   BIGINT,
	@NOTIFICATION_TYPE_ID  BIGINT,
	@IGNORE_SETTINGS	   BIT = 0,
	@NOTIFICATION_SET_ID   BIGINT
)
RETURNS @T_RESULT TABLE(
	   LoadedDate NVARCHAR(20)
    )
AS
BEGIN
    DECLARE @T_ORDERS_OUTBOUND TABLE(
	   RECYCLING_ORDER_ID BIGINT,
	   CUSTOMER_ID		  BIGINT,
	   [USER_ID]		  BIGINT
    )
    IF (@IGNORE_SETTINGS != 1)
    BEGIN
       -- have notification settings - take data about these orders.
	   -- join it to the order id from the queue to throw away spare rows
	   INSERT INTO @T_ORDERS_OUTBOUND (
		  RECYCLING_ORDER_ID,
		  CUSTOMER_ID,
		  [USER_ID]
	   )
	   SELECT DISTINCT
		  NGS.ORDER_ID,
		  O.CUSTOMER_ID,
		  O.[USER_ID]
	   FROM F_NOTIFICATION_GROUP_SCOPE		   NGS WITH(NOLOCK)
	   INNER JOIN F_NOTIFICATION_SET_GROUP	   NSG WITH(NOLOCK)
		ON NSG.NOTIFICATION_GROUP_ID = NGS.NOTIFICATION_GROUP_ID
	   INNER JOIN F_NOTIFICATION			   N	  WITH(NOLOCK)
		ON N.NOTIFICATION_GROUP_ID = NSG.NOTIFICATION_GROUP_ID
	   INNER JOIN F_RECYCLING_ORDER		   O	  WITH (NOLOCK)
		ON O.RECYCLING_ORDER_ID = NGS.ORDER_ID
	   WHERE NSG.NOTIFICATION_SET_ID = @NOTIFICATION_SET_ID
    		AND N.NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
    		AND NGS.ORDER_ID IS NOT NULL
		AND O.IS_INBOUND = 0
    END

    IF (@IGNORE_SETTINGS != 1 AND EXISTS(SELECT TOP(1) 1 FROM @T_ORDERS_OUTBOUND))
    BEGIN
	   -- ignore all the orders except the ones in @T_ORDERS_OUTBOUND (if there will be some)
	   INSERT INTO @T_RESULT (
		  LoadedDate
	   )
	   SELECT
		   T.LOADED_DT
	   FROM (
		  SELECT
			 R.LOADED_DT
		  FROM @T_ORDERS_OUTBOUND				  O
		  INNER JOIN F_RECYCLING_ORDER_OUTBOUND	  R	 WITH(NOLOCK)
		    ON R.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
		  WHERE R.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	   ) AS T
    END
    ELSE BEGIN
	   -- no order-specific notifications settings - take the requested row
	   INSERT INTO @T_RESULT (
		  LoadedDate
	   )
	   SELECT
		  T.LOADED_DT
	   FROM (
		  SELECT
		      R.LOADED_DT
		  FROM F_RECYCLING_ORDER_OUTBOUND			  R  WITH(NOLOCK)
		  WHERE R.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	   ) AS T
    END
    RETURN;
END
go
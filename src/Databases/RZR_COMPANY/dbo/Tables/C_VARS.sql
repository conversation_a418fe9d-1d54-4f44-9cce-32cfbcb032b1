CREATE TABLE [dbo].[C_VARS] (
    [VAR_ID]      BIGINT        NULL,
    [VAR_NAME]    VARCHAR (100) NULL,
    [VAR_VALUE]   VARCHAR (MAX) NULL,
    [IS_INACTIVE] BIT           CONSTRAINT [DF_C_VARS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]  BIT           CONSTRAINT [DF_C_VARS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY] VARCHAR (150) CONSTRAINT [DF_C_VARS_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT] DATETIME      CONSTRAINT [DF_C_VARS_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]  VARCHAR (150) NULL,
    [UPDATED_DT]  DATETIME      NULL,
    [DELETED_BY]  VARCHAR (150) NULL,
    [DELETED_DT]  DATETIME      NULL
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_VARS';


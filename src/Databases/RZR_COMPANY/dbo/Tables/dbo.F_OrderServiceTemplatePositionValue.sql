CREATE TABLE [dbo].[F_OrderServiceTemplatePositionValue] (
    [Id]                                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                                          BIGINT          NULL,
    [AttributeTypeId]                                           INT             NOT NULL,
    [ServiceSubcategoryPositionId]                              BIGINT          NULL,
    [ChargeValue]                                               DECIMAL (18, 4) CONSTRAINT [DF_F_OrderServiceTemplatePositionValue_ChargeValue] DEFAULT ((0)) NOT NULL,
    [Pos]                                                       INT             NOT NULL,
    [IsRequired]                                                BIT             CONSTRAINT [DF_F_OrderServiceTemplatePositionValue_IsRequired] DEFAULT ((0)) NOT NULL,
    [IsSelected]                                                BIT             CONSTRAINT [DF_F_OrderServiceTemplatePositionValue_IsSelected] DEFAULT ((0)) NOT NULL,
    [IsRadioButton]                                             BIT             CONSTRAINT [DF_F_OrderServiceTemplatePositionValue_IsRadioButton] DEFAULT ((0)) NOT NULL,
    [IsDeleted]                                                 BIT             CONSTRAINT [DF_F_OrderServiceTemplatePositionValue_IsDeleted] DEFAULT ((0)) NOT NULL,
    [InsertedBy]                                                NVARCHAR (150)  NOT NULL,
    [InsertedDate]                                              DATETIME        NOT NULL,
    [InsertedByUserId]                                          BIGINT          NULL,
    [InsertedByUserIp]                                          BIGINT          NULL,
    [UpdatedBy]                                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                                               DATETIME        NULL,
    [UpdatedByUserId]                                           BIGINT          NULL,
    [UpdatedByUserIp]                                           BIGINT          NULL,
    [DeletedBy]                                                 NVARCHAR (150)  NULL,
    [DeletedDate]                                               DATETIME        NULL,
    [DeletedByUserId]                                           BIGINT          NULL,
    [DeletedByUserIp]                                           BIGINT          NULL,
    CONSTRAINT [PK_F_OrderServiceTemplatePositionValue] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE, 
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([AttributeTypeId]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_D_ServiceSubCategoryPosition] FOREIGN KEY ([ServiceSubcategoryPositionId]) REFERENCES [dbo].[D_ServiceSubcategoryPosition] ([Id]),
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_tb_User] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_tb_User1] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_OrderServiceTemplatePositionValue_tb_User2] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_ServiceSubcategoryPositionId]
    ON [dbo].[F_OrderServiceTemplatePositionValue]([RecyclingOrderId] ASC, [ServiceSubcategoryPositionId] ASC);


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId]
    ON [dbo].[F_OrderServiceTemplatePositionValue]([RecyclingOrderId] ASC);


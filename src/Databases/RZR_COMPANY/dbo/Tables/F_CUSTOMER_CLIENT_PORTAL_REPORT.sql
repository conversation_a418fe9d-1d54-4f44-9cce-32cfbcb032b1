CREATE TABLE [dbo].[F_CUSTOMER_CLIENT_PORTAL_REPORT] (
    [CUSTOMER_CLIENT_PORTAL_REPORT_ID]   BIGINT         IDENTITY (1, 1) NOT NULL,
    [CUSTOMER_ID]                        BIGINT         NOT NULL,
    [CLIENT_PORTAL_ORDER_REPORT_TYPE_ID] INT            NOT NULL,
    [IS_ENABLED]                         BIT            NOT NULL,
    [INSERTED_BY]                        NVARCHAR (150) NULL,
    [INSERTED_DT]                        DATETIME       NULL,
    [UPDATED_BY]                         NVARCHAR (150) NULL,
    [UPDATED_DT]                         DATETIME       NULL,
    CONSTRAINT [PK_F_CUSTOMER_CLIENT_PORTAL_REPORT] PRIMARY KEY CLUSTERED ([CUSTOMER_CLIENT_PORTAL_REPORT_ID] ASC),
    CONSTRAINT [FK_F_CUSTOMER_CLIENT_PORTAL_REPORT_C_CLIENT_PORTAL_ORDER_TYPE] FOREIGN KEY ([CLIENT_PORTAL_ORDER_REPORT_TYPE_ID]) REFERENCES [dbo].[C_CLIENT_PORTAL_ORDER_REPORT_TYPE] ([CLIENT_PORTAL_ORDER_REPORT_TYPE_ID]) ON UPDATE CASCADE,
    CONSTRAINT [FK_F_CUSTOMER_CLIENT_PORTAL_REPORT_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The table specifies which report types are allowed for this customer on the client portal', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CUSTOMER_CLIENT_PORTAL_REPORT';

GO
CREATE NONCLUSTERED INDEX [IX_F_CUSTOMER_CLIENT_PORTAL_REPORT_CUSTOMER_ID_CLIENT_PORTAL_ORDER_REPORT_TYPE_ID]
    ON [dbo].[F_CUSTOMER_CLIENT_PORTAL_REPORT] ([CUSTOMER_ID],[CLIENT_PORTAL_ORDER_REPORT_TYPE_ID])
    INCLUDE ([IS_ENABLED]);
GO
CREATE TABLE [dbo].[F_CONTRACT_RESALE_PRICING] (
    [CONTRACT_RESALE_PRICING_ID]   BIGINT         IDENTITY (1, 1) NOT NULL,
    [CONTRACT_RESALE_PRICING_NAME] NVARCHAR (256) NOT NULL,
    [CUSTOMER_ID]                  BIGINT         NULL,
    [USER_ID]                      BIGINT         NULL,
    [IS_DELETED]                   BIT            CONSTRAINT [DF_F_CONTRACT_RESALE_PRICING_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                  VARCHAR (150)  CONSTRAINT [DF_F_CONTRACT_RESALE_PRICING_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]                  DATETIME       CONSTRAINT [DF_F_CONTRACT_RESALE_PRICING_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                   VARCHAR (150)  NULL,
    [UPDATED_DT]                   DATETIME       NULL,
    [DELETED_BY]                   VARCHAR (150)  NULL,
    [DELETED_DT]                   DATETIME       NULL,
    [IsGlobal]                     BIT            CONSTRAINT [DF_F_CONTRACT_RESALE_PRICING_IsGlobal] DEFAULT ((0)) NULL,
    [GlobalPricingId]              BIGINT         NULL,
    CONSTRAINT [PK_F_CONTRACT_RESALE_PRICING] PRIMARY KEY CLUSTERED ([CONTRACT_RESALE_PRICING_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_RESALE_PRICING_F_CONTRACT_RESALE_PRICING1] FOREIGN KEY ([GlobalPricingId]) REFERENCES [dbo].[F_CONTRACT_RESALE_PRICING] ([CONTRACT_RESALE_PRICING_ID]),
    CONSTRAINT [FK_F_CONTRACT_RESALE_PRICING_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_CONTRACT_RESALE_PRICING_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);








GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is the Recycling -> ITAD FMV Price Sheet details', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_RESALE_PRICING';


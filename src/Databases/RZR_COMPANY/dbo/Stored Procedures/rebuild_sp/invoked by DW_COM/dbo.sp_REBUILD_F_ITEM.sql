-- =============================================
-- Author:		<PERSON>
-- Edit date:   04/29/2013
-- Description:	<Description,,>
-- =============================================
-- SELECT * FROM F_ITEM 
-- EXEC sp_REBUILD_F_ITEM @C_BUILD_ID = 1, @C_ITEM_MASTER_ID = 1945252
-- EXEC sp_REBUILD_F_ITEM @C_BUILD_ID = 1, @@C_ITEM_IDS= '<Root><Items ID="1946987" /><Items ID="1946988" /></Root>'
CREATE PROCEDURE [dbo].[sp_REBUILD_F_ITEM] (
	@C_BUILD_ID			INT		= 0
	,@C_ITEM_MASTER_ID	BIGINT	= 0
	,@C_ITEM_ID			bigint	= 0
	,@C_ITEM_MASTER_IDS	XML		= '<Root/>' --'<Root><Items ID="1946987" /><Items ID="1946988" /></Root>'
	,@C_ITEM_IDS		XML		= '<Root/>' --'<Root><Items ID="1946987" /><Items ID="1946988" /></Root>'
	,@C_IS_DEBUG		bit		= 0
)
AS
BEGIN

	DECLARE @C_PROCESS_CD VARCHAR(150) = [DW_COM].[dbo].[fn_str_GET_BUILD_PROCESS_CD](@@SERVERNAME, @C_BUILD_ID, DB_NAME() + '_' + OBJECT_NAME(@@PROCID))

	DECLARE @T_ITEM_MASTER_IDs	dbo.bigint_ID_ARRAY
		INSERT INTO @T_ITEM_MASTER_IDs
		SELECT
			ID
		FROM dbo.fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY(@C_ITEM_MASTER_IDs, 'Items')
		WHERE @C_ITEM_MASTER_IDS IS NOT NULL
		UNION
		SELECT @C_ITEM_MASTER_ID
		WHERE isnull(@C_ITEM_MASTER_ID, 0) > 0
	
	DECLARE @T_ITEM_IDs	dbo.bigint_ID_ARRAY
		INSERT INTO @T_ITEM_IDs
		SELECT
			ID
		FROM dbo.fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY(@C_ITEM_IDS, 'Items')
		WHERE @C_ITEM_IDS IS NOT NULL
		UNION
		SELECT @C_ITEM_ID
		WHERE isnull(@C_ITEM_ID, 0) > 0

	exec [dbo].[sp_REBUILD_F_ITEM_SKU_QTY]
		@T_ITEM_MASTER_IDs	= @T_ITEM_MASTER_IDs
		,@T_ITEM_IDs		= @T_ITEM_IDs
		,@C_PROCESS_CD		= @C_PROCESS_CD
		,@C_IS_DEBUG		= @C_IS_DEBUG
	
END

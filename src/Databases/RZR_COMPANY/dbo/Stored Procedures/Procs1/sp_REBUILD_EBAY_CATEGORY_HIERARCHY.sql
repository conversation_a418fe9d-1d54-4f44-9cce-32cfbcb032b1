-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_REBUILD_EBAY_CATEGORY_HIERARCHY]

AS
BEGIN
SET XACT_ABORT ON
	BEGIN TRAN
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET		
		CATEGORYNAME = cht.CATEGORYNAME,
		EBAY_CATEGORY_FULL_PATH = cht.EBAY_CATEGORY_FULL_PATH,
		PARENT_ID = cht.PARENT_ID,
		CATEGORY_KEY = cht.CATEGORY_KEY,
		CATEGORY_LEVEL = cht.CATEGORY_LEVEL,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP cht
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch 
		ON cht.CATEGORY_ID = ch.CATEGORY_ID
		
	INSERT INTO dbo.D_EBAY_CATEGORY_HIERARCHY 
		(
			CATEGORY_ID,
			CATEGORYNAME,
			EBAY_CATEGORY_FULL_PATH,
			PARENT_ID,
			CATEGORY_KEY,
			CATEGORY_LEVEL,
			INSERTED_BY,
			INSERTED_DT
		)
	SELECT 		
		cht.CATEGORY_ID,
		cht.CATEGORYNAME,
		cht.EBAY_CATEGORY_FULL_PATH,
		cht.PARENT_ID,
		cht.CATEGORY_KEY,
		cht.CATEGORY_LEVEL,
		'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP cht	WITH (NOLOCK)
	LEFT JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch WITH (NOLOCK)
		ON cht.CATEGORY_ID = ch.CATEGORY_ID
	WHERE ch.CATEGORY_ID IS NULL			
	
	--SEVERAL OLD TO ONE NEW AND ONE NEW TO SEVERAL OLD		
	
	UPDATE dbo.D_EBAY_CATEGORY_MAPPING SET
		IS_IN_WORK = 1,
		STEP_ID = 1
	WHERE OLD_CATEGORY_ID IN
	(
		SELECT OLD_CATEGORY_ID
		FROM dbo.D_EBAY_CATEGORY_MAPPING
		WHERE OLD_CATEGORY_ID IN
		(
			SELECT OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING
			WHERE CATEGORY_ID IN
				(	
					SELECT cm.CATEGORY_ID
					FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
					GROUP BY cm.CATEGORY_ID
					HAVING COUNT(cm.OLD_CATEGORY_ID) > 1
				)		
		) 
		GROUP BY OLD_CATEGORY_ID
		HAVING COUNT(CATEGORY_ID) > 1	
	)	
		
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP SET
		IS_IN_WORK = 1		
	WHERE CATEGORY_ID IN
		(	
			SELECT cm.OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
			WHERE cm.IS_IN_WORK = 1
		)

	INSERT INTO dbo.S_EBAY_CATEGORY_HIERARCHY	
		(
			CATEGORY_ID, 
			CATEGORYNAME, 
			EBAY_CATEGORY_FULL_PATH, 
			PARENT_ID,
			MAPPED_CATEGORY_ID,
			CATEGORY_KEY,
			CATEGORY_LEVEL,
			RECENTLY_USED_DT,
			MASTER_ITEM_ID,
			INSERTED_BY,
			INSERTED_DT
		)	
	SELECT DISTINCT	
		ch.CATEGORY_ID, 
		ch.CATEGORYNAME, 
		ch.EBAY_CATEGORY_FULL_PATH, 
		ch.PARENT_ID,
		ch.MAPPED_CATEGORY_ID,
		ch.CATEGORY_KEY,
		ch.CATEGORY_LEVEL,
		ch.RECENTLY_USED_DT,
		im.ITEM_MASTER_ID,
		'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		GETUTCDATE()
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1		
				
	UPDATE 	dbo.F_ITEM_MASTER SET
		EBAY_CATEGORY_ID = NULL,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1
		
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
		MAPPED_CATEGORY_ID = t.MAPPED_CATEGORY_ID,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1
	INNER JOIN
	(		
		SELECT cm.CATEGORY_ID, MAX(ch.MAPPED_CATEGORY_ID) AS MAPPED_CATEGORY_ID
		FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
		INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
			ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1
		GROUP BY cm.CATEGORY_ID									
	) t		
		ON cm.CATEGORY_ID = t.CATEGORY_ID
		
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY
	WHERE CATEGORY_ID IN 
		(
			SELECT OLD_CATEGORY_ID FROM dbo.D_EBAY_CATEGORY_MAPPING WHERE IS_IN_WORK = 1
		)	
		
	DELETE FROM dbo.D_EBAY_CATEGORY_MAPPING	
	WHERE IS_IN_WORK = 1
	
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP
	WHERE IS_IN_WORK = 1					
	
	--SEVERAL OLD TO ONE NEW AND ONE NEW TO SEVERAL OLD
	
	--SEVERAL OLD TO ONE NEW
	
	UPDATE dbo.D_EBAY_CATEGORY_MAPPING SET
		IS_IN_WORK = 1,
		STEP_ID = 1
	WHERE CATEGORY_ID IN
		(	
			SELECT cm.CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
			GROUP BY cm.CATEGORY_ID
			HAVING COUNT(cm.OLD_CATEGORY_ID) > 1
		)	
		
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP SET
		IS_IN_WORK = 1		
	WHERE CATEGORY_ID IN
		(	
			SELECT cm.OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
			WHERE cm.IS_IN_WORK = 1
		)
					
	UPDATE dbo.F_ITEM_MASTER SET
		EBAY_CATEGORY_ID = 
			(
				SELECT TOP(1)
					chn.EBAY_CATEGORY_HIERARCHY_ID 
				FROM dbo.D_EBAY_CATEGORY_HIERARCHY chn WITH (NOLOCK)
				WHERE chn.CATEGORY_ID = cm.CATEGORY_ID
			),
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()			
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1			
	
	UPDATE dbo.D_EBAY_CATEGORY_MAPPING SET
		STEP_ID = 2
	FROM dbo.D_EBAY_CATEGORY_MAPPING cm
	WHERE cm.CATEGORY_ID IN
		(
			SELECT cm.CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
			INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
				ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1	
			GROUP BY cm.CATEGORY_ID	
			HAVING COUNT(DISTINCT ch.MAPPED_CATEGORY_ID) = 1
		)
		
	INSERT INTO dbo.S_EBAY_CATEGORY_HIERARCHY	
		(
			CATEGORY_ID, 
			CATEGORYNAME, 
			EBAY_CATEGORY_FULL_PATH, 
			PARENT_ID,
			MAPPED_CATEGORY_ID,
			CATEGORY_KEY,
			CATEGORY_LEVEL,
			RECENTLY_USED_DT,
			INSERTED_BY,
			INSERTED_DT
		)	
	SELECT 		
		CATEGORY_ID, 
		CATEGORYNAME, 
		EBAY_CATEGORY_FULL_PATH, 
		PARENT_ID,
		MAPPED_CATEGORY_ID,
		CATEGORY_KEY,
		CATEGORY_LEVEL,
		RECENTLY_USED_DT,
		'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY	WITH (NOLOCK)
	WHERE CATEGORY_ID IN 
		(
			SELECT OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING
			WHERE IS_IN_WORK = 1 AND STEP_ID = 1	
		)
						
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
		MAPPED_CATEGORY_ID = t.MAPPED_CATEGORY_ID,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.CATEGORY_ID AND cm.STEP_ID = 2
	INNER JOIN
	(
		SELECT cm.CATEGORY_ID, MAX(ch.MAPPED_CATEGORY_ID) AS MAPPED_CATEGORY_ID
		FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
		INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
			ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.STEP_ID = 2
		GROUP BY cm.CATEGORY_ID					
	) t		
		ON cm.CATEGORY_ID = t.CATEGORY_ID			
		
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY
	WHERE CATEGORY_ID IN 
		(
			SELECT OLD_CATEGORY_ID FROM dbo.D_EBAY_CATEGORY_MAPPING WHERE IS_IN_WORK = 1
		)	
		
	DELETE FROM dbo.D_EBAY_CATEGORY_MAPPING	
	WHERE IS_IN_WORK = 1
	
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP
	WHERE IS_IN_WORK = 1
	
	--SEVERAL OLD TO ONE NEW
	
	--ONE OLD TO SEVERAL NEW
	
	UPDATE dbo.D_EBAY_CATEGORY_MAPPING SET
		IS_IN_WORK = 1,
		STEP_ID = 1
	WHERE OLD_CATEGORY_ID IN
		(	
			SELECT cm.OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
			GROUP BY cm.OLD_CATEGORY_ID
			HAVING COUNT(cm.CATEGORY_ID) > 1
		)	
		
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP SET
		IS_IN_WORK = 1		
	WHERE CATEGORY_ID IN
		(	
			SELECT cm.OLD_CATEGORY_ID
			FROM dbo.D_EBAY_CATEGORY_MAPPING cm				
			WHERE cm.IS_IN_WORK = 1
		)			
		
	INSERT INTO dbo.S_EBAY_CATEGORY_HIERARCHY	
		(
			CATEGORY_ID, 
			CATEGORYNAME, 
			EBAY_CATEGORY_FULL_PATH, 
			PARENT_ID,
			MAPPED_CATEGORY_ID,
			CATEGORY_KEY,
			CATEGORY_LEVEL,
			RECENTLY_USED_DT,
			MASTER_ITEM_ID,
			INSERTED_BY,
			INSERTED_DT
		)	
	SELECT DISTINCT	
		ch.CATEGORY_ID, 
		ch.CATEGORYNAME, 
		ch.EBAY_CATEGORY_FULL_PATH, 
		ch.PARENT_ID,
		ch.MAPPED_CATEGORY_ID,
		ch.CATEGORY_KEY,
		ch.CATEGORY_LEVEL,
		ch.RECENTLY_USED_DT,
		im.ITEM_MASTER_ID,
		'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		GETUTCDATE()
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1						
		
	UPDATE 	dbo.F_ITEM_MASTER SET
		EBAY_CATEGORY_ID = NULL,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1	
		
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
		MAPPED_CATEGORY_ID = t.MAPPED_CATEGORY_ID,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1
	INNER JOIN
	(		
		SELECT cm.CATEGORY_ID, MAX(ch.MAPPED_CATEGORY_ID) AS MAPPED_CATEGORY_ID
		FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
		INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
			ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1
		GROUP BY cm.CATEGORY_ID									
	) t		
		ON cm.CATEGORY_ID = t.CATEGORY_ID
		
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY
	WHERE CATEGORY_ID IN 
		(
			SELECT OLD_CATEGORY_ID FROM dbo.D_EBAY_CATEGORY_MAPPING WHERE IS_IN_WORK = 1
		)	
		
	DELETE FROM dbo.D_EBAY_CATEGORY_MAPPING	
	WHERE IS_IN_WORK = 1
	
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP
	WHERE IS_IN_WORK = 1
	
	--ONE OLD TO SEVERAL NEW
	
	--ONE OLD TO ONE NEW
	
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP SET
		IS_IN_WORK = 1
		
	UPDATE 	dbo.D_EBAY_CATEGORY_MAPPING	SET
		IS_IN_WORK = 1,
		STEP_ID = 1	
	
	UPDATE 	dbo.F_ITEM_MASTER SET
		EBAY_CATEGORY_ID = 
			(
				SELECT top(1)
					chn.EBAY_CATEGORY_HIERARCHY_ID 
				FROM dbo.D_EBAY_CATEGORY_HIERARCHY chn WITH (NOLOCK)
				WHERE chn.CATEGORY_ID = cm.CATEGORY_ID
			),
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.F_ITEM_MASTER im
	INNER JOIN dbo.D_EBAY_CATEGORY_HIERARCHY ch
		ON im.EBAY_CATEGORY_ID = ch.EBAY_CATEGORY_HIERARCHY_ID
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 
	
	UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
		MAPPED_CATEGORY_ID = t.MAPPED_CATEGORY_ID,
		UPDATED_BY = 'sp_REBUILD_EBAY_CATEGORY_HIERARCHY',
		UPDATED_DT = GETUTCDATE()
	FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
	INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
		ON ch.CATEGORY_ID = cm.CATEGORY_ID
	INNER JOIN
	(
		SELECT cm.CATEGORY_ID, ch.MAPPED_CATEGORY_ID
		FROM dbo.D_EBAY_CATEGORY_HIERARCHY ch
		INNER JOIN dbo.D_EBAY_CATEGORY_MAPPING cm
			ON ch.CATEGORY_ID = cm.OLD_CATEGORY_ID AND cm.IS_IN_WORK = 1 AND cm.STEP_ID = 1		
	) t		
		ON cm.CATEGORY_ID = t.CATEGORY_ID	
		
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY
	WHERE CATEGORY_ID IN 
		(
			SELECT OLD_CATEGORY_ID FROM dbo.D_EBAY_CATEGORY_MAPPING WHERE IS_IN_WORK = 1
		)	
		
	DELETE FROM dbo.D_EBAY_CATEGORY_MAPPING	
	WHERE IS_IN_WORK = 1
	
	DELETE FROM dbo.D_EBAY_CATEGORY_HIERARCHY_TEMP
	WHERE IS_IN_WORK = 1
	
	--ONE OLD TO ONE NEW									
	COMMIT TRAN
END
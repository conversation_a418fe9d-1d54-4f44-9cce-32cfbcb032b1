CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER]
    @ORDER_ID bigint = -1
AS
BEGIN	
	
	DECLARE @UTC_NOW DATETIME = GETUTCDATE();	

	SELECT
		 FSO.SALES_ORDER_ID					as <PERSON><PERSON><PERSON><PERSON>Id
		,FSO.IS_QUOTE						as <PERSON><PERSON>uo<PERSON>
		,FSO.QUOTE_CHILD_ORDER_ID			as <PERSON><PERSON><PERSON><PERSON>Id
		,FSO.QUOTE_IS_VOIDED				as Quo<PERSON><PERSON>sVoided
		,FSO.QUOTE_EST_CLOSE_DATE			as QuoteEstCloseDate
		,FSO.QUOTE_PROBABILITY_PERECENT		as QuoteProbabilityPercent
		,FSO.QUOTE_PURCHASE_TIME_FRAME_ID	as QuotePurchaseTimeFrameId
		,FSO.SALES_ORDER_NUMBER				as SoNo
		,FSO.SALES_ORDER_DATE				as SalesOrderDate
		,FSO.NEED_BY_DATE					as NeedByDate
		,FSOS.SHIPPING_DATE					as DateShipped
		,FSO.IS_FROM_MAGENTO				as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
		,ISNULL(FSO.ON_HOLD, 0)				as <PERSON><PERSON><PERSON><PERSON><PERSON>
		,CASE WHEN((SELECT 
						COUNT(Id) 
				   FROM F_PickList WITH(NOLOCK) 
				   WHERE SalesOrderId = FSO.SALES_ORDER_ID)
				    < 2) 
			  THEN 1 
			  ELSE 0 END					as IsGeneratePlEnabled -- If SO has none OR only one Pick list then - true
		,FSO.STATUS_ID						as StatusID
		,S.SALES_ORDER_STATUS_CD			as StatusValue
		,FSO.CUSTOMER_ID					as CustomerID
		,C.CUSTOMER_NAME					as CustomerValue
		,C.CUSTOMER_CODE					as CustomerCode
		,C.VAT								as CustomerVAT
		,FSO.PO_NUMBER						as PoNo
		,FSO.TERM_ID						as TermsID
		,T.TRANSACTION_TERM_VALUE			as TermsValue
		,FSO.PACKING_NOTES					as PackingNotes
		,FSO.NOTES							as Comments
		,FSO.INTERNAL_COMMENTS				as CommentsInternal
		,FSO.WAREHOUSE_ID					as WarehouseId
		,FSO.WARRANTY_ID					AS WarrantyId
		,DATEDIFF(DD, @UTC_NOW, DATEADD(dd, CW.DURATION_DAYS, FSO.SALES_ORDER_DATE)) as WarrantyExpiresInDays

		,INV.INVOICE_ID						AS InvoiceId
		,IV.STATUS_ID						AS InvoiceStatusId
		,dbo.fn_str_GET_SALES_ORDER_INVOICE_STATUS(FSO.SALES_ORDER_ID)	AS InvoiceStatus
		,CASE
			WHEN OO.RECYCLING_ORDER_ID IS NULL 
			AND RI.RECYCLING_ORDER_ID IS NOT NULL 
				THEN 1
			WHEN RI.RECYCLING_ORDER_ID IS NULL 
			AND OO.RECYCLING_ORDER_ID IS NOT NULL 
				THEN 0
			ELSE NULL
		END														AS RefOrderIsInbound
		,ISNULL(RI.RECYCLING_ORDER_ID, OO.RECYCLING_ORDER_ID)	AS OrderRefId
		,ISNULL(RI.AUTO_NAME, OO.AUTO_NAME)						AS OrderAutoName
		,IV.PAID_DATE											AS PaidDate		
		,CASE
			WHEN IV.IS_VOIDED = 1 THEN NULL
			ELSE DATEADD(dd, T.DUE_DAYS, IV.DATE_CREATED)
		END 													AS DueDate
		,IV.DATE_CREATED										AS CreatedDate
		,IV.AUTO_NAME											as InvoiceAutoName
		,W.WAREHOUSE_CD											AS WarehouseValue
		,C2.CUSTOMER_NAME										as CarrierValue

		--costs-------------------------------------------
		,COSTS.COST						AS Cost
		,COSTS.GrossProfit				AS GrossProfit
		,COSTS.PaymentsApplied			AS PaymentsApplied
		,COSTS.Subtotal					AS Subtotal
		,COSTS.Tax						AS Tax
		,COSTS.ShippingCost				AS ShippingCost
		,COSTS.HandingFee				AS HandingFee
		,COSTS.AmountDue				AS AmountDue
		,COSTS.ECommerceFee				AS ECommerceFee
		-------------------------------------------costs--
		,PL.Id							AS PickListId
		,PL.StatusId					AS PickListStatusId

		,RMA.RMA_IDS					AS RmaIds

		,FSO_RMA.SALES_ORDER_NUMBER		AS OriginalSoNumber
		,FSO_RMA.SALES_ORDER_ID			AS OriginalSoId
		,FSO.RMA_ID						AS OriginalRmaId
		,FRPT.PACKAGE_TRACKING_NO		AS OriginalRmaTrackinkNumber
		,ISNULL(FST.STATEMENT_TERM_ID, (
				SELECT TOP(1) STATEMENT_TERM_ID
				FROM F_STATEMENT_TERM	 WITH(NOLOCK)
				WHERE STATEMENT_TERM_TYPE_ID = 1-- Invoice Terms
				  AND IS_DEFAULT_FOR_TYPE_ID = 1)) AS TermsTemplateId
		,ro.[REFERENCE_1]				AS [Reference1]
		,ro.[REFERENCE_2]				AS [Reference2]
		,ro.[REFERENCE_3]				AS [Reference3]
		,ro.[Reference4]				AS [Reference4]
		,ro.[Reference5]				AS [Reference5]
		,ro.[Reference6]				AS [Reference6]
		,ro.[Reference7]				AS [Reference7]
		,ro.[Reference8]				AS [Reference8]
		,dbo.fn_str_GET_RECYCLING_ORDER_BOL_NUMBER_REPORT(ro.RECYCLING_ORDER_ID) AS [BolNumber]
		-- RSW-9380: Use Shceduled Date (Pickup Start Date) as Order date (instead of FSO.SALES_ORDER_DATE)
		,ro.PICKUP_START_DATE			AS [OrderDate]
		,ro.RECYCLING_ORDER_ID			AS RecyclingOrderId
		,ro.RECEIVING_NOTES				AS ReceivingNotes	
		,ro.PO_NUMBER					AS RefPurchaseOrder
		,fso.TAX_ID						AS TaxId
	FROM F_SALES_ORDER													FSO	WITH (NOLOCK)
	LEFT JOIN [F_SHIPPING]												FSOS WITH (NOLOCK)
		ON FSO.SALES_ORDER_ID = FSOS.SALES_ORDER_ID
	OUTER APPLY [dbo].[tvf_GET_SALES_ORDER_TOTALS] (FSO.SALES_ORDER_ID)	COSTS
	LEFT JOIN [MAGENTO_ORDERS]											MO WITH (NOLOCK)
		ON FSO.SALES_ORDER_ID = MO.SALES_ORDER_ID
	LEFT JOIN (
		SELECT
			IV.ORDER_ID					AS SALES_ORDER_ID,
			MAX(INVOICE_ID)	AS INVOICE_ID
		FROM F_INVOICE					 IV	WITH (NOLOCK)
		WHERE IV.INVOICE_TYPE_ID = 1
			AND IV.IS_VOIDED = 0
		GROUP BY IV.ORDER_ID
	)																	INV
		ON FSO.SALES_ORDER_ID = INV.SALES_ORDER_ID
	left join [dbo].[D_CurrencyExchange]								ce
		on fso.CurrencyExchangeId = ce.Id
	left join [dbo].[C_Currency]										cc
		on ce.ForeignCurrencyId = cc.Id	
	LEFT JOIN F_INVOICE													IV	WITH(NOLOCK)
		ON INV.INVOICE_ID	= IV.INVOICE_ID
	INNER JOIN C_SALES_ORDER_STATUS										S	WITH(NOLOCK)
		ON FSO.STATUS_ID	= S.SALES_ORDER_STATUS_ID
	INNER JOIN F_CUSTOMER												C	WITH(NOLOCK)
		ON FSO.CUSTOMER_ID	= C.CUSTOMER_ID	
	LEFT JOIN C_WARRANTY												CW	WITH(NOLOCK)
		ON CW.ID = FSO.WARRANTY_ID
	LEFT JOIN C_CUSTOMER_TRANSACTION_TERM								T	WITH(NOLOCK)
		ON FSO.TERM_ID		= T.CUSTOMER_TRANSACTION_TERM_ID		
	LEFT JOIN F_RECYCLING_ORDER_INBOUND									RI	WITH(NOLOCK)
		ON FSO.RECYCLING_ORDER_ID = RI.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND								OO	WITH(NOLOCK)
		ON FSO.RECYCLING_ORDER_ID = OO.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER											ro WITH(NOLOCK)
		ON ro.RECYCLING_ORDER_ID = FSO.RECYCLING_ORDER_ID
	LEFT JOIN D_WAREHOUSE												w WITH(NOLOCK)
		ON w.WAREHOUSE_ID = ro.WAREHOUSE_ID
	OUTER APPLY (
		select top(1)
			ffc.[RecyclingOrderId],
			ffc.[CUSTOMER_ID]
		from [dbo].[F_FREIGHT_CARRIER]	ffc with(nolock)
		where ffc.[RecyclingOrderId] = FSO.RECYCLING_ORDER_ID
		order by [FREIGHT_CARRIER_ID]
	)	fc
	LEFT JOIN F_CUSTOMER												C2	WITH(NOLOCK)
		ON fc.CUSTOMER_ID	= C2.CUSTOMER_ID
	LEFT JOIN [F_PickList]												PL	WITH(NOLOCK)
		ON FSO.SALES_ORDER_ID = PL.SalesOrderId
	LEFT JOIN
	(
		SELECT
			RMA.SALES_ORDER_ID,
			STUFF(
				(SELECT 
					', ' + CONVERT(VARCHAR, RMA_ID)
					FROM dbo.F_SALES_ORDER_RMA FSOR	WITH (NOLOCK)
					WHERE RMA.SALES_ORDER_ID = FSOR.SALES_ORDER_ID
				FOR XML PATH ('')), 
				1, 
				2, 
				'')	AS RMA_IDS
		FROM F_SALES_ORDER_RMA	RMA WITH(NOLOCK)
		GROUP BY RMA.SALES_ORDER_ID
	)	RMA
		ON RMA.SALES_ORDER_ID = FSO.SALES_ORDER_ID
	LEFT JOIN F_SALES_ORDER_RMA											FSOR WITH(NOLOCK)
		ON FSOR.RMA_ID = FSO.RMA_ID
	LEFT JOIN F_SALES_ORDER												FSO_RMA WITH(NOLOCK)
		ON FSO_RMA.SALES_ORDER_ID = FSOR.SALES_ORDER_ID
	LEFT JOIN F_RMA_PACKAGE_TRACKING									FRPT WITH(NOLOCK)
		ON FRPT.RMA_ID = FSO.RMA_ID
	LEFT JOIN F_STATEMENT_TERM											FST	 WITH(NOLOCK)
		ON FST.STATEMENT_TERM_ID	   = FSO.STATEMENT_TERM_ID
		AND FST.STATEMENT_TERM_TYPE_ID = 1 -- Invoice Terms
	WHERE FSO.SALES_ORDER_ID = @ORDER_ID

END


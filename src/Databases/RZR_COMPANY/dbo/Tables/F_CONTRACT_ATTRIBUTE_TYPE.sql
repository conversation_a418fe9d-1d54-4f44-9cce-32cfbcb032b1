CREATE TABLE [dbo].[F_CONTRACT_ATTRIBUTE_TYPE] (
    [CONTRACT_ATTRIBUTE_TYPE_ID]  BIGINT         IDENTITY (1, 1) NOT NULL,
    [ATTRIBUTE_TYPE_NAME]         NVARCHAR (512) NOT NULL,
    [FAIR_MARKET]                 MONEY          NULL,
    [ATTRIBUTE_TYPE_ID]           INT            NOT NULL,
    [INVENTORY_ATTRIBUTE_TYPE_ID] INT            NOT NULL,
    [CONTRACT_RESALE_PRICING_ID]  BIGINT         NOT NULL,
    [INSERTED_BY]                 VARCHAR (150)  CONSTRAINT [DF_F_CONTRACT_ATTRIBUTE_TYPE_INSERTED_BY] DEFAULT (object_name(@@procid)) NOT NULL,
    [INSERTED_DT]                 DATETIME       CONSTRAINT [DF_F_CONTRACT_ATTRIBUTE_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                  VARCHAR (150)  CONSTRAINT [DF_F_CONTRACT_ATTRIBUTE_TYPE_INSERTED_BY1] DEFAULT (object_name(@@procid)) NULL,
    [UPDATED_DT]                  DATETIME       CONSTRAINT [DF_F_CONTRACT_ATTRIBUTE_TYPE_INSERTED_DT1] DEFAULT (getutcdate()) NULL,
    CONSTRAINT [PK_F_CONTRACT_ATTRIBUTE_TYPE] PRIMARY KEY CLUSTERED ([CONTRACT_ATTRIBUTE_TYPE_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_ATTRIBUTE_TYPE_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([INVENTORY_ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_F_CONTRACT_ATTRIBUTE_TYPE_D_CONTRACT_ATTRIBUTE_TYPE] FOREIGN KEY ([ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[D_CONTRACT_ATTRIBUTE_TYPE] ([CONTRACT_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_F_CONTRACT_ATTRIBUTE_TYPE_F_CONTRACT_RESALE_PRICING] FOREIGN KEY ([CONTRACT_RESALE_PRICING_ID]) REFERENCES [dbo].[F_CONTRACT_RESALE_PRICING] ([CONTRACT_RESALE_PRICING_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is used for Recycling -> ITAD FMV Price Sheet. It is used to set fair market price which will be set by default for specific values. For example for these specific values of desktop’s CPU the fair market will be this.. or the fair market can be set for exact capability value (F_CONTRACT_CAPABILITY)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_ATTRIBUTE_TYPE';


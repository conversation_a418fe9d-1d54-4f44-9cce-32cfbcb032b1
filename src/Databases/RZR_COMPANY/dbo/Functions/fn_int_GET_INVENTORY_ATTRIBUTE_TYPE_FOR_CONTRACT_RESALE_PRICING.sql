-- =============================================
-- Author:		<PERSON>
-- Create date: 11/27/2015
-- Description:	Gets inventory attribute type by contract resale pricing attribute type
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_INVENTORY_ATTRIBUTE_TYPE_FOR_CONTRACT_RESALE_PRICING]
(
	@C_CONTRACT_ATTRIBUTE_TYPE_ID	INT
)
RETURNS INT
AS
BEGIN
	DECLARE @RESULT INT = -1
	SELECT 
		@RESULT = ATTRIBUTE_TYPE_ID
	FROM D_CONTRACT_ATTRIBUTE_TYPE				cat		WITH(NOLOCK)
	WHERE CONTRACT_ATTRIBUTE_TYPE_ID = @C_CONTRACT_ATTRIBUTE_TYPE_ID

	RETURN @RESULT
END
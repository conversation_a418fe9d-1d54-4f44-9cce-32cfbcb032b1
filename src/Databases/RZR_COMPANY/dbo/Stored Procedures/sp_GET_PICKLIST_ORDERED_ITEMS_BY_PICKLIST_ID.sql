 -- =============================================
-- Author:		<I.Orobets>
-- Create date: <07/07/2016>
-- Description: <Get ordered picklist items>
-- =============================================
--EXEC sp_GET_PICKLIST_ORDERED_ITEMS_BY_PICKLIST_ID  @C_PICKLIST_ID = 10138, @C_WITH_SUBSTITUTES = 1
CREATE PROCEDURE [dbo].[sp_GET_PICKLIST_ORDERED_ITEMS_BY_PICKLIST_ID] 
     @C_PICKLIST_ID		 BIGINT
	,@C_WITH_SUBSTITUTES BIT = 1
AS
BEGIN

	DECLARE @T_PICK_LIST_INVENTORY [dbo].[SalesPickListAvailableInventoryItem];
	INSERT INTO @T_PICK_LIST_INVENTORY
	SELECT * FROM [dbo].[tvf_GET_PICKLIST_AVAILABLE_INVENTORY](@C_PICKLIST_ID, @C_WITH_SUBSTITUTES, 0);
	
	SELECT
		 T.LineId
		,T.ForLineId
		,T.IsAllocated
		,T.ItemQtySelected
		,T.ItemQty
		,T.AllocatedQty
		,T.Condition
		,T.PartNumber
		,T.Serial
		,T.UniqueId
		,T.CustomerName
		,T.[Location]
		,T.Manufacturer
		,T.Heci
		,T.Revision
	FROM @T_PICK_LIST_INVENTORY T
	ORDER BY T.[Location]

END
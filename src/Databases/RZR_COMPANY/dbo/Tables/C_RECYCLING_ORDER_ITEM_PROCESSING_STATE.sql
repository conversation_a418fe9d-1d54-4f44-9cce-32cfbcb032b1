CREATE TABLE [dbo].[C_RECYCLING_ORDER_ITEM_PROCESSING_STATE] (
    [RECYCLING_ORDER_ITEM_PROCESSING_STATE_ID]   BIGINT        NOT NULL,
    [RECYCLING_ORDER_ITEM_PROCESSING_STATE_CD]   VARCHAR (250) NOT NULL,
    [RECYCLING_ORDER_ITEM_PROCESSING_STATE_DESC] VARCHAR (250) NOT NULL,
    [IS_INACTIVE]                                BIT           CONSTRAINT [DF_C_RECYCLING_ORDER_ITEM_ASSET_STATE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                                 BIT           CONSTRAINT [DF_C_RECYCLING_ORDER_ITEM_ASSET_STATE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                                VARCHAR (250) NOT NULL,
    [INSERTED_DT]                                DATETIME      NOT NULL,
    [UPDATED_BY]                                 VARCHAR (250) NULL,
    [UPDATED_DT]                                 DATETIME      NULL,
    [DELETED_BY]                                 VARCHAR (250) NULL,
    [DELETED_DT]                                 DATETIME      NULL,
    CONSTRAINT [PK_C_RECYCLING_ORDER_ITEM_ASSET_STATE] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_ITEM_PROCESSING_STATE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It is not used in logic, items’ state', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_ORDER_ITEM_PROCESSING_STATE';


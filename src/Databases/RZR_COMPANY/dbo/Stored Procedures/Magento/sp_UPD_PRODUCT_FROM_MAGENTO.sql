-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_UPD_PRODUCT_FROM_MAGENTO]
	@DATA XML,
	@MAGENTO_SHOP_ID INT
AS
BEGIN
	DECLARE @idoc INT
	-- read xml list into "@idoc"
    EXEC sp_xml_preparedocument @idoc OUTPUT, @DATA
	-- move it values into the "@caps" table variable
	DECLARE	@temp TABLE ( M_PRODUCT_ID BIGINT,
						  SKU NVARCHAR(50),
						  NAME	NVARCHAR(MAX),
						  [SET]	BIGINT,
						  [TYPE] NVARCHAR(50),
						  UPDATED_AT DATETIME)

    INSERT INTO @temp (
		M_PRODUCT_ID,
		SKU,
		NAME,
		[SET],
		[TYPE],
		UPDATED_AT)
    	SELECT -- select from xml doc as table
			x.M_PRODUCT_ID,
			x.SKU,
			x.NAME,
			x.[SET],
			x.[TYPE],
			CONVERT(DATETIME, x.UPDATED_AT,120)
		FROM OPENXML (@idoc, '/Root/Items', 1)
		WITH (M_PRODUCT_ID BIGINT,
			  SKU NVARCHAR(50),
			  NAME NVARCHAR(MAX),
			  [SET] BIGINT,
			  [TYPE] NVARCHAR(50),
			  UPDATED_AT DATETIME) AS x

    -- delete xml doc
	EXEC sp_xml_removedocument @idoc

	UPDATE mp
	SET 
		mp.SKU = t.SKU,
		mp.NAME = t.NAME,
		mp.[SET] = t.[SET],
		mp.[TYPE] = t.[TYPE],
		mp.UPDATED_AT = t.UPDATED_AT
	FROM MAGENTO_PRODUCTS mp
		INNER JOIN @temp AS t 
			ON mp.M_PRODUCT_ID = t.M_PRODUCT_ID
	WHERE mp.SHOP_ID = @MAGENTO_SHOP_ID
END
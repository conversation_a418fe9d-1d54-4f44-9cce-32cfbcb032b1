
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
create function [dbo].[fn_bit_IsInventoryCanBePartAdded]
(
	@ParentInventoryId		BIGINT,
	@Parts					dbo.bigint_ID_ARRAY readonly
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @result bit = 1

	DECLARE @T_PARTS_LEVELS bigint_PARE_ARRAY
	INSERT INTO @T_PARTS_LEVELS (ID, [VALUE])
	SELECT @ParentInventoryId, -1
	UNION
	SELECT
		ID, [VALUE]
	FROM [dbo].[tvf_GET_ITEM_INVENTORY_ADDED_PARTS] (@Parts, 1, 64)

	-- Cycling found
	IF (SELECT MAX([VALUE]) FROM @T_PARTS_LEVELS) > 60
	-- A part item is used in several parent parts	
		OR EXISTS (
			SELECT TOP(1)
				1
			FROM (
				SELECT 
					COUNT(ID) AS ENTRIES_COUNT
				FROM @T_PARTS_LEVELS
				GROUP BY ID
			) T
			WHERE T.ENTRIES_COUNT > 1)
	BEGIN
		SELECT @result = 0
	END
	
	RETURN 
		ISNULL(@result, 0)
END
-- =============================================
-- Author:		<<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>>
-- Create date: <2023.07.27>
-- Description:	<Returns the Customer Contact data for Account Welcome and Change Password notifications at Client Portal>
-- =============================================
CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER_CONTACT]
(		
	@CUSTOMER_CONTACT_ID		   BIGINT
)
RETURNS @T_RESULT TABLE(
	   [CUSTOMER_ID]			 [varchar](150)
	  ,[CUSTOMER_NAME]			 [varchar](150)
	  ,[CUSTOMER_FIRST_NAME]	 [varchar](150)
	  ,[CUSTOMER_LAST_NAME]		 [varchar](150)
	  ,[CUSTOMER_MIDDLE_INITIAL]	 [varchar](5)  
	  ,[CUSTOMER_SALUTATION]		 [varchar](50) 
	  ,[CUSTOMER_JOB_TITLE]		 [varchar](200)
	  ,[CUSTOMER_PHONE_MAIN]		 [varchar](50) 
	  ,[CUSTOMER_PHONE_MOBILE]	 [varchar](50) 
	  ,[CUSTOMER_PHONE_WORK]		 [varchar](50) 
	  ,[CUSTOMER_PHONE_HOME]		 [varchar](50) 
	  ,[CUSTOMER_FAX]			 [varchar](50) 
	  ,[CUSTOMER_PRIMARY_EMAIL]	 [varchar](100) 
	  ,[CUSTOMER_SECONDARY_EMAIL]	 [varchar](100)
	  ,[CUSTOMER_OTHER_EMAIL]	 [varchar](100)
	  ,[CUSTOMER_WEBSITE]		 [varchar](200)
	  ,[CUSTOMER_PRIMARY_CONTACT_ID] BIGINT
	  ,[LOGIN]					[varchar](100)
	  ,[PASSWORD]				[varchar](100)
    )
AS
BEGIN
    INSERT INTO @T_RESULT (
		   [CUSTOMER_ID]
		  ,[CUSTOMER_NAME]		
		  ,[CUSTOMER_FIRST_NAME]		
		  ,[CUSTOMER_LAST_NAME]			
		  ,[CUSTOMER_MIDDLE_INITIAL]		
		  ,[CUSTOMER_SALUTATION]		
		  ,[CUSTOMER_JOB_TITLE]			
		  ,[CUSTOMER_PHONE_MAIN]	
		  ,[CUSTOMER_PHONE_MOBILE]	
		  ,[CUSTOMER_PHONE_WORK]	
		  ,[CUSTOMER_PHONE_HOME]	
		  ,[CUSTOMER_FAX]				
		  ,[CUSTOMER_PRIMARY_EMAIL]		
		  ,[CUSTOMER_SECONDARY_EMAIL]			
		  ,[CUSTOMER_OTHER_EMAIL]		
		  ,[CUSTOMER_WEBSITE]
		  ,[CUSTOMER_PRIMARY_CONTACT_ID]
		  ,[LOGIN]	
		  ,[PASSWORD]
	   )
	   SELECT	   
		  CC.CUSTOMER_ID
		  ,C.CUSTOMER_NAME
		  ,CC.FIRST_NAME		
		  ,CC.LAST_NAME		
		  ,CC.MIDDLE_INITIAL	
		  ,CC.SALUTATION		
		  ,CC.JOB_TITLE		
		  ,[dbo].[fn_str_GET_CONTRACT_MAIN_PHONE](CC.CUSTOMER_CONTACT_ID, CC.MAIN_PHONE_NUMBER)
		  ,[dbo].[fn_str_GET_CONTRACT_MOBILE_PHONE](CC.CUSTOMER_CONTACT_ID, CC.MOBILE_PHONE_NUMBER)
		  ,[dbo].[fn_str_GET_CONTRACT_WORK_PHONE](CC.CUSTOMER_CONTACT_ID, CC.WORK_PHONE_NUMBER)	
		  ,[dbo].[fn_str_GET_CONTRACT_HOME_PHONE](CC.CUSTOMER_CONTACT_ID, CC.HOME_PHONE_NUMBER)		
		  ,CC.FAX				
		  ,CC.MAIN_EMAIL		
		  ,CC.CC_EMAIL			
		  ,CC.OTHER_EMAIL		
		  ,CC.WEBSITE
		  ,CC.[CUSTOMER_CONTACT_ID]
		  ,CC.[LOGIN]
		  ,CC.[PASSWORD]
	   FROM F_CUSTOMER_CONTACT				CC WITH(NOLOCK)
	   INNER JOIN F_CUSTOMER	C  
		ON CC.CUSTOMER_ID = C.CUSTOMER_ID
	   WHERE CC.CUSTOMER_CONTACT_ID = @CUSTOMER_CONTACT_ID
    RETURN			
END
CREATE TABLE [dbo].[F_InboundOrderOnsiteOption] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [ServiceOnsiteOptionHierarchyId]            INT             NOT NULL,
    [IsOptionDone]                              BIT             CONSTRAINT [DF_F_InboundOrderOnsiteOption_IsOptionDone] DEFAULT ((0)) NOT NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderOnsiteOption_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderOnsiteOption_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteOption_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteOption_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderOnsiteOption] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderOnsiteOption_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderOnsiteOption_D_ServiceOnsiteOptionHierarchy] FOREIGN KEY ([ServiceOnsiteOptionHierarchyId]) REFERENCES [dbo].[D_ServiceOnsiteOptionHierarchy] ([Id]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderOnsiteOption]([RecyclingOrderId])
    INCLUDE([ServiceOnsiteOptionHierarchyId]);
GO

CREATE NONCLUSTERED INDEX [IX_ServiceOnsiteOptionHierarchyId]
    ON [dbo].[F_InboundOrderOnsiteOption]([ServiceOnsiteOptionHierarchyId])
    INCLUDE ([RecyclingOrderId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Onsite option for Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderOnsiteOption';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_ServiceOnsiteOptionHierarchyId]
    ON [dbo].[F_InboundOrderOnsiteOption]([RecyclingOrderId] ASC, [ServiceOnsiteOptionHierarchyId] ASC);

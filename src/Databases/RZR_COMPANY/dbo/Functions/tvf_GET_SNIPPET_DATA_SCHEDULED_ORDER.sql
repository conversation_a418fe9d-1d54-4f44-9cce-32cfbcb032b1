CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_SCHEDULED_ORDER]
(
	@OrderScheduledId BIGINT
)
RETURNS @T_RESULT TABLE(
	[SCHEDULED_ORDER_ID] BIGINT,
	[CustomerContactId] BIGINT,
	[PORTAL_USER_NAME] NVARCHAR(1024),
	[PICKUP_LOCATION] NVARCHAR(1024),
	[PALLET_COUNT] INT,
	[ESTIMATED_WEIGHT] FLOAT,
	[PREFERRED_DATE] NVARCHAR(50)
)
AS
BEGIN

	INSERT INTO @T_RESULT(
		[SCHEDULED_ORDER_ID],
		[CustomerContactId],
		[PORTAL_USER_NAME],
		[PICKUP_LOCATION],
		[PALLET_COUNT],
		[ESTIMATED_WEIGHT],
		[PREFERRED_DATE]
	)
	SELECT
		O.[OrderScheduledId],
		O.[CustomerContactId],
		ISNULL(RTRIM(LTRIM(CC.FIRST_NAME)), N'') + ISNULL(N' ' + NULLIF(RTRIM(LTRIM(CC.LAST_NAME)), N''), N'') AS [PORTAL_USER_NAME],
		[clientportal].[fn_GetOrderScheduleLocationAddress](O.[OrderScheduledId]) AS [PICKUP_LOCATION],
		O.EstimatedPalletCount AS [PALLET_COUNT],
		O.EstimatedWeight_lbs AS [ESTIMATED_WEIGHT],
		FORMAT(O.StartWindowdateTime, 'dd-MM-yyyy') + N' - ' + FORMAT(O.EndWindowdateTime, 'dd-MM-yyyy') AS [PREFERRED_DATE]
	FROM cp.[F_ORDER_SCHEDULED] AS O WITH(NOLOCK)	
	LEFT JOIN F_CUSTOMER_CONTACT AS CC WITH(NOLOCK)
		ON O.CreatorCustomerContactId = CC.CUSTOMER_CONTACT_ID
	WHERE O.[OrderScheduledId] = @OrderScheduledId
	RETURN

END
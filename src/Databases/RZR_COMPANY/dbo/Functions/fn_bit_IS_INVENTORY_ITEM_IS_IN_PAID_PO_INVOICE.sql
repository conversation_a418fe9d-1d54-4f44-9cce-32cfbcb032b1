-- =============================================
-- Author:		<PERSON><PERSON><PERSON><PERSON><PERSON>
-- Create date: 05/26/2017
-- Description: Check if itnventory item is linked with a PO item and included in paid invoice.
--              Need to check if we are able to update receive price for the inventory item.
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IS_INVENTORY_ITEM_IS_IN_PAID_PO_INVOICE]
(
	@C_INVENTORY_ITEM_ID BIGINT
)
RETURNS BIT
AS
BEGIN
	DECLARE @RESULT BIT;

	SET @RESULT = IIF(EXISTS(
		SELECT TOP(1) 1
		FROM [dbo].[F_ITEM_INVENTORY] II WITH(NOLOCK)
		INNER JOIN [dbo].[F_PURCHASE_ORDER_ITEM] POI WITH(ROWLOCK)
			ON POI.[INVENTORY_ITEM_ID] = II.[ITEM_INVENTORY_ID] AND POI.[RECEIVE_STATUS_ID] = 3 -- StatusId = Received
		INNER JOIN [dbo].[F_PURCHASE_ORDER_ITEM_INVOICE] POII WITH (NOLOCK)
			ON POII.[PURCHASE_ORDER_ITEM_ID] = POI.[PURCHASE_ORDER_ITEM_ID]
		INNER JOIN [dbo].[F_INVOICE] I WITH (NOLOCK)
			ON I.[INVOICE_ID] = POII.[PURCHASE_ORDER_INVOICE_ID]
				AND I.[IS_PAID] = 1 AND I.[IS_VOIDED] = 0 AND I.[IS_ADDITIONAL] = 0
		WHERE II.[ITEM_INVENTORY_ID] = @C_INVENTORY_ITEM_ID
	), 1, 0);
	
	RETURN @RESULT;

END
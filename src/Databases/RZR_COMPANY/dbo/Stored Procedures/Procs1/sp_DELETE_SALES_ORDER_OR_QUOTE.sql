CREATE PROCEDURE [dbo].[sp_DELETE_SALES_ORDER_OR_QUOTE]
	 @C_QUOTE_OR_ORDER_ID	BIGINT
	,@C_IS_QUOTE			BIT = 0
	,@C_USER_ID				BIGINT
	,@C_INT_IP				BIGINT
	,@C_USE_TRANSACTION		BIT = 1
AS
BEGIN
        
	-- Type mismatch/deleted
	IF NOT EXISTS(SELECT TOP(1) 1 FROM F_SALES_ORDER FSO WITH(NOLOCK) WHERE FSO.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID AND FSO.IS_QUOTE = @C_IS_QUOTE)
	BEGIN
		SELECT 
			2		AS ResultCode, --NotExists
			NULL	AS ErrorMessage
	END

    if (
        (select [ENTITY_SUBJECT_TYPE_ID]
         from F_PURCHASE_ORDER FPO with (nolock)
                  inner join F_ORDER_ORDER_SUBJECT_TYPE FOOST with (nolock)
                             on FOOST.[ORDER_ID] = FPO.[PURCHASE_ORDER_ID] and [ENTITY_TYPE_ID] = 3
         where FPO.[SALES_ORDER_ID] = @C_QUOTE_OR_ORDER_ID) = 6
        )
        begin
            declare @POId bigint;
            declare @POName nvarchar(250);
            select @POId = PURCHASE_ORDER_ID, @POName = AUTO_NAME
            from F_PURCHASE_ORDER with (nolock)
            where SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

            if (@POId is not null)
                begin
                    select 3                                                     as ResultCode,
                           'The Purchase Order ' + @POName + ' should be deleted first.' as ErrorMessage
                    return
                end
        end

	IF EXISTS(
		SELECT TOP(1) 1
		FROM F_SALES_ORDER		 FSQ WITH(NOLOCK)
		INNER JOIN F_SALES_ORDER FSO WITH(NOLOCK)
			ON FSO.REVISION_ORIGIN_SALES_ORDER_ID = FSQ.SALES_ORDER_ID
		WHERE FSQ.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID)
	BEGIN
		SELECT
			3														AS ResultCode,
			'The revisions of the Quote should be deleted first.'	AS ErrorMessage
	   RETURN
	END


    DECLARE @ERROR NVARCHAR(MAX) = NULL

    -- Cannot delete the quote having the child order
    SELECT
		@ERROR = N'The Quote is already transformed into Sales Order #'+S.SALES_ORDER_NUMBER+N'. It cannot be deleted.'
    FROM F_SALES_ORDER			Q	WITH(NOLOCK)
    INNER JOIN F_SALES_ORDER	S	WITH(NOLOCK)
	  ON Q.QUOTE_CHILD_ORDER_ID = S.SALES_ORDER_ID
    WHERE Q.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
	  AND Q.IS_QUOTE = 1
	  AND Q.QUOTE_CHILD_ORDER_ID IS NOT NULL

    IF (@ERROR IS NOT NULL)
    BEGIN
	   SELECT
			3		AS ResultCode,
			@ERROR  AS ErrorMessage
	   RETURN
    END
    
    -- There can be a credit which is already used for the payments
    IF (EXISTS(
	   SELECT TOP(1) 1 
	   FROM F_INVOICE					   I  WITH (NOLOCK) 
	   INNER JOIN F_INVOICE_PAYMENT		   IP WITH (NOLOCK) 
		  ON I.INVOICE_ID = IP.INVOICE_ID
	   INNER JOIN F_CUSTOMER_CREDIT		   CC WITH (NOLOCK) 
		  ON  IP.PAYMENT_ID = CC.CUSTOMER_PAYMENT_ID
		  AND CC.TYPE_BY_ACCOUNT_TYPE_ID = 2 -- AR
	   INNER JOIN F_CREDIT_PAYMENT		   CP WITH (NOLOCK)
		  ON CC.CUSTOMER_CREDIT_ID = CP.CREDIT_ID
	   WHERE I.INVOICE_TYPE_ID = 1
	     AND I.ORDER_ID = @C_QUOTE_OR_ORDER_ID)
    )
    BEGIN
	   SET @ERROR = N'An invoice of the order has payments or credit payments.';
	   SELECT
			3		AS ResultCode,
			@ERROR  AS ErrorMessage;
	   RETURN
    END

	if ([dbo].[fn_bit_IsSalesOrderCanBeDeleted](@C_QUOTE_OR_ORDER_ID) = 0)
	BEGIN
	   SET @ERROR = N'Credit memo can''t be changed or deleted. It''s credit is used or AP invoice is paid.';
	   SELECT
			3		AS ResultCode,
			@ERROR  AS ErrorMessage;
	   RETURN
    END

	DECLARE
		 @PROCESS_CD NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
		,@UTC_NOW	 DATETIME		= GETUTCDATE()
    
	IF (@C_USE_TRANSACTION = 1)
	BEGIN
		SET XACT_ABORT ON
		BEGIN TRAN
	END
		DECLARE @recyclingOrderId BIGINT,
			@soAutoName NVARCHAR (250),
			@invoiceAutoName NVARCHAR (250)
		
		SELECT
			@recyclingOrderId = FRO.RECYCLING_ORDER_ID,
			@soAutoName = FSO.SALES_ORDER_NUMBER,
			@invoiceAutoName = AR.AUTO_NAME
		FROM F_SALES_ORDER					FSO WITH(NOLOCK)
		INNER JOIN dbo.F_RECYCLING_ORDER	FRO WITH(NOLOCK)
			ON FSO.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
			AND FSO.CUSTOMER_ID = FRO.CUSTOMER_ID
		LEFT JOIN dbo.vw_AR_INVOICE			AR WITH(NOLOCK)
			ON  AR.RECYCLING_ORDER_ID = FRO.RECYCLING_ORDER_ID
			AND AR.CUSTOMER_ID = FRO.CUSTOMER_ID
			AND AR.ORDER_ID = @C_QUOTE_OR_ORDER_ID
			AND AR.IS_DELETED = 0
			AND AR.IS_VOIDED = 0
		WHERE FSO.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		IF (@recyclingOrderId IS NOT NULL)
		BEGIN
			UPDATE FRO SET
				PREV_SALES_ORDER_NUMBER	= ISNULL(PREV_SALES_ORDER_NUMBER, @soAutoName)
				,PREV_INVOICE_AR		= ISNULL(PREV_INVOICE_AR, @invoiceAutoName)
			FROM dbo.F_RECYCLING_ORDER	fro WITH(ROWLOCK)
			WHERE FRO.RECYCLING_ORDER_ID = @recyclingOrderId
		END

		UPDATE F_SALES_ORDER WITH(ROWLOCK) SET
			 QUOTE_CHILD_ORDER_ID	= NULL
			,UPDATED_BY				= @PROCESS_CD
			,UPDATED_DT				= @UTC_NOW
		WHERE QUOTE_CHILD_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		DECLARE @SALES_ORDER_ID_RMA_CREATED BIGINT = 
		(SELECT
			TOP(1) SO.SALES_ORDER_ID
		FROM dbo.F_SALES_ORDER			SO	WITH (NOLOCK)
		INNER JOIN F_SALES_ORDER_RMA	SOR WITH (NOLOCK)
			ON SO.RMA_ID = SOR.RMA_ID
		WHERE SOR.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID)

	   IF (@SALES_ORDER_ID_RMA_CREATED IS NOT NULL)
	   BEGIN

			EXEC dbo.sp_DELETE_SALES_ORDER_OR_QUOTE
				 @C_QUOTE_OR_ORDER_ID	= @SALES_ORDER_ID_RMA_CREATED
				,@C_USER_ID				= @C_USER_ID
				,@C_INT_IP				= @C_INT_IP
				,@C_USE_TRANSACTION		= @C_USE_TRANSACTION
	   	
	   END
		
		 /*Get Items to change status*/
		DECLARE @T_INVENTORY_IDS bigint_ID_ARRAY
			
		INSERT INTO @T_INVENTORY_IDS
		SELECT 
			ITEM_INVENTORY_ID 
		FROM F_SALES_ORDER_ITEM I WITH(NOLOCK)
		WHERE I.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID	
		   /* We can't change status for item were allocated in other SO*/ 
		AND NOT EXISTS(
			SELECT TOP(1) 1
			FROM dbo.F_SALES_ORDER_ITEM t WITH(NOLOCK)
				WHERE SALES_ORDER_ID != @C_QUOTE_OR_ORDER_ID
				AND t.ITEM_INVENTORY_ID = I.ITEM_INVENTORY_ID)
	
		-- UPDATE ADDED PARTS SOLD ---------------------------



		EXEC dbo.sp_UPDATE_INVENTORY_ADDED_PARTS_SOLD
			 @C_PARENT_ITEM_INVENTORY_IDS = @T_INVENTORY_IDS
			,@C_INVOKED_BY = @PROCESS_CD
		
		-- DELETE RMA PACKAGE ITEMS
		DELETE	RPI
		FROM F_RMA_PACKAGE_ITEM			RPI	WITH(ROWLOCK)
		INNER JOIN F_RMA_PACKAGE		RP	WITH(ROWLOCK)
			ON RPI.PACKAGE_ID = RP.PACKAGE_ID
		INNER JOIN F_SALES_ORDER_RMA	R	WITH(ROWLOCK)
			ON RP.RMA_ID = R.RMA_ID
		WHERE R.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		-- DELETE RMA PACKAGES
		DELETE	RP
		FROM F_RMA_PACKAGE				RP 	WITH(ROWLOCK)
		INNER JOIN F_SALES_ORDER_RMA	R  	WITH(ROWLOCK)
			ON RP.RMA_ID = R.RMA_ID
		WHERE R.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		-- DELETE RMA ITEMS
		DELETE RI
		FROM F_RMA_ITEM					RI	WITH(ROWLOCK)
		INNER JOIN F_SALES_ORDER_RMA	R	WITH(ROWLOCK)
			ON RI.RMA_ID = R.RMA_ID
		WHERE R.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		-- DELETE RMA INVOICE
		DELETE RINV
		FROM F_INVOICE					RINV 	WITH(ROWLOCK)
		inner join [dbo].[F_CreditMemo] cm with (nolock)
			on RINV.INVOICE_TYPE_ID = 3
				AND RINV.ORDER_ID = cm.Id
		INNER JOIN F_SALES_ORDER_RMA	R		WITH(ROWLOCK)
			ON R.RMA_ID = cm.RmaId
		WHERE R.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID						

		declare 
            @CreditMemoId bigint
		declare credit_memos cursor for
			select cm.Id            
			from [dbo].[F_CreditMemo] cm with (nolock)
			inner join dbo.F_SALES_ORDER_RMA rma WITH(nolock)
				on cm.RmaId = rma.RMA_ID
			where rma.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
			union
			select cm.Id            
			from [dbo].[F_CreditMemo] cm with (nolock)
			inner join dbo.F_INVOICE i WITH(nolock)
				on cm.InvoiceId = i.INVOICE_ID
			where i.INVOICE_TYPE_ID = 1 and i.ORDER_ID = @C_QUOTE_OR_ORDER_ID
		open credit_memos;
		fetch next from credit_memos INTO @CreditMemoId			
		while @@FETCH_STATUS = 0
		begin;
			exec [dbo].[sp_DeleteCreditMemo]
				@Id				= @CreditMemoId
				,@UserId		= @C_USER_ID
				,@UserIp		= @C_INT_IP
		
			FETCH NEXT FROM credit_memos INTO @CreditMemoId
		end
		close credit_memos;
		DEALLOCATE credit_memos;
	
		-- DELETE RMAS
		DELETE
		FROM F_SALES_ORDER_RMA	WITH(ROWLOCK)
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		-- DELETE ITEMS

		EXEC dbo.[sp_DELETE_SALES_ORDER_ITEMS]
			@ORDER_ITEM_IDS = NULL,
			@SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID,
			@USER_ID = @C_USER_ID,
			@USER_IP = @C_INT_IP
		
		
		DELETE 
		FROM dbo.F_SALES_ORDER_ADDRESS	WITH(ROWLOCK)
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		-- REMOVE INVOICE-LINKED LOGS
		-- transaction items
		DELETE	TI
		FROM F_ACCOUNT_TRANSACTION_ITEM			TI	WITH(ROWLOCK)
		INNER JOIN F_ACCOUNT_TRANSACTION		T	WITH(ROWLOCK)
			ON TI.ACCOUNT_TRANSACTION_ID = T.ACCOUNT_TRANSACTION_ID
		INNER JOIN F_INVOICE					I	WITH(ROWLOCK)
			ON i.INVOICE_TYPE_ID = 1
			AND T.INVOICE_ID = I.INVOICE_ID
		WHERE I.ORDER_ID = @C_QUOTE_OR_ORDER_ID

		-- transactions
		DELETE	T
		FROM F_ACCOUNT_TRANSACTION		T	WITH(ROWLOCK)
		INNER JOIN F_INVOICE			I	WITH(ROWLOCK)
			ON i.INVOICE_TYPE_ID = 1
			AND T.INVOICE_ID = I.INVOICE_ID
		WHERE I.ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		-- DELETE INVOICE PAYMENTS
		DELETE IP
		FROM F_INVOICE_PAYMENT		IP	WITH(ROWLOCK)
		INNER JOIN F_INVOICE		I	WITH(ROWLOCK)
			ON i.INVOICE_TYPE_ID = 1
			AND IP.INVOICE_ID = I.INVOICE_ID
		WHERE I.ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		-- DELETE THE INVOICES
		DELETE 
		FROM F_SALES_TAX_INVOICE 
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		DELETE 
		FROM F_INVOICE	WITH(ROWLOCK)
		WHERE INVOICE_TYPE_ID = 1
			AND ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		--DELETE MAGENTO INFO
		DECLARE @MAGENTO_ORDER_ID	BIGINT;
		DECLARE @MAGENTO_ADDR_ID	BIGINT;
		
		SELECT
			@MAGENTO_ORDER_ID = MO.M_ORDER_ID,
			@MAGENTO_ADDR_ID = MO.MAG_SHIP_ADDR
		FROM MAGENTO_ORDERS		MO	WITH(NOLOCK)
		WHERE MO.SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
		
		DELETE
		FROM MAGENTO_ORDER_PRODUCTS	WITH(ROWLOCK)
		WHERE M_ORDER_ID = @MAGENTO_ORDER_ID

		DELETE
		FROM MAGENTO_PAYMENT_INFO	WITH(ROWLOCK)
		WHERE M_ORDER_ID = @MAGENTO_ORDER_ID

		DELETE
		FROM MAGENTO_ADDRESSES	WITH(ROWLOCK)
		WHERE M_ADDRESS_ID = @MAGENTO_ADDR_ID

		DELETE
		FROM MAGENTO_ORDERS WITH(ROWLOCK)
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
		--DELETE MAGENTO INFO END	    

		
		EXEC dbo.sp_DELETE_SHIPPING 
			 @C_ORDER_ID				= @C_QUOTE_OR_ORDER_ID
			,@C_ORDER_ENTITY_TYPE_ID	= 1
			,@C_IS_INTERNAL_TRANSACTION	= 0
			,@C_SELECT_RESULT			= 0

		UPDATE F_PURCHASE_ORDER WITH(ROWLOCK) SET
			SALES_ORDER_ID	= NULL,
			UPDATED_BY	= @PROCESS_CD,
			UPDATED_DT	= @UTC_NOW
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID

		IF ((SELECT [ENTITY_SUBJECT_TYPE_ID]
			FROM F_ORDER_ORDER_SUBJECT_TYPE WITH(NOLOCK)
			WHERE ORDER_ID = @C_QUOTE_OR_ORDER_ID AND ENTITY_TYPE_ID = 1) = 12)		-- 1 - SalesOrder, 12 - OutBound Repair
		BEGIN
			DECLARE @PURCHASE_ORDER_ID BIGINT = (SELECT TOP(1) PURCHASE_ORDER_ID FROM F_PURCHASE_ORDER WITH(NOLOCK) WHERE REPAIR_SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID)

			IF (@PURCHASE_ORDER_ID IS NOT NULL)
				EXEC dbo.sp_DEL_PURCHASE_ORDER
					@PURCHASE_ORDER_ID	= @PURCHASE_ORDER_ID,
					@USER_ID			= @C_USER_ID,	
					@IP					= @C_INT_IP,
                    @SOURCE			    = @PROCESS_CD,
                    @Reason             = N'Reason: Repair Sales Order was deleted.'
		END
		ELSE
		BEGIN
			UPDATE F_PURCHASE_ORDER	WITH(ROWLOCK) SET
				 REPAIR_SALES_ORDER_ID = NULL
				,UPDATED_BY				= @PROCESS_CD
				,UPDATED_DT				= @UTC_NOW
			WHERE REPAIR_SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID
		END


		-- Picklists
		DELETE plai
		FROM dbo.F_PickListAllocatedItem	plai WITH(ROWLOCK)
		INNER JOIN dbo.F_PickList			pl WITH(ROWLOCK)
			ON pl.Id = plai.PickListId
			AND pl.SalesOrderId = @C_QUOTE_OR_ORDER_ID

		DELETE pli
		FROM dbo.F_PickListItem				pli WITH(ROWLOCK)
		INNER JOIN dbo.F_PickList			pl WITH(ROWLOCK)
			ON pl.Id = pli.PickListId
			AND pl.SalesOrderId = @C_QUOTE_OR_ORDER_ID
		
		DELETE pl
		FROM dbo.F_PickList					pl WITH(ROWLOCK)
		WHERE pl.SalesOrderId = @C_QUOTE_OR_ORDER_ID
			AND pl.ParentId IS NOT NULL

		DELETE pl
		FROM dbo.F_PickList					pl WITH(ROWLOCK)
		WHERE pl.SalesOrderId = @C_QUOTE_OR_ORDER_ID
			AND pl.ParentId IS NULL
		-- Picklists


		DELETE FROM dbo.F_ORDER_ORDER_SUBJECT_TYPE	WITH(ROWLOCK)
		WHERE ORDER_ID = @C_QUOTE_OR_ORDER_ID
			AND ENTITY_TYPE_ID = 1
			AND ENTITY_SUBJECT_TYPE_ID = 1


		DELETE FROM dbo.F_SalesOrderRepUser WITH(ROWLOCK)
		WHERE SalesOrderId = @C_QUOTE_OR_ORDER_ID
		
		DECLARE @ENTITY_AUTO_NAME  nvarchar(256)= (SELECT TOP(1) SALES_ORDER_NUMBER FROM dbo.F_SALES_ORDER	WITH(ROWLOCK) WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID)
		/*Add log info*/
		
		EXEC sp_LOG
			@USER_ID			= @C_USER_ID
			,@USER_IP			= @C_INT_IP				
			,@OPERATION_NAME	= N'Deleted'	
			,@ENTITY_TYPE_ID	= 12 /*N'Sales Orders'*/	
			,@ENTITY_KEY_VALUE	=  @C_QUOTE_OR_ORDER_ID
			,@ENTITY_AUTO_NAME	= @ENTITY_AUTO_NAME	
			,@CHANGES			= 	'Sales Order was deleted' 
			,@SOURCE			= @PROCESS_CD			
		

		-- DELETE ORDERS
		DELETE
		FROM F_SALES_ORDER	WITH(ROWLOCK)
		WHERE SALES_ORDER_ID = @C_QUOTE_OR_ORDER_ID	
	
    IF (@C_USE_TRANSACTION = 1)
	BEGIN
		COMMIT TRAN
	END
END
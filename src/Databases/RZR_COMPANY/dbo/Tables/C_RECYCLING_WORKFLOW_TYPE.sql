CREATE TABLE [dbo].[C_RECYCLING_WORKFLOW_TYPE] (
    [WORKFLOW_TYPE_ID]   INT            IDENTITY (1, 1) NOT NULL,
    [WORKFLOW_TYPE_DESC] NVARCHAR (512) NOT NULL,
    [WOR<PERSON><PERSON>OW_SYSTEM_ID] INT            NULL,
    [POSITION]           INT            NOT NULL,
    [IS_SYSTEM]          BIT            CONSTRAINT [DF_C_RECYCLING_WORKFLOW_TYPE_IS_SYSTEM] DEFAULT ((0)) NOT NULL,
    [IS_VISIBLE]         BIT            CONSTRAINT [DF_C_RECYCLING_WORKFLOW_TYPE_IS_VISIBLE] DEFAULT ((1)) NOT NULL,
    [IS_DELETED]         BIT            CONSTRAINT [DF_C_RECYCLING_WORKFLOW_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]        VARCHAR (250)  CONSTRAINT [DF_C_RECYCLING_WORKFLOW_TYPE_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]        DATETIME       CONSTRAINT [DF_C_RECYCLING_WORKFLOW_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]         VARCHAR (250)  NULL,
    [UPDATED_DT]         DATETIME       NULL,
    [DELETED_BY]         VARCHAR (250)  NULL,
    [DELETED_DT]         DATETIME       NULL,
    [BusinessUnitId]     int            constraint [DF_C_RECYCLING_WORKFLOW_TYPE_BusinessUnitId] default ((3)) null,
    [IsFinal]            bit            constraint [DF_C_RECYCLING_WORKFLOW_TYPE_IsFinal] default ((0)) not null,
    CONSTRAINT [PK_D_RECYCLING_WORKFLOW_TYPE] PRIMARY KEY CLUSTERED ([WORKFLOW_TYPE_ID] ASC),
    constraint [FK_C_RECYCLING_WORKFLOW_TYPE_BusinessUnit] foreign key ([BusinessUnitId]) references [recycling].[C_BusinessUnit] ([Id])
);








GO
CREATE NONCLUSTERED INDEX [IX_WORKFLOW_SYSTEM_ID]
    ON [dbo].[C_RECYCLING_WORKFLOW_TYPE]([WORKFLOW_SYSTEM_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of workflow', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_RECYCLING_WORKFLOW_TYPE';


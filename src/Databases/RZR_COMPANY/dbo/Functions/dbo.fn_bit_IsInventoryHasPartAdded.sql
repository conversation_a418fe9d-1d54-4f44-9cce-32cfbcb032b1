

-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
create function [dbo].[fn_bit_IsInventoryHasPartAdded]
(
	@ParentInventoryId		BIGINT	
)
RETURNS bit
AS
BEGIN
	DECLARE @result bit = 0
		
	IF (exists(select top(1) 1 from [dbo].[F_ITEM_INVENTORY_ADDED_PART] with (nolock) where [PARENT_ITEM_INVENTORY_ID] = @ParentInventoryId))
	BEGIN
		SELECT @result = 1
	END
	
	RETURN 
		ISNULL(@result, 0)
END
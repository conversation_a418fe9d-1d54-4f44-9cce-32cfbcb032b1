-- =============================================
-- Author:		<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Rebuilds F_ITEM_INVENTORY.ITEM_INVENTORY_SKU_ATTRB and F_ITEM_MASTER_SKU_ATTRB.ITEM_MASTER_SKU_ATTRB_CD
-- which are used to link inventory to skus of a master item
-- =============================================
/*
	declare @tIds bigint_id_array
	insert into @tIds
	select top(100)
		item_inventory_id
	from f_item_inventory
	where is_deleted = 0
	  and SalesOrderItemId is null

	exec [dbo].[sp_REBUILD_F_ITEM_MASTER_SKU_ATTRB]
		@InventoryIds = @tIds
*/
CREATE PROCEDURE [dbo].[sp_REBUILD_F_ITEM_MASTER_SKU_ATTRB]
	@InventoryIds		dbo.bigint_ID_ARRAY readonly
	,@T_ITEM_IDs		dbo.bigint_ID_ARRAY readonly
	,@T_ITEM_MASTER_IDs	dbo.bigint_ID_ARRAY readonly
	,@C_REBUILD_ALL		bit     = 0 -- 1 comes from dbo.sp_RebuildInventorySkuIds from SKU_FULL_ job
	,@C_IS_DEBUG		bit		= 0
AS
BEGIN

	DECLARE
		@SP_NAME	VARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
		,@UTC_NOW	DATETIME		= GETUTCDATE()
	
	set @C_REBUILD_ALL = isnull(@C_REBUILD_ALL, 0)

	create table #inventory_item_masters(
		item_inventory_id bigint
		,item_master_id	  bigint
	)

	if @C_REBUILD_ALL = 1
		insert into #inventory_item_masters
		select
			fii.ITEM_INVENTORY_ID
			,fii.ITEM_MASTER_ID
		from dbo.F_ITEM_INVENTORY fii with(nolock)
		where fii.IS_DELETED  = 0
	else if exists(select top(1) 1 from @InventoryIds)
		insert into #inventory_item_masters
		select
			fii.ITEM_INVENTORY_ID
			,fii.ITEM_MASTER_ID
		from @InventoryIds				inv
		inner join dbo.F_ITEM_INVENTORY fii with(nolock)
			on fii.ITEM_INVENTORY_ID = inv.ID
		where @C_REBUILD_ALL = 0
    else if exists(select top(1) 1 from @T_ITEM_IDs)
		insert into #inventory_item_masters
		select
			fii.ITEM_INVENTORY_ID
			,fii.ITEM_MASTER_ID
		from @T_ITEM_IDs			fi
		inner join F_ITEM_INVENTORY	fii with(nolock)
			on fii.ITEM_ID = fi.ID
		where fii.IS_DELETED  = 0
    else if exists(select top(1) 1 from @T_ITEM_MASTER_IDs)
		insert into #inventory_item_masters
		select
			fii.ITEM_INVENTORY_ID
			,fii.ITEM_MASTER_ID
		from @T_ITEM_MASTER_IDs		fim
		inner join F_ITEM_INVENTORY	fii with(nolock)
			on fii.ITEM_MASTER_ID = fim.ID
		where fii.IS_DELETED  = 0

	SELECT
		fii.item_inventory_id
		,fii.item_master_id
		,STUFF(
			(
				SELECT  
					'|' + cast(fiic.INVENTORY_CAPABILITY_TYPE_ID as VARCHAR(20)) + '-' + cast(INVENTORY_CAPABILITY_ID as VARCHAR(20)) 
				FROM (
					SELECT 
						fiic.INVENTORY_CAPABILITY_TYPE_ID	AS INVENTORY_CAPABILITY_TYPE_ID
						,MAX(INVENTORY_CAPABILITY_ID)		AS INVENTORY_CAPABILITY_ID 	
					FROM dbo.F_ITEM_INVENTORY_CAPABILITY					fiic	WITH (NOLOCK)
					INNER JOIN F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE	fatict	WITH (NOLOCK)
						ON fatict.INVENTORY_CAPABILITY_TYPE_ID	= fiic.INVENTORY_CAPABILITY_TYPE_ID	
						AND fatict.IS_SKU_EFFECTING = 1 
						AND fiic.IS_INACTIVE		= 0
						AND fiic.IS_DELETED			= 0	
					where fiic.ITEM_INVENTORY_ID				= fii.ITEM_INVENTORY_ID
						AND fatict.INVENTORY_ATTRIBUTE_TYPE_ID	= TIMAS.INVENTORY_ATTRIBUTE_TYPE_ID -- from the current attribute set!
					GROUP BY fiic.INVENTORY_CAPABILITY_TYPE_ID									
				) fiic
				ORDER BY fiic.INVENTORY_CAPABILITY_TYPE_ID
				FOR XML PATH('')			
			)
		, 1, 1, '') as ITEM_INVENTORY_SKU_ATTRB -- build the composit SKU key for each inventory item 
	into #inventory_sku_attributes
	FROM (
		-- calculate once for each master item
		SELECT
			FIM.ITEM_MASTER_ID
			,[dbo].[fn_bigint_GET_ITEM_MASTER_ATTRIBUTE_SET_ID](FIM.ITEM_MASTER_ID) AS INVENTORY_ATTRIBUTE_TYPE_ID
		FROM (
			select distinct
				item_master_id
			from #inventory_item_masters
		) FIM
	)									TIMAS	
	INNER JOIN #inventory_item_masters	fii
		ON ISNULL(fii.item_master_id, 0) = ISNULL(TIMAS.ITEM_MASTER_ID, 0)

	if @C_IS_DEBUG = 1 
	BEGIN

		SELECT
			'#inventory_item_masters' AS [#inventory_item_masters]
			,*
		FROM #inventory_sku_attributes

		SELECT
			'##inventory_sku_attributes' AS [##inventory_sku_attributes]
			,*
		FROM #inventory_sku_attributes
	END

	UPDATE fii SET 
		fii.ITEM_INVENTORY_SKU_ATTRB	= tt.ITEM_INVENTORY_SKU_ATTRB
		,fii.UPDATED_BY					= @SP_NAME 
		,fii.UPDATED_DT					= @UTC_NOW
	FROM #inventory_sku_attributes	tt
	INNER JOIN F_ITEM_INVENTORY		fii WITH(ROWLOCK)
		ON fii.ITEM_INVENTORY_ID = tt.ITEM_INVENTORY_ID
	WHERE ISNULL(fii.ITEM_INVENTORY_SKU_ATTRB, '') != ISNULL(tt.ITEM_INVENTORY_SKU_ATTRB, '')


	INSERT INTO F_ITEM_MASTER_SKU_ATTRB (
		[ITEM_MASTER_ID]
		,[ITEM_MASTER_SKU_ATTRB_CD]
		,[INSERTED_BY]
		,[INSERTED_DT]
	)
	SELECT distinct
		fii.ITEM_MASTER_ID
		,fii.ITEM_INVENTORY_SKU_ATTRB
		,@SP_NAME	
		,@UTC_NOW
	FROM #inventory_sku_attributes		fii
	LEFT JOIN F_ITEM_MASTER_SKU_ATTRB	ft WITH (NOLOCK)
		ON  fii.ITEM_MASTER_ID			 = ft.ITEM_MASTER_ID
		AND fii.ITEM_INVENTORY_SKU_ATTRB = ft.ITEM_MASTER_SKU_ATTRB_CD
	WHERE fii.ITEM_INVENTORY_SKU_ATTRB IS NOT NULL
	  AND ft.ITEM_MASTER_SKU_ATTRB_ID IS NULL

END

-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_CLEAN_SYSTEM_NAME]
	(@C_SYSTEM_NAME NVARCHAR(250)
	,@C_LOCATION_ID BIGINT = NULL)
RETURNS VARCHAR(250)
with schemabinding
BEGIN
	SELECT
		@C_SYSTEM_NAME = REPLACE(@C_SYSTEM_NAME, LOCATION_SEPARATOR_CD, '')
	FROM dbo.C_LOCATION_SEPARATOR WITH (NOLOCK)
		
	SET @C_SYSTEM_NAME = ISNULL(REPLACE(@C_SYSTEM_NAME, ' ', ''), '')

	--SET @C_SYSTEM_NAME = dbo.fn_str_CLEAN_LOCATON_NAME(@C_SYSTEM_NAME, @C_LOCATION_ID)
	RETURN @C_SYSTEM_NAME
END
CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_PURCHASE_ORDER_ITEM]
(
	@C_PURCHASE_ORDER_ITEM_ID BIGINT
)
RET<PERSON><PERSON> @T_RESULT TABLE
(
	 PURCHASE_ORDER_ID					BIGINT
	,PURCHASE_ORDER_AUTO_NAME			NVARCHAR(MAX)
	,PURCHASE_ORDER_ITEM_SERIAL			NVARCHAR(MAX)
	,PURCHASE_ORDER_ITEM_INIQUE_ID		NVARCHAR(MAX)
	,PURCHASE_ORDER_ITEM_STATUS_ID	    BIGINT
	,PURCHASE_ORDER_ITEM_STATUS			VARCHAR(50)
	,ITEM_NUMBER						NVARCHAR(256)						
)
AS
BEGIN
	INSERT INTO @T_RESULT
	(
		 PURCHASE_ORDER_ID
		,PURCHASE_ORDER_AUTO_NAME
		,PURCHASE_ORDER_ITEM_SERIAL
		,PURCHASE_ORDER_ITEM_INIQUE_ID
		,PURCHASE_ORDER_ITEM_STATUS_ID
		,PURCHASE_ORDER_ITEM_STATUS
		,ITEM_NUMBER
	)
	SELECT
		 PO.PURCHASE_ORDER_ID
		,PO.AUTO_NAME
		,POI.SERIAL
		,POI.UNIQUE_ID
		,PORS.PURCHASE_ORDER_RECEIVE_STATUS_ID
		,PORS.ORDER_RECEIVE_STATUS_CD
		,IMR.ITEM_NUMBER
	FROM
	F_PURCHASE_ORDER_ITEM POI WITH (NOLOCK)
	INNER JOIN F_PURCHASE_ORDER PO WITH(NOLOCK)
		ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
	INNER JOIN C_PURCHASE_ORDER_RECEIVE_STATUS PORS WITH (NOLOCK)
		ON POI.RECEIVE_STATUS_ID = PORS.PURCHASE_ORDER_RECEIVE_STATUS_ID
	LEFT JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
		ON poi.[PRODUCT_MASTER_ID] = pm.[PRODUCT_MASTER_ID]
	LEFT JOIN F_ITEM_MASTER	IMR	WITH (NOLOCK)
		ON PM.[PRODUCT_MASTER_TYPE_ID] = 1
		AND PM.ITEM_MASTER_ID = IMR.ITEM_MASTER_ID
	WHERE POI.PURCHASE_ORDER_ITEM_ID = @C_PURCHASE_ORDER_ITEM_ID
	RETURN
END
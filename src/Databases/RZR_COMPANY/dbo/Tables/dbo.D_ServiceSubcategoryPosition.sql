CREATE TABLE [dbo].[D_ServiceSubcategoryPosition] (
    [Id]                         BIGINT         IDENTITY (1, 1) NOT NULL,
    [SubcategoryId]              BIGINT         NOT NULL,
    [Name]                       NVARCHAR (128) NOT NULL,
    [Pos]                        INT            NOT NULL,
    [IsDeleted]                  BIT            CONSTRAINT [DF_D_ServiceSubcategoryPosition_IsDeleted] DEFAULT ((0)) NOT NULL,
    [InsertedBy]                 NVARCHAR (150) CONSTRAINT [DF_D_ServiceSubcategoryPosition_InsetedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]               DATETIME       CONSTRAINT [DF_D_ServiceSubcategoryPosition_InsetedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]           BIGINT         CONSTRAINT [DF_D_ServiceSubcategoryPosition_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]           BIGINT         CONSTRAINT [DF_D_ServiceSubcategoryPosition_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                  NVARCHAR (150) NULL,
    [UpdatedDate]                DATETIME       NULL,
    [UpdatedByUserId]            BIGINT         NULL,
    [UpdatedByUserIp]            BIGINT         NULL,
    [DeletedBy]                  NVARCHAR (150) NULL,
    [DeletedDate]                DATETIME       NULL,
    [DeletedByUserId]            BIGINT         NULL,
    [DeletedByUserIp]            BIGINT         NULL,
    CONSTRAINT [PK_D_ServiceSubcategoryPosition] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_D_ServiceSubcategoryPosition_D_ServiceSubcategory] FOREIGN KEY ([SubcategoryId]) REFERENCES [dbo].[D_ServiceSubcategory] ([Id]),
    CONSTRAINT [FK_D_ServiceSubcategoryPosition_tb_USER_DeletedByUserId] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_D_ServiceSubcategoryPosition_tb_USER_InsertedByUserId] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_D_ServiceSubcategoryPosition_tb_USER_UpdatedByUserId] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);
GO

CREATE NONCLUSTERED INDEX [IX_SubcategoryId]
    ON [dbo].[D_ServiceSubcategoryPosition]([SubcategoryId], [IsDeleted]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Subcategory positions for Service', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ServiceSubcategoryPosition';

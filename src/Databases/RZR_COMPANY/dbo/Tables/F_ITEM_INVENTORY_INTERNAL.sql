CREATE TABLE [dbo].[F_ITEM_INVENTORY_INTERNAL] (
    [ITEM_INVENTORY_INTERNAL_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_INVENTORY_MAIN_ID]     BIGINT        NOT NULL,
    [ITEM_INVENTORY_ID]          BIGINT        NOT NULL,
    [INSERTED_BY]                VARCHAR (250) NULL,
    [INSERTED_DT]                DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_INVENTORY_INTERNAL] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_INTERNAL_ID] ASC),
    CONSTRAINT [FK_F_ITEM_INVENTORY_INTERNAL_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_MAIN_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_INTERNAL_F_ITEM_INVENTORY1] FOREI<PERSON><PERSON> KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID])
);
GO
CREATE INDEX IDX_F_ITEM_INVENTORY_INTERNAL_ITEM_INVENTORY_MAIN_ID ON F_ITEM_INVENTORY_INTERNAL (ITEM_INVENTORY_MAIN_ID)
INCLUDE(ITEM_INVENTORY_INTERNAL_ID, [ITEM_INVENTORY_ID])

GO
CREATE INDEX IDX_F_ITEM_INVENTORY_INTERNAL_ITEM_ITEM_INVENTORY_ID ON F_ITEM_INVENTORY_INTERNAL (ITEM_INVENTORY_ID)
INCLUDE(ITEM_INVENTORY_INTERNAL_ID)

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is addons for inventory', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_INTERNAL';


CREATE TABLE [dbo].[F_CreditMemo] (
    [Id]                  BIGINT         IDENTITY (1, 1) NOT NULL,
    [AutoName]            NVARCHAR (10)  NULL,
    [CustomerCreditId]    BIGINT         NULL,
    [InvoiceId]           BIGINT         NULL,
    [RmaId]               BIGINT         NULL,
    [RestockFee]          FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_RestockFee] DEFAULT ((0)) NOT NULL,
    [ShippingRefund]      FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_ShippingRefund] DEFAULT ((0)) NOT NULL,
    [MiscChargeRefund]    FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_MiscChargeRefund] DEFAULT ((0)) NOT NULL,
    [TaxShippingRefund]   FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_TaxShippingRefund] DEFAULT ((0)) NOT NULL,
    [TaxMiscChargeRefund] FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_TaxMiscChargeRefund] DEFAULT ((0)) NOT NULL,
    [PartialRefund]       FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_PartialRefund] DEFAULT ((0)) NOT NULL,
    [PartialTaxRefund]    FLOAT (53)     CONSTRAINT [DF_F_CreditMemo_PartialTaxRefund] DEFAULT ((0)) NOT NULL,
    [IsManualRefund]      BIT            CONSTRAINT [DF_F_CreditMemo_IsManualRefund] DEFAULT ((0)) NOT NULL,
    [TaxId]               BIGINT         NULL,
    [Notes]               NVARCHAR (MAX) NULL,
    [KidNumber]           NVARCHAR (250) NULL,
    [InsertedBy]          NVARCHAR (64)  NOT NULL,
    [InsertedDate]        DATETIME       NOT NULL,
    [InsertedByUserIp]    BIGINT         NOT NULL,
    [InsertedByUserId]    BIGINT         NOT NULL,
    [UpdatedBy]           NVARCHAR (64)  NULL,
    [UpdatedDate]         DATETIME       NULL,
    [UpdatedByUserIp]     BIGINT         NULL,
    [UpdatedByUserId]     BIGINT         NULL,
    [ExternalId]          NVARCHAR (350) NULL,
    [WarehouseId]         BIGINT         NULL,
    CONSTRAINT [PK_F_CreditMemo] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_CreditMemo_F_CUSTOMER_CREDIT] FOREIGN KEY ([CustomerCreditId]) REFERENCES [dbo].[F_CUSTOMER_CREDIT] ([CUSTOMER_CREDIT_ID]),
    CONSTRAINT [FK_F_CreditMemo_F_INVOICE] FOREIGN KEY ([InvoiceId]) REFERENCES [dbo].[F_INVOICE] ([INVOICE_ID]),
    CONSTRAINT [FK_F_CreditMemo_F_SALES_ORDER_RMA] FOREIGN KEY ([RmaId]) REFERENCES [dbo].[F_SALES_ORDER_RMA] ([RMA_ID]),
    CONSTRAINT [FK_F_CreditMemo_F_TAX] FOREIGN KEY ([TaxId]) REFERENCES [dbo].[F_TAX] ([TAX_ID])
);





GO
CREATE TRIGGER [dbo].[trg_CreditMemo]
	ON dbo.F_CreditMemo
AFTER INSERT
AS
BEGIN
    DECLARE
		 @Ids			dbo.[bigint_ID_ARRAY]
		,@Results		XML = NULL
		,@t_results		dbo.[bigint_PARE_ARRAY]
		,@PREFIX		NVARCHAR(10)
		,@PREFIX_Q		NVARCHAR(10)
	
	SELECT TOP(1) 
		 @PREFIX = IIF(ISNULL([PREFIX_CREDIT_MEMO], N'') = N'', N'', [PREFIX_CREDIT_MEMO] + N'-')
	FROM U_SYSTEM_SETTINGS WITH (NOLOCK)

	INSERT INTO @Ids
	SELECT Id FROM inserted
	WHERE AutoName IS NULL		

	IF EXISTS(SELECT TOP(1) 1 FROM @Ids)
	BEGIN

		exec dbo.sp_GetIndentityValue
			@TABLE_NAME = 'F_CreditMemo',
			@Ids		= @Ids,
			@Results	= @Results OUT

		INSERT INTO @t_results(ID, value)
		select --row.value('@ID', 'bigint')
			 t.row.value('@Id', 'bigint') as Id
			,t.row.value('@Value', 'bigint') as Value
			from @Results.nodes('/root/row') t(row)			

	END

	UPDATE I SET
		AutoName  = @PREFIX  + CAST(t.[value] AS NVARCHAR(250)),
		KidNumber = [dbo].[fn_nvarchar_MakeKIDNumber](@PREFIX  + CAST(t.[value] AS NVARCHAR(250)))
	FROM [dbo].[F_CreditMemo]	I
	INNER JOIN  @t_results		t
		ON t.ID = I.Id
	WHERE I.AutoName IS NULL

END

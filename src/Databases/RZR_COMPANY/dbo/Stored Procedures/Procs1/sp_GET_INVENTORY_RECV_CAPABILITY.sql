CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_RECV_CAPABILITY] 	
	@INVENTORY_RECV_IDS	bigint_ID_ARRAY READONLY,
	@ATTRIBUTE_TYPE_ID  INT = NULL,
    @isSystemReturn		bit = 1
AS
BEGIN
	
    SELECT
        T.[INVENTORY_RECV_CAPABILITY_ID]
        ,T.Id
        ,T.CapabilityId
        ,T.CapabilityValue
        ,T.CapabilityTypeId
        ,T.CapabilityIdValue
        ,T.CapabilityLabel
        ,ia.IS_SKU_EFFECTING					  AS IsSkuAffeting
    FROM (

        SELECT DISTINCT
            rc.ITEM_INVENTORY_CAPABILITY_ID		  AS [INVENTORY_RECV_CAPABILITY_ID],
            rc.ITEM_INVENTORY_ID				  AS Id,
            rc.INVENTORY_CAPABILITY_ID			  AS CapabilityId,
            ic.INVENTORY_CAPABILITY_VALUE		  AS CapabilityValue,		
            rc.INVENTORY_CAPABILITY_TYPE_ID		  AS CapabilityTypeId,
            ct.INVENTORY_CAPABILITY_VALUE		  AS CapabilityIdValue,
            ct.INVENTORY_CAPABILITY_LABEL		  as CapabilityLabel,
            fii.ITEM_MASTER_ID
        FROM @INVENTORY_RECV_IDS									IDS
        
        INNER JOIN dbo.F_ITEM_INVENTORY								fii WITH(NOLOCK)
            ON fii.ITEM_INVENTORY_ID = IDS.ID

        INNER JOIN dbo.F_ITEM_INVENTORY_CAPABILITY					rc	WITH(NOLOCK)
            ON  rc.ITEM_INVENTORY_ID = IDS.ID
            AND RC.IS_INACTIVE = 0
            AND RC.IS_DELETED  = 0

        INNER JOIN dbo.D_INVENTORY_CAPABILITY						ic	WITH(NOLOCK)
            ON  rc.INVENTORY_CAPABILITY_ID = ic.INVENTORY_CAPABILITY_ID
            AND IC.IS_INACTIVE = 0
            AND IC.IS_DELETED  = 0
        
        INNER JOIN dbo.C_INVENTORY_CAPABILITY_TYPE					ct	WITH(NOLOCK)
            ON  ct.INVENTORY_CAPABILITY_TYPE_ID = ic.INVENTORY_CAPABILITY_TYPE_ID
            AND CT.IS_INACTIVE = 0
            AND CT.IS_DELETED  = 0
            and (ct.IsSystem = 0 or ct.IsSystem = @isSystemReturn)

    ) T


    LEFT JOIN [dbo].[F_ITEM_MASTER_CATEGORY]		            FIMC WITH(NOLOCK)
		ON FIMC.[ITEM_MASTER_ID] = T.[ITEM_MASTER_ID]
		AND FIMC.[IS_PRIMARY] = 1
		AND FIMC.[IS_DELETED] = 0
		AND FIMC.[IS_INACTIVE] = 0
	LEFT JOIN [dbo].[D_CATEGORY_HIERARCHY]			            DCH WITH(NOLOCK)
		ON FIMC.[CATEGORY_ID] = DCH.[CATEGORY_ID]
		AND DCH.[IS_INACTIVE] = 0
	LEFT JOIN dbo.F_ITEM_MASTER						            FIM WITH(NOLOCK)
		ON FIM.ITEM_MASTER_ID = T.[ITEM_MASTER_ID]


	LEFT JOIN F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE		IA	WITH(NOLOCK)
		ON  IA.INVENTORY_CAPABILITY_TYPE_ID = T.CapabilityTypeId
		AND IA.IS_INACTIVE = 0
		AND IA.IS_DELETED  = 0
		AND IA.INVENTORY_ATTRIBUTE_TYPE_ID  = 
            --ISNULL(@ATTRIBUTE_TYPE_ID, [dbo].[fn_bigint_GET_ITEM_MASTER_ATTRIBUTE_SET_ID](T.ITEM_MASTER_ID)) --attribute type must be specified otherwise the duplicates exists	
            COALESCE(@ATTRIBUTE_TYPE_ID, DCH.[INVENTORY_ATTRIBUTE_TYPE_ID], FIM.INVENTORY_ATTRIBUTE_TYPE_ID, NULL)

END
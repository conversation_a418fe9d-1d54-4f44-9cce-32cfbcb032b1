CREATE FUNCTION [dbo].[fn_str_GET_CUSTOMER_ADDRESS_LOCATION_AUTO_NAME]
    (@CUSTOMER_ADDRESS_ID BIGINT)
RETURNS NVARCHAR(MAX)
BEGIN
    RETURN (
	   SELECT 
		  LTRIM(RTRIM(
			 ISNULL(ca.[STREET_1] +' ', '') +
			 ISNULL(ca.[STREET_2]+' ', ''))) + ', '+
		  ca.[COUNTRY] + ' '+
		  ca.[POSTAL_CODE]
	   FROM dbo.F_CUSTOMER_ADDRESS ca WITH(NOLOCK)
	   WHERE ca.CUSTOMER_ADDRESS_ID = @CUSTOMER_ADDRESS_ID)
END
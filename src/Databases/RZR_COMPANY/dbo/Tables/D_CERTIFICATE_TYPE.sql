CREATE TABLE [dbo].[D_CERTIFICATE_TYPE] (
    [CERTIFICATE_TYPE_ID] INT            IDENTITY (1, 1) NOT NULL,
    [CERTIFICATE_TYPE]    NVARCHAR (256) NOT NULL,
    [INSERTED_BY]         VARCHAR (150)  NOT NULL,
    [INSERTED_DT]         DATETIME       NOT NULL,
    [UPDATED_BY]          VARCHAR (150)  NULL,
    [UPDATED_DT]          DATETIME       NULL,
    [DELETED_BY]          VARCHAR (150)  NULL,
    [DELETED_DT]          DATETIME       NULL,
    [IS_DELETED]          BIT            CONSTRAINT [DF_D_CERTIFICATE_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_D_CERTIFICATE_TYPE] PRIMARY KEY CLUSTERED ([CERTIFICATE_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The types of certificate', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_CERTIFICATE_TYPE';


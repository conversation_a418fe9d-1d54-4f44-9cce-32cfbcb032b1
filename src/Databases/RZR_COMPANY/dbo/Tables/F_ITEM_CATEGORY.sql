CREATE TABLE [dbo].[F_ITEM_CATEGORY] (
    [ITEM_CATEGORY_ID] BIGINT        NOT NULL,
    [ITEM_ID]          BIGINT        NOT NULL,
    [IS_INACTIVE]      BIT           CONSTRAINT [DF_F_ITEM_CATEGORY_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]       BIT           CONSTRAINT [DF_F_ITEM_CATEGORY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]      VARCHAR (150) CONSTRAINT [DF_F_ITEM_CATEGORY_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]      DATETIME      CONSTRAINT [DF_F_ITEM_CATEGORY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]       VARCHAR (150) NULL,
    [UPDATED_DT]       DATETIME      NULL,
    [DELETED_BY]       VARCHAR (150) NULL,
    [DELETED_DT]       DATETIME      NULL
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_CATEGORY';



-- =============================================
-- Author:		O.Evseev
-- Create date: 08/18/2015
-- Description:	returns inhouse recycling order for the selected warehouse (can be null)
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ORDER_FROM_CONSUMABLE] 
	@WAREHOUSE_ID BIGINT = NULL
AS
BEGIN
	
	DECLARE @RECYCLING_ORDER_ID BIGINT = NULL

	EXEC sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID
		 @WAREHOUSE_ID = @WAREHOUSE_ID
	    ,@RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID OUTPUT
	

	SELECT @RECYCLING_ORDER_ID AS RECYCLING_ORDER_ID
END
-- =============================================
-- Author:		<PERSON>
-- Create date: 11/09/2015
-- Description:	Gets count of filled items for picklist requested item
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_ALL_PICKLIST_ITEM_COUNT]
(
	@C_PICKLIST_ITEM_ID	BIGINT
)
RETURNS INT
AS
BEGIN
	DECLARE 
		@RESULT							INT		= 0
		,@PICKLIST_ID					BIGINT
		,@ITEM_ID						BIGINT
		,@CONDITION_ID					BIGINT
		,@LOCATION_ID					BIGINT
		,@CUSTOMER_ID					BIGINT
		,@ITEM_MASTER_ID				BIGINT
		,@ITEM_MASTER_PRODUCT_CODE_ID	BIGINT
		,@INVENTORY_CAPABILITY_ID		BIGINT

	SELECT
		 @ITEM_ID						= FPLI.ItemId
		,@CONDITION_ID					= FPLI.ConditionId
		,@LOCATION_ID					= FPLI.LocationId
		,@CUSTOMER_ID					= FPLI.CustomerId
		,@ITEM_MASTER_ID				= FPLI.ItemMasterId
		,@ITEM_MASTER_PRODUCT_CODE_ID	= FPLI.ItemMasterProductCodeId
		,@INVENTORY_CAPABILITY_ID		= FPLI.InventoryCapabilityId
		,@PICKLIST_ID					= FPLI.PickListId
	FROM [dbo].[F_PickListItem]		as FPLI	WITH(NOLOCK)
	WHERE FPLI.Id = @C_PICKLIST_ITEM_ID	

	SELECT 
		@RESULT = ISNULL(CAST(SUM(ISNULL(QTY, 0)) AS INT), 0)
	FROM 
	(
		SELECT
			FII.SkuId
			,FII.ConditionId
			,FII.ItemMasterId
			,FII.LocationId
			,FII.CustomerId
			,FIIPC.ITEM_MASTER_PRODUCT_CODE_ID
			,FII.RevisionId
			,ISNULL(FII.Qty, 0) as QTY
		FROM [dbo].[vw_InventoryDetailsMain]					as FII		WITH(NOLOCK)
		LEFT JOIN dbo.F_ITEM_MASTER						as FIM		WITH(NOLOCK)
			ON FIM.ITEM_MASTER_ID = FII.ItemMasterId
		LEFT JOIN dbo.F_ITEM_INVENTORY_PRODUCT_CODE		as FIIPC	WITH(NOLOCK)
			ON FII.Id = FIIPC.ITEM_INVENTORY_ID			
		WHERE FII.IsAvailable = 1
		  AND FII.ItemMasterId = @ITEM_MASTER_ID
	) AS ITEMS
	-- The bundle of this SKU
	LEFT JOIN dbo.F_ITEM_LOT					AS IL			WITH(NOLOCK)
		ON IL.ITEM_ID_MASTER = ITEMS.SkuId
		AND IL.ITEM_ID = @ITEM_ID
	LEFT JOIN dbo.F_ITEM						AS BUNDLE_FI	WITH(NOLOCK)
		ON  BUNDLE_FI.ITEM_ID = IL.ITEM_ID
		AND BUNDLE_FI.ITEM_TYPE_ID = 4 --bundle --SELECT * FROM dbo.C_ITEM_TYPE
	WHERE (@ITEM_ID IS NULL
		  OR ITEMS.SkuId     = @ITEM_ID
		  OR BUNDLE_FI.ITEM_ID = @ITEM_ID) --PICKLIST ITEM ATTR
		AND ITEMS.ConditionId = @CONDITION_ID		--PICKLIST ITEM ATTR
		AND (@ITEM_MASTER_PRODUCT_CODE_ID IS NULL OR ITEMS.ITEM_MASTER_PRODUCT_CODE_ID = @ITEM_MASTER_PRODUCT_CODE_ID) --PICK LIST ITEM ATTR
		AND (@INVENTORY_CAPABILITY_ID IS NULL	  OR ITEMS.RevisionId = @INVENTORY_CAPABILITY_ID) --PICK LIST ITEM ATTR
		AND (@LOCATION_ID IS NULL				  OR ITEMS.LocationId = @LOCATION_ID) --PICK LIST ITEM ATTR
		AND (@CUSTOMER_ID IS NULL				  OR ITEMS.CustomerId = @CUSTOMER_ID) --PICK LIST ITEM ATTR

	RETURN @RESULT
END
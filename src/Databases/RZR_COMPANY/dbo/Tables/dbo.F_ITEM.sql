CREATE TABLE [dbo].[F_ITEM] (
    [ITEM_ID]                          BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]                   BIGINT         NOT NULL,
    [ITEM_NUMBER]                      NVARCHAR (256) NOT NULL,
    [ITEM_MASTER_SKU_ATTRB_ID]         BIGINT         NULL,
    [<PERSON>ONG_DESC]                        VARCHAR (MAX)  NULL,
    [CONDITION_ID]                     BIGINT         NULL,
    [ITEM_CONDITION_DESC]              VARCHAR (MAX)  NULL,
    [ITEM_DESC]                        VARCHAR (250)  NULL,
    [QUANTITY]                         FLOAT (53)     CONSTRAINT [DF_F_ITEM_QUANTITY] DEFAULT ((0)) NULL,
    [QUANTITY_AVAILABLE]               FLOAT (53)     CONSTRAINT [DF_F_ITEM_AVAILABLE] DEFAULT ((0)) NULL,
    [QUANTITY_ALLOCATED]               FLOAT (53)     CONSTRAINT [DF_F_ITEM_ALLOCATED] DEFAULT ((0)) NULL,
    [QUANTITY_AVAILABLE_FOR_ECOMMERCE] FLOAT (53)     CONSTRAINT [DF_F_ITEM_PRICE] DEFAULT ((0)) NULL,
    [PRICE]                            FLOAT (53)     CONSTRAINT [DF_F_ITEM_QUANTITY_AVAILABLE_FOR_ECOMMERCE] DEFAULT ((0)) NULL,
    [PRICE_CHANGED_DATE]               DATETIME       NULL,
    [SUB_QUANTITY]                     FLOAT (53)     NULL,
    [MAXQUANTITY]                      VARCHAR (50)   NULL,
    [UNIQUE_INVERNTORY_ID]             BIGINT         NULL,
    [IS_QUALIFIED]                     BIT            CONSTRAINT [DF_F_ITEM_IS_NEEDING_EXPORT1] DEFAULT ((0)) NOT NULL,
    [QUALIFIED_DT]                     DATETIME       NULL,
    [QUALIFIED_BY]                     BIGINT         NULL,
    [IS_FEATURED]                      BIT            CONSTRAINT [DF_F_ITEM_IS_FEATURED_1] DEFAULT ((0)) NOT NULL,
    [IS_DEAL_10_PERC_OFF_EBAY]         BIT            CONSTRAINT [DF_F_ITEM_IS_DEAL_10_PERC_OFF_EBAY_1] DEFAULT ((0)) NOT NULL,
    [ITEM_TYPE_ID]                     INT            CONSTRAINT [DF_F_ITEM_ITEM_TYPE_ID] DEFAULT ((1)) NOT NULL,
    [ITEM_TYPE_CHILD_ID]               BIGINT         NULL,
    [ITEM_PRICE_MSRP]                  FLOAT (53)     NULL,
    [ITEM_PRICE_QUOTE_AVG]             FLOAT (53)     NULL,
    [IS_HIDE_ECOMMERCE_ITEM]           BIT            CONSTRAINT [DF_F_ITEM_IS_HIDE_ECOMMERCE_ITEM] DEFAULT ((0)) NULL,
    [IS_INACTIVE]                      BIT            CONSTRAINT [DF_F_ITEM_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                       BIT            CONSTRAINT [DF_F_ITEM_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                      VARCHAR (150)  CONSTRAINT [DF_F_ITEM_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                      DATETIME       CONSTRAINT [DF_F_ITEM_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                       VARCHAR (150)  NULL,
    [UPDATED_DT]                       DATETIME       NULL,
    [DELETED_BY]                       VARCHAR (150)  NULL,
    [DELETED_DT]                       DATETIME       NULL,
    [DIMENSION_ID]                     BIGINT         NULL,
    CONSTRAINT [PK_F_ITEM] PRIMARY KEY CLUSTERED ([ITEM_ID] ASC),
    CONSTRAINT [FK__F_ITEM__F_DIMENSION__DIMENSION_ID] FOREIGN KEY ([DIMENSION_ID]) REFERENCES [dbo].[F_DIMENSION] ([DIMENSION_ID]),
    CONSTRAINT [FK_F_ITEM_D_ITEM_CONDITION] FOREIGN KEY ([CONDITION_ID]) REFERENCES [dbo].[D_ITEM_CONDITION] ([ITEM_CONDITION_ID]) ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_F_INVERNTORY] FOREIGN KEY ([UNIQUE_INVERNTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_F_ITEM_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]) ON UPDATE CASCADE,
    CONSTRAINT [FK_F_ITEM_tb_User] FOREIGN KEY ([QUALIFIED_BY]) REFERENCES [dbo].[tb_User] ([UserID])
)
GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM__IS_DELETED__QUANTITY]
    ON [dbo].[F_ITEM] ([IS_DELETED],[QUANTITY])
    INCLUDE ([ITEM_MASTER_ID],[ITEM_MASTER_SKU_ATTRB_ID],[CONDITION_ID],[QUANTITY_AVAILABLE_FOR_ECOMMERCE],[ITEM_ID],[QUANTITY_AVAILABLE],[IS_QUALIFIED],[IS_FEATURED],[IS_DEAL_10_PERC_OFF_EBAY],[ITEM_TYPE_ID],[INSERTED_DT],[UPDATED_DT])
GO

CREATE NONCLUSTERED INDEX [IX__F_ITEM__IS_DELETED__IS_QUALIFIED]
    ON [dbo].[F_ITEM] ([IS_QUALIFIED],[IS_DELETED])
    INCLUDE ([ITEM_ID],[ITEM_MASTER_ID],[ITEM_MASTER_SKU_ATTRB_ID],[CONDITION_ID],[ITEM_DESC])
GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_F_ITEM_ITEM_MASTER_ID_CONDITION_ID_ATTR_ID_ITEM_TYPE_ID]
    ON [dbo].[F_ITEM]([ITEM_MASTER_ID] ASC, [CONDITION_ID] ASC, [ITEM_MASTER_SKU_ATTRB_ID] ASC, [ITEM_TYPE_ID] ASC, [UNIQUE_INVERNTORY_ID] ASC)
    INCLUDE([ITEM_ID], [IS_DELETED], [DELETED_BY], [DELETED_DT]) WHERE ([ITEM_TYPE_ID]=(1))
GO

CREATE UNIQUE NONCLUSTERED INDEX [UIX_F_ITEM__DIMENSION_ID]
    ON [dbo].[F_ITEM]([DIMENSION_ID] ASC, [ITEM_TYPE_ID]) WHERE ([DIMENSION_ID] IS NOT NULL)
GO

CREATE NONCLUSTERED INDEX IX_F_ITEM_ITEM_TYPE_ID_IS_INACTIVE_IS_DELETE_QUANTITY
	ON [dbo].[F_ITEM] ([ITEM_TYPE_ID],[IS_INACTIVE],[IS_DELETED],[QUANTITY])
INCLUDE ([ITEM_ID],
	[ITEM_MASTER_ID],
	[ITEM_NUMBER], 
	[ITEM_MASTER_SKU_ATTRB_ID],
	[CONDITION_ID],
	[UNIQUE_INVERNTORY_ID],
	QUANTITY_AVAILABLE, 
	QUANTITY_ALLOCATED,
	QUANTITY_AVAILABLE_FOR_ECOMMERCE, 
	IS_QUALIFIED, 
	IS_FEATURED, 
	IS_DEAL_10_PERC_OFF_EBAY, 
	[INSERTED_DT], 
	[UPDATED_DT])

GO
CREATE NONCLUSTERED INDEX IX_F_ITEM_ITEM_TYPE_ID_ITEM_TYPE_ID_ITEM_TYPE_CHILD_ID
    ON [dbo].[F_ITEM] ([ITEM_TYPE_ID],[ITEM_TYPE_CHILD_ID])
    INCLUDE ([ITEM_ID],[ITEM_MASTER_ID],[ITEM_NUMBER],[ITEM_MASTER_SKU_ATTRB_ID],[LONG_DESC],[CONDITION_ID],[ITEM_CONDITION_DESC],[ITEM_DESC],[QUANTITY],[QUANTITY_AVAILABLE],[QUANTITY_ALLOCATED], [QUANTITY_AVAILABLE_FOR_ECOMMERCE], [PRICE],[PRICE_CHANGED_DATE],[SUB_QUANTITY],[MAXQUANTITY],[UNIQUE_INVERNTORY_ID],[IS_QUALIFIED],[QUALIFIED_DT],[QUALIFIED_BY],[IS_FEATURED],[IS_DEAL_10_PERC_OFF_EBAY],[ITEM_PRICE_MSRP],[ITEM_PRICE_QUOTE_AVG],[IS_HIDE_ECOMMERCE_ITEM],[IS_INACTIVE],[IS_DELETED],[INSERTED_BY],[INSERTED_DT],[DELETED_BY],[DELETED_DT])
GO


CREATE NONCLUSTERED INDEX [IX_F_ITEM_ITEM_MASTER_ID_CONDITION_ID_QTY_IS_QUALIFIED]
    ON [dbo].[F_ITEM]([ITEM_MASTER_ID] ASC, [CONDITION_ID] ASC, [QUANTITY] ASC)
    INCLUDE([IS_QUALIFIED])
GO


CREATE NONCLUSTERED INDEX [IX_F_ITEM_ITEM_MASTER_ID_CONDITION_ID]
    ON [dbo].[F_ITEM]([ITEM_MASTER_ID] ASC, [CONDITION_ID] ASC)
    INCLUDE([ITEM_MASTER_SKU_ATTRB_ID], [LONG_DESC], [ITEM_CONDITION_DESC], [ITEM_DESC], [QUANTITY_AVAILABLE],QUANTITY_AVAILABLE_FOR_ECOMMERCE, [PRICE], [MAXQUANTITY], [IS_QUALIFIED], [QUALIFIED_DT], [QUALIFIED_BY], [IS_FEATURED], [IS_DEAL_10_PERC_OFF_EBAY], [ITEM_PRICE_MSRP], [ITEM_PRICE_QUOTE_AVG], [IS_HIDE_ECOMMERCE_ITEM], [IS_INACTIVE], [IS_DELETED], [ITEM_ID])
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_ITEM_MASTER_ID]
    ON [dbo].[F_ITEM]([ITEM_MASTER_ID] ASC, [ITEM_ID] ASC)
    INCLUDE([ITEM_DESC])
GO
-- =============================================
-- Author:		I.Orobets
-- Create date: 05/12/2016
-- Description:	Occures after update item entry and logs changed data
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_AFTER_UPDATE]
    ON  [dbo].[F_ITEM]
    AFTER UPDATE
AS 
BEGIN

	INSERT INTO [dbo].[F_LOG_ACTION_DATA]
       ([ACTION_ID]
       ,[ENTITY_TYPE_ID]
       ,[ENTITY_KEY_VALUE]
       ,[ENTITY_AUTO_NAME]
       ,[USER_ID]     
       ,[SOURCE])
	SELECT
		8												AS [ACTION_ID]		-- Qualified
		,22												AS [ENTITY_TYPE_ID] -- SKU
		,I.ITEM_ID										AS [ENTITY_KEY_VALUE]
		,I.ITEM_ID										AS [ENTITY_AUTO_NAME]
		,ISNULL(I.[QUALIFIED_BY], D.[QUALIFIED_BY])		AS [USER_ID]		
		,'trg_ITEM_AFTER_UPDATE'						AS [SOURCE]
	FROM		INSERTED						I
	INNER JOIN	DELETED							D
		ON I.ITEM_ID = D.ITEM_ID	
	WHERE I.IS_QUALIFIED != D.IS_QUALIFIED
      AND I.IS_QUALIFIED = 1 

	DECLARE @TAIL NVARCHAR(4) = N'"';
    DECLARE @DEF  NVARCHAR(4) = N'"-"';

	INSERT INTO F_LOG_DATA 	WITH(ROWLOCK)
	(	
		[SOURCE]		
		,[OPERATION_NAME]
		,[ENTITY_TYPE_ID]
		,[ENTITY_KEY_VALUE]
		,[ENTITY_AUTO_NAME]
		,[CHANGES]
	)
	SELECT 'trg_ITEM_AFTER_UPDATE'                    
               ,NEW.[OPERATION_NAME]
               ,22 --N'SKU Quantity change'
               ,NEW.[ENTITY_KEY_VALUE]
               ,NEW.[ENTITY_AUTO_NAME]
	           ,[dbo].[fn_str_STRIP_XML_TAGS](
                    (SELECT
                         isnull(NEW.[QUANTITY], 0)                          as [QUANTITY]
                        ,isnull(NEW.[QUANTITY_AVAILABLE], 0)                as [QUANTITY_AVAILABLE]
                        ,isnull(NEW.[QUANTITY_ALLOCATED], 0)                as [QUANTITY_ALLOCATED]
						,isnull(NEW.[QUANTITY_AVAILABLE_FOR_ECOMMERCE], 0)  as [QUANTITY_AVAILABLE_FOR_ECOMMERCE]											
	                FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT)
            )		AS [CHANGES]
           FROM (
                     SELECT -- Fixed columns		  
                            N'Updated'											AS [OPERATION_NAME]                             
                           ,ISNULL(I.[ITEM_ID], D.[ITEM_ID])					AS [ENTITY_KEY_VALUE]
                           ,ISNULL(I.[ITEM_ID], D.[ITEM_ID])					AS [ENTITY_AUTO_NAME]
                           -- XML columns													
                           ,CASE
                               WHEN ISNULL(D.[QUANTITY], 0) <> ISNULL(I.[QUANTITY], 0) AND I.IS_DELETED = 0
                               THEN N'Quantity was ' + 
                                   ISNULL(N'"' + CAST(D.[QUANTITY] AS NVARCHAR(21)) + N'"', @DEF) + N' is ' +
                                   ISNULL(N'"' + CAST(I.[QUANTITY] AS NVARCHAR(21)) + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [QUANTITY]
                           ,CASE
                               WHEN ISNULL(D.[QUANTITY_AVAILABLE], 0) <> ISNULL(I.[QUANTITY_AVAILABLE], 0)
                               THEN N'Quantity available was ' + 
                                   ISNULL(N'"' + CAST(D.[QUANTITY_AVAILABLE] AS NVARCHAR(21)) + N'"', @DEF) + N' is ' +
                                   ISNULL(N'"' + CAST(I.[QUANTITY_AVAILABLE] AS NVARCHAR(21)) + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [QUANTITY_AVAILABLE]
                           ,CASE
                               WHEN ISNULL(D.[QUANTITY_ALLOCATED], 0) <> ISNULL(I.[QUANTITY_ALLOCATED], 0)
                               THEN N'Quantity allocated was ' + 
                                   ISNULL(N'"' + CAST(D.[QUANTITY_ALLOCATED] AS NVARCHAR(21)) + N'"',  @DEF) + N' is ' +
                                   ISNULL(N'"' + CAST(I.[QUANTITY_ALLOCATED] AS NVARCHAR(21)) + @TAIL, @DEF)
                               ELSE NULL
                            END																	AS [QUANTITY_ALLOCATED]
                           ,CASE WHEN D.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] <> I.[QUANTITY_AVAILABLE_FOR_ECOMMERCE]
								THEN N'Quantity available for ecom was changed. Old value:'
									+  ISNULL(N'"' + CAST(D.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] as nvarchar(21))+ @TAIL, @DEF) + N', new value: ' +	 
									 ISNULL(N'"' + CAST(I.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] as nvarchar(21)) + @TAIL, @DEF) 
								ELSE NULL 
							END																	AS [QUANTITY_AVAILABLE_FOR_ECOMMERCE]						   
                    FROM		INSERTED						I
					INNER JOIN	DELETED							D
						ON I.ITEM_ID = D.ITEM_ID
    	   
					WHERE I.IS_QUALIFIED = 1 and (I.[QUANTITY] != D.[QUANTITY] or
							i.[QUANTITY_AVAILABLE] != d.[QUANTITY_AVAILABLE] or
							i.[QUANTITY_ALLOCATED] != d.[QUANTITY_ALLOCATED] or
							i.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] != d.[QUANTITY_AVAILABLE_FOR_ECOMMERCE])
                ) NEW
			
END
GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_PUT_CHANGE]
    ON  [dbo].[F_ITEM]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_ID, d.ITEM_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_ID = d.ITEM_ID
    where isnull(i.PRICE, 0) <> isnull(d.PRICE, 0)
		or isnull(i.QUANTITY, 0) <> isnull(d.QUANTITY, 0)
		or isnull(i.QUANTITY_AVAILABLE, 0) <> isnull(d.QUANTITY_AVAILABLE, 0)												 
		or isnull(i.QUANTITY_ALLOCATED, 0) <> isnull(d.QUANTITY_ALLOCATED, 0)
		or isnull(i.QUANTITY_AVAILABLE_FOR_ECOMMERCE, 0) <> isnull(d.QUANTITY_AVAILABLE_FOR_ECOMMERCE, 0)
		or isnull(i.ITEM_MASTER_ID, 0) <> isnull(d.ITEM_MASTER_ID, 0)
		or isnull(i.ITEM_DESC, 0) <> isnull(d.ITEM_DESC, 0)
		or isnull(i.LONG_DESC, 0) <> isnull(d.LONG_DESC, 0)
		or isnull(i.PRICE_CHANGED_DATE, '1/1/1900') <> isnull(d.PRICE_CHANGED_DATE, '1/1/1900')
		or isnull(i.IS_QUALIFIED, 0) <> isnull(d.IS_QUALIFIED, 0)
		or isnull(i.QUALIFIED_BY, '') <> isnull(d.QUALIFIED_BY, '')
		or isnull(i.QUALIFIED_DT, '1/1/1900') <> isnull(d.QUALIFIED_DT, '1/1/1900')
	-- assuming that it is fast enough to update the records in elastic each time

    declare @inventoryIds dbo.bigint_id_array
    insert into @inventoryIds
    SELECT ITEM_INVENTORY_ID
    FROM @ids IDS
    INNER JOIN F_ITEM_INVENTORY FII WITH(NOLOCK)
        ON IDS.ID = FII.ITEM_ID
    WHERE FII.IS_DELETED = 0

	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	
	exec search.sp_SetEntityChanged
		@TypeId		= 11 --sku
		,@EntityIds = @ids
		,@Invoker	= @invoker
	
	exec search.sp_SetEntityChanged
		@TypeId		= 1 --Inventory
		,@EntityIds = @inventoryIds
		,@Invoker	= @invoker


END
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores SKUs and rebuilds in background.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM';
GO
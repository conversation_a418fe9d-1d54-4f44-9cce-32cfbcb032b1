CREATE PROCEDURE [dbo].[sp_SET_CATEGORY_INVENTORY_ATTRIBUTE_TYPE] 	
	@CATEGORY_ID					BIGINT,
	@INVENTORY_ATTRIBUTE_TYPE_ID	INT,
	@USER_ID						BIGINT,
	@USER_IP						BIGINT
AS
BEGIN

	DECLARE @spName NVARCHAR(150) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + N'.', N'') + OBJECT_NAME(@@PROCID);
	DECLARE @utcNow datetime = getutcdate()

	UPDATE dbo.D_CATEGORY_HIERARCHY WITH(ROWLOCK) SET
		INVENTORY_ATTRIBUTE_TYPE_ID = @INVENTORY_ATTRIBUTE_TYPE_ID
		,UPDATED_DT					= @utcNow
		,UPDATED_BY					= @spName
		,UpdatedByUserId			= @USER_ID
		,UpdatedByUserIp			= @USER_IP
	WHERE CATEGORY_ID = @CATEGORY_ID;

	-- Sync assets Attribute Set Id
	EXEC [recycling].[sp_SetAssetCorrectCategoryId]
		@CategoryId = @CATEGORY_ID,
		@UserId = @USER_ID,
		@UserIp = @USER_IP,
		@UseInternalTran = 1,
		@OuterSpName = @spName;
					
END
CREATE FUNCTION [dbo].[fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME] (
	@CUSTOMER_CONTACT_ID BIGINT
)
RETURNS NVARCHAR(1024)
BEGIN
	RETURN (
		SELECT TOP(1)
			ISNULL(NULLIF(RTRIM(LTRIM(cc.SALUTATION)), '') + ' ', '') +
			ISNULL(RTRIM(LTRIM(cc.FIRST_NAME)), '') + 
			ISNULL(' ' + NULLIF(RTRIM(LTRIM(cc.MIDDLE_INITIAL)), ''), '') +
			ISNULL(' ' + NULLIF(RTRIM(LTRIM(cc.LAST_NAME)), ''), '') +
			ISNULL(', ' + NULLIF(RTRIM(LTRIM(cc.JOB_TITLE)), ''), '')
		FROM dbo.F_CUSTOMER_CONTACT cc WITH(NOLOCK)
		WHERE cc.CUSTOMER_CONTACT_ID = @CUSTOMER_CONTACT_ID
	);
END

-- =============================================
-- Author:	 <Author,,Name>
-- Create date: <Create Date,,>
-- Description: <Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CUSTOMER_ADDRESS_FROM_MAGENTO]
--declare
    @CUSTOMER_ID	    BIGINT = 598,
    @MAGENTO_ADDRESS_ID BIGINT = 70,
    @SHOP_ID		    INT = 1
AS
BEGIN
    DECLARE 
	   @STREET	 VARCHAR(250),
	   @CITY		 VARCHAR(100),
	   @STATE		 VARCHAR(50),
	   @POSTAL_CODE VARCHAR(1050),
	   @COUNTRY	 VARCHAR(100),
	   @PHONE		 VARCHAR(50),
	   @FAX		 VARCHAR(50)
			
    SELECT
	   @STREET	 = ma.STREET,
	   @CITY		 = ma.CITY,
	   @STATE		 = ma.[STATE],
	   @POSTAL_CODE = ma.POSTAL_CODE,
	   @COUNTRY	 = ma.COUNTRY,
	   @PHONE		 = ma.PHONE,
	   @FAX		 = ma.FAX
    FROM MAGENTO_ADDRESSES ma
    WHERE M_ADDRESS_ID = @MAGENTO_ADDRESS_ID
      AND SHOP_ID = @SHOP_ID

	--RSW-10431: Razor by default works with 3letter codes. So here we will transform 2letter code from Magento into 3letter code for Razor
	IF (@COUNTRY = 'US') 
	BEGIN
		SET @COUNTRY = 'USA'
	END
		
    UPDATE dbo.F_CUSTOMER_CONTACT SET
	   MAIN_PHONE_NUMBER   = @PHONE
	   ,UPDATED_BY		   = N'sp_SET_CUSTOMER_ADDRESS_FROM_MAGENTO'
	   ,UPDATED_DT		   = GETUTCDATE()
    WHERE CUSTOMER_ID = @CUSTOMER_ID
      AND IS_MAIN = 1	
		
    DECLARE @LOCATION_NAME VARCHAR(250) = ISNULL(@STREET, '') + ',' + ISNULL(@CITY, '') + ',' +  ISNULL(@COUNTRY, '')     
    EXEC sp_UPD_CUSTOMER_ADDRESS 
	   @CUSTOMER_ADDRESS_ID		 = -1
	   ,@LOCATION_NAME			 = @LOCATION_NAME
	   ,@CUSTOMER_ID			 = @CUSTOMER_ID
	   ,@CUSTOMER_ADDRESS_TYPE_ID	 = 2
	   ,@IS_COMPANY					= 0
	   ,@COMPANY_NAME				= NULL
	   ,@STREET_1					= @STREET
	   ,@STREET_2					= N''
	   ,@STREET_3					= N''
	   ,@CITY						= @CITY
	   ,@STATE						= @STATE
	   ,@POSTAL_CODE				= @POSTAL_CODE
	   ,@COUNTRY					= @COUNTRY
	   ,@PHONE						= @PHONE
	   ,@FAX						= @FAX
	   ,@IS_MAIN					= 0
	   ,@WAREHOUSE_ID				= 0
    
    EXEC sp_UPD_CUSTOMER_ADDRESS 
	   @CUSTOMER_ADDRESS_ID		 = -1
	   ,@LOCATION_NAME			 = @LOCATION_NAME
	   ,@CUSTOMER_ID			 = @CUSTOMER_ID
	   ,@CUSTOMER_ADDRESS_TYPE_ID	 = 3
	   ,@IS_COMPANY					= 0
	   ,@COMPANY_NAME				= NULL
	   ,@STREET_1					= @STREET
	   ,@STREET_2					= N''
	   ,@STREET_3					= N''
	   ,@CITY						= @CITY
	   ,@STATE						= @STATE
	   ,@POSTAL_CODE				= @POSTAL_CODE
	   ,@COUNTRY					= @COUNTRY
	   ,@PHONE						= @PHONE
	   ,@FAX						= @FAX
	   ,@IS_MAIN					= 0
	   ,@WAREHOUSE_ID				= 0

END
-- =============================================
-- Author:		<PERSON><PERSON> <PERSON><PERSON>
-- Edit date:   04/22/2022
-- Description:	Rebuild the fi QTY and kits of the given master items
-- =============================================
/*
	declare @T_ITEM_IDs bigint_id_array
	insert into @T_ITEM_IDs
	select top(15)
		t.item_id--, t.qty
	from (
		select
			item_id,
			count(ITEM_INVENTORY_ID) qty
		from vw_F_ITEM_INVENTORY_AVAILABLE_FORSKU_REBUILD fii
		where item_id is not null
		group by item_id
	) t
	order by qty desc

	exec [dbo].[sp_REBUILD_F_ITEM_SKU_QTY]
		@T_ITEM_IDs  = @T_ITEM_IDs
		--,@C_IS_DEBUG = 1
*/
CREATE PROCEDURE [dbo].[sp_REBUILD_F_ITEM_SKU_QTY]
	@T_ITEM_MASTER_IDs	dbo.bigint_ID_ARRAY readonly
	,@T_ITEM_IDs		dbo.bigint_ID_ARRAY readonly
	,@C_PROCESS_CD		VARCHAR(150) = null
	,@C_IS_DEBUG		bit			= 0
AS
BEGIN

	DECLARE
		@UTC_NOW				DATETIME	= GETUTCDATE()
		,@IS_BY_SKU				BIT			= IIF(EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_IDs), 1, 0);
	
	DECLARE
		@IS_ALL_MASTER_ITEMS	BIT			= IIF(@IS_BY_SKU = 1 OR EXISTS(SELECT TOP(1) 1 FROM @T_ITEM_MASTER_IDs), 0, 1)


	SET @C_PROCESS_CD = ISNULL(@C_PROCESS_CD, '') + ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)

	if @C_IS_DEBUG = 1 PRINT 'Take all available inventory items of the ItemMaster'


	create table #skus(item_id bigint not null primary key clustered);

	if (@IS_BY_SKU = 1)
		insert into #skus
		select distinct
			item_id
		from (
			select
				id					as ITEM_ID
			from @T_ITEM_IDs

			union all -- include the heading bundle skus
		
			select -- when triggered for one of the in-bundle SKUs - should recalculate the qty in the master bundle SKU
				fil.ITEM_ID_MASTER	as ITEM_ID
			from @T_ITEM_IDs				sku
			inner join F_ITEM_LOT			fil WITH(NOLOCK)
				on fil.ITEM_ID = sku.ID
			inner join F_ITEM				fi WITH(NOLOCK)
				ON fi.ITEM_ID = sku.ID
				and fi.ITEM_TYPE_ID = 4

			union all

			SELECT -- when the qty of Kit included SKUs changes - should recalculate the heading SKU
				FIC.ITEM_ID
			FROM @T_ITEM_IDs				SKU
			INNER JOIN F_ITEM_COMBO			FIC WITH(NOLOCK)
				ON FIC.ITEM_ID_MASTER = SKU.ID
		) fi
		order by fi.item_id asc
	else if (@IS_ALL_MASTER_ITEMS = 0)
		insert into #skus
		select
			fi.item_id
		from @T_ITEM_MASTER_IDs fim
		inner join f_item					fi WITH(NOLOCK)
			on fi.ITEM_MASTER_ID = fim.ID
		order by fi.item_id asc
	else
		-- all SKUS
		insert into #skus
		select
			fi.item_id
		from f_item							fi WITH(NOLOCK)
		order by fi.item_id asc

	create table #t_Actual_SKU_Qty(ITEM_ID bigint, QUANTITY bigint)-- DROP TABLE #t_Actual_SKU_Qty

	insert into #t_Actual_SKU_Qty
	select
		fig.ITEM_ID					as ITEM_ID
		,SUM(fig.QUANTITY)			as QUANTITY
	from #skus														sku
	inner join [dbo].[vw_F_ITEM_INVENTORY_FILTERED_FORSKU_REBUILD]	fig WITH (NOLOCK)
		on fig.item_id = sku.item_id
	   and fig.ITEM_STATUS_ID = 1 -- Unallocated
	left join recycling.F_Asset										fa with(nolock)
		on fa.Id = fig.AssetId
	where isnull(fa.AssetWorkflowStepId, 102) = 102 -- Resale
	GROUP BY
		fig.ITEM_ID
	order by fig.ITEM_ID

	CREATE INDEX #ix__t_F_ITEM_INVENTORY_AVAILABLE__ITEM_ID__include__QUANTITY
		ON #t_Actual_SKU_Qty (
			ITEM_ID, 
			QUANTITY
		)

	-- NEVER make "Hard Deletes" in this stored procedure as that will destroy fi Logic
	declare @msg nvarchar(256);
	if @C_IS_DEBUG = 1
	begin 

		select 
			'#skus' as [what]
			,*
		from #skus


		set @msg = 'Reset fi qty to 0 for all SKUs of the ItemMaster NOT containing the pre-selected Inventory';
		PRINT @msg
		select
			@msg as what
			,st.*
		FROM #skus					sku
		inner join	F_ITEM			fi WITH(ROWLOCK)
			on  fi.ITEM_ID      = sku.ITEM_ID
			AND fi.QUANTITY		> 0
			AND fi.IS_INACTIVE	= 0
			AND fi.IS_DELETED	= 0
		LEFT JOIN #t_Actual_SKU_Qty	st
			ON st.ITEM_ID = sku.ITEM_ID
		WHERE st.ITEM_ID IS NULL -- without the inventory
	end

	UPDATE fi SET
		fi.QUANTITY								= 0
		,fi.QUANTITY_AVAILABLE					= 0
		,fi.QUANTITY_AVAILABLE_FOR_ECOMMERCE	= 0
		,fi.UPDATED_BY							= @C_PROCESS_CD
		,fi.UPDATED_DT							= @UTC_NOW
	FROM #skus					sku
	inner join	F_ITEM			fi WITH(ROWLOCK)
		on  fi.ITEM_ID      = sku.ITEM_ID
		AND fi.QUANTITY		> 0
		AND fi.ITEM_TYPE_ID != 9 -- NOT INVENTORY KITTING
		AND fi.IS_INACTIVE	= 0
		AND fi.IS_DELETED	= 0
	LEFT JOIN #t_Actual_SKU_Qty	st
		ON st.ITEM_ID = sku.ITEM_ID
	WHERE st.ITEM_ID IS NULL -- without the inventory
	
	
	if @C_IS_DEBUG = 1
	begin
		set @msg = 'Undelete the items with inventory available';
		PRINT @msg;

		select
			@msg as what
			,st.*
		FROM #skus						sku
		inner join #t_Actual_SKU_Qty	st
			ON st.ITEM_ID = sku.ITEM_ID
		inner join F_ITEM				fi WITH(ROWLOCK)
			on fi.item_id = st.ITEM_ID
		where fi.IS_DELETED = 1
	end

	UPDATE fi SET
		fi.IS_DELETED = 0
		,fi.DELETED_BY = iif(fi.IS_DELETED = 1, NULL, fi.DELETED_BY)
		,fi.DELETED_DT = iif(fi.IS_DELETED = 1, NULL, fi.DELETED_DT)
	FROM #skus						sku
	inner join #t_Actual_SKU_Qty	st
		ON st.ITEM_ID = sku.ITEM_ID
	inner join F_ITEM				fi WITH(ROWLOCK)
		on fi.item_id = st.ITEM_ID
	where fi.IS_DELETED = 1
		
	
	if @C_IS_DEBUG = 1
	begin 
		set @msg = 'Update the total Qty under fi'
		PRINT @msg;

		select
			@msg as what
			,fi.item_id
			,st.*
		from #skus						sku
		inner join #t_Actual_SKU_Qty	st
			ON sku.ITEM_ID = st.ITEM_ID
		inner join F_ITEM				fi WITH(ROWLOCK)
			on fi.item_id = st.ITEM_ID
		where fi.QUANTITY != st.QUANTITY
	end

	UPDATE fi SET
		 fi.QUANTITY			 = st.QUANTITY
		,fi.UPDATED_BY			 = @C_PROCESS_CD
		,fi.UPDATED_DT			 = @UTC_NOW
	from #skus						sku
	inner join #t_Actual_SKU_Qty	st
		ON sku.ITEM_ID = st.ITEM_ID
	inner join F_ITEM				fi WITH(ROWLOCK)
		on fi.item_id = st.ITEM_ID
	where fi.QUANTITY != st.QUANTITY


	if @C_IS_DEBUG = 1
	begin 
		set @msg = 'UPDATE QUANTITY CALCULATED COLUMNS'
		PRINT @msg;
		select
			@msg as what
			,sku.*
			,IIF(fi.ITEM_TYPE_ID = 7, 0,  II.ALLOCATED)						as QUANTITY_ALLOCATED
			,IIF(QUANTITY < ISNULL(II.LOCKED,0) OR fi.ITEM_TYPE_ID = 7
				, 0
				, QUANTITY - ISNULL(II.LOCKED, 0))							as QUANTITY_AVAILABLE
			,CASE ITEM_TYPE_ID
				WHEN 1 THEN 
					IIF(IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] is null or IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] < ISNULL(II.LOCKED,0)
						, 0
						, IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] - ISNULL(II.LOCKED, 0))
				WHEN 7 THEN IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE]
				ELSE 0
			END																as [QUANTITY_AVAILABLE_FOR_ECOMMERCE]
		FROM #skus									sku
		inner join	F_ITEM							fi WITH(ROWLOCK)
			on fi.item_id = sku.ITEM_ID
			and fi.ITEM_TYPE_ID IN(1,7)
			AND fi.IS_INACTIVE = 0
			AND fi.IS_DELETED = 0
		OUTER APPLY(
			SELECT 
				ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fi.ITEM_ID),0)		AS LOCKED	 --Item is LOCKED IS SO
				,ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fi.ITEM_ID),0)	as ALLOCATED --Item is ALLOCATED in SO
		) II
		LEFT JOIN (
			SELECT
				FII.ITEM_ID
				,SUM(
					CASE ISNULL(fii.NOT_INCLUDE_IN_RESALE_CHANNELS, 0) 
						WHEN 1 THEN 0 
						ELSE ISNULL(ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 0)
					END
				) AS [QUANTITY_AVAILABLE_FOR_ECOMMERCE]
			FROM vw_F_ITEM_INVENTORY_AVAILABLE_FORSKU_REBUILD	FII
			left join recycling.F_Asset							fa with(nolock)
				on fa.Id = FII.AssetId
			WHERE FII.IS_DROP_SHIP_ITEM = 0
			  and fii.ITEM_STATUS_ID = 1 --Items in "Correct Item Returned" is not available on e-comm and "fi Generate" too
			  and isnull(fa.AssetWorkflowStepId, 102) = 102 -- Resale
			GROUP BY  FII.ITEM_ID
		) IIV
			ON IIV.ITEM_ID = fi.ITEM_ID --Item is AVAILABLE_FOR_ECOMMERC
	end

	UPDATE fi SET 
		fi.QUANTITY_ALLOCATED = IIF(fi.ITEM_TYPE_ID = 7, 0,  II.ALLOCATED)
		,fi.QUANTITY_AVAILABLE = IIF(QUANTITY < ISNULL(II.LOCKED,0) OR fi.ITEM_TYPE_ID = 7, 0, QUANTITY - ISNULL(II.LOCKED, 0))
		,fi.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] = CASE ITEM_TYPE_ID
			WHEN 1 THEN IIF(IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] is null or IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] < ISNULL(II.LOCKED,0), 0, IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE] - ISNULL(II.LOCKED, 0))
			WHEN 7 THEN IIV.[QUANTITY_AVAILABLE_FOR_ECOMMERCE]
			ELSE 0
		END
	FROM #skus									sku
	inner join	F_ITEM							fi WITH(ROWLOCK)
		on fi.item_id = sku.ITEM_ID
		and fi.ITEM_TYPE_ID IN(1,7)
		AND fi.IS_INACTIVE = 0
		AND fi.IS_DELETED = 0
	OUTER APPLY(
		SELECT 
			ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fi.ITEM_ID),0)		AS LOCKED	 --Item is LOCKED IS SO
			,ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fi.ITEM_ID),0)	as ALLOCATED --Item is ALLOCATED in SO
	) II
	LEFT JOIN (
		SELECT
			FII.ITEM_ID
			,SUM(
				CASE ISNULL(fii.NOT_INCLUDE_IN_RESALE_CHANNELS, 0) 
					WHEN 1 THEN 0 
					ELSE ISNULL(ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 0)
				END
			) AS [QUANTITY_AVAILABLE_FOR_ECOMMERCE]
		FROM vw_F_ITEM_INVENTORY_AVAILABLE_FORSKU_REBUILD	FII
		left join recycling.F_Asset							fa with(nolock)
			on fa.Id = FII.AssetId
		WHERE FII.IS_DROP_SHIP_ITEM = 0
			and fii.ITEM_STATUS_ID = 1 --Items in "Correct Item Returned" is not available on e-comm and "fi Generate" too
			and isnull(fa.AssetWorkflowStepId, 102) = 102 -- Resale
		GROUP BY  FII.ITEM_ID
	) IIV
		ON IIV.ITEM_ID = fi.ITEM_ID --Item is AVAILABLE_FOR_ECOMMERC


	if @C_IS_DEBUG = 1
	begin
		set @msg =  'SKU bundles - child qty recalculation'
		PRINT @msg;
		select
			@msg as what
			,iif(CONVERT(FLOAT, fil.ITEM_LOT_VALUE) = 0
				,0
				,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
			) as QUANTITY
			,iif(CONVERT(FLOAT, fil.ITEM_LOT_VALUE) = 0 or CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE)) < ISNULL(FII.LOCKED, 0)
				,0
				,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
			) as QUANTITY_AVAILABLE
			,ISNULL(FII.ALLOCATED, 0) as QUANTITY_ALLOCATED
			,iif(CONVERT(BIGINT, fil.ITEM_LOT_VALUE) = 0
				,0
				,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE_FOR_ECOMMERCE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
			) as QUANTITY_AVAILABLE_FOR_ECOMMERCE
			,fil.ITEM_LOT_VALUE as SUB_QUANTITY
			,fi.ITEM_TYPE_ID
			,sku.*
		from (
			select
				sku.item_id
			-- when the qty of the heading SKU changes - should recalculate all bundles (qty variations of this SKU)
			from #skus sku
			union 
			select
				fil.ITEM_ID
			from #skus						sku
			-- when triggered for one of the bundle SKUs - should recalculate this bundle (qty variations of this SKU)
			inner join F_ITEM				fi WITH(NOLOCK)
				ON fi.ITEM_ID = sku.item_id
				and fi.ITEM_TYPE_ID = 1
			inner join F_ITEM_LOT			fil WITH(NOLOCK)
				on fil.ITEM_ID_MASTER = sku.item_ID
		)									sku
		inner join F_ITEM							fi_child WITH(rowlock)
			ON fi_child.ITEM_ID = sku.item_id
			and fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
		inner join F_ITEM_LOT						fil		 WITH (NOLOCK)
			ON fil.ITEM_ID = sku.item_id
		inner join F_ITEM							FI		 WITH(NOLOCK)
			ON FI.ITEM_ID = fil.ITEM_ID_MASTER
		OUTER APPLY (
			SELECT
				dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fil.ITEM_ID)		as LOCKED
				,dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fil.ITEM_ID)	as ALLOCATED
		) FII

	end

	update fi_child set
		fi_child.QUANTITY = iif(CONVERT(FLOAT, fil.ITEM_LOT_VALUE) = 0
			,0
			,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
		)
		,fi_child.QUANTITY_AVAILABLE = iif(CONVERT(FLOAT, fil.ITEM_LOT_VALUE) = 0 or CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE)) < ISNULL(FII.LOCKED, 0)
			,0
			,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
		)
		,fi_child.QUANTITY_ALLOCATED = ISNULL(FII.ALLOCATED, 0)
		,fi_child.QUANTITY_AVAILABLE_FOR_ECOMMERCE = iif(CONVERT(BIGINT, fil.ITEM_LOT_VALUE) = 0
			,0
			,CONVERT(BIGINT, CONVERT(FLOAT, fi.QUANTITY_AVAILABLE_FOR_ECOMMERCE) / CONVERT(FLOAT, fil.ITEM_LOT_VALUE))
		)
		,fi_child.SUB_QUANTITY	= fil.ITEM_LOT_VALUE
		,fi_child.UPDATED_BY	= @C_PROCESS_CD
		,fi_child.UPDATED_DT	= @UTC_NOW
	from (
		select
			sku.item_id
		-- when the qty of the heading SKU changes - should recalculate all bundles (qty variations of this SKU)
		from #skus sku
		union 
		select
			fil.ITEM_ID
		from #skus						sku
		-- when triggered for one of the bundle SKUs - should recalculate this bundle (qty variations of this SKU)
		inner join F_ITEM				fi WITH(NOLOCK)
			ON fi.ITEM_ID = sku.item_id
			and fi.ITEM_TYPE_ID = 1
		inner join F_ITEM_LOT			fil WITH(NOLOCK)
			on fil.ITEM_ID_MASTER = sku.item_ID
	)									sku
	inner join F_ITEM							fi_child WITH(rowlock)
		ON fi_child.ITEM_ID = sku.item_id
		and fi_child.ITEM_TYPE_ID = 4 -- BUNDLE
	inner join F_ITEM_LOT						fil		 WITH (NOLOCK)
		ON fil.ITEM_ID = sku.item_id
	inner join F_ITEM							FI		 WITH(NOLOCK)
		ON FI.ITEM_ID = fil.ITEM_ID_MASTER
	OUTER APPLY (
		SELECT
			dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](fil.ITEM_ID)		as LOCKED
			,dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](fil.ITEM_ID)	as ALLOCATED
	) FII

	
	if @C_IS_DEBUG = 1
	begin
		set @msg =  'SKU Kit - Qty in the heading SKU of'
		print @msg;

		select
			@msg as what
			,FI.ITEM_ID
			,FI.QUANTITY_AVAILABLE - FII.Locked						as QUANTITY_AVAILABLE
			,FI.QUANTITY_AVAILABLE_FOR_ECOMMERCE - FII.Locked		as QUANTITY_AVAILABLE_FOR_ECOMMERCE
			,FI.QUANTITY_ALLOCATED									as QUANTITY_ALLOCATED
		from (
			select
				FI.ITEM_ID
				,min(isnull(cast(isnull(inner_skus.QUANTITY_AVAILABLE, 0)				/ nullif(inner_skus.QUANTITY, 0) as bigint), 0))	as QUANTITY_AVAILABLE
				,min(isnull(cast(isnull(inner_skus.QUANTITY_AVAILABLE_FOR_ECOMMERCE, 0) / NULLIF(inner_skus.QUANTITY, 0) as bigint), 0))	as QUANTITY_AVAILABLE_FOR_ECOMMERCE
				,min(cast(isnull(inner_skus.QUANTITY_ALLOCATED / NULLIF(inner_skus.QUANTITY, 0), 0) as bigint))								as QUANTITY_ALLOCATED
			FROM (
				SELECT
					FIC.ITEM_ID
				FROM #skus				SKU
				-- when triggered for the heading SKU - should recalculate the heading SKU
				INNER JOIN F_ITEM_COMBO FIC WITH(NOLOCK)
					ON FIC.ITEM_ID = SKU.item_id
				UNION
				SELECT
					FIC.ITEM_ID
				FROM #skus				SKU
				-- when the qty of included SKUs changes - should recalculate the heading SKU
				INNER JOIN F_ITEM_COMBO FIC WITH(NOLOCK)
					ON FIC.ITEM_ID_MASTER = SKU.item_id
			)									sku
			INNER JOIN dbo.F_ITEM				fi with(nolock)
				on fi.item_id = sku.ITEM_ID
				AND fi.ITEM_TYPE_ID = 3
			LEFT JOIN (
				SELECT
					FI.QUANTITY_ALLOCATED					as QUANTITY_ALLOCATED
					,FI.QUANTITY_AVAILABLE					as QUANTITY_AVAILABLE
					,FI.QUANTITY_AVAILABLE_FOR_ECOMMERCE	as QUANTITY_AVAILABLE_FOR_ECOMMERCE
					,fic.QUANTITY
					,fic.item_id
					,fic.ITEM_ID_MASTER
				FROM dbo.F_ITEM_COMBO		fic WITH (NOLOCK)
				INNER JOIN dbo.F_ITEM		fi	WITH (NOLOCK)
					on  fic.ITEM_ID_MASTER = fi.item_id
				WHERE fi.IS_DELETED = 0
			) inner_skus 
				ON inner_skus.ITEM_ID = FI.ITEM_ID
			GROUP BY FI.ITEM_ID
		) as FI
		outer apply (
			select
				isnull([dbo].[fn_float_GET_ITEM_QUANTITY_LOCKED](FI.ITEM_ID), 0) as Locked
		) as FII;
	end


	update FI set
		fi.QUANTITY							 = t.QUANTITY_AVAILABLE
		,fi.QUANTITY_AVAILABLE				 = t.QUANTITY_AVAILABLE - FII.Locked
		,fi.QUANTITY_AVAILABLE_FOR_ECOMMERCE = t.QUANTITY_AVAILABLE_FOR_ECOMMERCE - FII.Locked
		,fi.QUANTITY_ALLOCATED				 = t.QUANTITY_ALLOCATED
		,fi.UPDATED_BY						 = @C_PROCESS_CD
		,fi.UPDATED_DT						 = @UTC_NOW
	from (
		select 
			FI.ITEM_ID
			,min(isnull(cast(isnull(inner_skus.QUANTITY_AVAILABLE, 0)				/ nullif(inner_skus.QUANTITY, 0) as bigint), 0))	as QUANTITY_AVAILABLE
			,min(isnull(cast(isnull(inner_skus.QUANTITY_AVAILABLE_FOR_ECOMMERCE, 0) / nullif(inner_skus.QUANTITY, 0) as bigint), 0))	as QUANTITY_AVAILABLE_FOR_ECOMMERCE
			,min(cast(isnull(inner_skus.QUANTITY_ALLOCATED / nullif(inner_skus.QUANTITY, 0), 0) as bigint))								as QUANTITY_ALLOCATED
		from (
			SELECT
				FIC.ITEM_ID
			FROM #skus				SKU
			-- when triggered for the heading item
			INNER JOIN F_ITEM_COMBO FIC WITH(NOLOCK)
				ON FIC.ITEM_ID = SKU.item_id
			UNION
			SELECT
				FIC.ITEM_ID
			FROM #skus				SKU
			-- when the qty of included items changes
			INNER JOIN F_ITEM_COMBO FIC WITH(NOLOCK)
				ON FIC.ITEM_ID_MASTER = SKU.item_id
		)									sku
		INNER JOIN dbo.F_ITEM				fi with(nolock)
			on fi.item_id = sku.ITEM_ID
			AND fi.ITEM_TYPE_ID = 3
		LEFT JOIN (
			SELECT
				QUANTITY_ALLOCATED					as QUANTITY_ALLOCATED
				,QUANTITY_AVAILABLE					as QUANTITY_AVAILABLE
				,QUANTITY_AVAILABLE_FOR_ECOMMERCE	as QUANTITY_AVAILABLE_FOR_ECOMMERCE
				,fic.QUANTITY
				,fic.item_id
				,fic.ITEM_ID_MASTER
			FROM dbo.F_ITEM_COMBO		fic WITH (NOLOCK)
			INNER JOIN dbo.F_ITEM		fi	WITH (NOLOCK)
				on  fic.ITEM_ID_MASTER = fi.item_id
			WHERE fi.IS_DELETED = 0
		) inner_skus 
			ON inner_skus.ITEM_ID = FI.ITEM_ID
		GROUP BY FI.ITEM_ID
	)									t
	inner join	F_ITEM					fi		WITH(ROWLOCK)
		on fi.item_id = t.ITEM_ID
	outer apply (
		select
			isnull([dbo].[fn_float_GET_ITEM_QUANTITY_LOCKED](FI.ITEM_ID), 0) as Locked
	) as FII;


	if @C_IS_DEBUG = 1
	begin
		set @msg = 'QTY for inventory kit'
		PRINT @msg;
		select
			@msg as what
			,iif(II.IS_IN_SALES = 1, 0, 1)					as QUANTITY
			,iif(II.LOCKED > 0 or II.IS_IN_SALES = 1, 0, 1) as QUANTITY_AVAILABLE
			,iif(II.ALLOCATED > 0, 1, 0)					as QUANTITY_ALLOCATED
			,iif(II.LOCKED > 0 or II.IS_IN_SALES = 1 or ld.INVENTORY_CAPABILITY_ID is not null, 0, 1) as QUANTITY_AVAILABLE_FOR_ECOMMERCE
		from #skus									sku
		inner join	F_ITEM							fi	WITH(ROWLOCK)
			on fi.item_id = sku.ITEM_ID
		inner join [dbo].[F_ItemInventoryKitHeader]	iik WITH (NOLOCK)
			on iik.Id = fi.ITEM_TYPE_CHILD_ID
			and fi.ITEM_TYPE_ID = 9 -- INVENTORY KITTING
		inner join [dbo].[F_ITEM_INVENTORY]			iin with (nolock)
			on iik.ItemInventoryId = iin.ITEM_INVENTORY_ID
		left join [dbo].[F_LOCATION_DETAIL]			ld	with (nolock)
			on iin.LOCATION_ID = ld.INVENTORY_CAPABILITY_ID
			and LOCATION_DETAIL_NOT_INCLUDED = 1 --if kit location is not sellable
		OUTER APPLY(
			SELECT 
				 ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](iik.[ItemId]), 0)			AS LOCKED	   /*Item is LOCKED in SO*/
				,ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](iik.[ItemId]), 0)		as ALLOCATED   /*Item is ALLOCATED in SO*/	
				,ISNULL(dbo.[fn_bit_IS_KIT_ITEM_INVENTORY_NOT_AVAILABLE](iik.[ItemId]), 0)	as IS_IN_SALES
		) II   /*Item is in SO*/
	end
		UPDATE FI SET 
			 fi.QUANTITY						 = iif(II.IS_IN_SALES = 1, 0, 1)
			,fi.QUANTITY_AVAILABLE				 = iif(II.LOCKED > 0 or II.IS_IN_SALES = 1, 0, 1)
			,fi.QUANTITY_ALLOCATED				 = iif(II.ALLOCATED > 0, 1, 0)
			,fi.QUANTITY_AVAILABLE_FOR_ECOMMERCE = iif(II.LOCKED > 0 or II.IS_IN_SALES = 1 or ld.INVENTORY_CAPABILITY_ID is not null, 0, 1)
			,fi.UPDATED_BY						 = @C_PROCESS_CD
			,fi.UPDATED_DT						 = @UTC_NOW
		from #skus									sku
		inner join	F_ITEM							fi	WITH(ROWLOCK)
			on fi.item_id = sku.ITEM_ID
		inner join [dbo].[F_ItemInventoryKitHeader]	iik WITH (NOLOCK)
			on iik.Id = fi.ITEM_TYPE_CHILD_ID
			and fi.ITEM_TYPE_ID = 9 -- INVENTORY KITTING
		inner join [dbo].[F_ITEM_INVENTORY]			iin with (nolock)
			on iik.ItemInventoryId = iin.ITEM_INVENTORY_ID
		left join [dbo].[F_LOCATION_DETAIL]			ld	with (nolock)
			on iin.LOCATION_ID = ld.INVENTORY_CAPABILITY_ID
			and LOCATION_DETAIL_NOT_INCLUDED = 1 --if kit location is not sellable
		OUTER APPLY(
			SELECT 
				 ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_LOCKED](iik.[ItemId]), 0)			AS LOCKED	   /*Item is LOCKED in SO*/
				,ISNULL(dbo.[fn_float_GET_ITEM_QUANTITY_ALLOCATED](iik.[ItemId]), 0)		as ALLOCATED   /*Item is ALLOCATED in SO*/	
				,ISNULL(dbo.[fn_bit_IS_KIT_ITEM_INVENTORY_NOT_AVAILABLE](iik.[ItemId]), 0)	as IS_IN_SALES
		) II   /*Item is in SO*/


	-- create/copy dimensions
	if @C_IS_DEBUG = 1 PRINT 'Create/copy fi dimensions'

	declare @sku_ids bigint_id_array
	insert into @sku_ids
	select
		item_id 
	from #skus

	exec [dbo].[sp_UPDATE_ITEM_DIMENSIONS_BY_ITEM_MASTER]
		@T_ITEM_IDS  = @sku_ids
		,@C_USE_TRANSACTION = 1
	
END

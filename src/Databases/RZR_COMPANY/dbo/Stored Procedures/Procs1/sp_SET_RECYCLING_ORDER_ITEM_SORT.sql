-- =============================================
-- Author:		<V.DREBEZOVA>
-- Create date: <02/07/2014>
-- Description:	<create recycling order item SORTED>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_SORT]
--declare
	@RECYCLING_ORDER_ITEM_ID	BIGINT,
	@RECYCLING_ORDER_ID			BIGINT,	
	@ITEM_TYPE_ID				BIGINT,
	@WEIGHT_RECIEVED			FLOAT,
	@WEIGHT_TARE				FLOAT,
	@PACKAGING_TYPE_ID			INT,
	@WORKFLOW_TYPE_ID			INT,
	@ITEM_COUNT					INT,
	@LOCATION_ID				BIGINT,
	@NOTES						NVARCHAR(MAX),
	@REFERENCE					NVARCHAR(MAX)='',
	@USER_ID					BIGINT,
	@PARENT_ID					BIGINT,
	@PARENTS_KEY				VARCHAR(700),
	@STATE_PROGRAM_ID			BIGINT = NULL,
	@BusinessUnitId				INT = NULL,
	@IS_KEEP_PARENT_WORKFLOW	BIT = 0,
	@IP							BIGINT,
	@IsAudit					bit = 0
AS
BEGIN				

	DECLARE 
		@MAX_NUMBER				BIGINT,
		@PREFIX_START			BIGINT = 0, -- will add + 1			
		@SUBSTITUTE_PARENT_ID	BIGINT = @PARENT_ID,
		@ParentsKey				VARCHAR(700),
		@AUTO_NAME				VARCHAR(200),
		@IS_GET_AFTER_SETTLE	BIT = 0,
		@UTC_NOW				DATETIME = GETUTCDATE(),
		@PROCESS_CD				NVARCHAR(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID),
		@shouldLogWorkflowChange bit = 0;

	SET @WORKFLOW_TYPE_ID = ISNULL(@WORKFLOW_TYPE_ID, 1); -- Sort - indicated when unset
		
	SET XACT_ABORT ON
	BEGIN TRANSACTION SORT_TRAN		

		DECLARE @PARENT_ITEM_TYPE_ID BIGINT = (SELECT TOP(1) RECYCLING_ITEM_MASTER_ID FROM F_RECYCLING_ORDER_ITEM WITH(NOLOCK) WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID)


		IF (ISNULL(@RECYCLING_ORDER_ITEM_ID, -1) < 0)
		BEGIN
			-- FIX workflow setep and weight of the parent item
			UPDATE F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
				WEIGHT_REMAIN = ROUND(WEIGHT_REMAIN - (@WEIGHT_RECIEVED - @WEIGHT_TARE), 2),
				WORKFLOW_STEP_ID = CASE WHEN @IS_KEEP_PARENT_WORKFLOW = 0 
					THEN 1 -- Sort
					ELSE WORKFLOW_STEP_ID
					END,
				WorkflowStepPrevId			= iif(WorkflowStepPrevId is null, WORKFLOW_STEP_ID, WorkflowStepPrevId),
			/*	CASE
					WHEN 
						ROUND(WEIGHT_REMAIN - (@WEIGHT_RECIEVED - @WEIGHT_TARE), 2) <= 0 THEN 8
					ELSE 1
				END,*/
				UPDATED_BY = @PROCESS_CD,
				UPDATED_DT = @UTC_NOW,
				UPDATED_BY_USER = @USER_ID,
				UPDATED_BY_IP	= @IP,
				StateProgramId = @STATE_PROGRAM_ID
			WHERE RECYCLING_ORDER_ITEM_ID = @PARENT_ID
			
			SELECT TOP(1) 
				@IS_GET_AFTER_SETTLE = IIF(IS_INBOUND = 0, 
												/*Settlement Completed*/
												IIF(ro.RECYCLING_ORDER_STATUS_ID = 5, 1, 0), 
												IIF(OI.StatusId = 6, 1, 0) ),
				@SUBSTITUTE_PARENT_ID = ISNULL(roi.MAIN_INNER_LOT_IN_MERGE_ID, roi.RECYCLING_ORDER_ITEM_ID),
				@ParentsKey = roi.PARENTS_KEY
				
			FROM F_RECYCLING_ORDER_ITEM roi WITH (NOLOCK)
			INNER JOIN F_RECYCLING_ORDER ro WITH (NOLOCK)
				ON roi.RECYCLING_ORDER_ID = ro.RECYCLING_ORDER_ID
			LEFT JOIN dbo.F_RECYCLING_ORDER_INBOUND	OI  WITH (NOLOCK) 
				ON OI.RECYCLING_ORDER_ID = ro.RECYCLING_ORDER_ID
			WHERE roi.RECYCLING_ORDER_ITEM_ID = @PARENT_ID
			
			-- AUTO_NAME for the new item
			SELECT 
				@MAX_NUMBER = MAX(CAST(SUBSTRING(AUTO_NAME, PATINDEX('%[\-][^\-]%', AUTO_NAME) + 1, LEN(AUTO_NAME)) AS INT))
			FROM dbo.F_RECYCLING_ORDER_ITEM WITH (NOLOCK)
			WHERE PARENT_ID = @PARENT_ID

			SET @AUTO_NAME = CAST(@SUBSTITUTE_PARENT_ID AS VARCHAR(200))
					+ '-'
					+ CAST(ISNULL(@MAX_NUMBER, @PREFIX_START) + 1 AS VARCHAR(250))
			

			-- Price from the contract (if any)
			DECLARE 
				@CONTRACT_ID			BIGINT,
				@ITEM_PRICE				FLOAT	= NULL,
				@PRICE_TYPE_ID			INT		= NULL,
				@USE_DEFAULT_PRICE		BIT,
				@DEFAULT_PRICE			FLOAT,
				@DEFAULT_PRICE_TYPE_ID	INT

			SELECT 
				@CONTRACT_ID			= CUSTOMER_ID,
				@USE_DEFAULT_PRICE		= USE_DEFAULT_PRICE,
				@DEFAULT_PRICE			= DEFAULT_PRICE,
				@DEFAULT_PRICE_TYPE_ID	= DEFAULT_PRICE_TYPE_ID
			FROM [dbo].[F_RECYCLING_ORDER] WITH (NOLOCK)
			WHERE [RECYCLING_ORDER_ID] = @RECYCLING_ORDER_ID

			IF (@CONTRACT_ID IS NOT NULL) BEGIN
				
				SELECT 
					@ITEM_PRICE			= PRICE,
					@PRICE_TYPE_ID		= [PriceTypeId]
				FROM [recycling].[F_CommodityRule] WITH (NOLOCK)
				WHERE [RecyclingOrderId] = @RECYCLING_ORDER_ID
				  AND [CommodityId]     = ISNULL(@ITEM_TYPE_ID, @PARENT_ITEM_TYPE_ID)

				IF (ISNULL(@USE_DEFAULT_PRICE, 0) = 0) BEGIN
					SELECT @DEFAULT_PRICE = NULL, @DEFAULT_PRICE_TYPE_ID = NULL
				END				
			END

			--take id from the custom identity
			DECLARE @ROI_ID BIGINT
			SELECT 
				@ROI_ID = TABLE_IDENTITY_VALUE + 1
			FROM [dbo].[U_TABLE_IDENTITY]  	WITH (ROWLOCK, UPDLOCK) --raise isolation level to snapshot
			WHERE TABLE_IDENTITY_ID = 2 -- [dbo].[F_RECYCLING_ORDER_ITEM]

			if (nullif(rtrim(ltrim(@PARENTS_KEY)), '') is null) begin
				SET @PARENTS_KEY = @ParentsKey
			end

			INSERT INTO F_RECYCLING_ORDER_ITEM
			(
				RECYCLING_ORDER_ITEM_ID,
				RECYCLING_ORDER_ID,
				RECYCLING_ITEM_MASTER_ID,
				ITEM_PRICE,
				PRICE_TYPE_ID,
				WEIGHT_RECEIVED,
				WEIGHT_TARE,
				PACKAGING_TYPE_ID,
				WORKFLOW_STEP_ID,
				AUTO_NAME,
				ITEM_COUNT,
				LOCATION_ID,
				NOTES,
				REFERENCE,
				INSERTED_BY_USER,
				PARENT_ID,
				PARENTS_KEY,
				WEIGHT_REMAIN,
				IS_INCLUDE_IN_INITIAL,
				IS_GET_AFTER_SETTLE,
				INSERTED_BY,
				INSERTED_DT,
				[InitialWorkflowId],
				StateProgramId,
				BusinessUnitId
			)
			VALUES
			(
				@ROI_ID,
				@RECYCLING_ORDER_ID,
				ISNULL(@ITEM_TYPE_ID, @PARENT_ITEM_TYPE_ID),
				ISNULL(@ITEM_PRICE, @DEFAULT_PRICE),
				ISNULL(ISNULL(@PRICE_TYPE_ID, @DEFAULT_PRICE_TYPE_ID), 1),
				@WEIGHT_RECIEVED,
				@WEIGHT_TARE,
				@PACKAGING_TYPE_ID,
				@WORKFLOW_TYPE_ID,
				@AUTO_NAME,
				@ITEM_COUNT,
				@LOCATION_ID,
				@NOTES,
				@REFERENCE,
				@USER_ID,
				@PARENT_ID,
				ISNULL(@PARENTS_KEY, '') + CAST(@PARENT_ID AS VARCHAR(100)) + '|',
				ROUND(@WEIGHT_RECIEVED - @WEIGHT_TARE, 2),			
				0,	
				@IS_GET_AFTER_SETTLE,
				@PROCESS_CD,
				@UTC_NOW,
				@WORKFLOW_TYPE_ID,
				@STATE_PROGRAM_ID,
				@BusinessUnitId
			)

			--set new id into the custom identity (TODO: move this into the trigger, if someone will deside, that it would be better)
			UPDATE [dbo].[U_TABLE_IDENTITY]	WITH (ROWLOCK, UPDLOCK)	SET
				TABLE_IDENTITY_VALUE = @ROI_ID
			WHERE TABLE_IDENTITY_ID = 2 -- [dbo].[F_RECYCLING_ORDER_ITEM]
				
			SET @RECYCLING_ORDER_ITEM_ID = @ROI_ID
			set @shouldLogWorkflowChange = 1;

		END	
		ELSE BEGIN

			IF (@PARENT_ID IS NOT NULL) BEGIN
				DECLARE @OLD_WEIGHT FLOAT = 
					(SELECT WEIGHT_RECEIVED - WEIGHT_TARE FROM F_RECYCLING_ORDER_ITEM WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID)
					
				UPDATE 	F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
					WEIGHT_REMAIN = ROUND(WEIGHT_REMAIN + (@OLD_WEIGHT - (@WEIGHT_RECIEVED - @WEIGHT_TARE)), 2),
					--WORKFLOW_STEP_ID = 
					--CASE
					--	WHEN 
					--		ROUND(WEIGHT_REMAIN + (@OLD_WEIGHT - (@WEIGHT_RECIEVED - @WEIGHT_TARE)), 2) > 0 THEN 1
					--	ELSE 8
					--END,
					UPDATED_BY = @PROCESS_CD,
					UPDATED_DT = @UTC_NOW,
					UPDATED_BY_USER	= @USER_ID,
					UPDATED_BY_IP   = @IP
				WHERE 	RECYCLING_ORDER_ITEM_ID = @PARENT_ID	
			END				
		
			UPDATE F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
				RECYCLING_ITEM_MASTER_ID		= ISNULL(@ITEM_TYPE_ID, RECYCLING_ITEM_MASTER_ID),
				WEIGHT_RECEIVED					= @WEIGHT_RECIEVED,
				WEIGHT_TARE						= @WEIGHT_TARE,
				PACKAGING_TYPE_ID				= @PACKAGING_TYPE_ID,
				WORKFLOW_STEP_ID				= @WORKFLOW_TYPE_ID,				
				ITEM_COUNT						= @ITEM_COUNT,
				LOCATION_ID						= @LOCATION_ID,
				NOTES							= @NOTES,
				REFERENCE						= @REFERENCE,
				[UPDATED_BY_USER]				= @USER_ID,
				WEIGHT_REMAIN					= 
					ROUND(CASE
						WHEN WEIGHT_REMAIN = (WEIGHT_RECEIVED - WEIGHT_TARE) THEN @WEIGHT_RECIEVED - @WEIGHT_TARE
						ELSE ((@WEIGHT_RECIEVED - @WEIGHT_TARE) - (WEIGHT_RECEIVED - WEIGHT_TARE)) + WEIGHT_REMAIN
					END, 2),
				UPDATED_BY			= @PROCESS_CD,
				UPDATED_DT			= @UTC_NOW,
				UPDATED_BY_IP		= @IP,
				StateProgramId		= @STATE_PROGRAM_ID,
				BusinessUnitId		= @BusinessUnitId
			WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID			
		
		END	
			
	COMMIT TRANSACTION SORT_TRAN
	
	if (@shouldLogWorkflowChange = 1)
		begin
			declare @lotsToLog [dbo].[bigint_ID_ARRAY];
				insert into @lotsToLog
				select FROI_CHILD.[RECYCLING_ORDER_ITEM_ID]
				from [dbo].[F_RECYCLING_ORDER_ITEM] FROI with (nolock)		
						 inner join [dbo].[F_RECYCLING_ORDER_ITEM] FROI_CHILD with (nolock)
									on FROI_CHILD.[PARENT_ID] = FROI.[RECYCLING_ORDER_ITEM_ID]
						 inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT with (nolock)
									on FROI.[WORKFLOW_STEP_ID] = CRWT.[WORKFLOW_TYPE_ID]
						 inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT_CHILD with (nolock)
									on FROI_CHILD.[WORKFLOW_STEP_ID] = CRWT_CHILD.[WORKFLOW_TYPE_ID]
						 inner join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
									on FROI.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID]
						 inner join [dbo].[F_RECYCLING_ORDER_INBOUND] FROIN with (nolock)
									on FRO.[RECYCLING_ORDER_ID] = FROIN.[RECYCLING_ORDER_ID]
						 inner join [dbo].[F_CUSTOMER] FC with (nolock)
									on FRO.[CUSTOMER_ID] = FC.[CUSTOMER_ID]
				where coalesce(ISNULL(FROI.BusinessUnitId, 0), CRWT.[BusinessUnitId], FROIN.[BusinessUnitId], FC.[BusinessUnitId]) <>
					  coalesce(FROI_CHILD.BusinessUnitId, CRWT_CHILD.[BusinessUnitId], FROIN.[BusinessUnitId],
							   FC.[BusinessUnitId])
				  and FROI.[IS_EXCESS_FROM_SCRAP] = 0
				  and CRWT_CHILD.IsFinal = 1
				  and FROI_CHILD.[RECYCLING_ORDER_ITEM_ID] = @RECYCLING_ORDER_ITEM_ID

				exec [recycling].[sp_SetLotWorkflowChangeLog]
					 @LotIds = @lotsToLog,
					 @Activity = N'Lot from Sort',
					 @StatusId = 1, -- pending
					 @UserId = @USER_ID
		end

	-- this tabel result is used in exec-insert. Update all usages on column modifications!
	SELECT 
		i.RECYCLING_ORDER_ITEM_ID	as [value]
		,i.LOT_AUTO_NAME			as [label]
	from dbo.F_RECYCLING_ORDER_ITEM i with (nolock)
	where RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID
				
END
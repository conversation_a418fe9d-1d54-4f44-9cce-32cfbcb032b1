-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <02/03/2014>
-- Description:	<Returns the Sub Total of the account>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_ITEM_PRICE]
	(@RECYCLING_ORDER_ITEM_ID	BIGINT
	,@SETTLEMENT_STATE_ID		INT)
	--,@MODE_PROCESSED		  BIT = 1)	
RETURNS FLOAT
BEGIN
	DECLARE @PRICE FLOAT = 0.0
	
	SELECT
		@PRICE = [dbo].[fn_money_GET_ITEM_PRICE_BY_TYPE_RECYCLING](
			OI.ITEM_PRICE, 
			OI.PRICE_TYPE_ID, 
			OI.ITEM_COUNT, 
			OI.WEIGHT_RECEIVED, 
			OI.WEIGHT_TARE)
	FROM F_RECYCLING_ORDER_ITEM					OI	WITH(NOLOCK)
	LEFT JOIN recycling.F_SortedLotFromBatch	FSL WITH(NOLOCK)
		ON FSL.LotId = OI.RECYCLING_ORDER_ITEM_ID
	WHERE OI.RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID
	  AND OI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
	  AND OI.IS_MERGED = 0
	  AND((
	  		(@SETTLEMENT_STATE_ID = 2
	  			OR @SETTLEMENT_STATE_ID = 3 AND NOT EXISTS(
	  				SELECT TOP(1) 1
	  				FROM [recycling].F_Asset	WITH(NOLOCK)
	  				WHERE [RecyclingOrderItemId] = OI.RECYCLING_ORDER_ITEM_ID
	  					AND [IsGetAfterSettle] = 0)
	  		)
	  		--AND OI.IS_INCLUDE_IN_INITIAL = 0
	  		AND NOT EXISTS(
	  			SELECT TOP(1) 1
	  			FROM F_RECYCLING_ORDER_ITEM FOI	WITH (NOLOCK)
	  			WHERE OI.RECYCLING_ORDER_ID = FOI.RECYCLING_ORDER_ID
				  AND FOI.IS_GET_AFTER_SETTLE = 0 --SORTED BEFORE THE SETTLEMENT
	  			  AND (FOI.PARENT_ID = @RECYCLING_ORDER_ITEM_ID
	  				OR FOI.PARENT_ID IN (
	  					SELECT RECYCLING_ORDER_ITEM_PRIMARY_ID
	  					FROM dbo.F_RECYCLING_ORDER_ITEM_MERGE WITH (NOLOCK)
	  					WHERE RECYCLING_ORDER_ITEM_ID = OI.RECYCLING_ORDER_ITEM_ID)
	  				)
	  		)
	  	)
	  	OR ((@SETTLEMENT_STATE_ID = 1 or @SETTLEMENT_STATE_ID = 5) AND OI.IS_INCLUDE_IN_INITIAL = 1)
	  )
	  AND FSL.InputId IS NULL

	RETURN @PRICE
END
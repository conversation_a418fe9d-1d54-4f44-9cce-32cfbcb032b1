CREATE TABLE [dbo].[F_DOCUMENT_SET] (
    [DOCUMENT_SET_ID]      BIGINT          IDENTITY (1, 1) NOT NULL,
    [DOCUMENT_SET_TYPE_ID] BIGINT          NOT NULL,
    [TITLE]                NVARCHAR (2048) NOT NULL,
    [EXP_DATE]             DATETIME        NULL,
    [DESCRIPTION]          NVARCHAR (MAX)  NULL,
    [IS_DELETED]           BIT             CONSTRAINT [DF_F_DOCUMENT_SET_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]          BIGINT          NOT NULL,
    [INSERTED_DT]          DATETIME        CONSTRAINT [DF_F_DOCUMENT_SET_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]           BIGINT          NULL,
    [UPDATED_DT]           DATETIME        NULL,
    [DELETED_BY]           BIGINT          NULL,
    [DELETED_DT]           DATETIME        NULL,
    CONSTRAINT [PK_F_DOCUMENT_SET] PRIMARY KEY CLUSTERED ([DOCUMENT_SET_ID] ASC),
    CONSTRAINT [FK_F_DOCUMENT_SET_D_DOCUMENT_SET_TYPE] FOREIGN KEY ([DOCUMENT_SET_TYPE_ID]) REFERENCES [dbo].[D_DOCUMENT_SET_TYPE] ([DOCUMENT_SET_TYPE_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is for crm -> document management', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_DOCUMENT_SET';


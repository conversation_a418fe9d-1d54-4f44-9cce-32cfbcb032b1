-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
CREATE function [dbo].[fn_str_GetWarehouseCapabilityId]
(	
)
RETURNS varchar(200)
AS
BEGIN
	
	DECLARE @C_RETURN_VAL bigint = null
	
	select
		@C_RETURN_VAL = [INVENTORY_CAPABILITY_TYPE_ID]
	from [dbo].[C_INVENTORY_CAPABILITY_TYPE] with (nolock)
	where [INVENTORY_CAPABILITY_VALUE] = 'warehouse' and IsSystem = 1
	
	RETURN @C_RETURN_VAL

END
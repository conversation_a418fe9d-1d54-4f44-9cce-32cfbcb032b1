CREATE FUNCTION [dbo].[fn_str_GET_RECYCLING_ORDER_BOL_NUMBER]
    (@C_RECYCLING_ORDER_ID BIGINT)
RETURNS NVARCHAR(MAX)
BEGIN

	DECLARE @ZEROS VARCHAR(10) = '000000000'
	
    RETURN (
	   SELECT TOP(1) 
			'BOL-' + 
			SUBSTRING(@ZEROS, 
				1, 
				<PERSON><PERSON>(@ZEROS) 
					- LEN([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](FROI.AUTO_NAME)))
					+ CAST([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](FROI.AUTO_NAME) AS NVARCHAR(150)
			)
		FROM F_RECYCLING_ORDER_INBOUND		  FROI WITH(NOLOCK)
		WHERE FROI.RECYCLING_ORDER_ID = @C_RECYCLING_ORDER_ID)
END
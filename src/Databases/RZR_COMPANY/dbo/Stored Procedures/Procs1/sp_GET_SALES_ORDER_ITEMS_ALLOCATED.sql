CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ITEMS_ALLOCATED]
--declare
	@ORDER_ID			BIGINT			
	,@ORDER_COLUMN_NAME	NVARCHAR(150)	= N'SalesOrderItemId'
	,@ORDER_DIRECTION	NVARCHAR(20)	= N'DESC'
	,@ITEMS_PER_PAGE	INT				= 1000
	,@PAGE_INDEX		INT				= 0
	,@FILTER_WHERE		NVARCHAR(MAX)	= NULL
	,@C_IS_DEBUG		BIT				= 0
	,@PARENT_ID			BIGINT			= NULL
AS
BEGIN	

	if (@PARENT_ID is null)
		select @PARENT_ID = 0
	else
	begin
		select @PARENT_ID = Id
		from [dbo].[F_ItemInventoryKitHeader] with (nolock)
		where  [ItemInventoryId] = @PARENT_ID
	end

	SET @ITEMS_PER_PAGE = ISNULL(@ITEMS_PER_PAGE,0)
	SET @PAGE_INDEX = ISNULL(@PAGE_INDEX,0) 

	SET @ORDER_COLUMN_NAME	= ISNULL(NULLIF(@ORDER_COLUMN_NAME, ''), N'SalesOrderItemId')
	SET @ORDER_DIRECTION	= ISNULL(NULLIF(@ORDER_DIRECTION, ''), N'DESC')

	DECLARE @filterCondition NVARCHAR(MAX) = N'';
	IF (@FILTER_WHERE IS NOT NULL AND @FILTER_WHERE != N'' AND @FILTER_WHERE != 'null') BEGIN
		SET @filterCondition = N' WHERE (' + @FILTER_WHERE + N')';
	END

	create table #kitheaders (Id bigint)
	insert into #kitheaders
	select 
		iikh.Id
	from [dbo].[F_ItemInventoryKit] iik with (nolock)
	inner join [dbo].[F_SALES_ORDER_ITEM] soi with (nolock)
		on iik.ItemInventoryId = soi.ITEM_INVENTORY_ID
	inner join [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)
		on iik.ItemInventoryKitHeaderId = iikh.Id
	where soi.SALES_ORDER_ID = @ORDER_ID	

	DECLARE @query1 NVARCHAR (MAX) = N' 
	declare @TaxPc money = 0;
	select 	@TaxPc = 
			CASE
				WHEN EXISTS (SELECT TOP(1) 1 FROM F_INVOICE	IV WITH(NOLOCK) WHERE IV.INVOICE_TYPE_ID = 1 AND 
					IV.ORDER_ID = ' + CAST(@ORDER_ID AS NVARCHAR(20)) + ' AND IV.IS_VOIDED = 0 AND IV.PAID_DATE IS NOT NULL) THEN FSO.TAX 
				ELSE dbo.fn_float_GET_TAX_RATE(FSO.TAX_ID, 1)
			END
	from [dbo].[F_SALES_ORDER] FSO with (nolock)
	where FSO.[SALES_ORDER_ID] = ' + CAST(@ORDER_ID AS NVARCHAR(20)) + '
	   ;WITH m_data as (
		SELECT 
			DISTINCT
			 FSOI.SALES_ORDER_ITEM_ID							AS SalesOrderItemId
			,FSOI.SALES_ORDER_ID								AS SalesOrderId
			,FPM.ITEM_MASTER_ID									AS MasterItemId
			,fim.[ITEM_NUMBER]									AS MasterItemNumber
			,fim.[ITEM_IPN]										AS IPN
			,fim.[ITEM_MPN]										AS MPN
			,FSOIMIN.SALES_ORDER_ITEM_MASTER_INTERNAL_NUMBER	AS InternalMasterItemNumber
			,ISNULL(FSOI.ITEM_TITLE, ISNULL(
				(SELECT 
			 		ITEM_DESC 
				 FROM dbo.F_ITEM WITH (NOLOCK) 
				 WHERE ITEM_ID = FII.ITEM_ID)
				,fimt.ITEM_TITLE))							AS MasterItemTitle
			,m.[MANUFACTURER_ID]								AS MFGId
			,m.[MANUFACTURER_CD]								AS MFGCd
			,FSOI.ITEM_INVENTORY_ID								AS InventoryItemId
			,FII.[ITEM_INVENTORY_SERIAL]						AS Serial
			,FII.[ITEM_INVENTORY_UNIQUE_ID]						AS UniqueId
			,ISNULL(wloc.WAREHOUSE_ID, DW_PREV.WAREHOUSE_ID)		AS WarehouseId
			,ISNULL(wloc.WAREHOUSE_CD, DW_PREV.WAREHOUSE_CD)		AS WarehouseName
			,ISNULL(FII.[LOCATION_ID], FII.LOCATION_PREV_ID)		AS LocationId
			,(CASE 
				WHEN FII.LOCATION_ID IS NOT NULL THEN dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FII.LOCATION_ID)
				ELSE dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FL_PREV.LOCATION_ID)
			END)												AS LocationName
			,FSOI.CONDITION_ID									AS RealConditionId
			,Cond.ITEM_CONDITION_DESC								AS RealConditionCd
			,FSOI.ITEM_QUANTITY									AS Quantity
			,FSOI.ITEM_QUANTITY_ORDERED							AS QuantityOrdered
			,-1													AS Available
			,FII.[ITEM_STATUS_ID]								AS StatusId
			,CASE 
				WHEN RI.RMA_ITEM_ID IS NULL THEN iis.[STATUS_CD]						
				ELSE ''RMA received''
				END AS StatusCd
			,FSOI.ITEM_PRICE									AS Price
			,FSOI.ITEM_PRICE									AS PriceInitial
			,isnull(FSOI.TaxRate, @TaxPc)	AS TaxPc ' 
	    
	    declare @query2 nvarchar(max) = N'
			,FSOI.APPLY_TAX										AS TaxOn
			,IIF(FSOI.APPLY_TAX = 1, FSOI.ITEM_PRICE * FSOI.ITEM_QUANTITY * isnull(FSOI.TaxRate, @TaxPc), 0) AS [Tax]
			,FSOI.ITEM_NOTES									AS Notes
			,(FSOI.ITEM_PRICE * FSOI.ITEM_QUANTITY)				AS Amount
			,FII.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED			AS QtyInUnit		
			,ISNULL(FSOI.ITEM_ID, FII.ITEM_ID)					AS Sku
			,FII.IS_DELETED										AS IsRelatedInventoryItemDeleted
			,PC.PRODUCT_CODE_ID									AS ProductCodeId
			,PC.PRODUCT_CODE_VALUE								AS ProductCodeValue
			,PC.PRODUCT_CODE_VALUE								AS UnallocateProductCodeValue
			,rev.INVENTORY_CAPABILITY_ID						AS RevisionId
			,rev.INVENTORY_CAPABILITY_VALUE						AS Revision
			,unallocateDic.INVENTORY_CAPABILITY_VALUE			AS UnallocateRevision
			,FC.CUSTOMER_ID										AS CustomerId
			,FC.CUSTOMER_NAME									AS CustomerName
			,FII.[CUSTOMER_ID]									AS VendorId
			,c.CUSTOMER_NAME + 
				ISNULL( ''(''+ FC.CUSTOMER_NAME + '')'', '''')		AS VendorName
			,U.UserName											AS UserName
			,SHIP.SHIPPING_ID									AS ShippingId
			,SHIP.SHIPPING_ID									AS ShippingLabel
			,SHIP.STATUS_ID										AS ShippingStatusId
			,SHIPS.SHIPPING_STATUS_CD							AS ShippingStatusLabel
			,PO.PURCHASE_ORDER_ID								AS PurchaseOrderId
			,PO.AUTO_NAME										AS PurchaseOrderName
			,OOST.ENTITY_SUBJECT_TYPE_ID							AS PurchaseOrderTypeId 
			,COALESCE(PO.PURCHASE_ORDER_ID, NPO.PURCHASE_ORDER_ID)	AS AllPurchaseOrderId --for sales order admin tab we need not only DropShip POs, also stock POs with which items come 
			,COALESCE(PO.AUTO_NAME, NPO.AUTO_NAME)					AS AllPurchaseOrderName
			,COALESCE(POI.[InternalCost], NPOI.[InternalCost])		AS [InternalCost]
			,COALESCE(POU.UserName, NPOU.UserName)				AS PurchaseOrderRep
			,ISNULL(FII.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0.00) AS ItemCost	
			,[dbo].[fn_money_GET_ITEM_PRICE_BY_TYPE_RESALE](
					FSOI.ITEM_PRICE
					,FSOI.PRICE_TYPE_ID
					,FSOI.ITEM_QUANTITY) 
					- ISNULL(FII.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0.00) * FSOI.ITEM_QUANTITY
					- (ISNULL(RPOI.PRICE, 0.00))
					- COALESCE(POI.[InternalCost], NPOI.[InternalCost], 0)	AS [Profit]	-- Subtotal - Cost - RepairCost - InternalCost
			,iik.[ItemInventoryKitHeaderId]
			,[dbo].[fn_bit_IsInventoryHasPartAdded](FSOI.ITEM_INVENTORY_ID)	as HasChildren
			,ASSET.[Tag]										AS [AssetTag]
			,ASSET.[AssetWorkflowStepId]						AS [AssetWorkflowStepId]
			,AWS.[Label]										AS [AssetWorkflowStepName]
			,FAG.GradeLevelMark									AS FinalGrade
			,FAG.Notes											AS GradeNote
			,ISNULL(RPOI.PRICE, 0.00) AS RepairCost
			,isnull(FSOI.TaxRate, @TaxPc) AS TaxRate
			,FSOI.TaxRateDescription AS TaxRateDescription
			,FSOI.TaxRateApplicationDate AS TaxRateApplicationDate
		'
	
	declare @query3 nvarchar(max) = N'
	    FROM F_SALES_ORDER_ITEM							FSOI	WITH(NOLOCK)
		INNER JOIN [dbo].[F_PRODUCT_MASTER]				FPM		WITH(NOLOCK)
			ON FSOI.PRODUCT_MASTER_ID = FPM.PRODUCT_MASTER_ID
		INNER JOIN F_SALES_ORDER						FSO		WITH(NOLOCK)
			ON FSOI.SALES_ORDER_ID = FSO.SALES_ORDER_ID
	    inner join F_ORDER_ORDER_SUBJECT_TYPE FOOST with (nolock)
            on FSO.SALES_ORDER_ID = FOOST.ORDER_ID and FOOST.ENTITY_TYPE_ID = 1
		INNER JOIN F_ITEM_INVENTORY					FII		WITH(NOLOCK)		
			ON FSOI.ITEM_INVENTORY_ID = FII.ITEM_INVENTORY_ID		
		inner join [dbo].[F_CUSTOMER] c with (nolock)
			on fii.CUSTOMER_ID = c.CUSTOMER_ID
		inner JOIN [dbo].F_ITEM_MASTER fim  with (nolock)
			ON fii.ITEM_MASTER_ID	 = fim.ITEM_MASTER_ID
		inner join [dbo].[D_MANUFACTURER] m with (nolock)
			on fim.MANUFACTURER_ID = m.MANUFACTURER_ID
		inner JOIN [dbo].F_ITEM_MASTER_TITLE	fimt  with (nolock)
			ON fim.ITEM_MASTER_ID = fimt.ITEM_MASTER_ID						
		LEFT JOIN D_ITEM_CONDITION	Cond	WITH(NOLOCK)
			ON FSOI.CONDITION_ID  = Cond.ITEM_CONDITION_ID	
		left join [dbo].[F_LOCATION] loc with (nolock)
			on fii.LOCATION_ID = loc.LOCATION_ID
		left join D_WAREHOUSE			wloc	WITH(NOLOCK)
			ON loc.WAREHOUSE_ID		= wloc.WAREHOUSE_ID	
		LEFT JOIN F_LOCATION '
		 
		DECLARE @query4 NVARCHAR (MAX) = N'	FL_PREV	WITH(NOLOCK)
			ON FII.LOCATION_PREV_ID = FL_PREV.LOCATION_ID		
		LEFT JOIN D_WAREHOUSE							DW_PREV	WITH(NOLOCK)
			ON DW_PREV.WAREHOUSE_ID		= FL_PREV.WAREHOUSE_ID	
	
		LEFT JOIN F_PRODUCT_CODE						PC		WITH(NOLOCK)
			ON FII.[ProductCodeIdHeci] = PC.PRODUCT_CODE_ID	
		LEFT JOIN D_INVENTORY_CAPABILITY				unallocateDic WITH(NOLOCK)
			ON unallocateDic.INVENTORY_CAPABILITY_ID = FSOI.INVENTORY_CAPABILITY_ID
		LEFT JOIN F_CUSTOMER							FC		WITH(NOLOCK)
			ON FC.CUSTOMER_ID = FSOI.CUSTOMER_ID	
		LEFT JOIN tb_User								U		WITH(NOLOCK)
			ON U.UserID = FII.AUTHOR_USER_ID
		LEFT JOIN F_SALES_ORDER_ITEM_MASTER_INTERNAL_NUMBER    FSOIMIN	WITH(NOLOCK)
			ON FSOIMIN.SALES_ORDER_ID = FSO.SALES_ORDER_ID AND
			   FSOIMIN.ITEM_MASTER_ID = FII.ITEM_MASTER_ID
		LEFT JOIN F_SHIPPING_PACKAGE 					PACK 	WITH(NOLOCK)
			ON PACK.PACKAGE_ID = FSOI.PACKAGE_ID
		LEFT JOIN F_SHIPPING							SHIP	WITH(NOLOCK)
			ON SHIP.SHIPPING_ID = PACK.SHIPPING_ID
		LEFT JOIN D_SHIPPING_STATUS						SHIPS	WITH(NOLOCK)
			ON SHIPS.SHIPPING_STATUS_ID = SHIP.STATUS_ID
		LEFT JOIN dbo.F_RMA_ITEM RI  
			ON RI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID
		LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM POI	 WITH(NOLOCK) 
			ON POI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID
				AND POI.IS_DELETED = 0
		LEFT JOIN dbo.F_PURCHASE_ORDER PO WITH(NOLOCK) 
			ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
				AND PO.SALES_ORDER_ID = FSO.SALES_ORDER_ID
				AND PO.IS_DELETED = 0
		LEFT JOIN tb_User								POU		WITH(NOLOCK)
			ON POU.UserID = PO.USER_ID
		LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM NPOI	 WITH(NOLOCK) 
			ON NPOI.INVENTORY_ITEM_ID = FII.ITEM_INVENTORY_ID
				AND NPOI.IS_DELETED = 0
		LEFT JOIN dbo.F_PURCHASE_ORDER NPO WITH(NOLOCK) 
			ON NPO.PURCHASE_ORDER_ID = NPOI.PURCHASE_ORDER_ID
				AND NPO.IS_DELETED = 0
		LEFT JOIN tb_User								NPOU		WITH(NOLOCK)
			ON NPOU.UserID = NPO.USER_ID
		left join [dbo].[D_INVENTORY_CAPABILITY] rev with (nolock)
			on fii.RevisionId = rev.INVENTORY_CAPABILITY_ID				         
		LEFT JOIN dbo.F_ORDER_ORDER_SUBJECT_TYPE OOST WITH(NOLOCK) ON OOST.ORDER_ID = PO.PURCHASE_ORDER_ID
			AND OOST.ENTITY_TYPE_ID = 3 /*Purchase Order*/
		left join [dbo].[F_ItemInventoryKit] iik with (nolock)
			on FSOI.[ITEM_INVENTORY_ID] = iik.[ItemInventoryId]
		left join [dbo].[D_ITEM_INVENTORY_STATUS] iis with (nolock)
			on fii.ITEM_STATUS_ID = iis.[ITEM_INVENTORY_STATUS_ID]
		LEFT JOIN [recycling].[F_Asset]					ASSET		WITH(NOLOCK)
			ON FII.AssetId = ASSET.Id
		LEFT JOIN [recycling].[C_AssetWorkflowStep]		AWS			WITH(NOLOCK)
			ON ASSET.[AssetWorkflowStepId] = AWS.[Id]

		LEFT JOIN [recycling].[F_AssetGrading]		FAG WITH (NOLOCK)
			ON FAG.AssetId = FII.AssetId
		outer apply (select sum(RPOI.PRICE) as PRICE
                      from F_PURCHASE_ORDER_ITEM RPOI with (nolock)
                               inner join F_PURCHASE_ORDER RPO with (nolock)
                                          on RPOI.PURCHASE_ORDER_ID = RPO.PURCHASE_ORDER_ID
                      where (FOOST.ENTITY_SUBJECT_TYPE_ID = 12 and RPO.REPAIR_SALES_ORDER_ID = FSO.SALES_ORDER_ID and FII.ITEM_INVENTORY_ID = RPOI.REPAIR_INVENTORY_ITEM_ID or
                             FOOST.ENTITY_SUBJECT_TYPE_ID <> 12 and
                             FII.ITEM_INVENTORY_ID = RPOI.REPAIR_INVENTORY_ITEM_ID)
                        and RPOI.IS_DELETED = 0
                        and RPOI.RECEIVE_STATUS_ID = 3) as RPOI
		WHERE FSOI.SALES_ORDER_ID = ' + CAST(@ORDER_ID AS NVARCHAR(20)) + '
		  AND FSOI.ITEM_INVENTORY_ID IS NOT NULL AND (' + CAST(@PARENT_ID AS NVARCHAR(20)) + ' = 0 and iik.[ItemInventoryId] is null OR ' + 
		  CAST(@PARENT_ID AS NVARCHAR(20)) + ' = iik.[ItemInventoryKitHeaderId])' 
	
	DECLARE @query5 NVARCHAR (MAX) = N' union all
select distinct 
	-1													AS SalesOrderItemId
	,' + CAST(@ORDER_ID AS NVARCHAR(20)) + '			AS SalesOrderId
	,FPM.ITEM_MASTER_ID									AS MasterItemId
	,im.ITEM_NUMBER										AS MasterItemNumber
	,im.ITEM_IPN										AS IPN
	,im.ITEM_MPN										AS MPN
	,null												AS InternalMasterItemNumber
	,null												AS MasterItemTitle
	,im.MANUFACTURER_ID									AS MFGId
	,m.MANUFACTURER_CD								AS MFGCd
	,ii.ITEM_INVENTORY_ID								AS InventoryItemId
	,ii.ITEM_INVENTORY_SERIAL							AS Serial
	,ii.ITEM_INVENTORY_UNIQUE_ID						AS UniqueId
	,ISNULL(loc.WAREHOUSE_ID, DW_PREV.WAREHOUSE_ID)		AS WarehouseId
	,ISNULL(war.WAREHOUSE_CD, DW_PREV.WAREHOUSE_CD)		AS WarehouseName
	,ISNULL(ii.LOCATION_ID, ii.LOCATION_PREV_ID)		AS LocationId
	,(CASE WHEN ii.LOCATION_ID IS NOT NULL THEN dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(ii.LOCATION_ID)
		ELSE dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FL_PREV.LOCATION_ID)
	END)												AS LocationName
	,null												AS RealConditionId
	,null												AS RealConditionCd
	,1													AS Quantity
	,1													AS QuantityOrdered
	,-1													AS Available
	,ii.ITEM_STATUS_ID									AS StatusId
	,iis.STATUS_CD						AS StatusCd
	,kinv.Cost											AS Price
	,kinv.Cost											AS PriceInitial
	,@TaxPc												AS TaxPc
	,0													AS TaxOn --tax is set for kit inventory 
	,kinv.Tax											AS [Tax]
	,null												AS Notes
	,kinv.Cost 											AS Amount
	,ii.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED			AS QtyInUnit		
	,iikh.ItemId										AS Sku
	,ii.IS_DELETED										AS IsRelatedInventoryItemDe
	,null												AS ProductCodeId
	,null												AS ProductCodeValue
	,null												AS UnallocateProductCodeVal
	,null												AS RevisionId
	,null												AS Revision
	,null												AS UnallocateRevision
	,FC.CUSTOMER_ID										AS CustomerId
	,FC.CUSTOMER_NAME									AS CustomerName
	,ii.CUSTOMER_ID										AS VendorId
	,FC.CUSTOMER_NAME									AS VendorName
	,U.UserName											AS UserName
	,null												AS ShippingId
	,null												AS ShippingLabel
	,null												AS ShippingStatusId
	,null												AS ShippingStatusLabel
	,null												AS PurchaseOrderId '

	DECLARE @query6 NVARCHAR (MAX) = N'
	,null												AS PurchaseOrderName
	,null												AS PurchaseOrderTypeId 
	,null												AS AllPurchaseOrderId 
	,null												AS AllPurchaseOrderName
	,NULL                                               AS [InternalCost]
	,null												AS PurchaseOrderRep
	,ISNULL(ii.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0.00) AS ItemCost
	,ISNULL(kinv.Subtotal, 0) 
		- ISNULL(ii.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0.00) * kinv.Qty
														AS [Profit]	-- Subtotal - Cost - InternalCost
	,iikh.Id											as ItemInventoryKitHeaderId
	,1													as HasChildren
	,NULL												AS [AssetTag]
	,NULL												AS [AssetWorkflowStepId]
	,NULL												AS [AssetWorkflowStepName]
	,NULL												AS FinalGrade
	,NULL												AS GradeNote
	,NULL												AS RepairCost
	,NULL												AS TaxRate
	,NULL												AS TaxRateDescription
	,NULL												AS TaxRateApplicationDate
	from [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)	
	inner join 	#kitheaders	 k
		on iikh.Id = k.Id	
	inner join [dbo].[F_ITEM_INVENTORY] ii with (nolock)
		on iikh.[ItemInventoryId]  = ii.ITEM_INVENTORY_ID 
	INNER JOIN [dbo].[F_PRODUCT_MASTER]				FPM		WITH(NOLOCK)
		ON ii.ITEM_MASTER_ID = FPM.ITEM_MASTER_ID		
	inner join [dbo].[F_ITEM_MASTER] im with (nolock)
		on ii.ITEM_MASTER_ID = im.ITEM_MASTER_ID
	inner join [dbo].[D_MANUFACTURER] m with (nolock)
		on im.MANUFACTURER_ID = m.[MANUFACTURER_ID]	
	inner join [dbo].[D_ITEM_INVENTORY_STATUS] iis with (nolock)
		on ii.[ITEM_STATUS_ID] = iis.ITEM_INVENTORY_STATUS_ID
	left join [dbo].[F_LOCATION] loc with (nolock)
		on ii.Location_Id = loc.Location_Id
	left join D_WAREHOUSE war with (nolock)
		on loc.WAREHOUSE_ID = war.WAREHOUSE_ID
	LEFT JOIN F_LOCATION FL_PREV	WITH(NOLOCK)
		ON ii.LOCATION_PREV_ID = FL_PREV.LOCATION_ID		
	LEFT JOIN D_WAREHOUSE							DW_PREV	WITH(NOLOCK)
		ON DW_PREV.WAREHOUSE_ID		= FL_PREV.WAREHOUSE_ID	
	LEFT JOIN F_CUSTOMER							FC		WITH(NOLOCK)
		ON FC.CUSTOMER_ID = II.CUSTOMER_ID	
	LEFT JOIN tb_User								U		WITH(NOLOCK)
		ON U.UserID = ii.AUTHOR_USER_ID	
	left join 
		(
			select 
				iik.[ItemInventoryKitHeaderId],
				round(sum(ii.[ITEM_PRICE]), 2) as Cost,
				round(sum(IIF(ii.[APPLY_TAX] = 1, ii.ITEM_QUANTITY * ii.[ITEM_PRICE] * isnull(ii.TaxRate, @TaxPc), 0)), 2) as Tax,
				SUM(
					[dbo].[fn_money_GET_ITEM_PRICE_BY_TYPE_RESALE](
						ii.ITEM_PRICE
						,ii.PRICE_TYPE_ID
						,ii.ITEM_QUANTITY)
				)								as Subtotal,
				SUM(ii.ITEM_QUANTITY)				as Qty
			from [dbo].[F_ItemInventoryKit] iik with (nolock)
			inner join [dbo].[F_SALES_ORDER_ITEM] ii with (nolock)
				on iik.[ItemInventoryId] = ii.[ITEM_INVENTORY_ID]
			group by iik.[ItemInventoryKitHeaderId]
		) kinv
			on k.Id	 = kinv.ItemInventoryKitHeaderId
	where ' + CAST(@PARENT_ID AS NVARCHAR(20)) + ' = 0'

	DECLARE @query7 NVARCHAR (MAX) = N' union all
		SELECT 
			DISTINCT
			 -FII.ITEM_INVENTORY_ID								AS SalesOrderItemId
			,FSOI.SALES_ORDER_ID								AS SalesOrderId
			,FII.ITEM_MASTER_ID									AS MasterItemId
			,fim.[ITEM_NUMBER]									AS MasterItemNumber
			,fim.[ITEM_IPN]										AS IPN
			,fim.[ITEM_MPN]										AS MPN
			,NULL												AS InternalMasterItemNumber
			,ISNULL(
				(SELECT 
			 		ITEM_DESC 
				 FROM dbo.F_ITEM WITH (NOLOCK) 
				 WHERE ITEM_ID = FII.ITEM_ID)
				,fimt.ITEM_TITLE)								AS MasterItemTitle
			,m.[MANUFACTURER_ID]								AS MFGId
			,m.[MANUFACTURER_CD]								AS MFGCd
			,FII.ITEM_INVENTORY_ID								AS InventoryItemId
			,FII.[ITEM_INVENTORY_SERIAL]						AS Serial
			,FII.[ITEM_INVENTORY_UNIQUE_ID]						AS UniqueId
			,ISNULL(wloc.WAREHOUSE_ID, DW_PREV.WAREHOUSE_ID)	AS WarehouseId
			,ISNULL(wloc.WAREHOUSE_CD, DW_PREV.WAREHOUSE_CD)	AS WarehouseName
			,ISNULL(FII.[LOCATION_ID], FII.LOCATION_PREV_ID)	AS LocationId
			,(CASE 
				WHEN FII.LOCATION_ID IS NOT NULL THEN dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FII.LOCATION_ID)
				ELSE dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FL_PREV.LOCATION_ID)
			END)												AS LocationName
			,FII.CONDITION_ID									AS RealConditionId
			,Cond.ITEM_CONDITION_DESC							AS RealConditionCd
			,1													AS Quantity
			,1													AS QuantityOrdered
			,-1													AS Available
			,FII.[ITEM_STATUS_ID]								AS StatusId
			,CASE 
				WHEN RI.RMA_ITEM_ID IS NULL THEN iis.[STATUS_CD]						
				ELSE ''RMA received''
				END AS StatusCd
			,0													AS Price
			,0													AS PriceInitial
			,0													AS TaxPc 
			,0													AS TaxOn
			,0													AS [Tax]
			,FSOI.ITEM_NOTES									AS Notes
			,0													AS Amount
			,FII.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED			AS QtyInUnit		
			,FII.ITEM_ID										AS Sku
			,FII.IS_DELETED										AS IsRelatedInventoryItemDeleted
			,PC.PRODUCT_CODE_ID									AS ProductCodeId
			,PC.PRODUCT_CODE_VALUE								AS ProductCodeValue
			,PC.PRODUCT_CODE_VALUE								AS UnallocateProductCodeValue
			,rev.INVENTORY_CAPABILITY_ID						AS RevisionId
			,rev.INVENTORY_CAPABILITY_VALUE						AS Revision
			,unallocateDic.INVENTORY_CAPABILITY_VALUE			AS UnallocateRevision
			,FC.CUSTOMER_ID										AS CustomerId
			,FC.CUSTOMER_NAME									AS CustomerName
			,FII.[CUSTOMER_ID]									AS VendorId
			,c.CUSTOMER_NAME + 
				ISNULL( ''(''+ FC.CUSTOMER_NAME + '')'', '''')		AS VendorName
			,U.UserName											AS UserName
			,SHIP.SHIPPING_ID									AS ShippingId
			,SHIP.SHIPPING_ID									AS ShippingLabel
			,SHIP.STATUS_ID										AS ShippingStatusId
			,SHIPS.SHIPPING_STATUS_CD							AS ShippingStatusLabel
			,PO.PURCHASE_ORDER_ID								AS PurchaseOrderId
			,PO.AUTO_NAME										AS PurchaseOrderName
			,OOST.ENTITY_SUBJECT_TYPE_ID							AS PurchaseOrderTypeId 
			,COALESCE(PO.PURCHASE_ORDER_ID, NPO.PURCHASE_ORDER_ID)	AS AllPurchaseOrderId --for sales order admin tab we need not only DropShip POs, also stock POs with which items come 
			,COALESCE(PO.AUTO_NAME, NPO.AUTO_NAME)					AS AllPurchaseOrderName
			,COALESCE(POI.[InternalCost], NPOI.[InternalCost])		AS [InternalCost]
			,COALESCE(POU.UserName, NPOU.UserName)				AS PurchaseOrderRep
			,ISNULL(FII.ITEM_INVENTORY_UNIT_COST_ORIGINAL, 0.00) AS ItemCost	
			,null												AS [Profit]	-- Subtotal - Cost - InternalCost
			,null												as [ItemInventoryKitHeaderId]
			,0													as HasChildren
			,ASSET.[Tag]										AS [AssetTag]
			,ASSET.[AssetWorkflowStepId]						AS [AssetWorkflowStepId]
			,AWS.[Label]										AS [AssetWorkflowStepName]
			,FAG.GradeLevelMark									AS FinalGrade
			,FAG.Notes											AS GradeNote
			,ISNULL(RPOI.PRICE, 0.00) AS RepairCost
			,isnull(FSOI.TaxRate, @TaxPc) AS TaxRate
			,FSOI.TaxRateDescription AS TaxRateDescription
			,FSOI.TaxRateApplicationDate AS TaxRateApplicationDate
'
	
	declare @query8 nvarchar(max) = N'
		FROM  [dbo].[F_ITEM_INVENTORY_ADDED_PART] iiap with (nolock)
		inner join [dbo].F_ITEM_INVENTORY FII		WITH(NOLOCK)	
			on iiap.[PART_ITEM_INVENTORY_ID] = FII.ITEM_INVENTORY_ID
		inner join [dbo].F_ITEM_INVENTORY iipar		WITH(NOLOCK)	
			on iiap.[PARENT_ITEM_INVENTORY_ID] = iipar.ITEM_INVENTORY_ID
		inner join 	F_SALES_ORDER_ITEM	FSOI	WITH(NOLOCK)						
			on iipar.[SalesOrderItemId] = FSOI.SALES_ORDER_ITEM_ID
		INNER JOIN F_SALES_ORDER		FSO		WITH(NOLOCK)
			ON FSOI.SALES_ORDER_ID = FSO.SALES_ORDER_ID
	    inner join F_ORDER_ORDER_SUBJECT_TYPE FOOST with (nolock)
            on FSO.SALES_ORDER_ID = FOOST.ORDER_ID and FOOST.ENTITY_TYPE_ID = 1
		inner join [dbo].[F_CUSTOMER] c with (nolock)
			on fii.CUSTOMER_ID = c.CUSTOMER_ID
		left JOIN [dbo].F_ITEM_MASTER fim  with (nolock)
			ON fii.ITEM_MASTER_ID	 = fim.ITEM_MASTER_ID
		left join [dbo].[D_MANUFACTURER] m with (nolock)
			on fim.MANUFACTURER_ID = m.MANUFACTURER_ID
		left JOIN [dbo].F_ITEM_MASTER_TITLE	fimt  with (nolock)
			ON fim.ITEM_MASTER_ID = fimt.ITEM_MASTER_ID						
		LEFT JOIN D_ITEM_CONDITION	Cond	WITH(NOLOCK)
			ON FII.CONDITION_ID  = Cond.ITEM_CONDITION_ID	
		left join [dbo].[F_LOCATION] loc with (nolock)
			on fii.LOCATION_ID = loc.LOCATION_ID
		left join D_WAREHOUSE			wloc	WITH(NOLOCK)
			ON loc.WAREHOUSE_ID		= wloc.WAREHOUSE_ID	
		LEFT JOIN F_LOCATION  FL_PREV	WITH(NOLOCK)
			ON FII.LOCATION_PREV_ID = FL_PREV.LOCATION_ID		
		LEFT JOIN D_WAREHOUSE							DW_PREV	WITH(NOLOCK)
			ON DW_PREV.WAREHOUSE_ID		= FL_PREV.WAREHOUSE_ID '	
	
	declare @query9 nvarchar(max) = N'    
		LEFT JOIN F_PRODUCT_CODE						PC		WITH(NOLOCK)
			ON FII.[ProductCodeIdHeci] = PC.PRODUCT_CODE_ID	
		LEFT JOIN D_INVENTORY_CAPABILITY				unallocateDic WITH(NOLOCK)
			ON unallocateDic.INVENTORY_CAPABILITY_ID = FSOI.INVENTORY_CAPABILITY_ID
		LEFT JOIN F_CUSTOMER							FC		WITH(NOLOCK)
			ON FC.CUSTOMER_ID = FSOI.CUSTOMER_ID	
		LEFT JOIN tb_User								U		WITH(NOLOCK)
			ON U.UserID = FII.AUTHOR_USER_ID		
		LEFT JOIN F_SHIPPING_PACKAGE 					PACK 	WITH(NOLOCK)
			ON PACK.PACKAGE_ID = FSOI.PACKAGE_ID
		LEFT JOIN F_SHIPPING							SHIP	WITH(NOLOCK)
			ON SHIP.SHIPPING_ID = PACK.SHIPPING_ID
		LEFT JOIN D_SHIPPING_STATUS						SHIPS	WITH(NOLOCK)
			ON SHIPS.SHIPPING_STATUS_ID = SHIP.STATUS_ID
		LEFT JOIN dbo.F_RMA_ITEM RI  
			ON RI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID
		LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM POI	 WITH(NOLOCK) 
			ON POI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID
				AND POI.IS_DELETED = 0
		LEFT JOIN dbo.F_PURCHASE_ORDER PO WITH(NOLOCK) 
			ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
				AND PO.SALES_ORDER_ID = FSO.SALES_ORDER_ID
				AND PO.IS_DELETED = 0
		LEFT JOIN tb_User								POU		WITH(NOLOCK)
			ON POU.UserID = PO.USER_ID
		LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM NPOI	 WITH(NOLOCK) 
			ON NPOI.INVENTORY_ITEM_ID = FII.ITEM_INVENTORY_ID
				AND NPOI.IS_DELETED = 0
		LEFT JOIN dbo.F_PURCHASE_ORDER NPO WITH(NOLOCK) 
			ON NPO.PURCHASE_ORDER_ID = NPOI.PURCHASE_ORDER_ID
				AND NPO.IS_DELETED = 0
		LEFT JOIN tb_User								NPOU		WITH(NOLOCK)
			ON NPOU.UserID = NPO.USER_ID
		left join [dbo].[D_INVENTORY_CAPABILITY] rev with (nolock)
			on fii.RevisionId = rev.INVENTORY_CAPABILITY_ID				         
		LEFT JOIN dbo.F_ORDER_ORDER_SUBJECT_TYPE OOST WITH(NOLOCK) ON OOST.ORDER_ID = PO.PURCHASE_ORDER_ID
			AND OOST.ENTITY_TYPE_ID = 3 /*Purchase Order*/		
		left join [dbo].[D_ITEM_INVENTORY_STATUS] iis with (nolock)
			on fii.ITEM_STATUS_ID = iis.[ITEM_INVENTORY_STATUS_ID]
		LEFT JOIN [recycling].[F_Asset]					ASSET		WITH(NOLOCK)
			ON FII.AssetId = ASSET.Id
		LEFT JOIN [recycling].[C_AssetWorkflowStep]		AWS			WITH(NOLOCK)
			ON ASSET.[AssetWorkflowStepId] = AWS.[Id]

		LEFT JOIN [recycling].[F_AssetGrading]		FAG WITH (NOLOCK)
			ON FAG.AssetId = FII.AssetId
	    outer apply (select sum(RPOI.PRICE) as PRICE
                      from F_PURCHASE_ORDER_ITEM RPOI with (nolock)
                               inner join F_PURCHASE_ORDER RPO with (nolock)
                                          on RPOI.PURCHASE_ORDER_ID = RPO.PURCHASE_ORDER_ID
                      where (FOOST.ENTITY_SUBJECT_TYPE_ID = 12 and RPO.REPAIR_SALES_ORDER_ID = FSO.SALES_ORDER_ID and FII.ITEM_INVENTORY_ID = RPOI.REPAIR_INVENTORY_ITEM_ID or
                             FOOST.ENTITY_SUBJECT_TYPE_ID <> 12 and
                             FII.ITEM_INVENTORY_ID = RPOI.REPAIR_INVENTORY_ITEM_ID)
                        and RPOI.IS_DELETED = 0
                        and RPOI.RECEIVE_STATUS_ID = 3) as RPOI
		WHERE ' + CAST(@PARENT_ID AS NVARCHAR(20)) + ' = iiap.[PARENT_ITEM_INVENTORY_ID] )'

	DECLARE @query10 NVARCHAR (MAX) = N'	 
	  SELECT TOP(1)
			-1							AS RowID
			,COUNT(SalesOrderItemId) AS SalesOrderItemId
			,NULL as SalesOrderId
			,NULL as MasterItemId
			,NULL as MasterItemNumber
			,NULL as IPN
			,NULL as MPN
			,NULL as InternalMasterItemNumber
			,NULL as MasterItemTitle
			,NULL as MFGId
			,NULL as MFGCd
			,NULL as InventoryItemId
			,NULL as Serial
			,NULL as UniqueId
			,NULL as WarehouseId
			,NULL as WarehouseName
			,NULL as LocationId
			,NULL as LocationName
			,NULL as RealConditionId
			,NULL as RealConditionCd
			,NULL as Quantity
			,NULL as QuantityOrdered
			,NULL as Available
			,NULL as StatusId
			,NULL as StatusCd
			,NULL as Price
			,NULL as PriceInitial
			,NULL as TaxPc
			,NULL as TaxOn
			,NULL AS Tax
			,NULL as Notes
			,NULL as Amount
			,NULL as QtyInUnit		
			,NULL as Sku
			,NULL as IsRelatedInventoryItemDeleted
			,NULL as ProductCodeId
			,NULL as ProductCodeValue
			,NULL as UnallocateProductCodeValue
			,NULL as RevisionId
			,NULL as Revision
			,NULL as UnallocateRevision
			,NULL as CustomerId
			,NULL as CustomerName
			,NULL as VendorId
			,NULL as VendorName
			,NULL as UserName
			,NULL as ShippingId
			,NULL as ShippingLabel
			,NULL as ShippingStatusId
			,NULL as ShippingStatusLabel
			,NULL as PurchaseOrderId 
			,null as PurchaseOrderName
			,NULL as PurchaseOrderTypeId
			,NULL as PurchaseOrderRep
			,NULL as AllPurchaseOrderId
			,NULL as AllPurchaseOrderName
			,NULL AS [InternalCost]
			,NULL as ItemCost 
			,NULL as [Profit]
			,NULL as ItemInventoryKitHeaderId
			,NULL as HasChildren
			,NULL as [AssetTag]
			,NULL as [AssetWorkflowStepId]
			,NULL as [AssetWorkflowStepName]
			,NULL as FinalGrade
			,NULL as GradeNote
			,NULL as RepairCost
			,NULL as TaxRate
			,NULL as TaxRateDescription
			,NULL as TaxRateApplicationDate
		FROM m_data ' + @filterCondition + ' 
		
		UNION ALL
		SELECT * FROM (
		SELECT
			ROW_NUMBER() OVER (ORDER BY HasChildren desc, ItemInventoryKitHeaderId, ' + @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + ') as RowID
			,SalesOrderItemId
			,SalesOrderId
			,MasterItemId
			,MasterItemNumber
			,IPN
			,MPN
			,InternalMasterItemNumber
			,MasterItemTitle
			,MFGId
			,MFGCd
			,InventoryItemId
			,Serial
			,UniqueId
			,WarehouseId
			,WarehouseName
			,LocationId
			,LocationName
			,RealConditionId
			,RealConditionCd
			,Quantity
			,QuantityOrdered
			,Available
			,StatusId
			,StatusCd
			,Price
			,PriceInitial
			,TaxPc
			,TaxOn
			,Tax
			,Notes
			,Amount
			,QtyInUnit		
			,Sku
			,IsRelatedInventoryItemDeleted
			,ProductCodeId
			,ProductCodeValue
			,UnallocateProductCodeValue
			,RevisionId
			,Revision
			,UnallocateRevision
			,CustomerId
			,CustomerName'

			DECLARE @query11 NVARCHAR (MAX) = N'
			,VendorId
			,VendorName
			,UserName
			,ShippingId
			,ShippingLabel
			,ShippingStatusId
			,ShippingStatusLabel
			,PurchaseOrderId 
			,PurchaseOrderName
			,PurchaseOrderTypeId 
			,PurchaseOrderRep
			,AllPurchaseOrderId
			,AllPurchaseOrderName
			,[InternalCost]
			,ItemCost
			,[Profit]
			,ItemInventoryKitHeaderId
			,HasChildren
			,[AssetTag]
			,[AssetWorkflowStepId]
			,[AssetWorkflowStepName]
			,FinalGrade
			,GradeNote
			,RepairCost
			,TaxRate
			,TaxRateDescription
			,TaxRateApplicationDate
		FROM m_data	' + @filterCondition + ' 
		ORDER BY HasChildren desc, ItemInventoryKitHeaderId,' + @ORDER_COLUMN_NAME  + N' '+ @ORDER_DIRECTION + N'

		OFFSET '+ CAST(@PAGE_INDEX * @ITEMS_PER_PAGE AS VARCHAR(100)) + ' ROWS
			FETCH NEXT CASE ' + CAST(@ITEMS_PER_PAGE AS VARCHAR(100)) + ' WHEN  0 THEN ISNULL((SELECT COUNT(1) FROM m_data), 1) ELSE ' + CAST(@ITEMS_PER_PAGE AS VARCHAR(100)) + ' END
			  ROWS ONLY
			  ) t'

	IF @C_IS_DEBUG = 1
	    begin
			PRINT @query1
			print @query2 
			print @query3 
			print @query4 
			print @query5 
			print @query6
			print @query7 
			print @query8 
			print @query9
			print @query10
			print @query11
        end
		ELSE 
			EXEC (@query1 + @query2 + @query3 + @query4 + @query5 + @query6+ @query7 + @query8 + @query9 + @query10 + @query11);

	drop table #kitheaders

END
-- =============================================
-- Author:		<PERSON><PERSON> <PERSON><PERSON>
-- Edit date:   04/22/2022
-- Description:	Rebuild the SKU ids of the given master/inventory items
-- =============================================
/*
declare @InventoryIds		dbo.bigint_ID_ARRAY
insert into @InventoryIds
select top(10)
	item_inventory_id
from [dbo].[vw_F_ITEM_INVENTORY_FILTERED_FORSKU_REBUILD] fii WITH (NOLOCK)
where UNIQUE_INVERNTORY_ID is not null

select
	fii.ITEM_INVENTORY_ID
	,fii.ITEM_ID
from F_ITEM_INVENTORY fii
inner join @InventoryIds ids
	on ids.ID = fii.ITEM_INVENTORY_ID

update fii set item_id = null
from F_ITEM_INVENTORY fii
inner join @InventoryIds ids
	on ids.ID = fii.ITEM_INVENTORY_ID

EXEC [sp_INSERT_SKU_AND_SET_INVENTORY_SKU_ID]
	@InventoryIds = @InventoryIds
	,@C_IS_DEBUG = 1

select
	fii.ITEM_INVENTORY_ID
	,fii.ITEM_ID
from F_ITEM_INVENTORY fii
inner join @InventoryIds ids
	on ids.ID = fii.ITEM_INVENTORY_ID

*/
CREATE PROCEDURE [dbo].[sp_INSERT_SKU_AND_SET_INVENTORY_SKU_ID]
	@InventoryIds	dbo.bigint_ID_ARRAY readonly
	,@C_REBUILD_ALL	bit		= 0
	,@C_IS_DEBUG	bit		= 0
AS

	DECLARE
		@SP_NAME	VARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
		,@UTC_NOW	DATETIME		= GETUTCDATE()

	set @C_REBUILD_ALL = isnull(@C_REBUILD_ALL, 0)

	if @C_IS_DEBUG = 1 PRINT 'Take all available inventory items of the ItemMaster'

	create table #t_INVENTORY (
		ITEM_INVENTORY_ID			bigint
		,ITEM_MASTER_ID				bigint
		,ITEM_NUMBER				NVARCHAR (256)
		,ITEM_MASTER_SKU_ATTRB_ID	bigint
		,CONDITION_ID				bigint
		,ITEM_TYPE_ID				int
		,UNIQUE_INVERNTORY_ID		bigint
	);

	insert into #t_INVENTORY
	select distinct
		 fif.ITEM_INVENTORY_ID
		,fif.ITEM_MASTER_ID
		,fif.ITEM_NUMBER
		,fif.ITEM_MASTER_SKU_ATTRB_ID
		,fif.CONDITION_ID
		,fif.ITEM_TYPE_ID
		,fif.UNIQUE_INVERNTORY_ID
	from (
		select
			ID
		from @InventoryIds
		where @C_REBUILD_ALL = 0
		union all
		select
			item_inventory_id
		from F_ITEM_INVENTORY with(nolock)
		where @C_REBUILD_ALL = 1
		  and IS_DELETED = 0
	) i
	inner join [dbo].[vw_F_ITEM_INVENTORY_FILTERED_FORSKU_REBUILD]	fif WITH (NOLOCK)
		on fif.ITEM_INVENTORY_ID = i.ID
	
	if @C_IS_DEBUG = 1
	BEGIN
		SELECT * FROM #t_INVENTORY
		PRINT 'Insert new SKUs for the not-SKUed inventory'
	END

	insert into F_ITEM with(rowlock) (
		 ITEM_MASTER_ID
		,ITEM_TYPE_ID
		,ITEM_NUMBER
		,ITEM_MASTER_SKU_ATTRB_ID
		,CONDITION_ID
		,UNIQUE_INVERNTORY_ID
		,QUANTITY
		,IS_QUALIFIED
		,IS_FEATURED
		,IS_DEAL_10_PERC_OFF_EBAY
		,IS_HIDE_ECOMMERCE_ITEM
		,INSERTED_DT
		,INSERTED_BY
	)
	select distinct
		 st.ITEM_MASTER_ID
		,st.ITEM_TYPE_ID
		,st.ITEM_NUMBER
		,st.ITEM_MASTER_SKU_ATTRB_ID
		,st.CONDITION_ID
		,st.UNIQUE_INVERNTORY_ID
		,0											as QUANTITY
		,0											as IS_QUALIFIED
		,0											as IS_FEATURED
		,0											as IS_DEAL_10_PERC_OFF_EBAY
		,0											as IS_HIDE_ECOMMERCE_ITEM
		,@UTC_NOW									as INSERTED_DT
		,@SP_NAME									as INSERTED_BY
	from #t_INVENTORY			st
	left join F_ITEM			sku with(nolock)
		ON ST.ITEM_MASTER_ID = sku.ITEM_MASTER_ID
		AND ISNULL(st.ITEM_MASTER_SKU_ATTRB_ID, 0)	= ISNULL(sku.ITEM_MASTER_SKU_ATTRB_ID, 0)
		AND ISNULL(st.CONDITION_ID, 0)				= ISNULL(sku.CONDITION_ID, 0)
		AND sku.ITEM_TYPE_ID						= ST.ITEM_TYPE_ID
		AND ISNULL(st.UNIQUE_INVERNTORY_ID, 0)		= ISNULL(sku.UNIQUE_INVERNTORY_ID, 0)
	where sku.ITEM_ID is null

	if @C_IS_DEBUG = 1 
	BEGIN
		select
			'inserted' as [Inserted SKUs]
			,*
		from f_item
		where INSERTED_DT = @UTC_NOW
		  and INSERTED_BY = @SP_NAME

		PRINT 'Write F_ITEM_INVENTORY.F_ITEM_ID into the inserted records'
	END

	UPDATE fii SET
		 ITEM_ID					= SKU.ITEM_ID
		,ITEM_INVENTORY_SKU_ATTRB	= fimsa.ITEM_MASTER_SKU_ATTRB_CD
		,UPDATED_BY					= @SP_NAME
		,UPDATED_DT					= @UTC_NOW
	FROM #t_INVENTORY							fif
	inner join F_ITEM_INVENTORY					fii		WITH (ROWLOCK)
		on fii.ITEM_INVENTORY_ID = fif.ITEM_INVENTORY_ID
	
	LEFT JOIN F_ITEM_MASTER_SKU_ATTRB			fimsa	WITH (NOLOCK)
		ON  fimsa.ITEM_MASTER_ID								= fii.ITEM_MASTER_ID
		AND CAST(fimsa.ITEM_MASTER_SKU_ATTRB_CD AS varchar(900))= fii.ITEM_INVENTORY_SKU_ATTRB

	INNER JOIN F_ITEM							SKU		WITH (NOLOCK)
		ON  SKU.ITEM_MASTER_ID								= fii.ITEM_MASTER_ID
		AND SKU.CONDITION_ID								= fii.CONDITION_ID
		AND SKU.IS_INACTIVE									= 0
		AND SKU.IS_DELETED									= 0
		AND ISNULL(SKU.ITEM_MASTER_SKU_ATTRB_ID, 0)			= ISNULL(fimsa.ITEM_MASTER_SKU_ATTRB_ID, 0)
		AND iif(SKU.ITEM_TYPE_ID = 8, 1, SKU.ITEM_TYPE_ID)	= IIF(fii.IS_VIRTUAL = 1, 7, 1) 
		AND (fif.UNIQUE_INVERNTORY_ID IS NULL AND SKU.UNIQUE_INVERNTORY_ID IS NULL OR fif.UNIQUE_INVERNTORY_ID = SKU.UNIQUE_INVERNTORY_ID)
		-- only with different SKUs
		AND (fii.ITEM_ID IS NULL OR fii.ITEM_ID != ISNULL(SKU.ITEM_ID, 0))
	
	if @C_IS_DEBUG = 1
		select
			'updated' as [Updated inventory]
			,*
		from F_ITEM_INVENTORY with(nolock)
		where UPDATED_DT = @UTC_NOW
			and UPDATED_BY = @SP_NAME

	drop table #t_INVENTORY;

GO
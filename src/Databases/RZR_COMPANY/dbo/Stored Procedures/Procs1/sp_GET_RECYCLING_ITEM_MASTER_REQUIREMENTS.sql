-- =============================================
-- Author:		<O.Evseev>
-- Create date: <08/11/2014>
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ITEM_MASTER_REQUIREMENTS]
	@T_ITEM_MASTER_IDS bigint_ID_ARRAY READONLY
AS
BEGIN
	
	SELECT
		I.ID				AS ITEM_MASTER_ID
		,M.REQUIRES_COUNT	AS [REQUIRE_ITEM_COUNT]
		,M.REQUIRES_NOTES	AS [REQUIRE_NOTES]
		,0					AS [REQUIRE_PICTURES]
		,0					AS [REQUIRE_MANUFACTURER]
		,0					AS [REQUIRE_MODEL]
		,0					AS [REQUIRE_SERIAL]
		,M.DEFAULT_COUNT    AS [DEFAULT_COUNT]
	FROM F_RECYCLING_ITEM_MASTER	M	WITH(NOLOCK)
	INNER JOIN @T_ITEM_MASTER_IDS	I
	  ON M.RECYCLING_ITEM_MASTER_ID = I.ID

END
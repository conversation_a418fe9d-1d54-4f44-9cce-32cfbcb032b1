-- =============================================
-- Author:		<PERSON><PERSON> <PERSON><PERSON>
-- Edit date:   08/10/2022
-- Description:	Invoked Manually. Generate/assign inventory SKU id, put a rebuild job
-- =============================================
CREATE PROCEDURE [dbo].[sp_ManuallySetInventorySkuIdAndRebuild]
	@InventoryIds	dbo.bigint_ID_ARRAY readonly
	,@IsDebug		bit = 0
AS

	if not exists(select top(1) 1 from @InventoryIds)
		return

	if @IsDebug = 1
		select '@InventoryIds' as [@InventoryIds], * from @InventoryIds


	declare @skuIdsQualified table(InventoryId bigint, SkuId bigint, IsQualified bit)
	insert into @skuIdsQualified(InventoryId, SkuId, IsQualified)
	EXEC [queue].sp_GetInventorySkuIdsQualified 
		@InventoryIds = @InventoryIds

	EXEC [queue].sp_RebuildInventorySkuIds 
		 @InventoryIds   = @InventoryIds
		 ,@C_REBUILD_ALL = 0
		 ,@C_IS_DEBUG    = @IsDebug

	insert into @skuIdsQualified(InventoryId, SkuId, IsQualified)
	EXEC [queue].sp_GetInventorySkuIdsQualified 
		@InventoryIds = @InventoryIds


	if @IsDebug = 1
		select '@skuIdsQualified' as [@skuIdsQualified], * from @InventoryIds


	declare @skuIds bigint_id_array
	insert into @skuIds
	select distinct
		SkuId
	from @skuIdsQualified
	where isnull(IsQualified, 0) = 0


	if exists(select top(1) 1 from @skuIds)
		exec [queue].sp_PutRebuildTask
			@ItemIds					= @skuIds
			,@JobName					= 'ITEM_BASIC_'
			,@JobNameToRunOnCompleted	= 'ITEM_SALES_BASIC_'

	delete from @skuIds
	insert into @skuIds
	select distinct
		SkuId
	from @skuIdsQualified
	where IsQualified = 1

	if exists(select top(1) 1 from @skuIds)
		exec [queue].sp_PutRebuildTask
			@ItemIds					= @skuIds
			,@JobName					= 'ITEM_BASIC_'
			,@JobNameToRunOnCompleted	= 'ITEM_SALES_'

GO

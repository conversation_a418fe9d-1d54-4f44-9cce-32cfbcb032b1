CREATE TABLE [dbo].[F_ITEM_COMBO_HEADER] (
    [ITEM_COMBO_HEADER_ID]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]              BIGINT        NULL,
    [MANUFACTURER]                VARCHAR (550) NOT NULL,
    [CO<PERSON><PERSON>ION_ID]                BIGINT        NOT NULL,
    [ITEM_COMBO_HEADER_TITLE]     VARCHAR (250) NOT NULL,
    [ITEM_COMBO_HEADER_DESC]      VARCHAR (MAX) NULL,
    [ITEM_COMBO_HEADER_KIT_PRICE] MONEY         NOT NULL,
    [ITEM_COMBO_HEADER_KIT_COST]  MONEY         NOT NULL,
    [IS_INACTIVE]                 BIT           CONSTRAINT [DF_F_ITEM_COMBO_HEADER_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                  BIT           CONSTRAINT [DF_F_ITEM_COMBO_HEADER_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                 VARCHAR (250) NOT NULL,
    [INSERTED_DT]                 DATETIME      NOT NULL,
    [UPDATED_BY]                  VARCHAR (250) NULL,
    [UPDATED_DT]                  DATETIME      NULL,
    [DELETED_BY]                  VARCHAR (250) NULL,
    [DELETED_DT]                  DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_COMBO_HEADER] PRIMARY KEY CLUSTERED ([ITEM_COMBO_HEADER_ID] ASC),
    CONSTRAINT [FK_F_ITEM_COMBO_HEADER_D_ITEM_CONDITION] FOREIGN KEY ([CONDITION_ID]) REFERENCES [dbo].[D_ITEM_CONDITION] ([ITEM_CONDITION_ID])
);
GO

CREATE INDEX IDX_F_ITEM_COMBO_HEADER_ITEM_MASTER_ID ON F_ITEM_COMBO_HEADER([ITEM_MASTER_ID])
	INCLUDE(ITEM_COMBO_HEADER_ID, ITEM_COMBO_HEADER_DESC, ITEM_COMBO_HEADER_TITLE, ITEM_COMBO_HEADER_KIT_PRICE)

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores the general information for combo. The combo is set of SKUs with quantities. The combo is separate SKU too', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_COMBO_HEADER';

CREATE TABLE [dbo].[D_EBAY_CATEGORY_HIERARCHY_TEMP] (
    [CATEGORY_ID]             BIGINT        NOT NULL,
    [CATE<PERSON><PERSON>YNAME]            VARCHAR (512) NOT NULL,
    [EBAY_CATEGORY_FULL_PATH] VARCHAR (MAX) NULL,
    [PARENT_ID]               INT           NULL,
    [CATEGORY_KEY]            VARCHAR (MAX) NULL,
    [CATEGORY_LEVEL]          INT           NULL,
    [IS_IN_WORK]              BIT           NULL
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The intermediate table is used for ebay imported categories update. It is used with EbayCategoriesService', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_EBAY_CATEGORY_HIERARCHY_TEMP';


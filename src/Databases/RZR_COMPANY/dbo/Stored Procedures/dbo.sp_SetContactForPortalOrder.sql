-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_SetContactForPortalOrder] 
	@CustomerId		bigint,
	@ContactId		bigint,
	@FirstName		nvarchar(150),
	@LastName		nvarchar(150),
	@ContactEmail	nvarchar(50),
	@ContactPhone	nvarchar(50)
AS
BEGIN	

	select 
		@FirstName = nullif(rtrim(ltrim(@FirstName)), N''),
		@LastName = nullif(rtrim(ltrim(@LastName)), N''),
		@ContactEmail = nullif(rtrim(ltrim(@ContactEmail)), N''),
		@ContactPhone = nullif(rtrim(ltrim(@ContactPhone)), N'')

	declare @ExistentContactId bigint
	SET @ExistentContactId = (
		  SELECT TOP(1)
			 [CUSTOMER_CONTACT_ID]
		  FROM [dbo].[F_CUSTOMER_CONTACT] WITH (NOLOCK)
		  WHERE CUSTOMER_ID = @CustomerId and [FIRST_NAME] = @FirstName and [LAST_NAME] = @LastName
	   )
	
	if (@ExistentContactId is not null) begin		
		update [dbo].[F_CUSTOMER_CONTACT] set 	
			[FIRST_NAME] = isnull(@FirstName, [FIRST_NAME]),
			[LAST_NAME]  = isnull(@LastName, [LAST_NAME]),	
			[MAIN_EMAIL] = isnull(@ContactEmail, [MAIN_EMAIL]),
			[MAIN_PHONE_NUMBER] = isnull(@ContactPhone, [MAIN_PHONE_NUMBER]),
			UPDATED_BY = 'sp_SetContactForPortalOrder',
			UPDATED_DT = GETUTCDATE()	
		where [CUSTOMER_CONTACT_ID] = @ExistentContactId

		select @ExistentContactId as ContactId
	end
	else if (@ContactId is null and @FirstName is not null and @LastName is not null) 
	begin
		
		INSERT INTO [dbo].[F_CUSTOMER_CONTACT] ([CUSTOMER_ID], [FIRST_NAME], [LAST_NAME], [MAIN_PHONE_NUMBER], [MAIN_EMAIL], [INSERTED_BY], [INSERTED_DT])
		  SELECT 
			  @CustomerId
			 ,@FirstName
			 ,@LastName
			 ,@ContactPhone
			 ,@ContactEmail
			 ,'sp_SetContactForPortalOrder'
			 ,GETUTCDATE()			 

		  SET @ContactId = SCOPE_IDENTITY()		

		  select @ContactId
	end	
	else if (@ContactId is not null) begin
		update [dbo].[F_CUSTOMER_CONTACT] set 
			[FIRST_NAME] = isnull(@FirstName, [FIRST_NAME]),
			[LAST_NAME]  = isnull(@LastName, [LAST_NAME]),
			[MAIN_EMAIL] = isnull(@ContactEmail, [MAIN_EMAIL]),
			[MAIN_PHONE_NUMBER] = isnull(@ContactPhone, [MAIN_PHONE_NUMBER]),
			UPDATED_BY = 'sp_SetContactForPortalOrder',
			UPDATED_DT = GETUTCDATE()	
		where [CUSTOMER_CONTACT_ID] = @ContactId

		select @ContactId
	end
END

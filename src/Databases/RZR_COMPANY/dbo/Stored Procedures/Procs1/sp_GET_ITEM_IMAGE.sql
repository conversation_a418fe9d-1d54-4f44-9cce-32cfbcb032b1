CREATE PROCEDURE [dbo].[sp_GET_ITEM_IMAGE]
	@IMAGE_ID BIGINT,
	@IMAGE_TYPE INT
AS
BEGIN
	SELECT
		NAME AS [Name],
		CASE @IMAGE_TYPE
			WHEN 0 THEN BASE_FILE_ID
			WHEN 1 THEN NULL
			WHEN 2 THEN GALLEREY_FILE_ID
			WHEN 3 THEN SMALL_FILE_ID
			ELSE ISNULL(THUMBNAIL_FILE_ID, BASE_FILE_ID)
		END AS [FileId]
		,CASE @IMAGE_TYPE
			WHEN 0 THEN BASE_BASE64
			WHEN 1 THEN NULL
			WHEN 2 THEN GALLEREY_BASE64
			WHEN 3 THEN SMALL_BASE64
			ELSE THUMBNAIL_BASE64
		END AS [Base64]
	FROM
		F_ITEM_IMAGE
	WHERE IMAGE_ID = @IMAGE_ID
END
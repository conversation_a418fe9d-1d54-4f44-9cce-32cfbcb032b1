CREATE TABLE [dbo].[F_ITEM_MASTER] (
    [ITEM_MASTER_ID]              BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_NUMBER]                 NVARCHAR (256) NOT NULL,
    [ITEM_IPN]                    NVARCHAR (256) NULL,
    [ITEM_MPN]                    NVARCHAR (256) NULL,
    [ITEM_WHITE_LABEL]            NVARCHAR (256) NULL,
    [ITEM_HARMONIZATION_CODE]     NVARCHAR (256) NULL,
    [ITEM_ECCN_CODE]              NVARCHAR (256) NULL,
    [MANUFACTURER_ID]             BIGINT         NOT NULL,
    [ITEM_TYPE_ID]                BIGINT         NULL,
    [EBAY_CATEGORY_ID]            BIGINT         NULL,
    [INVENTORY_ATTRIBUTE_TYPE_ID] INT            CONSTRAINT [DF_F_ITEM_MASTER_INVENTORY_ATTRIBUTE_TYPE_ID] DEFAULT ((7)) NOT NULL,
    [IS_INACTIVE]                 BIT            CONSTRAINT [DF_F_ITEM_MASTER_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                  BIT            CONSTRAINT [DF_F_ITEM_MASTER_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                 VARCHAR (150)  CONSTRAINT [DF_F_ITEM_MASTER_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]                 DATETIME       CONSTRAINT [DF_F_ITEM_MASTER_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                  VARCHAR (150)  NULL,
    [UPDATED_DT]                  DATETIME       NULL,
    [DELETED_BY]                  VARCHAR (150)  NULL,
    [DELETED_DT]                  DATETIME       NULL,
    [DIMENSION_ID]                BIGINT         NULL,
    [IS_DISCONTINUED]             BIT            CONSTRAINT [DF_F_ITEM_MASTER_IS_DISCONTINUED] DEFAULT ((0)) NOT NULL,
    [ExternalId]                  NVARCHAR (350) NULL,
    [RecyclingItemMasterId]       BIGINT         NULL,
    [WorkflowTypeId]              INT            NULL,
    [UpdatedUserId]               BIGINT         NULL,
    CONSTRAINT [PK_F_ITEM_MASTER] PRIMARY KEY CLUSTERED ([ITEM_MASTER_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_C_RECYCLING_WORKFLOW_TYPE] FOREIGN KEY ([WorkflowTypeId]) REFERENCES [dbo].[C_RECYCLING_WORKFLOW_TYPE] ([WORKFLOW_TYPE_ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_F_ITEM_MASTER_D_EBAY_CATEGORY_HIERARCHY] FOREIGN KEY ([EBAY_CATEGORY_ID]) REFERENCES [dbo].[D_EBAY_CATEGORY_HIERARCHY] ([EBAY_CATEGORY_HIERARCHY_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_D_MANUFACTURER] FOREIGN KEY ([MANUFACTURER_ID]) REFERENCES [dbo].[D_MANUFACTURER] ([MANUFACTURER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_F_RECYCLING_ITEM_MASTER] FOREIGN KEY ([RecyclingItemMasterId]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_tb_User] FOREIGN KEY ([UpdatedUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);
GO

CREATE NONCLUSTERED INDEX [IX__dbo_F_ITEM_MASTER__IS_INACTIVE__IS_DELETED__INCLUDE__ITEM_NUMBER__IS_DISCONTINUED]
  ON [dbo].[F_ITEM_MASTER] (
    [IS_INACTIVE], [IS_DELETED]
  )
  INCLUDE (
    [ITEM_NUMBER], [IS_DISCONTINUED],[MANUFACTURER_ID]
  )

GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_F_ITEM_MASTER]
    ON [dbo].[F_ITEM_MASTER]([ITEM_MASTER_ID], [MANUFACTURER_ID] ASC, [ITEM_NUMBER] ASC, IS_DELETED )
	INCLUDE (INVENTORY_ATTRIBUTE_TYPE_ID, [IS_DISCONTINUED]) ;
--GO
--CREATE UNIQUE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_unique_tuple]
--    ON [dbo].[F_ITEM_MASTER]([MANUFACTURER_ID], [ITEM_NUMBER], IS_DELETED, IS_INACTIVE, [ITEM_IPN], [ITEM_MPN])
--    where (IS_DELETED=(0) and IS_INACTIVE=(0))
GO
CREATE UNIQUE NONCLUSTERED INDEX [UX_F_ITEM_MASTER]
    ON [dbo].[F_ITEM_MASTER]([ITEM_MASTER_ID], IS_DELETED, IS_INACTIVE, MANUFACTURER_ID)
	INCLUDE ([ITEM_NUMBER],[ITEM_IPN],[ITEM_MPN],[IS_DISCONTINUED]);
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_ITEM_NUMBER_SEARCH]
    ON [dbo].[F_ITEM_MASTER] ([MANUFACTURER_ID], IS_DELETED, IS_INACTIVE, [ITEM_NUMBER])
    INCLUDE ([ITEM_IPN],[ITEM_MPN],[IS_DISCONTINUED])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_IPN]
    ON [dbo].[F_ITEM_MASTER]([MANUFACTURER_ID], IS_DELETED, IS_INACTIVE, [ITEM_IPN])
    INCLUDE ([ITEM_NUMBER],[ITEM_MPN],[IS_DISCONTINUED])
GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_MPN]
    ON [dbo].[F_ITEM_MASTER]([MANUFACTURER_ID], IS_DELETED, IS_INACTIVE, [ITEM_MPN])
    INCLUDE ([ITEM_NUMBER],[ITEM_IPN],[IS_DISCONTINUED])
GO

CREATE NONCLUSTERED INDEX [IX_F_ITEM_MASTER_ITEM_NUMBER]
    ON [dbo].[F_ITEM_MASTER]([ITEM_NUMBER] ASC, [MANUFACTURER_ID], IS_DELETED)
    INCLUDE([ITEM_MASTER_ID], [INVENTORY_ATTRIBUTE_TYPE_ID], [ITEM_TYPE_ID]);
GO

CREATE TRIGGER trg_F_ITEM_MASTER_AFTER_NOTIFICATION
    ON  dbo.F_ITEM_MASTER
    AFTER INSERT, UPDATE 
AS 
BEGIN
	DECLARE @params dbo.NOTIFICATION_QUEUE_PARAMS
	
	IF NOT EXISTS(SELECT TOP(1) 1 FROM DELETED)
	BEGIN
		INSERT INTO @params (NAME, VALUE) 
		SELECT
			'ITEM_MASTER_ID'
			,ITEM_MASTER_ID
		FROM INSERTED

		----add notifications about item create
	EXEC sp_ADD_NOTIFICATION_TO_QUEUE
		@C_NOTIFICATION_TYPE_ID = 28 -- Master Item is Created
		,@C_PARAMS = @params
	END
END
GO



GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This table is stores models. He model is combination of item master and manufacturer. ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER';


GO
CREATE NONCLUSTERED INDEX [IX__F_ITEM_MASTER__IS_ACTIVE]
    ON [dbo].[F_ITEM_MASTER]([IS_INACTIVE] ASC, [IS_DELETED] ASC, [ITEM_MASTER_ID] ASC)
    INCLUDE([ITEM_TYPE_ID], [ITEM_NUMBER], [MANUFACTURER_ID]);
GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ITEM_MASTER_PUT_CHANGE]
    ON  [dbo].[F_ITEM_MASTER]
    AFTER INSERT, UPDATE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.ITEM_MASTER_ID, d.ITEM_MASTER_ID)
	from inserted		i
	full join deleted	d
		on i.ITEM_MASTER_ID = d.ITEM_MASTER_ID
	where isnull(i.[ITEM_NUMBER], N'') <> isnull(d.[ITEM_NUMBER], N'')
		or isnull(i.INVENTORY_ATTRIBUTE_TYPE_ID, 0) <> isnull(d.INVENTORY_ATTRIBUTE_TYPE_ID, 0)
		or isnull(i.MANUFACTURER_ID, 0) <> isnull(d.MANUFACTURER_ID, 0)
		or isnull(i.ITEM_IPN, '') <> isnull(d.ITEM_IPN, '')
		or isnull(i.ITEM_MPN, '') <> isnull(d.ITEM_MPN, '')
		or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
		or isnull(i.IS_DELETED, 0) <> isnull(d.IS_DELETED, 0)
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 3 --MasterItem
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO

CREATE UNIQUE NONCLUSTERED INDEX [UIX_F_ITEM_MASTER__DIMENSION_ID]
    ON [dbo].[F_ITEM_MASTER]([DIMENSION_ID] ASC) WHERE ([DIMENSION_ID] IS NOT NULL);
GO

CREATE NONCLUSTERED INDEX [IX_MANUFACTURER_ID]
    ON [dbo].[F_ITEM_MASTER]([MANUFACTURER_ID] ASC);
GO

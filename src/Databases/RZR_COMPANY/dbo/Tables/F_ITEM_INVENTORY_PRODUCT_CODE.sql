CREATE TABLE [dbo].[F_ITEM_INVENTORY_PRODUCT_CODE] (
    [ITEM_INVENTORY_PRODUCT_CODE_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_INVENTORY_ID]              BIGINT        NOT NULL,
    [ITEM_MASTER_PRODUCT_CODE_ID]    BIGINT        NOT NULL,
    [PRODUCT_CODE_ID]                BIGINT        NULL,
    [IS_INACTIVE]                    BIT           CONSTRAINT [DF_F_ITEM_INVENTORY_PRODUCT_CODE_IS_INACTIVE] DEFAULT ((0)) NULL,
    [IS_DELETED]                     BIT           CONSTRAINT [DF_F_ITEM_INVENTORY_PRODUCT_CODE_IS_DELETED] DEFAULT ((0)) NULL,
    [INSERTED_BY]                    VARCHAR (150) NULL,
    [INSERTED_DT]                    DATETIME      CONSTRAINT [DF_F_ITEM_INVENTORY_PRODUCT_CODE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                     VARCHAR (150) NULL,
    [UPDATED_DT]                     DATETIME      NULL,
    [DELETED_DT]                     DATETIME      NULL,
    [PRODUCT_CODE_TYPE_ID]           BIGINT        NULL,
    CONSTRAINT [PK_F_ITEM_INVENTORY_PRODUCT_CODE] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_PRODUCT_CODE_ID] ASC),
    CONSTRAINT [FK_F_ITEM_INVENTORY_PRODUCT_CODE_F_ITEM_INVENTORY] FOREIGN KEY ([ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_PRODUCT_CODE_F_ITEM_MASTER_PRODUCT_CODE] FOREIGN KEY ([ITEM_MASTER_PRODUCT_CODE_ID]) REFERENCES [dbo].[F_ITEM_MASTER_PRODUCT_CODE] ([ITEM_MASTER_PRODUCT_CODE_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);



GO
CREATE NONCLUSTERED INDEX [IX_PRODUCT_CODE_ID]
    ON [dbo].[F_ITEM_INVENTORY_PRODUCT_CODE]([ITEM_MASTER_PRODUCT_CODE_ID] ASC)
	INCLUDE(ITEM_INVENTORY_ID)

GO
CREATE NONCLUSTERED INDEX [IX_PRODUCT_CODE]
    ON [dbo].[F_ITEM_INVENTORY_PRODUCT_CODE]([PRODUCT_CODE_ID] ASC)
	INCLUDE(ITEM_INVENTORY_PRODUCT_CODE_ID);

GO
CREATE NONCLUSTERED INDEX [IX_ITEM_INVENTORY_ID]
    ON [dbo].[F_ITEM_INVENTORY_PRODUCT_CODE]([ITEM_INVENTORY_ID] ASC)
    INCLUDE([ITEM_INVENTORY_PRODUCT_CODE_ID], [ITEM_MASTER_PRODUCT_CODE_ID], [PRODUCT_CODE_ID]);




GO

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The map table. It maps inventory to product code.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_PRODUCT_CODE';


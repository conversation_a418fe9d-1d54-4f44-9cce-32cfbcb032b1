CREATE FUNCTION [dbo].[fn_str_CleanUnallowedFileNameChars](
	@FileName NVARCHAR(256)
)
RETURNS NVARCHAR(256)
AS
BEGIN

	DECLARE @stripChars NVARCHAR(20) = N'\|/*?:<>&+"''#';
	DECLARE @stripCharsCount INT = LEN(@stripChars);
	DECLARE @charCounter INT = 1;

	WHILE (@charCounter <= @stripCharsCount)
	BEGIN
		SET @FileName = REPLACE(@FileName, SUBSTRING(@stripChars, @charCounter, 1), '-');
        SET @charCounter = @charCounter + 1;
	END

	RETURN @FileName;

END
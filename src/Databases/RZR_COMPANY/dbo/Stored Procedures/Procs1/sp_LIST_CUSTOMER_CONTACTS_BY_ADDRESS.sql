 
CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMER_CONTACTS_BY_ADDRESS] 		
	@ADDRESS_ID		BIGINT	
AS
BEGIN

	SELECT 
		cc.CUSTOMER_CONTACT_ID												AS [VALUE],		
		dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID)	AS [LABEL],
		cc.JOB_TITLE														AS [DESC]
	FROM dbo.F_CUSTOMER_CONTACT cc WITH(NOLOCK)
	INNER JOIN dbo.F_CUSTOMER_CONTACT_ADDRESS cca WITH (NOLOCK)
		ON cc.CUSTOMER_CONTACT_ID = cca.CUSTOMER_CONTACT_ID
	WHERE cca.CUSTOMER_ADDRESS_ID = @ADDRESS_ID			
	
END
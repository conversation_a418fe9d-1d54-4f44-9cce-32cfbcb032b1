-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT dbo.fn_str_D_CATEGORY_HIERARCHY_SET('FSP300-60BTV')
CREATE FUNCTION [dbo].[fn_str_D_CATEGORY_HIERARCHY_SET]
(
	@C_ITEM_NUMBER nvarchar(256)
)
RETURNS varchar(MAX)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @C_RETURN_VAL varchar(MAX) = ''

	DECLARE BuildStepID CURSOR LOCAL FORWARD_ONLY FOR
		SELECT
			vw.CategoryFullPath as ITEM_CATEGORY_FULL_PATH
		FROM F_ITEM_MASTER	fim WITH(NOLOCK)
		INNER JOIN [dbo].[vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY]	vw	WITH(NOLOCK)
			ON vw.ItemMasterId = fim.ITEM_MASTER_ID
		WHERE fim.ITEM_NUMBER = @C_ITEM_NUMBER
			
	OPEN BuildStepID
	DECLARE @CATEGORY_FULL_PATH varchar(150)
	FETCH NEXT FROM BuildStepID INTO @CATEGORY_FULL_PATH
	
	WHILE @@FETCH_STATUS = 0
	BEGIN
		-- Add the T-SQL statements to compute the return value here
		SELECT 
			@C_RETURN_VAL
			=
			CASE
				WHEN ISNULL(@C_RETURN_VAL, '') = '' THEN @CATEGORY_FULL_PATH
				ELSE @C_RETURN_VAL + '||' + @CATEGORY_FULL_PATH
			END
			
		FETCH NEXT FROM BuildStepID INTO @CATEGORY_FULL_PATH
	END
	CLOSE BuildStepID
	DEALLOCATE BuildStepID

	-- Return the result of the function
	RETURN ISNULL(@C_RETURN_VAL, '')

END
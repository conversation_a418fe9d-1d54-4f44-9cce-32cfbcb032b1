CREATE TABLE [dbo].[F_INVOICE_PAYMENT] (
    [INVOICE_PAYMENT_ID] BIGINT        IDENTITY (1, 1) NOT NULL,
    [INVOICE_ID]         BIGINT        NOT NULL,
    [PAYMENT_ID]         BIGINT        NOT NULL,
    [AMOUNT]             MONEY         CONSTRAINT [DF_F_INVOICE_PAYMENT_AMOUNT] DEFAULT ((0)) NOT NULL,
    [CREDIT_AMOUNT]      MONEY         CONSTRAINT [DF_F_INVOICE_PAYMENT_CREDIT_AMOUNT] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY_ID]     BIGINT        NULL,
    [INSERTED_BY_IP]     BIGINT        NULL,
    [UPDATED_BY_ID]      BIGINT        NULL,
    [UPDATED_BY_IP]      BIGINT        NULL,
    [DELETED_BY_ID]      BIGINT        NULL,
    [DELETED_BY_IP]      BIGINT        NULL,
    [IS_DELETED]         BIT           CONSTRAINT [DF_F_INVOICE_PAYMENT_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]        VARCHAR (150) CONSTRAINT [DF_F_INVOICE_PAYMENT_INSERTED_BY] DEFAULT (isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid)) NOT NULL,
    [INSERTED_DT]        DATETIME      CONSTRAINT [DF_F_INVOICE_PAYMENT_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [DELETED_BY]         VARCHAR (150) NULL,
    [DELETED_DT]         DATETIME      NULL,
    [SELF_ID]            AS            ([INVOICE_PAYMENT_ID]),
    [SELF_NAME]          AS            (CONVERT([nvarchar](20),[INVOICE_PAYMENT_ID],0)),
    [SELF_CREATE_DATE]   AS            (isnull([INSERTED_DT],'2000-01-01')),
    [PARENT_ID]          AS            (CONVERT([bigint],isnull(NULL,(-1)),0)),
    CONSTRAINT [PK_F_INVOICE_PAYMENT_1] PRIMARY KEY CLUSTERED ([INVOICE_PAYMENT_ID] ASC),
    CONSTRAINT [FK_F_INVOICE_PAYMENT_F_CUSTOMER_PAYMENT] FOREIGN KEY ([PAYMENT_ID]) REFERENCES [dbo].[F_CUSTOMER_PAYMENT] ([CUSTOMER_PAYMENT_ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_F_INVOICE_PAYMENT_F_SALES_ORDER_INVOICE] FOREIGN KEY ([INVOICE_ID]) REFERENCES [dbo].[F_INVOICE] ([INVOICE_ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_F_INVOICE_PAYMENT_tb_User__DELETED_BY_ID] FOREIGN KEY ([DELETED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_INVOICE_PAYMENT_tb_User__INSERTED_BY_ID] FOREIGN KEY ([INSERTED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_INVOICE_PAYMENT_tb_User__UPDATED_BY_ID] FOREIGN KEY ([UPDATED_BY_ID]) REFERENCES [dbo].[tb_User] ([UserID])
);









GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores the payment information. Which amount of invoice was paid', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_INVOICE_PAYMENT';


GO
CREATE NONCLUSTERED INDEX [IX_F_INVOICE_PAYMENT__PAYMENT_ID]
    ON [dbo].[F_INVOICE_PAYMENT]([PAYMENT_ID] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_F_INVOICE_PAYMENT_INVOICE_ID] ON [dbo].[F_INVOICE_PAYMENT]([INVOICE_ID] ASC) 
	INCLUDE([INVOICE_PAYMENT_ID],[AMOUNT], [PAYMENT_ID])
 GO
CREATE NONCLUSTERED INDEX [IX_F_INVOICE_PAYMEN__IS_DELETED]
    ON [dbo].[F_INVOICE_PAYMENT]([IS_DELETED] ASC)
    INCLUDE([INVOICE_ID], [INVOICE_PAYMENT_ID]);


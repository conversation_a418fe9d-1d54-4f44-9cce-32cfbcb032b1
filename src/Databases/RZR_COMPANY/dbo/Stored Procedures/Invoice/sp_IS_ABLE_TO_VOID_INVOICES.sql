CREATE PROCEDURE [dbo].[sp_IS_ABLE_TO_VOID_INVOICES]
	@C_INVOICE_IDS dbo.[bigint_ID_ARRAY] READONLY
AS
BEGIN
	
	SELECT
		 FI.INVOICE_ID											AS InvoiceId			
		,FI.AUTO_NAME											AS InvoiceAutoName
		,FI.CONSOLIDATED_IN_AUTO_NAME							AS ConsolidatedInvoiceAutoName
		,CASE
			WHEN FI.IS_DELETED = 1
				THEN 1	--IsDeleted = 1
			WHEN FI.IS_VOIDED = 1
				THEN 2	--IsVoided = 2
			WHEN FI.CONSOLIDATED_ID IS NOT NULL
				THEN 3	--IsIncludedIntoConsolidated = 3
			WHEN FI.RECYCLING_ORDER_ID IS NOT NULL
				THEN 4	--IsRecycling = 4
			WHEN FCP_SRC.CUSTOMER_PAYMENT_ID IS NOT NULL
				THEN 5	--HasPayments = 5	
			ELSE 0	--Can = 0
		END AS [State]		
		,FCP_SRC.CUSTOMER_PAYMENT_ID							AS PaymentId
		,cm.AutoName											as PaymentFromCreditMemo
		,FCP_SRC.AMOUNT + FCP_SRC.DISCOUNT						AS PaymentAmount
		,ISNULL(FCP_SRC.CHECK_NUMBER, FCP_SRC.REFERENCE_NUMBER)	AS CheckNumber
		,FCP_SRC.PAYMENT_DATE									AS PaymentDate	
		,json_query((
				select distinct
					cm.AutoName											as CreditMemoAutoName
					,inpmt.AUTO_NAME									as InvoiceCreditMemoApplied
					,cp.PAYMENT_ID										as AppliedPaymentId
					,iif(ipt.INVOICE_ID is null, null, i.AUTO_NAME)		as PaidApInvoiceAutoName		
				from dbo.F_CreditMemo				cm		with(nolock)					
				inner join dbo.F_CUSTOMER_CREDIT	cc		with(nolock)
					on cm.CustomerCreditId = cc.CUSTOMER_CREDIT_ID
					and cc.IS_DELETED = 0
				left join dbo.F_CREDIT_PAYMENT		cp		with(nolock) --used credit/credit memo or not
					on cc.CUSTOMER_CREDIT_ID = cp.CREDIT_ID	
				left join dbo.F_INVOICE_PAYMENT		ipmt	with(nolock)
					on cp.PAYMENT_ID = ipmt.PAYMENT_ID
				left join dbo.F_INVOICE				inpmt	with(nolock)
					on ipmt.INVOICE_ID = inpmt.INVOICE_ID
				left join dbo.F_INVOICE				i		with(nolock)
					on  i.INVOICE_TYPE_ID = 3
					and cm.Id = i.ORDER_ID
					and i.IS_VOIDED = 0
				left join dbo.F_INVOICE_PAYMENT		ipt		with(nolock) --credit memo AP invoice is paied or not
					on i.INVOICE_ID = ipt.INVOICE_ID
					and ipt.IS_DELETED = 0
				where fi.INVOICE_ID = cm.InvoiceId
				  and (cp.CREDIT_ID is not null
				    or ipt.INVOICE_ID is not null)
				for json path
			))	as CreditMemos	
	FROM (
		-- Invoices information
		SELECT
			 FI.INVOICE_ID
			,FI.AUTO_NAME
			,FI.CONSOLIDATED_ID
			,FI.CONSOLIDATED_IN_AUTO_NAME
			,FI.IS_DELETED
			,FI.IS_VOIDED
			,FI.RECYCLING_ORDER_ID
		FROM @C_INVOICE_IDS						IDS
		INNER JOIN dbo.vw_AR_INVOICE			FI		with(nolock) -- invoice payment	
			ON  FI.INVOICE_ID = IDS.ID
			AND FI.IS_INVOICE_CONSOLIDATED = 0
		UNION
		SELECT
			 FI.INVOICE_ID
			,FI.AUTO_NAME
			,FI.CONSOLIDATED_ID
			,FI.CONSOLIDATED_IN_AUTO_NAME
			,FI.IS_DELETED
			,FI.IS_VOIDED
			,FI.RECYCLING_ORDER_ID
		FROM @C_INVOICE_IDS						IDS
		INNER JOIN dbo.vw_AP_INVOICE			FI		with(nolock) -- invoice payment	
			ON  FI.INVOICE_ID = IDS.ID
			AND FI.IS_INVOICE_CONSOLIDATED = 0
	)									FI
	-- invoice payment	
	LEFT JOIN dbo.F_INVOICE_PAYMENT		FIP		with(nolock)
		ON  FIP.INVOICE_ID = FI.INVOICE_ID
		AND FIP.IS_DELETED = 0
	LEFT JOIN dbo.F_CUSTOMER_PAYMENT	FCP_SRC with(nolock)
		ON  FCP_SRC.CUSTOMER_PAYMENT_ID	= FIP.PAYMENT_ID
		AND FCP_SRC.IS_DELETED			= 0	
	left join dbo.F_CREDIT_PAYMENT FCRDP_SRC with(nolock)
		on FCP_SRC.CUSTOMER_PAYMENT_ID	= FCRDP_SRC.PAYMENT_ID
	left join dbo.F_CreditMemo			cm with(nolock)
		on cm.CustomerCreditId = FCRDP_SRC.CREDIT_ID
	ORDER BY 
		FI.AUTO_NAME			ASC
		--,FCP_SRC.PAYMENT_DATE	DESC
		,FCP_SRC.CUSTOMER_PAYMENT_ID DESC

END
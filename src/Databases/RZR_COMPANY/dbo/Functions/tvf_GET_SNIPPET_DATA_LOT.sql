CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_LOT]
(		
	@RECYCLING_ORDER_ITEM_ID BIGINT
)
RETURNS @T_RESULT TABLE(
	   Id			BIGINT
	  ,LotNumber	[varchar](200)
	  ,IsReadyToBePriced [varchar](3)
	  ,Commodity    [varchar](250)
	  ,Workflow     [varchar](250)
	  ,Packaging    [varchar](250)
	  ,Qty			BIGINT 
	  ,[Weight]		float 
	  ,[Tare]		float
	  ,[Net]		float
	  ,WeightRemain float
	  ,Warehouse	[varchar](50) 
	  ,[Location]	[varchar](50)
      ,[AuditDate]  datetime
	  ,[Notes]		[nvarchar](max)
    )
AS
BEGIN
	 INSERT INTO @T_RESULT (
		  Id			
		 ,LotNumber	
		 ,IsReadyToBePriced
		 ,Commodity    
		 ,Workflow     
		 ,Packaging    
		 ,Qty			
		 ,[Weight]		
		 ,[Tare]		
		 ,[Net]		
		 ,WeightRemain 
		 ,Warehouse	
		 ,[Location]
		 ,[AuditDate]
		 ,[Notes]
		 )
	 SELECT
		FROI.[RECYCLING_ORDER_ITEM_ID]				AS [Id],
		FROI.[LOT_AUTO_NAME]						AS [LotNumber],
		IIF(FROIN.[StatusId] = 8, 'Yes', 'No')
													AS [IsReadyToBePriced],
		FRIM.[RECYCLING_ITEM_MASTER_NAME]			AS [Commodity],
		CRWT.[WORKFLOW_TYPE_DESC]					AS [Workflow],
		DRPT.[PACKAGING_TYPE_DESC]					AS [Packaging],
		FROI.[ITEM_COUNT]							AS [Qty],
		FROI.[WEIGHT_RECEIVED]						AS [Weight],
		FROI.[WEIGHT_TARE]							AS [Tare],
		FROI.[WEIGHT_RECEIVED] - FROI.[WEIGHT_TARE]	AS [Net],
		FROI.[WEIGHT_REMAIN]						AS [WeightRemain],
		WH.[WAREHOUSE_CD]							AS [Warehouse],
		[dbo].[fn_str_GET_LOCATON_NAME_WITH_SECONDARY](FROI.[LOCATION_ID]) AS [Location],
	    FROI.Updated_dt                             AS AuditDate,
		FROI.[NOTES]								AS Notes
	FROM [dbo].[F_RECYCLING_ORDER_ITEM]			AS FROI WITH(NOLOCK)
		INNER JOIN [dbo].[F_RECYCLING_ORDER_INBOUND]	AS FROIN WITH(NOLOCK)
			ON FROIN.[RECYCLING_ORDER_ID] = FROI.[RECYCLING_ORDER_ID]
		INNER JOIN [dbo].[F_RECYCLING_ITEM_MASTER]		AS FRIM WITH(NOLOCK)
			ON FRIM.[RECYCLING_ITEM_MASTER_ID] = FROI.[RECYCLING_ITEM_MASTER_ID]
		INNER JOIN [dbo].[C_RECYCLING_WORKFLOW_TYPE]	AS CRWT WITH(NOLOCK)
			ON CRWT.[WORKFLOW_TYPE_ID] = FROI.[WORKFLOW_STEP_ID]
		INNER JOIN [dbo].[D_RECYCLING_PACKAGING_TYPE]	AS DRPT WITH(NOLOCK)
			ON DRPT.[RECYCLING_PACKAGING_TYPE_ID] = FROI.[PACKAGING_TYPE_ID]
		LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER				ROIT WITH(NOLOCK)
			ON  ROIT.RECYCLING_ORDER_ITEM_ID = FROI.RECYCLING_ORDER_ITEM_ID
			AND ROIT.OUTBOUND_ORDER_ID		 = FROI.OUTBOUND_ORDER_ID -- only the actual one
		LEFT JOIN dbo.F_RECYCLING_ORDER						ROT   WITH(NOLOCK)
			ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
		LEFT JOIN 	dbo.F_LOCATION								loc  WITH (NOLOCK)
			ON froi.LOCATION_ID = loc.LOCATION_ID
		LEFT JOIN [dbo].[D_WAREHOUSE]					AS WH WITH(NOLOCK)
			ON WH.[WAREHOUSE_ID] = COALESCE(ROT.[WAREHOUSE_ID], LOC.[WAREHOUSE_ID])
	WHERE FROI.RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID

    RETURN		
END
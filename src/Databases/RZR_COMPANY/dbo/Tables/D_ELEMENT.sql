CREATE TABLE [dbo].[D_ELEMENT] (
    [<PERSON>LEMENT_ID]         INT           IDENTITY (1, 1) NOT NULL,
    [<PERSON>LEMENT_CD]         VARCHAR (50)  NOT NULL,
    [ELEMENT_NAME]       VARCHAR (150) NOT NULL,
    [<PERSON><PERSON>MENT_DESC]       VARCHAR (250) NOT NULL,
    [<PERSON>LEMENT_GROUP]      INT           NULL,
    [ELEMENT_PERIOD]     INT           NOT NULL,
    [ELEMENT_WEIGHT]     FLOAT (53)    NULL,
    [ELEMENT_DENSITY]    FLOAT (53)    NULL,
    [ELEMENT_MELT]       FLOAT (53)    NULL,
    [<PERSON>LEMENT_BOIL]       FLOAT (53)    NULL,
    [ELEMENT_HEAT]       FLOAT (53)    NULL,
    [ELEMENT_ELECTRONEG] FLOAT (53)    NULL,
    [ELEMENT_ABUNDANCE]  FLOAT (53)    NOT NULL,
    [IS_INACTIVE]        BIT           CONSTRAINT [DF_D_ELEMENT_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]         BIT           CONSTRAINT [DF_D_ELEMENT_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]        VARCHAR (150) NOT NULL,
    [INSERTED_DT]        DATETIME      NOT NULL,
    [UPDATED_BY]         VARCHAR (250) NULL,
    [UPDATED_DT]         DATETIME      NULL,
    [DELETED_BY]         VARCHAR (150) NULL,
    [DELETED_DT]         DATETIME      NULL,
    CONSTRAINT [PK_D_ELEMENT] PRIMARY KEY CLUSTERED ([ELEMENT_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_ELEMENT';


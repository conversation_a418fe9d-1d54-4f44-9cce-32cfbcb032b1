CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_LINKED_ORDERS] 
	@ORDER_ID BIGINT
AS
BEGIN

	SELECT
		P.PURCHASE_ORDER_ID,
		P.AUTO_NAME,
		S.SALES_ORDER_ID,
		S.SALES_ORDER_NUMBER
	FROM F_RECYCLING_ORDER			O	WITH(NOLOCK)
	LEFT JOIN F_PURCHASE_ORDER		P	WITH(NOLOCK)
		ON O.RECYCLING_ORDER_ID = P.RECYCLING_ORDER_ID AND O.CUSTOMER_ID = P.CUSTOMER_ID
	LEFT JOIN F_SALES_ORDER			S	WITH (NOLOCK)
		ON O.RECYCLING_ORDER_ID = S.RECYCLING_ORDER_ID AND O.CUSTOMER_ID = S.CUSTOMER_ID
	WHERE O.RECYCLING_ORDER_ID = @ORDER_ID
					
END
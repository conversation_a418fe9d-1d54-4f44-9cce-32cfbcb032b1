-- =============================================
-- Author:		Unknown
-- Create date: Unknown
-- Description:	Pushes C_ITEM_JOB tasks as DW_COM queue jobs. Invoked by SQL Server Agent job named job_{DbName}_REBUILD_C_ITEM_JOB
-- =============================================
/*
	select * from c_item_job order by 1 desc --truncate table c_item_job

	insert into c_item_job (
		ITEM_ID
		,BUILD_TYPE_CD
		,NEXT_BUILD_TYPE_CD
		,IS_INACTIVE
		,IS_DELETED
		,INSERTED_BY
		,INSERTED_DT
	) select distinct top (10)
		fii.item_master_id
		,'ITEM_BASIC_'
		,NULL--'ITEM_SALES_'
		,0
		,0
		,'script'
		,getutcdate()
	from [dbo].[vw_F_ITEM_INVENTORY_AVAILABLE_FORSKU_REBUILD] fii
	where item_id is not null
	order by fii.item_id desc


	exec [dbo].[sp_REBUILD_C_ITEM_JOB_BY_ITEM_MASTER]
		--@C_ROWS_TO_PROCESS = 5,
		@C_IS_DEBUG = 1
*/
CREATE PROCEDURE [dbo].[sp_REBUILD_C_ITEM_JOB_BY_ITEM_MASTER] 
	@C_ROWS_TO_PROCESS  INT = 100
	,@C_IS_DEBUG		BIT = 0
AS

	SET STATISTICS IO OFF
	SET STATISTICS TIME OFF

	SET @C_ROWS_TO_PROCESS = ISNULL(@C_ROWS_TO_PROCESS, 100)

	DECLARE
		 @C_PROCESS_CD			varchar(128) = DW_COM.dbo.fn_str_GET_BUILD_PROCESS_CD(@@SERVERNAME, 0, DB_NAME() + '_' + OBJECT_NAME(@@PROCID))
		,@UTC_NOW				DATETIME     = GETUTCDATE()
		,@COMPANY_ID			BIGINT		 = [dbo].[fn_bigint_GET_COMPANY_ID]()
		,@job_ITEM_BASIC		NVARCHAR(64) = N'ITEM_BASIC_'
		,@job_ITEM_SALES		NVARCHAR(64) = N'ITEM_SALES_'
		,@job_ITEM_SALES_BASIC	NVARCHAR(64) = N'ITEM_SALES_BASIC_'

		
	UPDATE CIJ SET
		IS_DELETED = 1,
		DELETED_BY = @C_PROCESS_CD,
		DELETED_DT = @UTC_NOW
	FROM C_ITEM_JOB CIJ WITH(READPAST, ROWLOCK, UPDLOCK)
	WHERE IS_INACTIVE = 1 
	  AND IS_DELETED  = 0


	SELECT DISTINCT
		 CIJ.ITEM_MASTER_ID
		,CIJ.BUILD_TYPE_CD
		,CIJ.NEXT_BUILD_TYPE_CD
	into #tasks -- drop table #tasks
	from [dbo].[C_ITEM_JOB]	 CIJ WITH (NOLOCK)
	WHERE CIJ.IS_INACTIVE = 0
		AND CIJ.BUILD_TYPE_CD IS NOT NULL
		AND CIJ.BUILD_TYPE_CD != ISNULL(CIJ.NEXT_BUILD_TYPE_CD, N'')
		AND CIJ.ITEM_MASTER_ID > 0
		AND ISNULL(CIJ.ITEM_ID, 0) <= 0
	
	if (@@rowcount <= 0)
		return;

	SELECT
		ITEM_MASTER_ID
		,BUILD_TYPE_CD
		,NEXT_BUILD_TYPE_CD
		,row_id_in_group
		,floor((row_id_in_group - 1) / @C_ROWS_TO_PROCESS) as group_id
	INTO #by_blocks -- drop table #by_blocks
	FROM (
		SELECT 
			t.ITEM_MASTER_ID
			,t.BUILD_TYPE_CD
			,t.NEXT_BUILD_TYPE_CD
			,ROW_NUMBER() OVER (PARTITION by t.BUILD_TYPE_CD, t.NEXT_BUILD_TYPE_CD ORDER BY t.ITEM_MASTER_ID) AS row_id_in_group
		FROM (
			SELECT DISTINCT TOP 100 PERCENT
				tasks.ITEM_MASTER_ID
				,tasks.BUILD_TYPE_CD
				,tasks.NEXT_BUILD_TYPE_CD
			from #tasks			tasks
			where not (
				tasks.BUILD_TYPE_CD = @job_ITEM_BASIC
				and tasks.NEXT_BUILD_TYPE_CD = @job_ITEM_SALES_BASIC
				and exists(
					select top(1) 1
					from #tasks dupes
					where dupes.ITEM_MASTER_ID = tasks.ITEM_MASTER_ID
						and dupes.NEXT_BUILD_TYPE_CD = @job_ITEM_SALES
						and dupes.BUILD_TYPE_CD = tasks.BUILD_TYPE_CD
					)
			)
			and not (
				tasks.BUILD_TYPE_CD = @job_ITEM_SALES_BASIC
				and exists(
					select top(1) 1
					from #tasks dupes
					where dupes.ITEM_MASTER_ID = tasks.ITEM_MASTER_ID
						and dupes.BUILD_TYPE_CD = @job_ITEM_SALES
						and isnull(tasks.NEXT_BUILD_TYPE_CD, N'') = isnull(dupes.NEXT_BUILD_TYPE_CD, N'')
					)
			)
		) t
	) GROUPED_BY_BUILD_TYPE_X_ROWS_TO_PROCESS


	IF (@C_IS_DEBUG = 1)
		SELECT
			*
		FROM #by_blocks
		ORDER BY 
			BUILD_TYPE_CD
			,NEXT_BUILD_TYPE_CD
			,group_id
			,row_id_in_group
	
	DECLARE 
		@jobName				NVARCHAR(128)
		,@continuationJobName	NVARCHAR(128)
		,@groupId				int
		,@tMasterItemIds		dbo.bigint_Id_Array
		,@isJobAdded			BIT	= 0
		

	DECLARE cursorInsertJobs CURSOR LOCAL FORWARD_ONLY FOR
		SELECT
			BUILD_TYPE_CD
			,NEXT_BUILD_TYPE_CD
			,group_id
		FROM #by_blocks	t
		GROUP BY
			t.BUILD_TYPE_CD
			,t.NEXT_BUILD_TYPE_CD
			,t.group_id
		ORDER BY 
			CASE t.BUILD_TYPE_CD
				WHEN @job_ITEM_BASIC				THEN 1
				WHEN @job_ITEM_SALES_BASIC			THEN 2
				WHEN @job_ITEM_SALES				THEN 3
				WHEN N'ITEM_SALES_IMAGE_OUTPUT_'	THEN 6
				ELSE 7
			END			asc
			,t.group_id asc

	OPEN cursorInsertJobs
		FETCH NEXT FROM cursorInsertJobs INTO @jobName, @continuationJobName, @groupId
		WHILE @@FETCH_STATUS = 0
		BEGIN
			
			insert into @tMasterItemIds
			select
				ITEM_MASTER_ID
			from #by_blocks t
			where t.BUILD_TYPE_CD					= @jobName
			  and ISNULL(t.NEXT_BUILD_TYPE_CD, N'')	= ISNULL(@continuationJobName, N'')
			  and t.group_id						= @groupId
				
			if (@@rowcount > 0)
			begin
			
				EXEC [queue].[sp_RunDwComJob]
					@JobName					= @jobName
					,@JobNameToRunOnCompleted	= @continuationJobName
					,@TMasterItemIds			= @tMasterItemIds
					,@OutIsJobAdded				= @isJobAdded OUTPUT
					,@IsDebug					= @C_IS_DEBUG
				

				IF (@C_IS_DEBUG = 1)
				BEGIN
					SELECT 
						ID						AS ITEM_MASTER_ID
						,@groupId				AS [@groupId]
						,@jobName				AS [@jobName]
						,@continuationJobName	AS [@continuationJobName]
						,@isJobAdded			AS [@isJobAdded]
					FROM @tMasterItemIds
				END
				
				IF (@isJobAdded = 1)
					UPDATE CIJ SET
						IS_INACTIVE = 1,
						UPDATED_BY  = @C_PROCESS_CD,
						UPDATED_DT  = @UTC_NOW		
					FROM @tMasterItemIds	T
					inner join C_ITEM_JOB	CIJ WITH (READPAST, ROWLOCK, UPDLOCK)
						on CIJ.ITEM_MASTER_ID					= T.ID
						and CIJ.BUILD_TYPE_CD					= @jobName
						and ISNULL(CIJ.NEXT_BUILD_TYPE_CD, N'')	= ISNULL(@continuationJobName, N'')
					WHERE CIJ.IS_INACTIVE = 0


				SET @isJobAdded = 0
				delete from @tMasterItemIds

			end

			FETCH NEXT FROM cursorInsertJobs INTO @jobName, @continuationJobName, @groupId
		END
	CLOSE cursorInsertJobs
	DEALLOCATE cursorInsertJobs

GO

<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>RZR_COMPANY</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{e1425b26-e5f5-4115-bc06-eaf6d8d3b587}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql130DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>RZR_COMPANY</RootNamespace>
    <AssemblyName>RZR_COMPANY</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <SccProjectName />
    <SccProvider />
    <SccAuxPath />
    <SccLocalPath />
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
    <TargetDatabase>RZR_COMPANY</TargetDatabase>
    <GenerateCreateScript>True</GenerateCreateScript>
    <IncludeSchemaNameInFileName>True</IncludeSchemaNameInFileName>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">10.0</VisualStudioVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Staging|AnyCPU' ">
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Test|AnyCPU' ">
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Prod|AnyCPU' ">
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Dev|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Dynamic|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'EU|AnyCPU' ">
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="Security\" />
    <Folder Include="dbo\Functions\" />
    <Folder Include="dbo\Stored Procedures\" />
    <Folder Include="dbo\User Defined Types\" />
    <Folder Include="dbo\Stored Procedures\Procs1\" />
    <Folder Include="dbo\Stored Procedures\Procs2\" />
    <Folder Include="Storage\" />
    <Folder Include="api\" />
    <Folder Include="api\Stored Procedures\" />
    <Folder Include="api\User Defined Types\" />
    <Folder Include="Import" />
    <Folder Include="Scripts" />
    <Folder Include="Scripts\Clear" />
    <Folder Include="Scripts\Updates" />
    <Folder Include="dbo\Stored Procedures\rebuild_sp" />
    <Folder Include="dbo\Stored Procedures\Invoice" />
    <Folder Include="dbo\Tables\OperationBlocks" />
    <Folder Include="dbo\Stored Procedures\OperationBlocking" />
    <Folder Include="dbo\Functions\OperationBlocking" />
    <Folder Include="dbo\Stored Procedures\Payment" />
    <Folder Include="dbo\Stored Procedures\AutoNumbers" />
    <Folder Include="dbo\Stored Procedures\UserSession\" />
    <Folder Include="dbo\Stored Procedures\UserSession\Password\" />
    <Folder Include="dbo\Stored Procedures\Location" />
    <Folder Include="recycling" />
    <Folder Include="recycling\User Defined Types" />
    <Folder Include="dbo\Stored Procedures\Printing" />
    <Folder Include="dbo\Stored Procedures\Printing\Labels" />
    <Folder Include="recycling\Stored Procedures\" />
    <Folder Include="magento\Tables" />
    <Folder Include="magento\" />
    <Folder Include="dbo\Stored Procedures\EmailTemplates" />
    <Folder Include="recycling\Tables" />
    <Folder Include="recycling\Stored Procedures\Inventory" />
    <Folder Include="recycling\Stored Procedures\LotAudit" />
    <Folder Include="recycling\Stored Procedures\AuditReport" />
    <Folder Include="dbo\Stored Procedures\Addresses" />
    <Folder Include="reports" />
    <Folder Include="reports\Tables" />
    <Folder Include="reports\Stored Procedures" />
    <Folder Include="reports\User Defined Types" />
    <Folder Include="recycling\Stored Procedures\Assets" />
    <Folder Include="integration" />
    <Folder Include="integration\Stored Procedures" />
    <Folder Include="integration\Tables" />
    <Folder Include="integration\User Defined Types" />
    <Folder Include="dbo\Stored Procedures\Inventory" />
    <Folder Include="recycling\Functions\" />
    <Folder Include="integration\Functions\" />
    <Folder Include="recycling\Stored Procedures\DsvRestrictions" />
    <Folder Include="recycling\Functions\DsvRestrictions" />
    <Folder Include="dbo\Sequences" />
    <Folder Include="dbo\Stored Procedures\Sequences" />
    <Folder Include="inventory" />
    <Folder Include="inventory\Functions" />
    <Folder Include="inventory\Stored Procedures" />
    <Folder Include="inventory\Tables" />
    <Folder Include="inventory\User Defined Types" />
    <Folder Include="cp" />
    <Folder Include="cp\Stored Procedures" />
    <Folder Include="cp\Tables" />
    <Folder Include="dbo\Stored Procedures\MeasuringSystems" />
    <Folder Include="recycling\Stored Procedures\Contracts" />
    <Folder Include="recycling\Stored Procedures\Commodities" />
    <Folder Include="recycling\Stored Procedures\Common" />
    <Folder Include="recycling\Stored Procedures\Workflow" />
    <Folder Include="dbo\Stored Procedures\ItemMasters" />
    <Folder Include="dbo\Stored Procedures\Notifications" />
    <Folder Include="recycling\Functions\Workflow" />
    <Folder Include="recycling\Stored Procedures\Services" />
    <Folder Include="dbo\Stored Procedures\Logos" />
    <Folder Include="recycling\Functions\Contracts" />
    <Folder Include="dbo\Stored Procedures\Settings" />
    <Folder Include="recycling\Stored Procedures\InboundOrder" />
    <Folder Include="dbo\User Defined Types\Common" />
    <Folder Include="recycling\Functions\Quotes" />
    <Folder Include="recycling\Stored Procedures\Quote" />
    <Folder Include="recycling\Stored Procedures\Calendar" />
    <Folder Include="integration\Stored Procedures\SellerCloud" />
    <Folder Include="recycling\View" />
    <Folder Include="recycling\Stored Procedures\Settlement" />
    <Folder Include="bi" />
    <Folder Include="bi\Stored Procedures" />
    <Folder Include="Storage\Tables" />
    <Folder Include="Storage\Stored Procedures" />
    <Folder Include="Storage\User Defined Types" />
    <Folder Include="recycling\Functions\Commodities" />
    <Folder Include="Import\item_masters" />
    <Folder Include="recycling\Stored Procedures\Grading" />
    <Folder Include="Import\customers" />
    <Folder Include="search\" />
    <Folder Include="search\Tables\" />
    <Folder Include="search\Functions\" />
    <Folder Include="search\Stored Procedures\" />
    <Folder Include="search\Views" />
    <Folder Include="dbo\Stored Procedures\Picklists" />
    <Folder Include="dbo\User Defined Types\Tuples" />
    <Folder Include="dbo\Stored Procedures\Sales\Shipments" />
    <Folder Include="dbo\User Defined Types\Sales" />
    <Folder Include="dbo\User Defined Types\Sales\Shipments" />
    <Folder Include="dbo\Stored Procedures\Magento" />
    <Folder Include="reports\Stored Procedures\ReportBuilder" />
    <Folder Include="dbo\Stored Procedures\Report" />
    <Folder Include="Locks" />
    <Folder Include="reports\Stored Procedures\MicrosoftRecyclingReport" />
    <Folder Include="recycling\Stored Procedures\Lot" />
    <Folder Include="reports\Stored Procedures\ConcurReport" />
    <Folder Include="bi\Functions\" />
    <Folder Include="dbo\Stored Procedures\Attriubtes" />
    <Folder Include="dbo\Stored Procedures\Model" />
    <Folder Include="dbo\Stored Procedures\Capabilities" />
    <Folder Include="dbo\Stored Procedures\Manufacturer" />
    <Folder Include="recycling\Stored Procedures\Location" />
    <Folder Include="rzrapi" />
    <Folder Include="rzrapi\Customer" />
    <Folder Include="rzrapi\User" />
    <Folder Include="rzrapi\Customer\Address" />
    <Folder Include="rzrapi\Customer\TransactionTerms" />
    <Folder Include="rzrapi\Tax" />
    <Folder Include="rzrapi\Customer\Tax" />
    <Folder Include="rzrapi\Asset" />
    <Folder Include="recycling\Stored Procedures\HardDrives" />
    <Folder Include="rzrapi\Payment" />
    <Folder Include="dbo\Stored Procedures\CurrencyInfo" />
    <Folder Include="rzrapi\Stored Procedures\" />
    <Folder Include="dbo\Views\CreditMemo" />
    <Folder Include="dbo\Views\Payment" />
    <Folder Include="dbo\Stored Procedures\CreditMemo" />
    <Folder Include="dbo\Tables\CreditMemo" />
    <Folder Include="dbo\Functions\CreditMemo" />
    <Folder Include="reports\Stored Procedures\CreditMemo" />
    <Folder Include="rzrapi\CreditMemo" />
    <Folder Include="rzrapi\Credit" />
    <Folder Include="rzrapi\Invoice" />
    <Folder Include="rzrapi\Invoice\Ar" />
    <Folder Include="rzrapi\Invoice\Ap" />
    <Folder Include="rzrapi\Account" />
    <Folder Include="queue" />
    <Folder Include="queue\Queries" />
    <Folder Include="queue\Tasks" />
    <Folder Include="queue\Queries\Inventory" />
    <Folder Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM" />
    <Folder Include="dbo\Stored Procedures\rebuild_sp\invoked by SQL Agent" />
    <Folder Include="clientportal" />
    <Folder Include="clientportal\Functions" />
    <Folder Include="queue\Queries\Inventory\Split" />
    <Folder Include="clientportal\User defined Types" />
    <Folder Include="Move to Dev" />
    <Folder Include="rzrapi\PortalQuote" />
    <Folder Include="clientportal\Stored Procedures\" />
    <Folder Include="clientportal\Tables\" />
    <Folder Include="recycling\Stored Procedures\RemoteScale" />
    <Folder Include="clientportal\Stored Procedures\TakebackOrder" />
    <Folder Include="clientportal\Stored Procedures\ScheduledOrder" />
    <Folder Include="clientportal\Stored Procedures\SalesOrder" />
    <Folder Include="clientportal\Stored Procedures\CustomerContact" />
    <Folder Include="clientportal\Stored Procedures\Customer" />
    <Folder Include="clientportal\Stored Procedures\Lists" />
    <Folder Include="clientportal\Stored Procedures\Settings" />
    <Folder Include="clientportal\Stored Procedures\RecyclingOrder" />
    <Folder Include="clientportal\Stored Procedures\Asset" />
    <Folder Include="recycling\Commoidty" />
    <Folder Include="recycling\Commoidty\Profile" />
    <Folder Include="recycling\Commoidty\Profile\Stored Procedures" />
    <Folder Include="recycling\Commoidty\Profile\Stored Procedures\Items" />
    <Folder Include="recycling\Commoidty\Version" />
    <Folder Include="recycling\Commoidty\Version\Tables" />
    <Folder Include="recycling\Commoidty\Version\Stored Procedures" />
    <Folder Include="dbo\Functions\System" />
    <Folder Include="BusinessLogic\Sorting" />
    <Folder Include="BusinessLogic\Sorting\Shredding" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Input" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Location" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Commodity" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Scales" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Tables" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Batch" />
    <Folder Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder" />
    <Folder Include="BusinessLogic\Sorting\Shredding\rzrapi" />
    <Folder Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures" />
    <Folder Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Batch" />
    <Folder Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots" />
    <Folder Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch" />
    <Folder Include="recycling\Commoidty\Profile\Tables" />
    <Folder Include="recycling\Commoidty\Profile\User Defined Types" />
    <Folder Include="BusinessLogic\Sorting\Deman" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\SortingArea" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\InputLots" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\Job" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\JobTypes" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\OutputLots" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\Job\Tables" />
    <Folder Include="BusinessLogic\Sorting\Deman\recycling\SortingArea\Stored Procedures" />
    <Folder Include="rzrapi\OutboundOrder" />
    <Folder Include="BusinessLogic\Sorting\Analysis" />
    <Folder Include="BusinessLogic\Sorting\Analysis\rzrapi" />
    <Folder Include="BusinessLogic\Sorting\Analysis\rzrapi\Stored Procedures" />
    <Folder Include="BusinessLogic\Sorting\Analysis\rzrapi\Functions" />
    <Folder Include="rzrapi\Inventory" />
    <Folder Include="rzrapi\Inventory\Scan" />
    <Folder Include="rzrapi\Truck" />
    <Folder Include="rzrapi\Notifications" />
    <Folder Include="rzrapi\Notifications\Queue" />
    <Folder Include="rzrapi\Notifications\QueueParams" />
    <Folder Include="rzrapi\Notifications\QueueTypes" />
    <Folder Include="reports\Stored Procedures\ResaleOnHandSummary" />
    <Folder Include="rzrapi\WorkLog" />
    <Folder Include="rzrapi\ReportManifest" />
    <Folder Include="rzrapi\Roles" />
    <Folder Include="BusinessLogic\SharedPresets" />
    <Folder Include="BusinessLogic\SharedPresets\Tables" />
    <Folder Include="BusinessLogic\SharedPresets\Stored Procedures" />
    <Folder Include="BusinessLogic\SharedPresets\Stored Procedures\Favor" />
    <Folder Include="rzrapi\RecyclingService" />
    <Folder Include="rzrapi\Commodity" />
    <Folder Include="rzrapi\SalesOrder" />
    <Folder Include="recycling\Stored Procedures\AssetWorkflowTemplates" />
    <Folder Include="rzrapi\CommodityRule" />
    <Folder Include="rzrapi\User Defined Types" />
    <Folder Include="rzrapi\Attributes" />
    <Folder Include="BusinessLogic\EnvironmentalTracking" />
    <Folder Include="BusinessLogic\EnvironmentalTracking\dbo" />
    <Folder Include="BusinessLogic\EnvironmentalTracking\dbo\Tables" />
    <Folder Include="BusinessLogic\EnvironmentalTracking\dbo\Stored Procedures" />
    <Folder Include="BusinessLogic\EnvironmentalTracking\rzrapi" />
    <Folder Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures" />
    <Folder Include="rzrapi\ItemMaster" />
    <Folder Include="dbo\Tables\Partitioned" />
    <Folder Include="BusinessLogic\StatePrograms" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToCommodity" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToContract" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToLot" />
    <Folder Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder" />
    <Folder Include="BusinessLogic\StatePrograms\recycling" />
    <Folder Include="BusinessLogic\StatePrograms\recycling\Tables" />
    <Folder Include="dbo\Tables\LotLogs" />
    <Folder Include="dbo\Stored Procedures\LotLogs" />
    <Folder Include="dbo\User Defined Types\LotLogs" />
    <Folder Include="rzrapi\Customer\Tag" />
    <Folder Include="cp\ApiAccess" />
    <Folder Include="cp\ApiAccess\Stored Procedures" />
    <Folder Include="clientportal\Stored Procedures\ApiAccess" />
    <Folder Include="clientportal\Stored Procedures\Permissions" />
    <Folder Include="dbo\Stored Procedures\Accounts" />
    <Folder Include="shared" />
    <Folder Include="shared\Reports" />
    <Folder Include="shared\Reports\Stored Procedures" />
    <Folder Include="shared\Reports\User Defined Types" />
    <Folder Include="shared\Listings" />
    <Folder Include="shared\System" />
    <Folder Include="shared\System\Functions" />
    <Folder Include="shared\Listings\Views" />
    <Folder Include="clientportal\Stored Procedures\EnvironmentalReports" />
    <Folder Include="bi\Views" />
    <Folder Include="rzrapi\ContractServices" />
    <Folder Include="cp\Functions\" />
    <Folder Include="clientportal\Stored Procedures\BoxProgram" />
    <Folder Include="clientportal\Stored Procedures\Templates" />
    <Folder Include="dbo\Stored Procedures\FreightCarrier" />
    <Folder Include="rzrapi\EmailTemplates" />
    <Folder Include="rzrapi\Settings" />
    <Folder Include="BusinessLogic\PurchaseOrders" />
    <Folder Include="BusinessLogic\PurchaseOrders\dbo" />
    <Folder Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures" />
    <Folder Include="BusinessLogic\PurchaseOrders\cp" />
    <Folder Include="BusinessLogic\PurchaseOrders\cp\Stored Procedures" />
    <Folder Include="BusinessLogic\PurchaseOrders\dbo\Functions" />
    <Folder Include="BusinessLogic\PurchaseOrders\api" />
    <Folder Include="BusinessLogic\PurchaseOrders\api\Stored Procedures" />
    <Folder Include="BusinessLogic\PurchaseOrders\dbo\Tables" />
    <Folder Include="BusinessLogic\PurchaseOrders\dbo\Views" />
    <Folder Include="BusinessLogic\PurchaseOrders\rzrapi" />
    <Folder Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures" />
    <Folder Include="dbo\Stored Procedures\OneTimePassword" />
    <Folder Include="rzrapi\BoxProgram" />
    <Folder Include="clientportal\Stored Procedures\Commodity" />
    <Folder Include="clientportal\Stored Procedures\Lot" />
    <Folder Include="clientportal\Stored Procedures\Warehouse" />
    <Folder Include="clientportal\Stored Procedures\Location" />
    <Folder Include="dbo\Tables\BoxProgram" />
    <Folder Include="rzrapi\Shipping" />
    <Folder Include="rzrapi\Shipping\Address" />
    <Folder Include="dbo\Stored Procedures\BoxProgram" />
    <Folder Include="rzrapi\Emails" />
    <Folder Include="rzrapi\BoxProgram\Takeback" />
    <Folder Include="BusinessLogic\Relocate" />
    <Folder Include="BusinessLogic\Relocate\dbo" />
    <Folder Include="BusinessLogic\Relocate\dbo\Stored Procedures" />
    <Folder Include="BusinessLogic\Relocate\recycling" />
    <Folder Include="BusinessLogic\Relocate\recycling\Stored Procedures" />
    <Folder Include="rzrapi\ServiceOptions" />
    <Folder Include="BusinessLogic\Sso" />
    <Folder Include="BusinessLogic\Sso\dbo" />
    <Folder Include="BusinessLogic\Sso\clientportal" />
    <Folder Include="BusinessLogic\Sso\clientportal\Tables" />
    <Folder Include="BusinessLogic\Sso\clientportal\Stored Procedures" />
    <Folder Include="BusinessLogic\Sso\dbo\Stored Procedures" />
    <Folder Include="BusinessLogic\Sso\dbo\Tables" />
    <Folder Include="BusinessLogic\PasswordRecovery" />
    <Folder Include="BusinessLogic\PasswordRecovery\clientportal" />
    <Folder Include="BusinessLogic\PasswordRecovery\dbo" />
    <Folder Include="BusinessLogic\PasswordRecovery\clientportal\Stored Procedures" />
    <Folder Include="BusinessLogic\PasswordRecovery\clientportal\Tables" />
    <Folder Include="BusinessLogic\PasswordRecovery\dbo\Tables" />
    <Folder Include="BusinessLogic\PasswordRecovery\dbo\Stored Procedures" />
    <Folder Include="dbo\Stored Procedures\PricingService" />
    <Folder Include="rzrapi\BoxProgram\Redeployment" />
    <Folder Include="recycling\Stored Procedures\PricingService" />
    <Folder Include="BusinessLogic\Sla" />
    <Folder Include="BusinessLogic\Sla\dbo" />
    <Folder Include="BusinessLogic\Sla\dbo\Tables" />
    <Folder Include="BusinessLogic\Sla\dbo\User Defined Types" />
    <Folder Include="BusinessLogic\Sla\dbo\Stored Procedures" />
    <Folder Include="rzrapi\InboundOrderManifest" />
    <Folder Include="rzrapi\BoxProgram\Remarketing" />
    <Folder Include="BusinessLogic\ClassTracking\" />
    <Folder Include="BusinessLogic\ClassTracking\recycling\" />
    <Folder Include="BusinessLogic\ClassTracking\recycling\Tables\" />
    <Folder Include="BusinessLogic\ClassTracking\recycling\Stored Procedures\" />
    <Folder Include="BusinessLogic\ClassTracking\recycling\Functions\" />
    <Folder Include="BusinessLogic\ClassTracking\rzrapi\" />
    <Folder Include="BusinessLogic\ClassTracking\rzrapi\Stored Procedures\" />
    <Folder Include="rzrapi\Customer\PricingService" />
    <Folder Include="rzrapi\InboundOrder\" />
    <Folder Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules" />
    <Folder Include="BusinessLogic\Sla\dbo\Stored Procedures\Template" />
    <Folder Include="BusinessLogic\AutoPriceRules\" />
    <Folder Include="BusinessLogic\AutoPriceRules\dbo\" />
    <Folder Include="BusinessLogic\AutoPriceRules\dbo\Functions\" />
    <Folder Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\" />
    <Folder Include="BusinessLogic\AutoPriceRules\rzrapi\" />
    <Folder Include="BusinessLogic\AutoPriceRules\rzrapi\Stored Procedures\" />
    <Folder Include="BusinessLogic\AutoPriceRules\dbo\Tables\" />
    <Folder Include="BusinessLogic\AutoPriceRules\dbo\User Defined Types\" />
    <Folder Include="BusinessLogic\Picklist\" />
    <Folder Include="BusinessLogic\Picklist\rzrapi\" />
    <Folder Include="BusinessLogic\Picklist\rzrapi\Stored Procedures\" />
    <Folder Include="reports\Stored Procedures\Picklist\" />
    <Folder Include="recycling\Stored Procedures\InboundOrderJob" />
    <Folder Include="rzrapi\RecyclingQuoteStatus" />
    <Folder Include="dbo\Tables\OnsiteService" />
    <Folder Include="rzrapi\OnsiteService" />
    <Folder Include="dbo\Stored Procedures\Sales\" />
    <Folder Include="BusinessLogic\Taxes\" />
    <Folder Include="dbo\Tables\OnsiteService\Pricing\" />
    <Folder Include="rzrapi\RecyclingService\Pricing\" />
    <Folder Include="BusinessLogic\" />
    <Folder Include="BusinessLogic\CMS\" />
    <Folder Include="BusinessLogic\CMS\clientportal\" />
    <Folder Include="BusinessLogic\CMS\clientportal\Functions\" />
    <Folder Include="BusinessLogic\CMS\clientportal\Stored Procedures\" />
    <Folder Include="BusinessLogic\CMS\clientportal\Tables\" />
    <Folder Include="BusinessLogic\CMS\clientportal\User Defined Types\" />
    <Folder Include="BusinessLogic\CMS\clientportal\views\" />
    <Folder Include="clientportal\Stored Procedures\Outbound\" />
    <Folder Include="dbo\Stored Procedures\Onsite" />
    <Folder Include="BusinessLogic\Taxes\dbo\" />
    <Folder Include="BusinessLogic\Taxes\dbo\Functions\" />
    <Folder Include="BusinessLogic\Taxes\dbo\Stored Procedures\" />
    <Folder Include="BusinessLogic\Taxes\dbo\User Defined Types\" />
    <Folder Include="BusinessLogic\Taxes\dbo\Tables\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="BusinessLogic\CMS\clientportal\Functions\clientportal.sp_SanitizeInput.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Stored Procedures\clientportal.sp_GetWidgetData.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Stored Procedures\clientportal.sp_GetWidgetEnumLists.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Stored Procedures\clientportal.sp_GetWidgets.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Stored Procedures\clientportal.sp_SetWidget.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.D_WidgetDisplayType.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.D_WidgetEntity.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.D_WidgetEntityField.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.D_WidgetFilterType.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.F_PageWidget.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.F_Widget.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.F_WidgetFilter.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.F_WidgetGrouping.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\Tables\clientportal.F_WidgetSelectedEntityField.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\User Defined Types\clientportal.PageWidget.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\User Defined Types\clientportal.WidgetFilter.sql" />
    <Build Include="BusinessLogic\CMS\clientportal\views\clientportal.vw_WidgetInventoryData.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Functions\dbo.fn_bit_CanApplyAvalaraTaxToSalesOrder.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_ApplyAvalaraTransactionTaxRates.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_GetSalesOrderInfoForAvalara.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_GetSalesOrderInvoiceInfo.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_SetAvalaraTransaction.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\User Defined Types\dbo.AvalaraItemTax.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInventoryItemIdsOfSalesOrder.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\dbo.sp_GetNotificationsInboundOrderIsReadyToReview.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Functions\dbo.fn_bit_IsInboundOrderReadyToSettleWithAutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_IsInboundOrderItemsMatchAutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_GetAutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_SetAutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_SetAutoPriceRuleSet.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_GetAutoPriceRuleSets.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_UpdateReadyToBePricedOrders.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Stored Procedures\dbo.sp_CopyAutoPriceRulesToInboundOrder.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderAutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\rzrapi\Stored Procedures\rzrapi.sp_DeleteInboundOrderAutoPriceRule.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_GetAvalaraTaxSettings.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_GetSalesOrderTaxRates.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_SetAvalaraTaxSettings.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_SetSalesOrderTaxRate.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_SyncTaxRates.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\Tables\dbo.U_AvalaraTaxSettings.sql" />
    <Build Include="BusinessLogic\Taxes\dbo\User Defined Types\dbo.TaxRate.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\AM_AllProceduresUni.sql" />
    <Build Include="dbo\Functions\fn_str_FORMAT.sql" />
    <Build Include="dbo\Functions\fn_str_STRIP_XML_TAGS.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Tables\dbo.D_AutoPriceRules.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Tables\dbo.D_AutoPriceRuleSet.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\Tables\dbo.F_InboundOrderAutoPriceRule.sql" />
    <Build Include="BusinessLogic\Picklist\rzrapi\Stored Procedures\rzrapi.sp_GetPicklistPage.sql" />
    <Build Include="BusinessLogic\Picklist\rzrapi\Stored Procedures\rzrapi.sp_GetPicklistStatuses.sql" />
    <Build Include="BusinessLogic\Picklist\rzrapi\Stored Procedures\rzrapi.sp_SetPicklistHold.sql" />
    <Build Include="BusinessLogic\Picklist\rzrapi\Stored Procedures\rzrapi.sp_SetPicklistStatus.sql" />
    <Build Include="dbo\Tables\S_LOG_RAW_DATA.sql" />
    <Build Include="dbo\Tables\D_CHANNEL_CONNECTION_MASTER_DEFAULT.sql" />
    <Build Include="dbo\Tables\U_SYSTEM_SETTINGS.sql" />
    <Build Include="dbo\Tables\U_SYSTEM_INFO.sql" />
    <Build Include="dbo\Tables\U_SYSTEM_IMAGE.sql" />
    <Build Include="dbo\Tables\U_SMTP_SETTINGS.sql" />
    <Build Include="dbo\Tables\U_PAGE_VIEW_FILTER.sql" />
    <Build Include="dbo\Tables\U_FILTERED_PAGE.sql" />
    <Build Include="dbo\Tables\U_COMPANY_ADDRESS.sql" />
    <Build Include="dbo\Tables\tb_UserRole.sql" />
    <Build Include="dbo\Tables\tb_User.sql" />
    <Build Include="dbo\Tables\S_IMPORT_CATEGORIES.sql" />
    <Build Include="dbo\Tables\S_EBAY_CATEGORY_HIERARCHY.sql" />
    <Build Include="dbo\Tables\F_ZEBRA_PRINTER_PARAMS.sql" />
    <Build Include="dbo\Tables\F_USER_WORK_LOG.sql" />
    <Build Include="dbo\Tables\F_USER_WAREHOUSE.sql" />
    <Build Include="dbo\Tables\F_USER_ROLE.sql" />
    <Build Include="dbo\Tables\F_TRUCK_WAREHOUSE.sql" />
    <Build Include="dbo\Tables\F_TRUCK.sql" />
    <Build Include="dbo\Tables\F_TAX_GROUP.sql" />
    <Build Include="dbo\Tables\F_TAX.sql" />
    <Build Include="dbo\Tables\F_TAG_GROUP_TYPE.sql" />
    <Build Include="dbo\Tables\F_TAG_GROUP.sql" />
    <Build Include="dbo\Tables\F_TAG.sql" />
    <Build Include="dbo\Tables\F_SIGNATURE_TEMPLATE.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_TRACKING_LOG.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_PAYOR.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_PACKAGE_TRACKING_EVENT.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_PACKAGE_TRACKING.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_PACKAGE.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_CONFIG.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_ADDRESS.sql" />
    <Build Include="dbo\Tables\F_SALES_TAX_INVOICE.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER_RMA.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER_ITEM.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER_FILE.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER.sql" />
    <Build Include="dbo\Tables\F_RMA_TRACKING.sql" />
    <Build Include="dbo\Tables\F_RMA_PACKAGE_TRACKING_EVENT.sql" />
    <Build Include="dbo\Tables\F_RMA_PACKAGE_TRACKING.sql" />
    <Build Include="dbo\Tables\F_RMA_PACKAGE_ITEM.sql" />
    <Build Include="dbo\Tables\F_RMA_PACKAGE.sql" />
    <Build Include="dbo\Tables\F_RMA_ITEM.sql" />
    <Build Include="dbo\Tables\F_REPORTS.sql" />
    <Build Include="dbo\Tables\F_REMOTE_PRINTER_QUEUE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_TAPE_DRIVE_SHRED.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_OUTBOUND_TRUCKING.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_OUTBOUND_FILE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_OUTBOUND_CONTAINER.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_OTHER_SERVICES.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_SERVICE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_OUTBOUND.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_SERVICE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_MERGE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_MASTER.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_CONSUMED.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_INBOUND.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_EQUIPMENT.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_DOCUMENT.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ITEM_MASTER_IMAGE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ITEM_MASTER.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_INBOUND_FILE.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_HARD_DRIVE_SHRED.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_EQUIPMENT_COLLECTION.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_DATA_SECURITY_COLLECTION.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_CUSTOMER_REPORTS.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_CUSTOMER_CERTIFICATES.sql" />
    <Build Include="dbo\Tables\F_PERMISSION_ROLE_CATEGORY_ACTIONS.sql" />
    <Build Include="dbo\Tables\F_PERMISSION_CATEGORY_ACTIONS.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_USER.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_SET_GROUP.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_SET.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_QUEUE.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_GROUP_SCOPE.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_ENTITY.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION.sql" />
    <Build Include="dbo\Tables\F_LOG_DATA.sql" />
    <Build Include="dbo\Tables\F_LOCATION_DETAIL_CATEGORY.sql" />
    <Build Include="dbo\Tables\F_LOCATION_DETAIL.sql" />
    <Build Include="dbo\Tables\F_LOCATION.sql" />
    <Build Include="dbo\Tables\F_ITEM_SKU_ATTRB_VALUE.sql" />
    <Build Include="dbo\Tables\F_ITEM_SALES_REORDER_TRIGGER.sql" />
    <Build Include="dbo\Tables\F_ITEM_SALES_FORECAST.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING_FILTER.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING_bak.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING_2_0.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING_1_5.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING_1_0.sql" />
    <Build Include="dbo\Tables\F_ITEM_PRICING.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_TITLE.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_SUBSTITUTE.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_SKU_ATTRB_delete.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_SKU_ATTRB_backup.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_SKU_ATTRB.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_CATEGORY.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_BILL_OF_MATERIAL.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_ADD_ON.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER.sql" />
    <Build Include="dbo\Tables\F_ITEM_LOT.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_IQR.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_INTERNAL.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Tables\F_ITEM_IMAGE_EXPORT.sql" />
    <Build Include="dbo\Tables\F_ITEM_IMAGE.sql" />
    <Build Include="dbo\Tables\F_ITEM_FEATURED.sql" />
    <Build Include="dbo\Tables\F_ITEM_EXPORT.sql" />
    <Build Include="dbo\Tables\F_ITEM_COMBO_HEADER.sql" />
    <Build Include="dbo\Tables\F_ITEM_COMBO.sql" />
    <Build Include="dbo\Tables\F_ITEM_CATEGORY.sql" />
    <Build Include="dbo\Tables\F_ITEM_CAPABILITY.sql" />
    <Build Include="dbo\Tables\F_ITEM_ADDON.sql" />
    <Build Include="dbo\Tables\F_INVOICE_PAYMENT.sql" />
    <Build Include="dbo\Tables\F_INVENTORY_AUDIT_SESSION.sql" />
    <Build Include="dbo\Tables\F_GENERATED_UNIQUE_IDENTIFIER.sql" />
    <Build Include="dbo\Tables\F_FREIGHT_PLAN.sql" />
    <Build Include="dbo\Tables\F_ENTITY_TAG.sql" />
    <Build Include="dbo\Tables\F_EMAIL_TEMPLATE.sql" />
    <Build Include="dbo\Tables\F_DOCUMENT_SET_ATTACHMENT.sql" />
    <Build Include="dbo\Tables\F_DOCUMENT_SET.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_PAYMENT.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_MASTER_TAG.sql" />
    <Build Include="dbo\Tables\F_CustomerIpWhitelist.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_FILE.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_DSV.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_CONTACT_ADDRESS.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER.sql" />
    <Build Include="dbo\Tables\F_CREDIT_PAYMENT.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_VALIDATION_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_FILE.sql" />
    <Build Include="dbo\Tables\F_CONTRACT.sql" />
    <Build Include="dbo\Tables\F_COMMUNICATION.sql" />
    <Build Include="dbo\Tables\F_CATEGORY_IMAGE.sql" />
    <Build Include="dbo\Tables\F_CATEGORY_ELEMENT.sql" />
    <Build Include="dbo\Tables\F_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE.sql" />
    <Build Include="dbo\Tables\F_ACCOUNT_TRANSACTION_ITEM.sql" />
    <Build Include="dbo\Tables\F_ACCOUNT_TRANSACTION.sql" />
    <Build Include="dbo\Tables\F_ACCOUNT.sql" />
    <Build Include="dbo\Tables\D_WAREHOUSE.sql" />
    <Build Include="dbo\Tables\D_USER_SETTING.sql" />
    <Build Include="dbo\Tables\D_TRACKING_STATUS.sql" />
    <Build Include="dbo\Tables\D_SHIPPING_STATUS.sql" />
    <Build Include="dbo\Tables\D_SHIPPING_PROVIDER.sql" />
    <Build Include="dbo\Tables\D_SHIPPING_PAYOR_TYPE.sql" />
    <Build Include="dbo\Tables\D_RMA_VERIFICATION_STATUS.sql" />
    <Build Include="dbo\Tables\D_RMA_TYPE.sql" />
    <Build Include="dbo\Tables\D_RMA_STATUS.sql" />
    <Build Include="dbo\Tables\D_RMA_RETURN_REASON.sql" />
    <Build Include="dbo\Tables\D_REPORT_TYPE.sql" />
    <Build Include="dbo\Tables\D_RECYCLING_SERVICE.sql" />
    <Build Include="dbo\Tables\D_RECYCLING_ORDER_AUDIT_STATUS.sql" />
    <Build Include="dbo\Tables\D_RECYCLING_EQUIPMENT.sql" />
    <Build Include="dbo\Tables\D_RECYCLING_DOCUMENT.sql" />
    <Build Include="dbo\Tables\D_PERMISSION_CATEGORY_ENTITY.sql" />
    <Build Include="dbo\Tables\D_PERMISSION_CATEGORY.sql" />
    <Build Include="dbo\Tables\D_MANUFACTURER_LOOKUP.sql" />
    <Build Include="dbo\Tables\D_MANUFACTURER.sql" />
    <Build Include="dbo\Tables\D_LOCATION_SPACE.sql" />
    <Build Include="dbo\Tables\D_LOCATION_SECTION.sql" />
    <Build Include="dbo\Tables\D_LOCATION_RACK.sql" />
    <Build Include="dbo\Tables\D_LOCATION_PALLET_NO.sql" />
    <Build Include="dbo\Tables\D_LOCATION_LEVEL.sql" />
    <Build Include="dbo\Tables\D_LOCATION_DETAIL_PREFIX.sql" />
    <Build Include="dbo\Tables\D_LOCATION_BIN.sql" />
    <Build Include="dbo\Tables\D_ITEM_INVENTORY_STATUS.sql" />
    <Build Include="dbo\Tables\D_ITEM_EXTERNAL_CONDITION_TYPE.sql" />
    <Build Include="dbo\Tables\D_ITEM_EXTERNAL_CONDITION.sql" />
    <Build Include="dbo\Tables\D_ITEM_EXPORT_SYS.sql" />
    <Build Include="dbo\Tables\D_ITEM_CONDITION.sql" />
    <Build Include="dbo\Tables\D_ITEM_CATEGORY.sql" />
    <Build Include="dbo\Tables\D_INVENTORY_CAPABILITY_SORT_TYPE.sql" />
    <Build Include="dbo\Tables\D_INVENTORY_CAPABILITY_FORMATTER.sql" />
    <Build Include="dbo\Tables\D_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Tables\D_INVENTORY_AUDIT_STATUS.sql" />
    <Build Include="dbo\Tables\D_ELEMENT_CATEGORY.sql" />
    <Build Include="dbo\Tables\D_ELEMENT.sql" />
    <Build Include="dbo\Tables\D_EBAY_CATEGORY_MAPPING.sql" />
    <Build Include="dbo\Tables\D_EBAY_CATEGORY_HIERARCHY_TEMP.sql" />
    <Build Include="dbo\Tables\D_EBAY_CATEGORY_HIERARCHY.sql" />
    <Build Include="dbo\Tables\D_DOCUMENT_SET_TYPE.sql" />
    <Build Include="dbo\Tables\D_CERTIFICATE_TYPE.sql" />
    <Build Include="dbo\Tables\D_CATEGORY_HIERARCHY.sql" />
    <Build Include="dbo\Tables\D_ACCOUNT_TYPE.sql" />
    <Build Include="dbo\Tables\D_ACCOUNT_TRANSACTION_TYPE.sql" />
    <Build Include="dbo\Tables\C_VARS.sql" />
    <Build Include="dbo\Tables\C_USER_SETTING_TYPE.sql" />
    <Build Include="dbo\Tables\C_TRUCK_TYPE.sql" />
    <Build Include="dbo\Tables\C_TRUCK_BODY_TYPE.sql" />
    <Build Include="dbo\Tables\C_TAG_TYPE.sql" />
    <Build Include="dbo\Tables\C_SOURCE_SYS.sql" />
    <Build Include="dbo\Tables\C_SALES_ORDER_INVOICE_STATUS.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_WORKFLOW_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_WORK_PERMISSION.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_SHIPPING_METHOD.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_PRIORITY.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_PRICE_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_PICKUP_DURATION.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ORDER_STATUS.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ORDER_INBOUND_BILLING_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ITEM_SERVICE_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ITEM_MASTER_TYPE.sql" />
    <Build Include="dbo\Tables\C_PRINTER_JOB_TYPE.sql" />
    <Build Include="dbo\Tables\C_PHYSICAL_PARAMETER_TYPE.sql" />
    <Build Include="dbo\Tables\C_PERMISSION_ENTITY_TYPE.sql" />
    <Build Include="dbo\Tables\C_PERMISSION_CATEGORY_ACTION.sql" />
    <Build Include="dbo\Tables\C_NOTIFICATION_TYPE_ENTITY.sql" />
    <Build Include="dbo\Tables\C_NOTIFICATION_TYPE.sql" />
    <Build Include="dbo\Tables\C_NOTIFICATION_GROUP_TYPE.sql" />
    <Build Include="dbo\Tables\C_NOTIFICATION_ENTITY.sql" />
    <Build Include="dbo\Tables\C_MAXLIST_READ.sql" />
    <Build Include="dbo\Tables\C_LOGGED_ENTITY_TYPE.sql" />
    <Build Include="dbo\Tables\C_LOCATION_TYPE.sql" />
    <Build Include="dbo\Tables\C_LOCATION_SEPARATOR.sql" />
    <Build Include="dbo\Tables\C_LOCATION_ALLOWED_ITEM.sql" />
    <Build Include="dbo\Tables\C_ITEM_TYPE.sql" />
    <Build Include="dbo\Tables\C_ITEM_JOB.sql" />
    <Build Include="dbo\Tables\C_ITEM_CATEGORY.sql" />
    <Build Include="dbo\Tables\C_INVOICE_PAYMENT_TYPE.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_CAPABILITY_SIZE.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Tables\C_FREIGHT_PLAN_STATUS.sql" />
    <Build Include="dbo\Tables\C_EQUIPMENT_LIST_CATEGORY.sql" />
    <Build Include="dbo\Tables\C_EMAIL_TEMPLATE_TYPE.sql" />
    <Build Include="dbo\Tables\C_ELEMENT_CATEGORY_TYPE.sql" />
    <Build Include="dbo\Tables\C_DB_CONSTANTS.sql" />
    <Build Include="dbo\Tables\C_CUSTOMER_TRANSACTION_TERM_TYPE.sql" />
    <Build Include="dbo\Tables\C_CUSTOMER_TRANSACTION_TERM.sql" />
    <Build Include="dbo\Tables\C_CUSTOMER_ADDRESS_TYPE.sql" />
    <Build Include="dbo\Tables\C_CONTRACT_STATUS.sql" />
    <Build Include="dbo\Tables\C_CONTRACT_BILLING_FREQUENCY.sql" />
    <Build Include="dbo\Tables\C_CONTAINER_SIZE.sql" />
    <Build Include="dbo\Tables\C_COMMUNICATION_ENTITY_TYPE.sql" />
    <Build Include="dbo\Tables\C_CATEGORY_HEADER.sql" />
    <Build Include="dbo\Functions\UrlEncode.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_TAX.sql" />
    <Build Include="dbo\Functions\fn_float_CALCULATE_SALES_ORDER_SUBTOTAL.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_QUANTITY_ORDERED.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_QUANTITY.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_QTY_AVAILABLE.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_GROSS_PROFIT.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_SALES_ORDER_COST.sql" />
    <Build Include="dbo\Functions\sp_CALCULATE_ACCOUNT_TREE_CODE.sql" />
    <Build Include="dbo\Functions\fn_str_TO_BASE64.sql" />
    <Build Include="dbo\Functions\fn_str_GET_USER_AUTO_NAME.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_INVOICE_STATUS.sql" />
    <Build Include="dbo\Functions\fn_str_GET_NOTIFICATION_ENTITY_LIST.sql" />
    <Build Include="dbo\Functions\fn_str_GET_NOTIFICATION_EMAIL_LIST.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CUSTOMER_ADDRESS_LOCATION_AUTO_NAME.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CONTRACT_ALIAS.sql" />
    <Build Include="dbo\Functions\fn_str_FROM_BASE64.sql" />
    <Build Include="dbo\Functions\fn_str_ENUMERATE_ITEMS_INVENTORY_LOCATIONS.sql" />
    <Build Include="dbo\Functions\fn_str_DATE_TIME_TO_STRING.sql" />
    <Build Include="dbo\Functions\fn_str_D_INVENTORY_CAPABILITY_TYPE_VALUE.sql" />
    <Build Include="dbo\Functions\fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_VALUE.sql" />
    <Build Include="dbo\Functions\fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU_CAPABILITY_TYPE_ID.sql" />
    <Build Include="dbo\Functions\fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU.sql" />
    <Build Include="dbo\Functions\fn_str_D_EBAY_CATEGORY_HIERARCHY_SET.sql" />
    <Build Include="dbo\Functions\fn_str_D_CATEGORY_HIERARCHY_SET.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NUMBER_INBOUND_QUOTE_STARTING_NUMBER.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NUMBER_INBOUND_QUOTE_PREFIX.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NUMBER_INBOUND_ORDER_STARTING_NUMBER.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NUMBER_INBOUND_ORDER_PREFIX.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NAME_TAG.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NAME_SALES_ORDER.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NAME_OUTBOUND_ORDER.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NAME_CONTRACT.sql" />
    <Build Include="dbo\Functions\fn_SpaceCharacters.sql" />
    <Build Include="dbo\Functions\fn_nvarchar_GET_DB_CONSTANT_VALUE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_SALES_ORDER_AMOUNT_DUE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_SUBTOTAL.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_OUTBOUND_SUBTOTAL.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_OUTBOUND_ITEMS_SUBTOTAL.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_ITEMS_SERVICES_SUBTOTAL.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_INBOUND_ITEMS_TOTAL_PRICE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ITEM_PRICE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_INITIAL_AMOUNT.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_AMOUNT_OWED.sql" />
    <Build Include="dbo\Functions\fn_money_GET_CUSTOMER_BALANCE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_ACCOUNT_BALANCE_TOTAL.sql" />
    <Build Include="dbo\Functions\fn_int_GET_SKU_QTY_ON_HAND.sql" />
    <Build Include="dbo\Functions\fn_int_GET_DB_VERSION.sql" />
    <Build Include="dbo\Functions\fn_float_GET_TAX_RATE.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_NOTIFICATION_ALLOWED.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_CUSTOMERA_ADDRESS_EQUAL_TO.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_ABLE_TO_UPDATE_AUTO_NUMBERING.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_USER_ADMIN_ID.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_MAIN_CUSTOMER_ID.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_AUTO_NUMBER_VALUE.sql" />
    <Build Include="dbo\Functions\fn_bigint_F_ITEM_ID.sql" />
    <Build Include="dbo\Functions\fn_bigint_D_INVENTORY_CAPABILITY_TYPE_SKU_CAPABILITY_TYPE_ID.sql" />
    <Build Include="dbo\Functions\dbo.fn_float_GET_RECYCLING_ORDER_WEIGHT_TARE.sql" />
    <Build Include="dbo\Functions\dbo.fn_float_GET_RECYCLING_ORDER_WEIGHT_RECEIVED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\CreateView.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_ITEM_COMBO_HEADER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_ITEM_MASTER_ADD_ON.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_ITEM_MASTER_BILL_OF_MATERIAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_ITEM_MASTER_SUBSTITUTE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_LOCATION_PLACES_RANGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_NOTIFICATION_GROUP_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_NOTIFICATION_TO_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_RMA_PACKAGE_TRACKING_EVENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_RMA_RETURN_REASON.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_SHIPPING_PACKAGE_TRACKING_EVENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ADD_TAXES_TO_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_ALLOW_ADD_TAXES_TO_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_BOOLEAN_IS_CUSTOMER_HAS_INVENTORY_ITEMES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_CALC_SHIPPING_PACKAGE_DIMENSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_CALCULATE_PICKLIST_ORDER_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_CANCEL_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_COMPLETE_INVENTORY_AUDIT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_COUNT_INVENTORY_AUDIT_SESSION_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_CUSTOMER_PAYMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_CUSTOMER_PAYMENT_CREDIT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_CUSTOMER_TERM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_EMAIL_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_INVENTORY_RECV.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_INVENTORY_RECV_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_ITEMS_CATEGORY_REFS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_LOCATION_DETAIL_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_NOTIFICATION_GROUP_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_RECYCLING_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_RECYCLING_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_RECYCLING_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_RECYCLING_WORKFLOW_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_RMA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_ROLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_TAX_FROM_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_WAREHOUSE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CATEGORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_COMMUNICATION_MESSAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CONTRACT_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CONTRACTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CUSTOMER_DSV.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_CUSTOMER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_DOCUMENT_SET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_DOCUMENT_SET_ATTACHMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_INBOUND_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ITEM_INVENTORY_ADDONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ITEM_MASTER_ADD_ON.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ITEM_MASTER_BILL_OF_MATERIALS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_ITEM_MASTER_SUBSTITUTES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_NOTIFICATION_SETS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_NOTIFICATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_OUTBOUND_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_AUDIT_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_EQUIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_MASTER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER_ITEM_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER_OUTBOUND.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_SALES_ORDER_OR_QUOTE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_SALES_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_SALES_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_SYSTEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TAG.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TAG_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TAX_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TAXES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TRUCK.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_TRUCK_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_UNMAPPED_LOCATION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_USER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_USER_WORK_LOG_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_F_ITEM_NO_CATEGORY_REF.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_F_ITEM_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_F_ITEM_SUB.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GENERATE_INVENTORY_UID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_AUTO_NUMBERING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_BOM_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_BY_LEVEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_BY_PARENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_BY_TEXT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_BY_LEVEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_BY_LEVEL_SPECIFIC.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_BY_PARENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_BY_PARENT_SPECIFIC.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_MAPPED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_SEARCH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_EBAY_SEARCH_SPECIFIC.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORIES_SEARCH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY_HIERARCHY_FLAT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY_IMAGE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CATEGORY_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_COMMUNICATION_MESSAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_COMMUNICATION_MESSAGES_CHANGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_COMPANY_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CONTRACT_COMMODITIES_DEFAULT_PRICE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CONTRACT_COMMON_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CONTRACT_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CONTRACT_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CONTRACTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESS_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESS_CONTACT_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESS_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESS_PRIMARY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_BALANCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_CONTACT_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_CONTACT_PRIMARY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_CONTACTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DEFAULT_RECYCLING_CONTRACT_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DEFAULT_TRANSACTION_TERM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DEPENDENCES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DSV.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DSV_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_DSV_FILE_PATHES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_PAYMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_POSTAL_ACCOUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_PRIMARY_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_TAX.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_CUSTOMER_TRANSACTION_TERM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_D_ITEM_CONDITIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_DOCUMENT_SET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_DOCUMENT_SETS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EBAY_CATEGORY_HIERARCHY_VERSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EBAY_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EMAIL_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EMAIL_TEMPLATE_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EMAIL_TEMPLATE_BY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_EMAIL_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_GROUP_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ATTRIBUTE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_AUDIT_SESSION_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_AUDITABLE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_AUDITING_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_CAPABILITIES_BY_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_RECEIVE_ADVANCED_SEARCH_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_COMPETITION_INSIGHT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_INVENTORY_INSIGHT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_LISTING_INSIGHT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_LOCATION_BY_UID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_MARKETPLACE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM_SUBSTITUTE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEMS_FOR_PRINTING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_RECV_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_RECV_SERIAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_SEARCH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_STATUSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_ADDONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_BY_SKU.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_IMAGE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_IMAGES_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_INVENTORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_INVENTORY_ADDONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_INVENTORY_ALL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MANUFACTURERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_ADD_ONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_BILL_OF_MATERIAL_AVG_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_BILL_OF_MATERIALS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_DIMENSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_FOR_INVENTORY_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_IMAGES_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_MANUFACTURER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_MANUFACTURERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_SUBSTITUTES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_TRENDS_AVGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_TRENDS_BUYS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_TRENDS_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_TRENDS_SELLS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_SALES_FORECAST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_SKU_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEMS_FOR_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEMS_FOR_RMA_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEMS_TO_ALLOCATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LIST_CERTIFICATE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATION_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATION_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATION_DETAIL_BY_CAPABILITY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATION_SUGGESTED_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_BIN.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_BIN_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_BULK_RACK_SPACE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_BULK_RACK_SPACE_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_FOR_PRINTING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_PALLET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_PALLET_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_SUGGESTED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_SUGGESTED_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOCATIONS_UNMAPPED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_LOG_RECORDS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_MAIN_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_EMAIL_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_GROUP_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SET_CONTRACT_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SET_CUSTOMER_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SET_GROUPS_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SET_ORDER_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SETS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_OUTBOUND_ORDER_DYNAMIC_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_OUTBOUND_ORDER_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_OUTBOUND_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_PAGE_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_PARSED_ITEM_INVENTORY_SKU_ATTRB.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_PICKLIST_ORDERED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_PICKLIST_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_PICKLIST_PARAMETERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECENTLY_USED_CATEGORIES_EBAY_BY_LEVEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECENTLY_USED_CATEGORIES_EBAY_BY_PARENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECENTLY_USED_CATEGORIES_EBAY_SEARCH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_AUDIT_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_BOL_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTAINER_SIZES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTRACT_ITEM_MASTER_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTRACT_ITEM_MASTER_REQUIREMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTRACT_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTRACT_ITEM_SERVICE_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CONTRACT_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CUSTOMER_CERTIFICATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CUSTOMER_MASTER_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_CUSTOMER_REPORTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_DATA_SECURITY_COLLECTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_EQUIPMENT_COLLECTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_EQUIPMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_HARD_DRIVE_SHRED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INBOUND_ORDER_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INBOUND_ORDER_BY_ID_FOR_PDF_SIMPLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INBOUND_QUOTE_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INVENTORY_ITEMS_BY_MASTER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INVENTORY_ORDER_ITEM_MERGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INVENTORY_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INVENTORY_WEIGHT_TOTALS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_INVENTORY_WORKFLOW_TYPE_COUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ITEM_MASTER_REQUIREMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ITEM_MASTERS_FOR_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ITEM_MASTERS_FOR_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_LINKED_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_MASTER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_MASTER_ITEM_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_AUDIT_HEADER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_AUDIT_ITMES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_BY_ID_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_BY_ID_TO_RECIEVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_BY_ID_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_COMMODITIES_DEFAULT_PRICE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_DOCUMENT_STRUCTURE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_DOCUMENT_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_EQUIPMENT_STRUCTURE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_EQUIPMENT_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_IMAGE_DSC_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_INITIAL_AMOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_SERVICES_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_SERVICES_FOR_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_SERVICES_FOR_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEM_SERVICES_SUBTOTAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_FOR_OUTBOUND_SETTLEMENT_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_FOR_RECEIVING_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_FOR_SETTLEMENT_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_FOR_SUMMARY_SETTLEMENT_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_SORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_SORT_SUMMARY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_SUBTOTAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_TO_SORT_CORRECTED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_ITEMS_WEIGHT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_LOT_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_NAME_AND_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_AGGREGATE_ITEMS_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_BY_ID_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_COMMON_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_ITEMS_SUBTOTAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_ITEMS_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_OUTBOUND_SUBTOTAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_SELECTED_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_SUBTOTAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDERS_SETTLEMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDERS_STATUS_COUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OTHER_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_CONTAINER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_ORDER_BOL_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_ORDER_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_ORDER_BY_ID_FOR_PDF_SIMPLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_OUTBOUND_TRUCKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_PACKAGING_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_PICK_UP_DATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_QUOTE_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_QUOTES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_SERVICES_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_SERVICES_FOR_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingSummaryOrderItems.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_SUMMARY_WORKFLOW_TYPE_COUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_TAPE_DRIVE_SHRED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_WORKFLOW_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REMOTE_PRINTER_JOBS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_CATEGORY_SALES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_CUSTOMER_INVENTORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_CUSTOMER_NAME_INVENTORY_SHIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_ID_BY_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_INVENTORY_SHIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_INVENTORY_SOLD.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_TOTALS_INVENTORY_SHIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_CUSTOMER_EMAIL_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_LABEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_PACKAGE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_PACKAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_PACKAGES_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RMA_WORKFLOW_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ROLE_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ROLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ACTIVE_INVOICE_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_DYNAMIC_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_INVOICE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ITEM_SEARCH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ITEMS_ALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ITEMS_FOR_RMA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_ITEMS_PARAMS_FOR_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RECIPIENT_EMAIL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RECYCLING_DISCOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RECYCLING_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RECYCLING_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RMA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_RMAS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_PACKAGE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_TAX.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_UNPAID_INVOICE_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SELF_CUSTOMER_BILL_TO_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_LABEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_LOG.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_PACKAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_PACKAGES_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_RECIPIENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SIGNATURE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU_BUNDLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU_BUNDLING_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU_QTY_ON_HANDS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU_SRC_BUNDLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SMTP_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAG_GROUP_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAX_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAX_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAX_TREE_NODE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAXES_BY_PARENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TAXES_BY_TEXT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TRUCK_TYPE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TRUCK_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_TRUCKS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_UNAUDITED_LOCATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_COMMON_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_CONTACT_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_ID_ROLE_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_ROLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_ROLES_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_WORK_LOG.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_VAR_VALUE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_WAREHOUSE_LAST_PALLET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_WAREHOUSE_OF_LOCATION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ZEBRA_PRINTER_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetReadyForExportItemsForJob.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetReadyForExportItemsForJobAmazon.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetReadyForExportItemsForJobBrokerBin.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetReadyForExportItemsForJobEbay.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetReadyForExportItemsForJobICBin.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetTitleOptions_ByFilter.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_INSERT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_INSERT_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_INSERT_USER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_INVENTORY_RECV_MIGRATE_MODEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_ABLE_TO_DELETE_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_ABLE_TO_DELETE_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_ABLE_TO_DELETE_USER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_AUTO_NUMBERING_PREFIX_BEING_CHANGED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_CATEGORY_CONTAINING_CHILD_WITH_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_CISTOMER_CODE_DUPLICATED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_CONTRACT_DRAFT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_CONTRACT_SET_EDITABLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_CUSTOMER_LOCKED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GET_CUSTOMERS_BY_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_LOCATION_FIT_IN_SPACE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_LOCATION_MODELS_FIT_IN_SPACE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_ORDER_AUDIT_ITEM_SERIAL_NUMBER_VALID.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_ORDER_INBOUND_LOCATION_DETAILS_UPDATE_NEEDED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_ORDER_ITEM_MASTERS_IN_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_ORDER_ITEMS_ALLOW_TO_SORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_ORDER_SERVICES_IN_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_OUTBOUND_ORDER_CONTAINER_INFO_FILLED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_RECYCLING_PURCHASE_SALES_GENERATED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_TAX_GROUP_USED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_USER_AUTHORIZED_FOR_ACTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_IS_USER_PASSWORD_SENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CONTAINER_SIZES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CONTRACT_FILES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CONTRACT_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CONTRACTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CONTRACTS_ALL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_ADDRESSES_BY_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_ADDRESSES_FOR_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_CONTACTS_BY_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_CONTACTS_FOR_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMER_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_DOCUMENT_ATTACHMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_DOCUMENT_SET_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_E_BAY_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_EMAIL_TEMPLATE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_EMAIL_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INBOUND_ORDER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_ATTRIBUTE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_CAPABILITIES_OF_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_CAPABILITY_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_CONDITIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_IDS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_MASTER_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_MASTER_TITLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOCATION_DETAIL_PREFIX.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOCATION_SEPARATORS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOCATIONS_OF_WAREHOUSE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOCATIONS_SUGGESTED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOGGED_AUTO_NAMES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_LOGGED_ENTITY_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_MANUFACTURERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_MASTER_ITEM_ADD_ONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_MASTER_ITEM_BILL_OF_MATERIALS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_NOTIFICATION_ENTITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_NOTIFICATION_SET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_NOTIFICATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ORDER_ITEM_MASTER_MNFR.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_ORDER_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_OUTBOUND_ORDER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_PERMISSION_CATEGORY_ACTIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_PERMISSION_CATEGORY_ENTITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_AUDIT_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_EQUIPMENT_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ITEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ITEM_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_LOTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_LOTS_FOR_MOBILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_LOTS_OUTBOUNDABLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_MASTER_ITEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDER_INBOUND_BILLING_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDERS_FOR_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_ORDERS_FOR_NOTIFICATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_PACKAGING_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_PICKUP_DURATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_PO_NUMBERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_PRICE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_WORK_PERMISSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RECYCLING_WORKFLOW_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RMA_RETURN_REASONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_RMAS_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SALES_ATTACHMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SALES_ORDER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SALES_ORDERS_WITH_DUPLICATE_CUSTOMER_PO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SERIAL_NUMBERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SETTLEMENT_ITEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SHIPPING_FOR_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SHIPPING_PROVIDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SIGNATURE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TAG_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TAGS_AVAILABLE_FOR_RECYCLING_MASTER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TAXES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TRUCK_BODY_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_TRUCK_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_UNMAPPED_LOCATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_USER_AND_CONTACTS_EMAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_USER_ROLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_USER_TRACKS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_USER_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_USERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_BINS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_LEVELS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_PALLET_BULK_RACK_SPACES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_PALLETS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_RACKS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_SECTIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSE_SPACES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LOG_RECYCLING_ITEM_MASTER_DELETE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LOG_RECYCLING_ORDER_INBOUND_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LOG_RECYCLING_ORDER_ITEMS_DELETE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LOG_RECYCLING_ORDER_OUTBOUND_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MANAGE_CONTRACT_EXPIRE_DATE_NOTIFICATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MANAGE_NOTIFICATION_ENTITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MIGRATION_APPLY_SCRIPT_TO_RZR_DATABASES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MIGRATION_INSERT_NEW_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MIGRATION_INSERT_NEW_PAGE_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MIGRATION_INSERT_NEW_PAGE_OR_ENTITY_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_REBUILD_EBAY_CATEGORY_HIERARCHY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_REBUILD_F_ITEM_EXPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_REBUILD_F_ITEM_IMAGE_EXPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_RECALC_SHIPPING_PACKAGE_DIMENSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ADVANCED_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ALLOCATE_INVENTORY_AUTO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ATTRIBUTE_TYPE_DESCRIPTION_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_AUTO_NUMBERING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CATEGORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CATEGORY_IMAGES_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CATEGORY_INVENTORY_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CATEGORY_TO_EBAY_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_COMMUNICATION_MESSAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_COMPANY_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CONTRACT_COMPLETED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CONTRACT_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_ADDRESS_CONTACTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_ADDRESS_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_CODE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_CONTACT_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_DEFAULT_PRICING_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_MASTER_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_TAX.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_CUSTOMER_TERM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_DB_VERSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_DEFAULT_TERM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_DOCUMENT_SET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_DOCUMENT_SET_ATTACHMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_E_BAY_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EBAY_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EBAY_CATEGORIES_MAPPING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EBAY_CATEGORY_HIERARCHY_VERSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EBAY_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EMAIL_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_EXPORT_STATUS_FOR_QUALIFIED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INBOUND_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_AUDIT_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_ITEM_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV_ACTIVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV_BULK.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV_CONDITIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV_DEFECTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INVENTORY_RECV_MODEL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_DIMENSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IMAGE_DESCRIPTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IMAGE_SELECTED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IMAGES_SQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_INVENTORY_ADDONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IS_DEAL_10_PERC_OFF_EBAY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_IS_FEATURED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_MASTER_BILL_OF_MATERIALS_QTY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_MASTER_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_MASTER_DIMENSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_MASTER_INVENTORY_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEM_SALES_FORECAST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ITEMS_NEEDING_EXPORT_IF_QUALIFIED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_LOCATION_CATEGORIES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_LOCATION_RANGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_LOCATION_LIST_BULK.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_LOCATION_PALLET_LIST.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_ACTIVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_METHOD.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_QUEUE_ITEM_INACTIVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_SET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_SET_ACTIVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_SET_GROUP_ACTIVE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_OUBOUND_ORDER_FILES_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_OUTBOUND_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_OUTBOUND_ORDER_HAS_LOT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_AUDIT_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_CONTRACT_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_CUSTOMER_CERTIFICATES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_CUSTOMER_REPORTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_DATA_SECURITY_COLLECTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_EQUIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_EQUIPMENT_COLLECTION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_HARD_DRIVE_SHRED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ITEM_MASTER_DISABLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_MASTER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_MASTER_ITEM_IMAGE_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_DOCUMENT_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_EQUIPMENT_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_INBOUND_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_COMMENT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_MASTER_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_EXCLUDED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_LOADED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_UNLOADED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_PRESET_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_SORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEM_SORT_FROM_CONSUMED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_ITEMS_SORTED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_LOCATION_DETAILS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_OUTBOUND.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_OUTBOUND_CONTAINER_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_OUTBOUND_LOADED_DT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDERS_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_OTHER_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_OUTBOUND_CONTAINER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_OUTBOUND_TRUCKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_TAPE_DRIVE_SHRED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_WORKFLOW_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_WORKFLOW_TYPE_POSITION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_WORKFLOW_TYPE_VISIBILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_REMOTE_PRINTER_JOB.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_REMOTE_PRINTER_JOB_COMPLETED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_COMMENTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_ITEMS_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_PACKAGE_TRACKED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RMA_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ROLE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ROLE_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_FILES_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_INVOICE_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_INVOICE_UNPAID_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_ITEM_TAX_ENABLED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_ITEM_UNALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_RMA.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_RMA_PACKAGING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_SHIPPING_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_TAX.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_ADDR_IS_RESIDENTIAL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_PACKAGE_TRACKED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_PAYOR.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SHIPPING_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SIGNATURE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SKU_BUNDLES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SMTP_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_TAG.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_TAG_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_TRUCK.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_TRUCK_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_USER_COMMON_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_USER_CONTACT_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_USER_WORK_LOG_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_VAR_VALUE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_WAREHOUSE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_ZEBRA_PRINTER_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_U_PAGEVIEW_FILTER_ADD.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_U_PAGEVIEW_FILTER_DELETE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_U_PAGEVIEW_FILTER_EDIT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_U_PAGEVIEW_FILTER_GETDEFAULT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_U_PAGEVIEW_FILTER_SELECT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UNALLOCATE_SALES_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_REBUILD_CATEGORY_PATH.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_CUSTOMER_DSV.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_FINANCE_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_FINANCE_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_LOCATION_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_LOCATION_PALLET_BULK_RACK_SPACE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_QUALIFIED_ITEM_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_RECYCLING_ORDER_ITEM_NOTES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_RECYCLING_ORDER_ITEM_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_RECYCLING_ORDER_ITEM_PRICE_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_RECYCLING_ORDER_OUTBOUND_ITEM_PRICE_DATA.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_SALES_ORDER_INVOICE_AMOUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_SALES_ORDER_INVOICE_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_SALES_ORDER_SHIPPING_REFERENCE_NO.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_SALES_TAX_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_UPD_TAX.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_WMI_GET_UNIQUE_ITEM_NUMBER_CONDITION.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\dbo\User Defined Types\dbo.AutoPriceRule.sql" />
    <Build Include="dbo\User Defined Types\SKU_BUNDLE.sql" />
    <Build Include="dbo\User Defined Types\RECYCLING_CONTRACT_ITEM_MASTER_REQUIREMENTS.sql" />
    <Build Include="dbo\User Defined Types\bigint_PARE_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\bigint_NVARCHAR_PAIRS_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\bigint_ID_ARRAY.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_SALES_ORDER_ITEM_TITLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_TAG_TYPES_INTERACTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TAG_TYPES_INTERACTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_INBOUND_ORDER_BOL_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_INBOUND_BOL_ITEMS.sql" />
    <Build Include="dbo\Tables\F_UNLOADING_LOCATION.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_IMAGE.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_AUDIT_SERIAL.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_AUDIT_ATTRIBUTE.sql" />
    <Build Include="dbo\Tables\F_FREIGHT_CARRIER.sql" />
    <Build Include="dbo\Tables\D_RECYCLING_PACKAGING_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_LOGISTIC_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_FREIGHT_CLASS.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_CONDITION.sql" />
    <Build Include="dbo\Tables\C_LOCATION_LOADING_DOCK_EQUIPMENT_TYPE.sql" />
    <Build Include="dbo\Tables\C_ITEM_ATTRIBUTE_PAGE.sql" />
    <Build Include="dbo\Tables\C_ITEM_ATTRIBUTE_GROUP.sql" />
    <Build Include="dbo\Tables\C_ITEM_ATTRIBUTE_FIELD.sql" />
    <Build Include="dbo\Tables\C_ITEM_ATTRIBUTE_DEVICE.sql" />
    <Build Include="dbo\Tables\C_ITEM_ATTRIBUTE.sql" />
    <Build Include="dbo\Tables\C_INVOICE_TYPE.sql" />
    <Build Include="dbo\Tables\C_COUNTRY.sql" />
    <Build Include="dbo\Functions\fn_str_GET_FULL_COUNTRY_CODE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CONTRACT_WORK_PHONE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CONTRACT_MOBILE_PHONE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CONTRACT_MAIN_PHONE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CONTRACT_HOME_PHONE.sql" />
    <Build Include="dbo\Functions\fn_str_COUNTRY_CODE_FULL.sql" />
    <Build Include="dbo\Functions\fn_str_AUTO_NAME_SALES_QUOTE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_RECYCLING_ORDER_ITEMS_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_FREIGHT_SERVICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_WAREHOUSE_UNLOADING_LOCATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_VALIDATION_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_QUOTE_VOIDED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_QUOTE_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_INVENTORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_ATTRIBUTE_DESCRIPTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_ATTRIBUTE_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_UNLOADING_LOCATIONS_OF_WAREHOUSE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_PACKAGING_TYPE_EXT.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_FREIGTH_CLASS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_CONDITION.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_MASTER_ITEM_TITLES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_LOCATION_LOADING_DOCK_EQUIPMENT_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_INVENTORY_FOR_MOBILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_UNLOADING_LOCATIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_VALIDATION_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TEMPLATE_DATA_FOR_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TEMPLATE_DATA_FOR_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TAGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_QUOTES_COUNTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_QUOTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_QUOTE_PDF_DATA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_PACKAGING_TYPE_DIMENSIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_LOT_FOR_MOBILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_FOR_MOBILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ITEMS_IN_LOCATION_FOR_SALE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ITEM_IMAGES_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_AUDIT_MAPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_CONTACTS_EMAILS_BY_TAG_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_WAREHOUSE_UNLOADING_LOCATION.sql" />
    <Build Include="dbo\User Defined Types\TAG_TYPE.sql" />
    <Build Include="dbo\User Defined Types\bigint_money_ARRAY.sql" />
    <Build Include="dbo\Tables\F_PRODUCT_MASTER.sql" />
    <Build Include="dbo\Tables\C_SALES_ORDER_STATUS.sql" />
    <Build Include="dbo\Tables\C_PRODUCT_MASTER_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TRUCKS_FOR_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LOCATIONS_FOR_ORDERS.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_CREDIT.sql" />
    <Build Include="dbo\User Defined Types\SCHEDULE_LOCATION_ITEMS.sql" />
    <Build Include="dbo\Tables\F_TEMPLATE_COMMODITY_GROUP.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_CONTRACT.sql" />
    <Build Include="dbo\Tables\F_COMMODITY_GROUP.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ORDER_SERVICE_TYPE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ORDER_FREIGHT_STATUS_TYPE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_TIME_STRING.sql" />
    <Build Include="dbo\Functions\fn_str_CONCATENATE_RECYCLING_ORDER_ITEM_IDS.sql" />
    <Build Include="dbo\Functions\fn_str_CONCATENATE_ITEM_MASTER_TAGS.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ITEM_OUTBOUND_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_MARKET_COMMODITIES_PRICES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_TEMPLATE_SERVICES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_TEMPLATE_ITEM_MASTER_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDER_CONTRACT_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDER_CONTRACT_AS_IS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_TO_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_CLONE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_ARCHIVED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_COMMODITY_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG_ITEM_MASTER_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG_INVENTORY_RECV_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_SPARE_RECYCLING_ORDER_CONTRACTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_ORDER_SERVICE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_INVOICES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_COMMODITY_GROUPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_CONTRACT_INTERSECTING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_SALES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_PICKUP_LOG.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_SERVICES_FOR_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_PRICING_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_PICTURE_BY_ID_FOR_PDF_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEMS_QUANTITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_CONTRACTS_NAMES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_CONTRACTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_CONTRACT_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_CONTRACT_CONFLICTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ITEM_MASTERS_FOR_TEMPLATES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_CONTRACTS_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_COMMODITY_GROUPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_COMMODITIES_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_RECYCLING_ORDER_AUDIT_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_QUALIFIED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_CONTRACT_FROM_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_COMMODITY_GROUP.sql" />
    <Build Include="dbo\Stored Procedures\sp_CHECK_CONTRACT_COMMODITY_UNIQUE.sql" />
    <Build Include="dbo\User Defined Types\bigint_3_ID_ARRAY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_FINANCE_ACTIVITY.sql" />
    <Build Include="dbo\User Defined Types\RECYCLING_CONTRACT_ITEM_MASTER_RESALE.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_SETTLEMENT_STATE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_RECYCLING_ORDER_STATE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_SETTLEMENT_STATES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEMS_FOR_AUDIT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_AUDIT_ITEMS_BY_ORDER_ID.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobContact.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetInboundOrderTerms.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetInboundOrderTerms.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_WORKFLOW_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDER_ITEM_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_INVENTORY_STATUS_AFTER_RECYCLING.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_RECYCLING_ORDER_UNCOMPLETE_SORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_ORDER_READY_TO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_AUDIT_LOTS_ITMES.sql" />
    <Build Include="dbo\Functions\fn_float_GET_MASTER_PRODUCT_PRICE_AVG.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetRecyclingServicesCategories.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetRecyclingServicesLabels.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_RecalculateInboundOrderServicePricing.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_DeleteRecyclingServices.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderServiceInfo.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetInboundOrderServiceCategory.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetInboundOrderServiceLabel.sql" />
    <Build Include="Security\NT AUTHORITY_NETWORK SERVICE.sql" />
    <Build Include="Security\NT AUTHORITY_IUSR.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_TEST_COMPLETE.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_RECYCLING_ORDER_ITEM_HAS_AUDIT.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_RECYCLING_ORDER_HAS_AUDIT.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_RECYCLING_ORDER_AUDIT_ITEM_UID_VALID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_AUDIT_LOTS_ITEM_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_AUDIT_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ATTRIBUTE_BY_ITEM_MASTER_ID.sql" />
    <Build Include="dbo\Tables\F_PRODUCT_MASTER_ACCOUNT.sql" />
    <Build Include="dbo\Functions\fn_str_CONCATENATE_RECYCLING_ORDER_ITEM_TAGS.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_ITEMS_TOTAL_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ITEM_TO_CONSUMED_FROM_INVENTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_GROSS.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_RECYCLING_ITEM_MASTER_IN_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_EXIST_ITEMS_TO_SCRAP.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_SHIP_TO_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_RECV_TO_SEND.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_ADDRESS_BY_WAREHOUSE_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_STATUS.sql" />
    <Build Include="Security\cp.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_ADDRESS_WAREHOUSE.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_CONSIGNMENT.sql" />
    <Build Include="dbo\Tables\F_CONSIGNMENT_SPLIT_TIER.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_PRICE_ALIAS.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_RMA_INVOICE_DUE_STATUS_ID_BY_ID.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_RMA_INVOICE_DUE_STATUS_ID.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_INVOICE_DUE_STATUS_ID_BY_ID.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_INVOICE_DUE_STATUS_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_RECYCLING_ORDER_ITEM_WORKFLOW_AND_COMMODITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_LOCATION_REMOVE_PALLET_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_ATTRIBUTE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CUSTOMER_ADDRESS_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_CONSIGNMENTS_CLONE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONDITION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SAVE_INVENTORY_BREAKDOWN_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ZIP_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_STATES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_PRICE_ALIASES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_ORDER_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_PAGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_GROUPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_FIELDS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_DEVICES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CUSTOMER_WAREHOUSES_BY_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\sp_List_Contract_Commodities.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ALL_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_LOCATION_REMOVE_PALLET_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_INVOICE_DUE_STATUS_ID_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_OUTBOUND_NOTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEMS_FOR_CERTIFICATE_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEM_AUDIT_SUMMARY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_CONSIGNMENT_SPLIT_TIERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTACTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONDITIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_ATTRIBUTE.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_CONDITION.sql" />
    <Build Include="dbo\User Defined Types\CONTRACT_CONSIGNMENT.sql" />
    <Build Include="dbo\User Defined Types\nvarchar_LABEL_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\BreakdownItem.sql" />
    <Build Include="dbo\User Defined Types\IpRange.sql" />
    <Build Include="dbo\Tables\U_PRINTER.sql" />
    <Build Include="dbo\Tables\F_PRINTER_RESOLUTION.sql" />
    <Build Include="dbo\Tables\F_PRINTER_PAPER_SIZE.sql" />
    <Build Include="dbo\Functions\fn_nvarchar_GET_CHANNEL_CONDITION_CD.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_RECYCLING_ORDER_ITEM_ID_IN_CONSIGMENT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ITEMS_FOR_PRICE_CALCULATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRINTERS_RESOLUTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRINTERS_NEED_REFRESH.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRINTERS_ENABLED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRINTERS_AVAILABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRINTER_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEMS_FOR_PRICE_CALCULATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INBOUND_ORDER_FILES_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CHANNEL_CONFIG_ACTIVITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG_PROCESS_POST.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_PAGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_SORT_STARTING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_TIME_ZONE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_PAGE_PERMISSIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE_INITIAL_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PRINTERS_NEED_REFRESH.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PRINTER_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PRINTER_RESOLUTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PRINTER_PAPER_SIZES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_OUTBOUND_ORDER_CREATION_ADDRESS_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_AVAILABLE_PRINTERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ALL_PRINTERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ALL_CONSIGNMENT_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_PRINTERS.sql" />
    <Build Include="dbo\User Defined Types\nvarchar_money_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\NAMED_POINT.sql" />
    <Build Include="dbo\User Defined Types\bigint_Bit_Array.sql" />
    <Build Include="dbo\Tables\F_WAREHOUSE_STRUCTURE.sql" />
    <Build Include="dbo\Tables\F_WAREHOUSE_STORAGE_UNIT.sql" />
    <Build Include="dbo\Tables\F_USER_RESALE_SETTING.sql" />
    <Build Include="dbo\Tables\F_PRODUCT_CODE_HECI_TO_ECI.sql" />
    <Build Include="dbo\Tables\F_PRODUCT_CODE.sql" />
    <Build Include="dbo\Tables\F_PickListItem.sql" />
    <Build Include="dbo\Tables\F_PickListAllocatedItem.sql" />
    <Build Include="dbo\Tables\F_PickList.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_PRODUCT_CODE.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_PRODUCT_CODE.sql" />
    <Build Include="dbo\Tables\D_WAREHOUSE_STORAGE_UNIT.sql" />
    <Build Include="dbo\Tables\D_PRODUCT_CODE_TYPE.sql" />
    <Build Include="dbo\Tables\C_PickListStatus.sql" />
    <Build Include="dbo\Functions\fn_str_Rtrim.sql" />
    <Build Include="dbo\Functions\fn_str_GET_WAREHOUSE_LOCATION_CODE_WEIGHT.sql" />
    <Build Include="dbo\Functions\fn_str_GENERATE_WAREHOUSE_LOCATION_AUTO_NAME.sql" />
    <Build Include="dbo\Functions\fn_int_GET_LOCATION_STORAGE_UNIT_TYPE_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_SALES_ORDER_ITEM_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_RECYCLING_ORDER_ITEM_PRICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_WAREHOUSE_STORAGE_STRUCTURE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_USER_RESALE_SETTING_INVENTORY_RECEIVE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PRODUCT_CODE_TO_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ALLOCATED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ALLOCATED_ITEM_IS_VERIFIED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_PRODUCT_CODE_HECI.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_PRODUCT_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SAVE_OR_UPDATE_PICK_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_SALES_ORDERS_FOR_PICKLIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_MARK_SALES_ORDER_SHIPPING_PACKAGE_SCANED.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_WAREHOUSE_STORAGE_UNITS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_WAREHOUSE_STORAGE_UNIT_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_MASTER_PRODUCT_CODE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_SERIAL_IN_HISTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_ORDER_INVOICE_PAID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_STORAGE_UNITS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_STORAGE_STRUCTURE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_RESALE_SETTING_INVENTORY_RECEIVE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDERS_SHIPPINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLISTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_PARAMETERS_BY_PICKLIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_ORDERED_ITEMS_BY_PICKLIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_NOTIFICATION_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_ALLOCATED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICK_LIST_BY_ORDER_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICK_LIST_ALLOCATED_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICK_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ORDER_ITEMS_FOR_PICKLIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ORDER_ALLOCATED_ITEMS_FOR_PICKLIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LOGS_ACTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTERS_HAVING_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_PRODUCT_CODES_HECI.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_PRODUCT_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_FIRST_PRODUCT_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ITEMS_FOR_PICKLISTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_AUDIT_HISTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CODE_MASTER_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_PICKLIST_ALLOCATED_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_MASTER_PRODUCT_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_ADD_WAREHOUSE_LOCATION_PALLETS.sql" />
    <Build Include="dbo\Stored Procedures\IS_LOTS_INCLUDE_IN_OUTBOUND_ORDER.sql" />
    <Build Include="dbo\User Defined Types\WAREHOUSE_STORAGE_UNIT.sql" />
    <Build Include="dbo\User Defined Types\PickListItem.sql" />
    <Build Include="dbo\Tables\C_WARRANTY.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_PRODUCT_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CUSTOMER_CONTACTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_PAYMENT_INVOICE_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_FROM_CONSUMABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_USER.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE_ONSITE_CONTACT.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_ORDER_INBOUND.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_CUSTOMER.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_CONTRACT.sql" />
    <Build Include="dbo\Functions\tvf_CALCULATE_LOCATION_RANK.sql" />
    <Build Include="FullTextIndexes.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_TRACKING_NUMBERS.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER_ADDRESS.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_ITEM_TRANSFER.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_ASSET_STATE.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET_PROCESSING.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET_CHARGE_TYPE.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET.sql" />
    <Build Include="dbo\Tables\F_ACCOUNT_bak.sql" />
    <Build Include="dbo\Tables\D_LOCATION_PROCESSING_LINE_NO.sql" />
    <Build Include="dbo\Tables\C_SLA_TIME.sql" />
    <Build Include="dbo\Tables\C_RECYCLING_ORDER_ITEM_PROCESSING_STATE.sql" />
    <Build Include="dbo\Tables\C_PROCESSING_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_GRADE_POSITION.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_GRADE_LEVEL.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_GRADE_DEVALUATION.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_GRADE_CATEGORY.sql" />
    <Build Include="dbo\Tables\C_CLIENT_PORTAL_ORDER_TYPE.sql" />
    <Build Include="dbo\Tables\C_CLIENT_PORTAL_ORDER_REPORT_TYPE.sql" />
    <Build Include="dbo\Tables\C_CHARGE_TYPE.sql" />
    <Build Include="dbo\Tables\C_ASSET_STATE.sql" />
    <Build Include="Storage\F_PRODUCT_CODE_VALUE.sql" />
    <Build Include="Storage\F_ITEM_MASTER.sql" />
    <Build Include="dbo\Functions\fn_int_GET_RECYCLING_ORDER_SETTLEMENT_STATE.sql" />
    <Build Include="dbo\Functions\fn_float_GET_SEGREGATION_PALLET_WEIGHT.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_PROCESSING_REQUIREMENT_FOR_CONTRACT.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_RESYCLING_ORDER_STATUS_FOR_UNDO_CANCEL.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_RECYCLING_ORDER_AUDIT_NOTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_INVENTORY_ITEMS_FROM_XML.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDERS_UNDO_SETTLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_LOCATION_PROCESSING_LINE_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEMS_FOR_PRICE_CALCULATION_DEFAULT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_PRIMARY_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_INVENTORY_RECYCLING_ITEM_HISTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ATTRIBUTE_VALUE_MERGED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ATTRIBUTE_VALUE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ATTRIBUTE_RUNTIME_EDITABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CUSTOMER_CONTACT_CLIENT_PORTAL_CREDENTIAL.sql" />
    <Build Include="dbo\Stored Procedures\sp_SetCustomerContactOptions.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CUSTOMER_CLIENT_PORTAL_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_COMMODITY_AND_SERVICE_TO_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CLIENT_PORTAL_REPORTS_FOR_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_WAREHOUSE_PROCESSING_LINES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_MASTERS_WITH_HECI.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_HECI_WITH_ITEM_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CATEGORIES_WITH_INVENTORY_ATTRIBUTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_CUSTOMER_CONTACT_LOGIN_AVAILABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_INVENTORY_ATTRIBUTE_TYPE_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_LAST_PROCESSING_LINE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_WAREHOUSE_BY_ADDRESS_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SYSTEM_SETTINGS_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_SHIPPING_TRACKING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REQUEST_ORDER_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_MAPPED_COMPANY_CATEGORY_BY_EBAY_CATEGORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LOCATIONS_PROCESSING_LINE_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LOCATIONS_PROCESS_LINE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_PRIMARY_CATEGORY_FOR_AUTOCOMPLETE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ATTRIBUTE_VALUE_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ATTRIBUTE_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_CONTACTS_FOR_CLIENT_PORTAL.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_CONTACT_CLIENT_PORTAL_CREDENTIALS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_CLIENT_PORTAL_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CLIENT_PORTAL_REPORTS_FOR_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ASSET_SEGREGATION_PALLET_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ADDRESSES_FOR_NEW_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_INVENTORY_ATTRIBUTE_VALUE.sql" />
    <Build Include="dbo\Stored Procedures\sp_CREATE_TRANSFER_INBOUND_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\GET_RECYCLING_ORDER_ITEM_TARE_WEIGHT.sql" />
    <Build Include="dbo\User Defined Types\ut_labeled_uid.sql" />
    <Build Include="dbo\User Defined Types\tb_ID_TYPEID_TXT.sql" />
    <Build Include="dbo\User Defined Types\RECYCLING_ORDER_AUDIT_HEADER.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_UID_IN_HISTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_AUDIT_ATTRIBUTE_SET.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_AUDIT.sql" />
    <Build Include="dbo\Functions\fn_str_GET_FULL_PHONE_NUMBER.sql" />
    <Build Include="dbo\Functions\fn_money_GET_ITEM_PRICE_BY_TYPE_RECYCLING.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_KIT_ELEMENT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_KIT_ELEMENT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_KIT_ELEMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_MASTER_KIT_ELEMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_MASTER_KIT_ELEMENT.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_INVENTORY_GRADE_TEMPLATE.sql" />
    <Build Include="dbo\Views\vw_GET_NEW_ID.sql" />
    <Build Include="dbo\Functions\fn_str_GENERATE_INVENTORY_UID.sql" />
    <Build Include="dbo\Functions\fn_int_GET_FILLED_PICKLIST_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEMS_RAW_ALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEMS_ALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEM_ALLOCATED_BY_UID_OR_SERIAL.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ITEM_QUALIFY_CONTROL_CHANGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ITEMS_FROM_LOCATION_ALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ATTRIBUTE_MERGED.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_PICK_LIST_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICK_LIST_TEMPLATE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_DESC.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_RECV.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_ADDED_PART.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_ADDED_PARTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SCAN_INVENTORY_FOR_ADD_PART.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_FOR_ADD_PART.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ADDED_PARTS.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_MAIN_CUSTOMER_ADDRESS_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_SAVE_ITEM_INVENTORY_REMOVED_PARTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_OUTBOUND_ADDRESSES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_PARENT_PARTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SCAN_ITEM_FOR_KIT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_COMBOS.sql" />
    <Build Include="dbo\Tables\F_SYSTEM_LOG.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_RESALE_PRICING.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_CAPABILITY.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Tables\D_CONTRACT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SYSTEM_SETTINGS_OPTIONAL_SECTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG_SYSTEM_EVENT.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_SCHEDULING_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RESALE_PRICING_TEMPLATES_FOR_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CONTRACT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SYSTEM_SETTINGS_OPTIONAL_SECTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SCHEDULING_ORDERS_DATES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_RESALE_PRICINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CLIENT_PORTAL_INVOICE_REPORT_INFO.sql" />
    <Build Include="dbo\User Defined Types\IMPORT_PO_ITEMS.sql" />
    <Build Include="Storage\Non Alphabetical.sql" />
    <Build Include="dbo\Tables\D_CONTRACT_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_ATTRIBUTE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_ATTRIBUTE_TYPES.sql" />
    <Build Include="dbo\User Defined Types\CONTRACT_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\User Defined Types\CONTRACT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Tables\D_CONTRACT_ATTRIBUTE_CAPABILITY.sql" />
    <Build Include="dbo\Functions\fn_int_GET_INVENTORY_CAPABILITY_TYPE_FOR_CONTRACT_RESALE_PRICING.sql" />
    <Build Include="dbo\Functions\fn_int_GET_INVENTORY_ATTRIBUTE_TYPE_FOR_CONTRACT_RESALE_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_PRICING_ATTRIBUTE_TYPE_SET.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CAPABILITY_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_PRICING_ATTRIBUTE_TYPE_SET_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_PRICING_ATTRIBUTE_TYPE_SET.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_ATTRIBUTE_CAPABILITIES.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_CLIENT_PORTAL_REPORT.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_CHECKING_INVENTORY_TO_FIT_LOCATIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_RESALE_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_AVAILABLE_CLIENT_PORTAL_REPORTS_FOR_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_INVOICE_PAYMENT_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_RESALE_PRICINGS_CAPABILITIES_WITHOUT_CHIPSET.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_RESALE_PRICINGS_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_RESALE_PRICING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_AVAILABLE_CLIENT_PORTAL_REPORTS_FOR_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_CONTRACT_RESALE_PRICINGS.sql" />
    <Build Include="dbo\Tables\C_STORAGE_INFORMATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_FAIR_MARKET_FOR_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_STORAGE_FILE_NAME.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_CONTRACT_ATTRIBUTE_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CP_CUSTOMER_FINANCIAL_INFO_BLOCKS.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_MERGE.sql" />
    <Build Include="dbo\Functions\fn_int_GET_SALES_ORDER_RMA_COUNT.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_SCHEDULED_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_UNMERGE_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEMS_FROM_SKUS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_HOLD.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_FULLFILLMENT_COMMENT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_MERGE_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SKU_ITEMS_BY_IDS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_PACKAGE_ITEMS_NEW.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEMS_TO_QUALIFY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTERS_FOR_MERGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_PICKLIST.sql" />
    <Build Include="api\Stored Procedures\sp_GET_SALES_ORDER.sql" />
    <Build Include="api\Stored Procedures\sp_LIST_SALES_ORDERS.sql" />
    <Build Include="Security\api.sql" />
    <Build Include="api\Stored Procedures\sp_GET_SALES_ORDER_ITEM.sql" />
    <Build Include="api\Stored Procedures\sp_GET_SALES_ORDER_ADDRESSES.sql" />
    <Build Include="dbo\Views\dbo.vw_F_ITEM_INVENTORY_AVAILABLE_FOR_SALE.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_QUEUE_PARAMS.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_SALES_ORDER_ITEM_IN_RMA.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_PICK_LIST_ITEM.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_PICK_LIST.sql" />
    <Build Include="dbo\Functions\tvf_GET_NOTIFICATION_QUEUE_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEM_ALLOCATED_CORECTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_LOCATION_SIMPLE_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_PICK_LIST_NAME_UNIQUE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICK_LIST_NAME.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTERS_FOR_MERGE_BY_IDS.sql" />
    <Build Include="dbo\User Defined Types\nvarchar_PAIR_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\NOTIFICATION_QUEUE_PARAMS.sql" />
    <Build Include="dbo\Functions\fn_float_GET_ITEM_QUANTITY_LOCKED.sql" />
    <Build Include="dbo\Functions\fn_float_GET_ITEM_QUANTITY_AVAILABLE.sql" />
    <Build Include="dbo\Functions\fn_float_GET_ITEM_QUANTITY_ALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ITEM_MASTER_ENABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MANUFACTURER.sql" />
    <Build Include="dbo\Functions\fn_str_GET_RECYCLING_ORDER_BOL_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_REVISIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DEL_INVENTORY_RECV_WITH_PERMISSION_CHECK.sql" />
    <Build Include="dbo\Stored Procedures\sp_DEL_INVENTORY_RECV_LIST_WITH_PERMISSION_CHECK.sql" />
    <Build Include="dbo\Tables\F_RECYCLING_ORDER_OUTBOUND_ITEM_MASTER.sql" />
    <Build Include="dbo\Tables\F_CONSIGNMENT_RECYCLING_ITEM_MASTER.sql" />
    <Build Include="dbo\Tables\F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ALLOCATED_ITEMS_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_QUALIFIED_ITEMS_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PARSED_SKU_ATTR.sql" />
    <Build Include="api\Stored Procedures\sp_SET_ACCOUNT.sql" />
    <Build Include="api\Stored Procedures\sp_LIST_ACCOUNTS.sql" />
    <Build Include="api\Stored Procedures\sp_GET_ACCOUNT.sql" />
    <Build Include="api\Stored Procedures\sp_DELETE_ACCOUNT.sql" />
    <Build Include="dbo\User Defined Types\ItemCapability.sql" />
    <Build Include="api\User Defined Types\EntityBase.sql" />
    <Build Include="api\User Defined Types\ACCOUNT.sql" />
    <Build Include="dbo\Tables\F_LOG_DATA_HISTORY.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_SALES_MEASURE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_MASTER_ITEM_HISTORICAL_SALES_DATA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_MASTER_ITEM_HISTORICAL_QUOTE_DATA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_SALES_MEASURES.sql" />
    <Build Include="dbo\Tables\F_WAREHOUSE_ACCOUNT.sql" />
    <Build Include="dbo\Tables\C_INVENTORY_ATTRIBUTE_DATA_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_INVENTORY_ATTRIBUTE_DATA_TYPES.sql" />
    <Build Include="dbo\User Defined Types\AccountingCodes.sql" />
    <Build Include="api\Stored Procedures\sp_LIST_CUSTOMERS.sql" />
    <Build Include="api\Stored Procedures\sp_LIST_CUSTOMER_PAYMENTS.sql" />
    <Build Include="api\Stored Procedures\sp_GET_CUSTOMER_PAYMENT.sql" />
    <Build Include="api\Stored Procedures\sp_GET_CUSTOMER.sql" />
    <Build Include="api\User Defined Types\CustomerPayment.sql" />
    <Build Include="api\User Defined Types\Customer.sql" />
    <Build Include="api\Stored Procedures\sp_SET_CUSTOMER_PAYMENT.sql" />
    <Build Include="api\Stored Procedures\sp_SET_CUSTOMER.sql" />
    <Build Include="api\Stored Procedures\sp_DELETE_CUSTOMER_PAYMENT.sql" />
    <Build Include="api\Stored Procedures\sp_DELETE_CUSTOMER.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_RESALE_PRICING_OTHER_ASSET.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_RESALE_PRICING_OTHER_ASSETS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONTRACT_RESALE_PRICING_OTHER_ASSETS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DEL_CONTRACT_RESALE_PRICING_OTHER_ASSET.sql" />
    <Build Include="dbo\Tables\C_WEIGHT_MEASURE_SYSTEM.sql" />
    <Build Include="dbo\Views\vw_EXPORTABLE_SALES_ORDER.sql" />
    <Build Include="dbo\Views\vw_EXPORTABLE_SALES_INVOICE.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_ITEM_TITLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_IDS_BY_ORDER_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ATTRIBUTE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ATTRIBUTE_TYPE_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ASSET_ITEMS_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RECYCLING_ITEM_MASTER_FOR_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CLIENT_PORTAL_QUOTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ALLOCATE_INVENTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_F_ITEM_AS_EXPORTED.sql" />
    <Build Include="dbo\Functions\fn_int_GET_ALL_PICKLIST_ITEM_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_MATCHING_INVENTORY.sql" />
    <Build Include="dbo\User Defined Types\ID_QTY_PRICE_ARRAY.sql" />
    <Build Include="dbo\Tables\F_ORDER_ORDER_TYPE.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET_PHOTO_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET_GENERAL_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\F_CONTRACT_ASSET_DATA_WIPE_SERVICE.sql" />
    <Build Include="dbo\Tables\C_PHOTO_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\C_GENERAL_PROCESSING_REQUIREMENTS.sql" />
    <Build Include="dbo\Tables\C_ENTITY_TYPE.sql" />
    <Build Include="dbo\Functions\fn_str_CLEAN_SYSTEM_NAME.sql" />
    <Build Include="dbo\Functions\fn_str_CLEAN_LOCATON_NAME.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_COMPANY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_CUSTOMER_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_CUSTOMER_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_FOR_RMA.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ORDER_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_REMOVE_CUSTOMER_CODE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_WARRANTY.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_RMA_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ORDER_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CUSTOMER_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_ITEMS_ALLOCATED_SIMPLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ORDER_ORDER_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_TO_RELOCATE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_DEL_ORDER_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_AUDIT_ITEMS.sql" />
    <Build Include="dbo\Tables\F_USER_SESSION.sql" />
    <Build Include="dbo\Tables\F_ORDER_ORDER_SUBJECT_TYPE.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_ORDER_REF.sql" />
    <Build Include="dbo\Tables\D_ENTITY_SUBJECT_TYPE.sql" />
    <Build Include="dbo\Tables\C_SHIPPING_METHOD.sql" />
    <Build Include="dbo\Tables\F_PASSWORD_RESET_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_INVENTORY_ADDED_PARTS_SOLD.sql" />
    <Build Include="dbo\User Defined Types\InventoryGradePositionDevaluation.sql" />
    <Build Include="dbo\User Defined Types\InventoryGradeLevel.sql" />
    <Build Include="dbo\User Defined Types\InventoryGradePositionDevaluationItem.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_INVENTORY_AGAINST_PO.sql" />
    <Build Include="dbo\User Defined Types\InventoryReceiveAgainstPoLine.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_ON_TIME_DELIVERY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_RELATIONSHIPS_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_RELATIONSHIPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_TRANSACTION_VIEW.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INVENTORY_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_REP_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_INVENTORY_AND_SKU_ATTRIBUTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_USER_DEFAULT_PRINTER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICK_LIST_HOLD.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_RELATIONSHIPS_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_RELATIONSHIPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_DEFAULT_PRINTERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SALES_ORDER_RMA_PERCENTAGE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SALES_ORDER_RMA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_ON_HAND.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_ORDER_EVALUATION_ITEMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_AVAILABLE_IN_LOCATIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_IS_USER_EXISTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_UNUSED_ATTRIBUTES.sql" />
    <Build Include="dbo\User Defined Types\SalesPickListItems.sql" />
    <Build Include="dbo\User Defined Types\PART_ADD.sql" />
    <Build Include="dbo\Tables\F_LOG_ACTION_DATA.sql" />
    <Build Include="dbo\Tables\C_ACTION.sql" />
    <Build Include="dbo\Functions\fn_str_RESTORE_LOCATION_NAME_PATTERN.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_CONSIGNMENT_PO_HAS_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CONTRACTS_CONSIGNMENTS.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_HISTORICAL_SALES_DATA.sql" />
    <Build Include="dbo\Tables\F_ENTITY_CUTOMER_ORDER_REF.sql" />
    <Build Include="dbo\Functions\fn_int_GET_ALL_SALES_ORDER_ITEM_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RELOCATE_NOT_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ENTITY_CUSTOMER_CODES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CONSIGNMENT_ITEMS_AMOUNTS.sql" />
    <Build Include="dbo\User Defined Types\T_CONSIGNMENT_ITEMS_AMOUNTS.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_SALES_ORDER.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_PURCHASE_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_REPAIR_PURCHASE_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_REPAIR_PURCHASE_ORDER.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RMA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_ID_BY_SERIAL_OR_UID_AND_PO_ID.sql" />
    <Build Include="dbo\Tables\C_COUNTRY_CURRENCY.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_COMPANY_CURRENCY.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CURRENCIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_COMPANY_CURRENCY.sql" />
    <Build Include="dbo\User Defined Types\ConsignmentSmallModel.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPDATE_SALES_ORDER_ITEM_UNALLOCATED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICK_LIST_ITEM_ALLOCATED_BY_UID_OR_SERIAL.sql" />
    <Build Include="dbo\Tables\F_STATEMENT_TERM.sql" />
    <Build Include="dbo\Tables\C_STATEMENT_TERM_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_STATEMENT_TERMS_TO_ENTITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_STATEMENT_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_STATEMENT_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_STATEMENT_TERMS_OF_ENTITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_STATEMENT_TERMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_STATEMENT_TERMS.sql" />
    <Build Include="dbo\Tables\F_LOG_ACTION_DATA_HISTORY.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_USED_IN.sql" />
    <Build Include="dbo\Tables\F_ITEM_INVENTORY_ATTACHED_FILE.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_SALES_ORDER_ITEM_INCLUDE_INTO_RMA.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_ATTRIBUTE_SET_ATTRIBUTES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_INVENTORY_ATTACHED_FILES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_MASTER_USED_IN.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_INVENTORY_ATTACHED.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_PICK_LIST_HAS_CHILD.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_ORDERED_ITEMS_BY_PICKLIST_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LOG_INFO_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LATEST_LOG_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTERS_USED_IN.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_USED_IN.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_SUBSTITUTES_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_INFO_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_CONDITIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ATTRIBUTE_SET_FILES_UPLOAD_OPTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_MASTER_USED_IN.sql" />
    <Build Include="dbo\Stored Procedures\sp_ADD_ITEM_MASTER_USED_IN.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_AUDIT_ITEM_PRICE_FROM_CONTRACT.sql" />
    <Build Include="dbo\Tables\F_PERMISSION_ROLE_ENTITY_ACTIONS.sql" />
    <Build Include="dbo\Tables\F_CUSTOMER_TAX.sql" />
    <Build Include="dbo\Tables\C_TAX_TYPE.sql" />
    <Build Include="dbo\Tables\C_ENTITY_ACTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_SHIPPING_METHOD.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICK_LIST_UNALLOCATED_ITEMS_BY_SALES_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RMA_STATUS_COUNTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SHIPPING_LOG_PACKAGES_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SHIPPING_LOG_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SHIPPING_LOG.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_BY_SERIAL_OR_UID_FOR_PICKLIST.sql" />
    <Build Include="api\Stored Procedures\sp_GET_CUSTOMER_TAX.sql" />
    <Build Include="dbo\User Defined Types\TAX_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_COMMODITY_GROUPS.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_INVENTORY_ITEMS_SRC_LOT.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_SALES_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TAGS_BY_NAMES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PACKAGE_ITEMS_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_FROM_DELETION.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_ITEM_MASTER_DELETED.sql" />
    <Build Include="dbo\Tables\U_ITEM_TYPE_DEFAULTS.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_DOCUMENT.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_CUSTOMS_VALUE.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_COMMODITY.sql" />
    <Build Include="dbo\Tables\F_SHIPPING_COMMERCIAL_INVOICE.sql" />
    <Build Include="dbo\Tables\F_SALES_ITEM_FILTER_LOCATION.sql" />
    <Build Include="dbo\Tables\C_SHIPPING_DOCUMENT_TYPE.sql" />
    <Build Include="dbo\Functions\fn_nvarchar_GET_MASTER_PRODUCT_DESCR.sql" />
    <Build Include="dbo\Functions\fnSplit.sql" />
    <Build Include="dbo\Functions\iter_charlist_to_table.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_URLS_TEST.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_ENABLED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_CUSTOMS_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_COMMODITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_COMMERTIAL_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_QUOTE_FILES_TO_RECYCLING_INBOUND_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_TYPE_DEFAULTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_FROM_DELETION.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_INVENTORY_RETURN.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_SHIPPING_CONFIG.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_PRODUCT_MASTER_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_INVENTORY_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_INTERNAL_ITEM_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_UNIQUE_SERIAL_NUMBER_AND_UID.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_ITEM_MASTER_DELETED.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ADDITIONAL_DATA_FOR_RECYCLING_QUOTE_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_CAPABILITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_COMMON_INFO.sql" />
    <Build Include="dbo\User Defined Types\udtt_SHIPPING_COMMODITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_MOVE_HISTORY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_MAIN_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_PRODUCT_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_PURCHASING_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_SALES_INFO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_TYPE_DEFAULTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_TYPE_OF_PRODUCT_MASTER_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_PDF_DATA.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_COMMERTIAL_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_COMMODITIES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_CUSTOMS_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_DOCUMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_ENTITY_ACTION_PERMISSIONS.sql" />
    <Build Include="dbo\Functions\tvf_GET_FORBIDDEN_USER_ENTITY_ACTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_VALIDATE_SET_SALES_ORDER.sql" />
    <Build Include="dbo\Tables\F_SALES_ORDER_ITEM_MASTER_INTERNAL_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_ORDER_ITEM_MASTER_INTERNAL_NAMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_ITEM_MASTER_INTERNAL_NAMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_AUDIT_OPERATIONS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_MASTER_BILL_OF_MATERIALS_TOTALS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_AUDIT_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_FOR_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ATTIBUTE_CAPABILITIES_VALUES.sql" />
    <Build Include="dbo\User Defined Types\IMPORT_PO_CAPABILITIES.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_FILES_SEQUENCE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ITEM_MASTER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_ITEM_MASTER_FILE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_LABEL_COUNTER_FOR_PACKAGE.sql" />
    <Build Include="dbo\User Defined Types\int_ID_ARRAY.sql" />
    <Build Include="dbo\Tables\F_GLOBAL_ATTRIBUTES_MAPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_GLOBAL_ATTRIBUTE_MAPPING_BY_ATTRIBUTE_IDs.sql" />
    <Build Include="dbo\Tables\F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_ITEM_MASTER_DEFAULT_ATTRIBUTE_VALUES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTER_DEFAULT_ATTRIBUTE_VALUES.sql" />
    <Build Include="dbo\User Defined Types\Attribute.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVENTORY_ITEMS_OF_PURCHASE_ORDER_ITEMS.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_ITEM_INVENTORY_ID_BY_UID_OR_SERIAL.sql" />
    <Build Include="dbo\Tables\F_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_PURCHASE_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_DOCUMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_DELETE_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPDATE_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_LABELS.sql" />
    <Build Include="dbo\Stored Procedures\sp_CREATE_SHIPPING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_CUSTOMER_ADDRESS_BY_SHIPPING_ID.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_ORDER_SHIPPNGS.sql" />
    <Build Include="dbo\Views\F_EMPLOYEE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_PACKAGES_CANCELED.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_RMA_IDS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_RELOADABLE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_FIXED.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_PACKAGE_TRACKING_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_MASTER_TRACKING_NUMBER.sql" />
    <Build Include="dbo\User Defined Types\ITEMS_INFO_FOR_CREATE_PO_FROM_SO.sql" />
    <Build Include="Security\RoleMemberships.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SALES_QUOTE_REVISION.sql" />
    <Build Include="dbo\Stored Procedures\sp_REFERENCE_SALES_ORDER_FILES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_QUOTE_REVISIONS_AND_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_COPY_QUOTE_SHIPPING_TO.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_SKU.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_PACKAGE_LABEL.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RMA_PACKAGE_LABEL.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PACKAGE_INVOICE_IDS.sql" />
    <Build Include="dbo\Functions\tvf_GET_ITEM_INVENTORY_ADDED_PARTS.sql" />
    <Build Include="Import\Import Customer.sql" />
    <Build Include="Import\Import master commodity.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_ADDED_PARTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_EXTRACT_ITEM_INVENTORY_FROM_ADDED_PARTS_TREE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SKU_BY_MASTER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDER_SLA_FROM_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\dbo.sp_REBUILD_F_ITEM_MASTER_SKU_ATTRB.sql" />
    <Build Include="dbo\Functions\fn_str_GET_DEFAULT_STATEMENT_TERM_BY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RECYCLING_ORDER_SLA_FROM_CONTRACT.sql" />
    <Build Include="dbo\Functions\fn_str_GET_DEFAULT_STATEMENT_TERM_BY_TYPE.sql" />
    <Build Include="dbo\Tables\C_SLA_BEGINNING_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_WAREHOUSE_DEFAULT_LOCATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_WAREHOUSE_LOCATIONS_BY_TYPE.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_DEFAULT_TRANSFER_LOCATION_FOR_RECYCLING_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_NAVISION_INVOICE_EXPORT_INVOICES_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_NAVISION_INVOICE_EXPORT_LIST.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_NAVISION_INVOICE_EXPORTED.sql" />
    <Build Include="dbo\Tables\F_NAVISION_INVOICE_EXPORT.sql" />
    <Build Include="Scripts\Clear\clear_inventory_item__item.sql" />
    <Build Include="Scripts\Clear\clear_purchase_order.sql" />
    <Build Include="Scripts\Clear\clear_sales_order.sql" />
    <Build Include="Scripts\Updates\RSW-8170_check_missed_product_masters.sql" />
    <Build Include="Scripts\Updates\RSW-8170_repair_missed_product_masters.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_CUSTOMER_MATERIAL_STATUS.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SHIPPING_REFERENCE_NO.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_INVOICE_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_INVENTORY_RECV_PRINT.sql" />
    <Build Include="dbo\Functions\tvf_GET_SALES_ORDER_TOTALS.sql" />
    <Build Include="dbo\User Defined Types\SalesOrderTotals.sql" />
    <Build Include="dbo\Functions\fn_str_CONVERT_CAMEL_CASE_REMOVE_UNDERSCORE.sql" />
    <Build Include="dbo\Functions\fn_str_CONVERT_CAMEL_CASE.sql" />
    <Build Include="dbo\Functions\fn_tbl_XML_IDS_TO_BIGINT_ID_ARRAY.sql" />
    <Build Include="dbo\Functions\fn_nvarchar_GET_MASTER_PRODUCT_ITEM_NUMBER.sql" />
    <Build Include="dbo\Functions\fn_nvarchar_GET_MASTER_PRODUCT_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ORDER_CREATION_PARAMS.sql" />
    <Build Include="dbo\Stored Procedures\sp_ADD_ITEM_INVENTORY_AUDIT_OPERATION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPD_QUALIFIED_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_DATA_FOR_REBUILD_SALES_CHANNEL.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LIST_ORDER_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SALES_ORDER_COSTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_DAILY_PRODUCTION.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_OUTBOUND_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_CRM_ACCOUNTS_EXPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_VENDOR_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LIST_ORDER_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_INBOUND_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SALES_TAX.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SALES_ORDER_SUMMARY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_WORK_IN_PROGRESS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_INBOUND_ORDER_SUMMARY.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_CRM_ACCOUNTS_EXPORT.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USER_REPORTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_VALUATION.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_LIST_PART_NUMBERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_BACK_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_CUSTOMER_OPEN_BALANCE.sql" />
    <Build Include="dbo\Functions\fn_float_GET_MASTER_PRODUCT_PURCHASE_PRICE_AVG.sql" />
    <Build Include="dbo\Views\vw_EXPORTABLE_RMA_INVOICE.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_INVENTORY_ITEM_IS_IN_PAID_PO_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_AUDIT_WORKFLOW.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_AGING.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_ITAD_WORKFLOW_ASSETS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_INVENTORY_SLOW_MOVING_STOCK.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_TOTAL_AMOUNT_OF_SALE.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_RECYCLING_RESALE_COMMISSION.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_AR_PAYMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_AP_PAYMENTS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_AR_CREDITS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_AP_CREDITS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_KIT_SKUS.sql" />
    <Build Include="dbo\Tables\F_REPORT_LOG.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_REPORT_LOG_START.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_REPORT_LOG_END.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_LOG_LIST.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_REPORT_REAL_TIME_BACK_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_ITEM_MASTER_BOMS.sql" />
    <Build Include="dbo\Tables\dbo.F_ITEM_MASTER_SUBSTITUTE_1_WAY.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LIST_ITEM_MASTER_SUBSTITUTES.sql" />
    <Build Include="dbo\Views\dbo.vw_F_ITEM_MASTER_SUBSTITUTES_ALL.sql" />
    <Build Include="dbo\Functions\dbo.fn_varchar_GET_REMOTE_PRINTER_AUTHKEY.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_MERGE_ITEM_MASTER_SUBSTITUTES.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_REPORT_RECYCLING_SORTED_MATERIAL.sql" />
    <Build Include="dbo\Functions\dbo.fn_table_GET_RECYCLING_ORDER_WEIGHT.sql" />
    <Build Include="dbo\Functions\tvf_GET_ITEM_MASTER_SUBSTITUTES_IDS.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_CUSTOMER_FOLLOWERS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_UNALLOCATED_ITEM_FOR_INVENTORY_ITEM_TO_BE_ALLOCATED.sql" />
    <Build Include="dbo\User Defined Types\PickListInventoryToAllocate.sql" />
    <Build Include="dbo\Functions\fn_str_SPLIT_AND_TAKE.sql" />
    <Build Include="dbo\Functions\dbo.fn_xml_bigint_ID_ARRAY_TO_XML.sql" />
    <Build Include="dbo\User Defined Types\SalesPickListAvailableInventoryItem.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GET_PICKLIST_AVAILABLE_INVENTORY.sql" />
    <Build Include="dbo\Views\dbo.vw_UNIQUEIDENTIFIER_NEWID.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_PICKLIST_ITEM_UNALLOCATED_QTY_CORRECTION_BY_INVENTORY_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SPLIT_INVENTORY_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SYSTEM_SETTINGS_LABEL.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SYSTEM_SETTINGS_LABEL.sql" />
    <Build Include="dbo\Tables\F_INVOICE_CONSOLIDATED.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_SET_INVOICE_CONSOLIDATED.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_DEL_INVOICE_CONSOLIDATED.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_INVOICE_PAYMENT_DETAIL.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_INVOICES_BY_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_INVOICES_BY_CUSTOMER_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_INVOICES_BY_CUSTOMER_TO_CREATE_INVOICE_CONSOLIDATED.sql" />
    <Build Include="dbo\Functions\fn_money_GET_INVOICE_PAID_AMOUNT.sql" />
    <Build Include="dbo\Functions\fn_money_GET_INVOICE_CONSOLIDATED_PAID_AMOUNT.sql" />
    <Build Include="dbo\Views\dbo.vw_AR_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_SALES_ORDER_INVOICES.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_RECYCLING_SALES_ORDER_INVOICES.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GET_SALES_ORDERS_BY_INVOICE_CONSOLIDATED.sql" />
    <Build Include="dbo\Tables\OperationBlocks\C_OPERATION_BLOCKING_TYPE.sql" />
    <Build Include="dbo\Tables\OperationBlocks\C_OPERATION_BLOCKING_ENTITY_TYPE.sql" />
    <Build Include="dbo\Tables\OperationBlocks\F_OPERATION_BLOCKING.sql" />
    <Build Include="dbo\Stored Procedures\OperationBlocking\sp_SET_OPERATION_BLOCKING.sql" />
    <Build Include="dbo\Stored Procedures\OperationBlocking\sp_DEL_OPERATION_BLOCKING.sql" />
    <Build Include="dbo\Functions\OperationBlocking\fn_bit_IS_OPERATION_BLOCKING_SET.sql" />
    <Build Include="dbo\User Defined Types\OPERATION_BLOCKING.sql" />
    <Build Include="dbo\Stored Procedures\OperationBlocking\sp_SET_OPERATION_BLOCKINGS.sql" />
    <Build Include="dbo\Stored Procedures\OperationBlocking\sp_DEL_OPERATION_BLOCKINGS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_INVOICES_TO_BE_PAID.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_CONSOLIDATED_INVOICE_ITEMS_BY_ID_FOR_PDF.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_CONSOLIDATED_INVOICE_BY_ID_FOR_PDF.sql" />
    <Build Include="dbo\Functions\dbo.tvf_LOCATIONS_TO_DETAIL_NAME_MAPPING.sql" />
    <Build Include="dbo\User Defined Types\LocationsToDetailNameMapping.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_ROLES_BI.sql" />
    <Build Include="dbo\User Defined Types\udtt_INVOICE_PAYMENT_AMOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Payment\sp_GET_CONSOLIDATED_INVOICE_PAYMENT_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Payment\sp_SET_PAYMENT.sql" />
    <Build Include="dbo\Views\dbo.vw_AP_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_RECYCLING_ORDER_INBOUND_STATUSES.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SALES_INVOICE_RESALE_DOCUMENT_MODEL.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SALES_INVOICE_OUTBOUND_DOCUMENT_MODEL.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SALES_INVOICE_INBOUND_DOCUMENT_MODEL.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_CONCATENATE_ENTITY_TAGS.sql" />
    <Build Include="dbo\Functions\dbo.fn_datetime_GET_RECYCLING_ORDER_SORT_COMPLETE_DATE.sql" />
    <Build Include="dbo\Stored Procedures\Payment\sp_GET_CUSTOMER_CREDITS_PAGE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPDATE_SO_SHIPPING_AND_BILLING.sql" />
    <Build Include="dbo\Stored Procedures\Payment\sp_SET_ENTITY_AUTO_NUMBER_RESERVED_FOR_CUSTOMER_PAYMENT.sql" />
    <Build Include="dbo\Stored Procedures\AutoNumbers\sp_SET_ENTITY_AUTO_NUMBER_RESERVED.sql" />
    <Build Include="dbo\Stored Procedures\sp_IS_CUSTOMER_PAYMENT_REFERENCES_USED.sql" />
    <Build Include="dbo\Tables\dbo.U_ENTITY_RESERVED_AUTO_NUMBER.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IS_ENTITY_AUTO_NUMBER_RESERVED.sql" />
    <Build Include="dbo\User Defined Types\udtt_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_CHECK_INVENTORY_CAPABILITY_USING.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_PAYMENTS_TOTALS.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_PAYMENTS.sql" />
    <Build Include="dbo\Functions\fn_float_GET_RECYCLING_INVENTORY_CONSUMED_WEIGHT.sql" />
    <Build Include="dbo\Functions\fn_str_GET_USER_CURRENCY_MARK.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GET_INVENTORY_DETAIL_CONT.sql" />
    <Build Include="dbo\Functions\fn_byte_IS_ABLE_TO_VOID_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_IS_ABLE_TO_VOID_INVOICES.sql" />
    <Build Include="dbo\Functions\fn_byte_IS_ABLE_TO_VOID_INVOICE_BY_VALUES.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GET_LAST_NUMERIC_AUTO_CHECK_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_CUSTOMER_WORK_INSTRUCTIONS.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_ITEM_MASTERS_WITH_DEFAULT_INVENTORY_CAPABILITY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_USER_DEFAULT_LOG_IN_PAGE_ENTITY_ID.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_RECYCLING_ORDER_WORK_INSTRUCTIONS.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_REMOVE_USER_SESSION_BY_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_LOGOUT_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_LOGIN_USER.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_LOG_USER_LOGIN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_IS_USER_TOKEN_VALID.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_GET_USER_SESSIONS.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_GET_USER_ID_BY_EMAIL.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_GET_USER_BY_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_LOGIN_USER_BY_AUTHKEY.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_ADD_USER_SESSION.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\Password\sp_SET_USER_PASSWORD.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\Password\sp_SET_USER_PASSWORD_SENT.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\Password\sp_SET_PASSWORD_RESET_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\Password\sp_GET_USER_ID_BY_PASSWORD_RESET_TOKEN.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\sp_LOG_USER_LOGOUT.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\Password\sp_GET_PASSWORD_RESET_TOKENS.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_GET_DEFAULT_LOCATION_BY_WAREHOUSE.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_CUSTOMER_FILE_IS_SHARED.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_CUSTOMER_FILE_INFO.sql" />
    <Build Include="dbo\Functions\dbo.fn_stg_GET_RECYCLING_ORDER_NUMBER.sql" />
    <Build Include="dbo\Tables\dbo.F_PERMISSION_ENTITY_TO_MENU_ITEM.sql" />
    <Build Include="dbo\Tables\dbo.F_PERMISSION_CATEGORY_TO_MENU_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_MAIN_MENU_STRUCTURE.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_COPY_ORDER_SCHEDULED_FILES_INTO_RECYCLING_INBOUND_FILES.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_CUSTOMER_CONTACT_EMAIL_AND_PHONE.sql" />
    <Build Include="Security\recycling.sql" />
    <Build Include="dbo\Views\dbo.vw_F_ITEM_MASTER_ATTRIBUTE_SET_AND_CATEGORY.sql" />
    <Build Include="dbo\Functions\fn_bigint_GET_ITEM_MASTER_ATTRIBUTE_SET_ID.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GET_RECYCLING_ORDER_BOL_NUMBER_REPORT.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SALES_ORDER_CUSTOMER_ID.sql" />
    <Build Include="recycling\User Defined Types\recycling.UidSerial.sql" />
    <Build Include="dbo\User Defined Types\udtt_UserStringSetting.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_ORDER_HAS_PAID_INVOICES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DEL_SIGNATURE_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Printing\Labels\dbo.sp_GetInventoryLabelModel.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetCertificateTemplate.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SHIPPING_TEMPLATE_INTERMEDIATE_DATA.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_ListCertificateTemplate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCertificateTemplate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteCertificateTemplate.sql" />
    <Build Include="dbo\Stored Procedures\Printing\Labels\dbo.sp_GetInventoryCapabilitiesForLabels.sql" />
    <Build Include="dbo\Stored Procedures\Printing\Labels\sp_GetShippingPackingSlipLabelIntermediateModel.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\sp_GET_TEMPLATE_DATA_FOR_QUOTE.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\sp_GET_TEMPLATE_DATA_FOR_USER.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\sp_GET_TEMPLATE_DATA_FOR_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\sp_GET_TEMPLATE_DATA_FOR_QUOTE_ALT_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\sp_GET_TEMPLATE_DATA_FOR_QUOTE_PRIMARY_CONTACT.sql" />
    <Build Include="recycling\Stored Procedures\Inventory\recycling.sp_GetInventoryCapabilities.sql" />
    <Build Include="recycling\Tables\recycling.F_RecyclingOrderInboundCertificate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderInboundCertificateList.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderInboundCertificate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderInboundCertificate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteRecyclingOrderInboundCertificates.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_AssetBulkInsert.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GET_GENERAL_INFO.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GET_LOT_AUDITED_ASSETS.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GET_SUMMARY_INFO.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAsset.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetLot.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_UPD_AUDITOR.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAuditCompleteSummaryInfo.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_AuditComplete.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_RegroupLots.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_UndoAuditComplete.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetSignatureTemplateUseCount.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SHIPPING_ORDERS_INFO.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetItemInventoryIdsByAssetIds.sql" />
    <Build Include="dbo\Functions\fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME_WITH_COUNTY.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_ListRecyclingOrderInboundAuditCertificates.sql" />
    <Build Include="recycling\Stored Procedures\AuditReport\recycling.sp_GetAuditReportAuditOrders.sql" />
    <Build Include="recycling\Stored Procedures\AuditReport\recycling.sp_GetAuditReportAssets.sql" />
    <Build Include="recycling\Stored Procedures\AuditReport\recycling.sp_GetAuditReportRecyclingOrder.sql" />
    <Build Include="recycling\Stored Procedures\AuditReport\recycling.sp_GetAuditReportCertificates.sql" />
    <Build Include="dbo\Stored Procedures\Addresses\dbo.sp_GetAddress.sql" />
    <Build Include="dbo\Stored Procedures\Addresses\dbo.sp_GetAddresses.sql" />
    <Build Include="dbo\Stored Procedures\Addresses\dbo.sp_GetAddressesForCustomer.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GET_CUSTOMER_ADDRESS.sql" />
    <Build Include="dbo\Tables\dbo.D_DocumentTableColumn.sql" />
    <Build Include="dbo\Tables\dbo.C_DocumentTable.sql" />
    <Build Include="dbo\Tables\dbo.C_Document.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetDocumentColumns.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_ListDocuments.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetDocumentColumns.sql" />
    <Build Include="recycling\User Defined Types\recycling.DocumentColumn.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_ORDER_INBOUND_CREATED.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_MASTER_ITEM_CREATED.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_ATTRIBUTE_CREATED.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_ATTRIBUTE_VALUE_CREATED.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetDocumentTableVisibleColumns.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetCapability.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\sp_GetInvoicePaidStatus.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_CloneShipping.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsShippingDropShip.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PORTAL_QUOTES_OF_ORDER.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_PORTAL_QUOTE.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GetCustomerPrimaryContactId.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_CUSTOMERS_WITH_PERMISSIONS.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAssetCapabilities.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_SetAsset.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SetInventoryCapability.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SetItemInventoryCapabilities.sql" />
    <Build Include="recycling\Tables\recycling.F_AuditSession.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_REPORT_SALES_ORDER_PROFIT.sql" />
    <Build Include="Security\reports.sql" />
    <Build Include="reports\Tables\reports.F_RecyclingOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_CollectRecyclingOnHandSummary.sql" />
    <Build Include="reports\User Defined Types\reports.RawRecyclingOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRawRecyclingOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingOnHandSummary.sql" />
    <Build Include="reports\Tables\reports.U_SYS_RecyclingOnHandSummarySettings.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingOnHandSummarySettings.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_SetRecyclingOnHandSummarySettings.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_HandleRecyclingOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingOnHandSummaryCounts.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetOriginInfo.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetIdByUidOrSerial.sql" />
    <Build Include="integration\Tables\integration.D_Integration.sql" />
    <Build Include="integration\Tables\integration.C_EntityType.sql" />
    <Build Include="integration\Tables\integration.C_Operation.sql" />
    <Build Include="integration\Tables\integration.C_Result.sql" />
    <Build Include="integration\Tables\integration.F_Session.sql" />
    <Build Include="integration\Tables\integration.F_Log.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegrationByName.sql" />
    <Build Include="Security\integration.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_StartSession.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_UpdateSession.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_CloseSession.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_AddLog.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_AddLogItems.sql" />
    <Build Include="integration\User Defined Types\integration.AddLogTableType.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetLog.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_ImportAccount.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetSalesOrdersForExport.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetStock.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetPurchaseOrdersForExport.sql" />
    <Build Include="dbo\Functions\fn_tvf_GET_LOCATON_NAME_WITH_SECONDARY.sql" />
    <Build Include="dbo\Functions\fn_str_GET_LOCATON_NAME_WITH_SECONDARY.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetPriceForMaterialsOnDay.sql" />
    <Build Include="recycling\Tables\recycling.F_ItemMasterMaterial.sql" />
    <Build Include="recycling\Tables\recycling.C_ItemMasterUnitOfMeasure.sql" />
    <Build Include="recycling\Tables\recycling.C_ItemMasterMaterial.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetItemMasterMaterial.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingItemMasterUnitOfMeasure.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetItemMasterMaterials.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DelItemMasterMaterial.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetItemMasterIdAndManufacturerId.sql" />
    <Build Include="integration\User Defined Types\integration.AuditToolReportFieldMapping.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolReportFieldMappings.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFieldMappings.sql" />
    <Build Include="integration\Tables\integration.D_AuditToolReportField.sql" />
    <Build Include="integration\Tables\integration.F_AuditToolReportFieldMapping.sql" />
    <Build Include="integration\Tables\integration.C_AuditToolAttributeType.sql" />
    <Build Include="integration\Tables\integration.F_AuditToolAttributeTypeMapping.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFields.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolAttributeTypes.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolAttributeTypeMapping.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolAttributeTypeMappings.sql" />
    <Build Include="integration\User Defined Types\integration.AuditToolAttributeTypeMapping.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryAttributeTypes.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryAttributeSetCapabilityTypes.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetMappedAuditToolAttributeTypes.sql" />
    <Build Include="dbo\Functions\dbo.fn_datetime_SET_DATE_TIME_PART.sql" />
    <Build Include="integration\Tables\integration.F_IntegrationAsset.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegrationAssets.sql" />
    <Build Include="integration\User Defined Types\integration.AddIntegrationAssetTableType.sql" />
    <Build Include="recycling\Functions\recycling.fn_money_GetCommodityRTIPrice.sql" />
    <Build Include="recycling\Functions\recycling.fn_bit_IsCommodityAvailableAsMaterial.sql" />
    <Build Include="integration\Functions\integration.fn_money_GetMaterialRtiPrice.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetItemMasterMaterialSettings.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetMaterialUnitOfMeasureList.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetItemMasterMaterialSettings.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetItemMasterMaterialList.sql" />
    <Build Include="dbo\Functions\dbo.fn_float_ConvertUom.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetInvertedMappedAuditToolAttributeTypes.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetInvertedMappedAuditToolReportFields.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetMappedAuditToolReportFields.sql" />
    <Build Include="dbo\Stored Procedures\sp_SAVE_SALES_ORDER_ADMIN_INFO.sql" />
    <Build Include="recycling\Tables\recycling.F_CustomerDsvRestrictedRecyclingItemMaster.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_GetCustomerDsvRestrictedRecyclingItemMasters.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_SetCustomerDsvRestrictedRecyclingItemMasters.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_PageCustomerDsvRestrictedRecyclingItemMasters.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_SetOutboundOrderDsvsFromCustomer.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_LOCATION_DETAIL_NOT_INCLUDED.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_GetOutboundOrderDsvRestrictedRecyclingItemMasters.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_SetOutboundOrderDsvRestrictedRecyclingItemMasters.sql" />
    <Build Include="recycling\Functions\DsvRestrictions\recycling.fn_bit_CustomerDsvRestrictionsEnabled.sql" />
    <Build Include="recycling\Functions\DsvRestrictions\recycling.fn_bit_OubtoundOrderDsvRestrictionsEnabled.sql" />
    <Build Include="recycling\Stored Procedures\DsvRestrictions\recycling.sp_ListDsvRestrictedCarriersForOutboundOrder.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetMaterialIdsForReport.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFilesByUidOrSerial.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_DELETE_KIT.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFiles.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegrationsList.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegration.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetIntegration.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_DeleteIntegration.sql" />
    <Build Include="dbo\Functions\dbo.fn_datetime_SET_DATE_TIME_PART.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSkuByInventoryUidTemp.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegrations.sql" />
    <Build Include="dbo\Functions\fn_str_GET_INVOICE_AUTO_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Sequences\dbo.sp_GetIndentityValue.sql" />
    <Build Include="dbo\Sequences\U_TABLE_IDENTITY.sql" />
    <Build Include="Security\inventory.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFileOffset.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolReportFiles.sql" />
    <Build Include="inventory\Stored Procedures\inventory.sp_SetInventoryItemStatus.sql" />
    <Build Include="dbo\Tables\dbo.D_OrderSourceType.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_ListOrderSources.sql" />
    <None Include="Scripts\Updates\Fix dynamic menu.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_CreateAsset.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_UpdateAsset.sql" />
    <Build Include="recycling\Functions\DsvRestrictions\recycling.fn_bit_OubtoundOrderDsvRestrictionsEnabledAndFilled.sql" />
    <Build Include="recycling\Functions\DsvRestrictions\recycling.fn_bit_CustomerDsvRestrictionsEnabledAndFilled.sql" />
    <Build Include="recycling\Functions\DsvRestrictions\recycling.fn_bit_IsLotCommodityAllowedForOutboundOrder.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderNotes.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncItemInventoryToAsset.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncAssetToItemInventory.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerAssetVisionStatistic.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_LogData.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerColumns.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerCsrRepContactInfo.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_ListCustomerFiles.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetCustomerInVisibleGridColumns.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerAssetVision.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerAvailableGridColumns.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerAvailablePages.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerAvalibleAuditOrder.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerBalance.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerFinancialInfoBlocks.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerInventoryItems.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerRecyclingOrders.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerRepContactInfo.sql" />
    <Build Include="cp\Stored Procedures\sp_GetCustomerSalesOrders.sql" />
    <Build Include="cp\Stored Procedures\sp_GetSalesOrderItems.sql" />
    <Build Include="cp\Stored Procedures\sp_GetTimeZoneForNewOrderScheduled.sql" />
    <Build Include="cp\Stored Procedures\sp_ListInventoryItems.sql" />
    <Build Include="cp\Stored Procedures\sp_SetCustomerAvailablePages.sql" />
    <Build Include="cp\Tables\C_AVAILABLE_PAGE.sql" />
    <Build Include="cp\Tables\C_FINANCIAL_INFO_BLOCK.sql" />
    <Build Include="cp\Tables\C_GRID.sql" />
    <Build Include="cp\Tables\D_GRID_COLUMN.sql" />
    <Build Include="cp\Tables\F_CUSTOMER_AVAILABLE_PAGE.sql" />
    <Build Include="cp\Tables\F_CUSTOMER_FINANCIAL_INFO_BLOCK.sql" />
    <Build Include="cp\Tables\F_CUSTOMER_INVISIBLE_GRID_COLUMN.sql" />
    <Build Include="cp\Tables\F_ORDER_SCHEDULED.sql" />
    <Build Include="cp\Tables\F_ORDER_SCHEDULED_FILE.sql" />
    <Build Include="cp\Tables\F_ORDER_SCHEDULED_ITEMS_MASTER.sql" />
    <Build Include="cp\Tables\F_ORDER_SCHEDULED_RECYCLING_ITEMS_MASTER.sql" />
    <Build Include="cp\Tables\F_ORDER_SCHEDULED_SERVICE_TYPES.sql" />
    <Build Include="dbo\Functions\tvf_GetInventoryCapabilities.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_DeleteAssetServiceApplying.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetServiceApplyings.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetServicesForAssetApplying.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetServiceApplying.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetServiceApplyings.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractById.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractsPage.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractGeneralInfo.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetServiceApplying.sql" />
    <Build Include="recycling\User Defined Types\recycling.AssetServiceApplying.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReports.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractWorkInstructions.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractWorkInstructions.sql" />
    <Build Include="dbo\Stored Procedures\MeasuringSystems\dbo.sp_GetLengthMeasuringSystems.sql" />
    <Build Include="dbo\Stored Procedures\MeasuringSystems\dbo.sp_GetWeightMeasuringSystems.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SALES_ORDER_SHIPPING_PURCHASE_ORDERS.sql" />
    <Build Include="dbo\Tables\dbo.C_LengthMeasuringSystem.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LOG_RECYCLING_ORDER_ITEMS_DEMERGED.sql" />
    <Build Include="dbo\Stored Procedures\ItemMasters\dbo.sp_GetItemMasterDefaults.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_RESALE_REVENUE.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_RESALE_BACK_TO_CUSTOMER.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_ORDER_EST_SCRAP_VALUE.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingDetailedBreakout.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingDetailedBreakout_Totals.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_MASS_BALANCE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_TOTALS_MASS_BALANCE.sql" />
    <Build Include="integration\Tables\integration.F_SellerCloudOrder.sql" />
    <Build Include="magento\Tables\MAGENTO_ADDRESSES.sql" />
    <Build Include="magento\Tables\MAGENTO_ORDER_PRODUCTS.sql" />
    <Build Include="magento\Tables\MAGENTO_ORDERS.sql" />
    <Build Include="magento\Tables\MAGENTO_PAYMENT_INFO.sql" />
    <Build Include="magento\Tables\MAGENTO_PRODUCTS.sql" />
    <Build Include="integration\Tables\integration.F_SellerCloudOrderItem.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_ATTRIBUTE_IS_CREATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_ATTRIBUTE_VALUE_IS_CREATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_COMMODITY_IS_NOT_IN_CONTRACT_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_FIRST_PURCHASE_ORDER_ITEM_RECEIVED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_FREIGHT_CLIENT_CONFIRMATION_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_MASTER_ITEM_IS_CREATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PICK_LIST_CLOSED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PICK_LIST_ITEM_FAILED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PICK_LIST_OFF_HOLD_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PICK_LIST_ON_HOLD_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PURCHASE_ORDER_ITEM_STATUS_CHANGE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PURCHASE_ORDER_READY_TO_CLOSE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PURCHASE_ORDER_STATUS_CHANGE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_QUOTE_SUBMITTED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RMA_READY_TO_CLOSE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RMA_RECEIVED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_SALES_ORDER_INVOICE_GENERATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_SALES_ORDER_IS_APPROVED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_SALES_ORDER_IS_CREATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_SALES_ORDER_SHIPPED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_CONTRACT_DATE_EXPIRED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_CONTRACT_TIERED_ORDER_ITEM_PRICE_CHANGED_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_CUSTOMER_CREATION_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RECYCLING_ORDER_CREATION_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RECYCLING_ORDER_RECEIVE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_SETTLEMENT_COMPLETE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_AUDIT_SESSION_IS_COMPLETED_FROM_QUEUE.sql" />
    <Build Include="recycling\User Defined Types\recycling.CommodityRule.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractCommodities.sql" />
    <Build Include="recycling\Tables\recycling.F_ContractService.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractServices.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommoditiesList.sql" />
    <Build Include="recycling\Stored Procedures\Common\recycling.sp_GetPricetypesList.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetContractFile.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteContractFile.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetContractFiles.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetContractFile.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractConsignmentsList.sql" />
    <Build Include="recycling\Tables\recycling.F_WorkflowTemplate.sql" />
    <Build Include="recycling\Tables\recycling.F_WorkflowTemplateBlock.sql" />
    <Build Include="recycling\Tables\recycling.F_WorkflowTemplateBlockLink.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_SetWorkflowTemplate.sql" />
    <Build Include="recycling\Functions\Workflow\recycling.fn_nvarchar_MakeWorkflowBlockLinkCondition.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_GetWorkflowTemplatesPage.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_GetWorkflowTemplatesList.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_GetWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_DeleteWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_GetServicesList.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractServices.sql" />
    <Build Include="recycling\User Defined Types\recycling.ContractService.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractConsignmentRules.sql" />
    <Build Include="recycling\User Defined Types\recycling.ConsignmentRule.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractMainInfo.sql" />
    <Build Include="recycling\User Defined Types\recycling.ConsignmentSplitTier.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractConsignments.sql" />
    <Build Include="recycling\Tables\recycling.C_AssetWorkflowStep.sql" />
    <Build Include="recycling\User Defined Types\recycling.WorkflowTemplateBlock.sql" />
    <Build Include="recycling\User Defined Types\recycling.WorkflowTemplateBlockLink.sql" />
    <Build Include="recycling\Tables\recycling.C_WorkflowTemplateBlockType.sql" />
    <Build Include="recycling\Stored Procedures\Workflow\recycling.sp_GetWorkflowTemplateBlockTypes.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_GetPresetLogoItem.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_GetPresetLogoItemByName.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_ListPresetLogoItems.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_SetPresetLogoImageIds.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_DeletePresetLogoItem.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_SetPresetLogoItem.sql" />
    <Build Include="dbo\Tables\dbo.F_PresetLogo.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_GetContractLogoPresetId.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_GetContractLogoPresetIdByOrder.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractRoles.sql" />
    <Build Include="recycling\Tables\recycling.F_ContractRole.sql" />
    <Build Include="recycling\Functions\Contracts\recycling.fn_IsContractAccessibleForRoles.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_IsContractAccessibleForRoles.sql" />
    <Build Include="dbo\Stored Procedures\sp_DebugPrintSpArgs.sql" />
    <Build Include="recycling\User Defined Types\recycling.UidSerialAsset.sql" />
    <Build Include="dbo\Stored Procedures\Settings\sp_SET_USER_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Settings\sp_GET_COMMON_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Settings\sp_GET_USER_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Settings\sp_SET_COMMON_SETTINGS.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractOrdersCount.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractsArchivedFlag.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SCHEDULING_ORDER_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_LIST_RECYCLING_ORDER_STATUSES.sql" />
    <Build Include="recycling\Tables\recycling.C_InboundOrderStatus.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_SetInboundOrdersStatus.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_RECYCLING_ORDER_OUTBOUND_INFO.sql" />
    <Build Include="recycling\Functions\recycling.fn_bigint_GetInboundOrderStatusForUndoCancel.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_IsRecyclingOrdersHavePaidInvoices.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_IsInboundOrdersItemsAddedToOutboundOrders.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractCounts.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.ListWarehousesByUsers.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_ListFmvTemplatesForContract.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_ListGradeDevaluationTemplatesForContract.sql" />
    <Build Include="dbo\User Defined Types\Common\dbo.BulkUpdateField.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_GenerateLocationName.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_ListContractBillingFrequencies.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_GenerateCaseLocation.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_GeneratePalletLocation.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_SetLocationCase.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRelocateToInfo.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_RelocateAssets.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAssetToRelocate.sql" />
    <Build Include="recycling\Functions\Quotes\recycling.fn_GetRecyclingQuoteStateId.sql" />
    <Build Include="recycling\Stored Procedures\Quote\recycling.sp_GetQuotesPage.sql" />
    <Build Include="recycling\Stored Procedures\Quote\recycling.sp_ListQuotesFilterCounts.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_CreateSaleOrderFromSellerCloud.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_CreateShippingInfoForSellerCloudOrder.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetSellerCloudOrdersToPushInRazor.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_PushCustomerFromSellerCloudOrdersToRazor.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_PushItemsFromSellerCloudOrderToRazor.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SetSellerCloudOrder.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SetSellerCloudOrderItems.sql" />
    <Build Include="integration\User Defined Types\integration.SellerCloudOrder.sql" />
    <Build Include="integration\User Defined Types\integration.SellerCloudOrderItem.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SetSellerCloudOrderPayments.sql" />
    <Build Include="integration\User Defined Types\integration.SellerCloudOrderPayment.sql" />
    <Build Include="integration\Tables\integration.F_SellerCloudOrderPayment.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_ApplyPricingTemplateToContract.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetPickListItemAllocatedByInventoryId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInventoryItemsInLocationForPicklist.sql" />
    <Build Include="dbo\Stored Procedures\dbo.spGetItemMasterByProductCodeOrModel.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_DeleteContracts.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractClone.sql" />
    <Build Include="recycling\Tables\recycling.C_CommodityRequirementTemplate.sql" />
    <Build Include="recycling\User Defined Types\recycling.CommodityRuleRequirementSetting.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderSettlementCommodityForCertificate.sql" />
    <Build Include="recycling\Stored Procedures\Calendar\recycling.sp_GetCalendarEvents.sql" />
    <Build Include="recycling\Stored Procedures\Calendar\recycling.sp_GetCalendarEventInfo.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GET_LOCATON_NAME.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetIntegrationAssetIdsForSync.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_TAGS_LIST.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetShippingOrderInfoToPushSellerCloud.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetRecyclingOrderItemsList.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_GetInbounOrdersPage.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_GetInboundOrdersPortalQuotes.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssets.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetsByLocation.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_ListInboundOrderStatuses.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_ListInboundOrdersFilterCounts.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetRelocateAdditionalInfo.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetCapabilities.sql" />
    <Build Include="recycling\View\recycling.vw_InboundLots.sql" />
    <Build Include="recycling\View\recycling.vw_OutboundLots.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_CONTRACT_COMMON_INFO.sql" />
    <Build Include="recycling\Tables\recycling.C_RecyclingTemplateGroupTag.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetCommodityRuleRequirements.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetWorkflowSteps.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetDataDestructionServicesForAssetApplying.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_CONTRACT_RESALE_PRICINGS.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GenerateInboundDraftPurchaseOrder.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetInboundDraftAssetDetail.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetInboundDraftAssets.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetInboundDraftAssets.sql" />
    <Build Include="recycling\User Defined Types\recycling.InboundDraftAsset.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundDraftAsset.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetDataDestruction.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_SetAssetDataDestruction.sql" />
    <Build Include="recycling\User Defined Types\recycling.AssetDataDestruction.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetDataDestruction.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetCorrectItemMasterId.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetItemMasterToCorrect.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetPriceFromDraft.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_AutoCreateMasterItem.sql" />
    <Build Include="integration\Tables\integration.D_AuditToolService.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolServices.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SyncCalculatedServicesForRecyclingOrder.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetWorkflowStepCompletion.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetNotCompletedAuditedAssets.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetWorkflowStepCompletion.sql" />
    <Build Include="recycling\Tables\recycling.F_CommodityRule.sql" />
    <Build Include="recycling\Tables\recycling.F_CommodityRuleRequirement.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetGradePositionDevaluation.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetGradePositions.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetGradePositions.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetGrading.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetGrading.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_CREATE_TAX_FROM_ECOMMERCE.sql" />
    <Build Include="Security\storage.sql" />
    <Build Include="Storage\Tables\storage.F_FileRegistry.sql" />
    <Build Include="Storage\Stored Procedures\storage.sp_SetFileRegistryRecords.sql" />
    <Build Include="Storage\User Defined Types\storage.FileRegistryRecord.sql" />
    <Build Include="Storage\Stored Procedures\storage.sp_DeleteFileRegistryRecords.sql" />
    <Build Include="Storage\Stored Procedures\storage.sp_GetFileRegistryRecords.sql" />
    <Build Include="Storage\Stored Procedures\storage.sp_GetFileRegistryRecordsByAddressParams.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_IMAGES.sql" />
    <Build Include="recycling\Tables\recycling.F_CommodityRequirementGroup.sql" />
    <Build Include="recycling\Tables\recycling.F_CommodityRequirement.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractCommodityGeneralSettings.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_SetCommodityRuleRequirements.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_SetCommodityRules.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommodityRequirementTemplates.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommodityRules.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommodityRulesForApplyContract.sql" />
    <Build Include="recycling\Functions\Commodities\recycling.fn_nvarchar_GetCommodityRuleRequirementsJson.sql" />
    <Build Include="recycling\Functions\Commodities\recycling.fn_nvarchar_GetCommodityRuleRequirementsDefaultsJson.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolServiceMappings.sql" />
    <Build Include="recycling\Tables\recycling.D_GradeCategory.sql" />
    <Build Include="recycling\Tables\recycling.D_GradeSubcategory.sql" />
    <Build Include="recycling\Tables\recycling.D_GradePosition.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_SetGradingOptions.sql" />
    <Build Include="recycling\User Defined Types\recycling.GradeCategory.sql" />
    <Build Include="recycling\User Defined Types\recycling.GradeSubcategory.sql" />
    <Build Include="recycling\User Defined Types\recycling.GradePosition.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradingOptions.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradingCategories.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradingSubcategories.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradingPositions.sql" />
    <Build Include="dbo\Tables\dbo.F_GradeLevel.sql" />
    <Build Include="recycling\Tables\recycling.F_GradeCategoryPositionDevaluation.sql" />
    <Build Include="recycling\Tables\recycling.F_GradeTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_SetGradingTemplateData.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_DeleteGradeTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradeCategoryPositionDevaluation.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradeLevels.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradePositions.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradeTemplateById.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradeTemplatesPage.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_ListGradeCategories.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_SetGradeCategoryPositionDevaluation.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_SetGradeLevels.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_CloneGradeTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_SetInventoryGradeTemplate.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetContractTemplatesSimpleList.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_DeleteGradingCategory.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_GetGradeAttributeTypePositionStatus.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_IsGradePositionDevaluationUsed.sql" />
    <Build Include="Scripts\Updates\RSW-11755_change_recycling_order_customer.sql" />
    <Build Include="Import\item_masters\item masters categories.sql" />
    <Build Include="Import\item_masters\item masters substitutes.sql" />
    <Build Include="Import\item_masters\item masters.sql" />
    <None Include="Import\customers\addresses.sql" />
    <Build Include="Import\customers\customers.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetPriceByDevaluation.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_INVENTORY_STATUSES.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_LIST_SHIPPING_STATUSES.sql" />
    <Build Include="search\Tables\search.DocumentChanges.sql" />
    <Build Include="search\Stored Procedures\search.sp_SetEntityChanged.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetHashesForVerificationInventory.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsInventoryStructure.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentIdsRangeInventory.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetBatchOfUnprocessedChanges.sql" />
    <Build Include="search\Stored Procedures\search.sp_SetChangesProcessed.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDependentEntityIds.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsInventory.sql" />
    <Build Include="Security\search.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_AssetBulkInsertUid.sql" />
    <Build Include="Scripts\Clear\clear_item_master.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetLotDefaultReceiveRules.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommodityRuleDefaults.sql" />
    <Build Include="recycling\Tables\recycling.F_ServiceInternalCost.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_GetServiceInternalCostPage.sql" />
    <Build Include="search\Stored Procedures\search.sp_CleanupProcessedEntityChanges.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GetGradeLevel.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetGradingByDevaluationChange.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetOrdersTemplateList.sql" />
    <Build Include="recycling\Functions\recycling.fn_money_GetRecyclingOrderInternalCost.sql" />
    <Build Include="search\Views\search.vw_inventory.sql" />
    <Build Include="search\Views\search.vw_sku.sql" />
    <Build Include="dbo\Functions\tvf_GetSkuLocations.sql" />
    <Build Include="dbo\Views\vw_LocationDetails.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsSku.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsSkuStructure.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentIdsRangeSku.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetHashesForVerificationSku.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetIdByUid.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_BOL_NUMBER.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_SHIPPING_BOL_FOR_PDF_REPORT.sql" />
    <Build Include="Scripts\Updates\inbound_order_change_rep.sql" />
    <Build Include="recycling\Functions\recycling.fn_bigint_GetAssetGradingTemplateId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetDataDestructionForPdfReport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingInboundOrderByIdForPdfSimple.sql" />
    <Build Include="recycling\Stored Procedures\Grading\recycling.sp_ListAssetsGradeTemplates.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_SetContractFilesToInboundOrder.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryPartAddedTo.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetDataDestructionNestingState.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncAssetNestingToPartAddRemove.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncAssetDataDestructionToNestedAsset.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncNestedAssetToAssetDataDestruction.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_PRODUCT_MASTERS_MERGED.sql" />
    <Build Include="Scripts\Clear\clear_database_main_tables.sql" />
    <Build Include="Import\Import inventory via PO.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_SHIPPING_PACKAGE_GROUPS_BOL_FOR_PDF_REPORT.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetIdByUids.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetImportCapabilitiesColumns.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetImportTemplate.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetImportTemplateField.sql" />
    <Build Include="dbo\User Defined Types\recycling.AssetImportTemplateField.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetImportTemplateFields.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetImportTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_DeleteAssetImportTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetImportTemplates.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetImportTemplateFields.sql" />
    <None Include="Import\Import missing attributes and thier values from RZR_TEMPLATE.sql" />
    <Build Include="dbo\Stored Procedures\Picklists\dbo.sp_GetPicklistsPage.sql" />
    <Build Include="search\Stored Procedures\search.sp_SetChangeTrackingTriggersEnabled.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_MoveInventoryToPurchaseOrder.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_NOTIFICATIONS_IS_FINANCE_RELEASE_FROM_QUEUE.sql" />
    <Build Include="dbo\Views\dbo.vw_InventoryCapabilities.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_REBUILD_CATEGORY_CHILD_PATHS.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetAssetReconciled.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetShippingSystemSettingsInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRmaShippingCommoditiesByData.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetCategoryByFullPath.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UNALLOCATE_SALES_ORDER_ITEM_SIMILAR.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UNALLOCATE_ALL_SALES_ORDER_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\Sales\Shipments\dbo.sp_GetShipmentsPage.sql" />
    <Build Include="Import\CloneUser.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetSalesOrderFinanceRelease.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetCorrectCategoryId.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE_INITIAL_DETAIL_EXPORT.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_ALL_SALES_ORDER_ITEM_TAX_ENABLED.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_AddLogOverwriting.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetLogPage.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GetRecyclingItemMasterAvgPrices.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_CONFIG_VALID.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetBasic.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetNestingListItems.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetNestings.sql" />
    <Build Include="dbo\User Defined Types\Tuples\dbo.TupleBigintBigint.sql" />
    <Build Include="dbo\User Defined Types\Tuples\dbo.TubleBigintTinyint.sql" />
    <Build Include="dbo\User Defined Types\Tuples\dbo.TupleBigintNvarchar.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncPartAddToAssetNesting.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncPartRemoveToAssetNesting.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInventoryAuditedReport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_RECYCLING_ORDER_AUDIT_ITEMS_COLUMNS_BY_ORDER_ID.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetIdBySerials.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetSalesOrderActualShippingCost.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SetSalesOrdersActualShippingCost.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_ORDER_ITEMS_PRICES_FROM_CONTRACT.sql" />
    <Build Include="dbo\Stored Procedures\Sales\Shipments\dbo.sp_SetShippingPackages.sql" />
    <Build Include="dbo\User Defined Types\Sales\Shipments\dbo.SalesOrderItemPackage.sql" />
    <Build Include="dbo\User Defined Types\Sales\Shipments\dbo.ShippingPackage.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFilesList.sql" />
    <Build Include="dbo\Stored Procedures\Sales\Shipments\dbo.sp_SetShippingPriority.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteAsset.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAuditDataDestructionReportAssets.sql" />
    <Build Include="dbo\Tables\dbo.F_ItemInventoryKitHeader.sql" />
    <Build Include="dbo\Tables\dbo.F_ItemInventoryKit.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetItemInventoryKit.sql" />
    <Build Include="recycling\Functions\recycling.fn_bit_IsDataErasureRequired.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInventoryKit.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInventoryForKitBySerial.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSKUGeneratedByInventoryId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_DeleteItemInventoryKit.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFile.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetSalesOrderUnallocatedItemsImport.sql" />
    <Build Include="dbo\Functions\dbo.fn_float_GetInventoryCostWithPartAdded.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetCategoriesFlat.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetItemInventoryInboundOrder.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsUserHasPermissionForActions.sql" />
    <Build Include="dbo\User Defined Types\dbo.UnallocateImportModel.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetAssetsAllDevaluations.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SyncAssetGradeAttributeToFinalGrade.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_ADD_ADDRESS_FROM_MAGENTO.sql" />
    <Build Include="dbo\User Defined Types\MagentoAddress.sql" />
    <Build Include="dbo\Functions\dbo.fn_int_GetDefaultMeasureSystem.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_REPORT_BOL_AUDIT.sql" />
    <Build Include="dbo\User Defined Types\dbo.ImportModel.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetWorkflowStepsPage.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetWorkflowStep.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_DeleteAssetWorkflowStep.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingOutboundOrderByIdForReport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingInboundOrderByIdForReport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetAssetsDevaluations.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetImagesToPushSellerCloud.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetWorkflowStepsOrder.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_ListUserWarehousesId.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportsByIds.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCommodityByName.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommoditiesListWithWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomersPage.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomer.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetCustomerContactLastLogin.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetCustomerAttributeGridColumns.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetAttributesByAttributesSet.sql" />
    <Build Include="reports\Stored Procedures\sp_GetConsolidatedInvoice.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GET_GENERATED_INVENTORY_UID.sql" />
    <Build Include="cp\Tables\cp.D_CUSTOMER_SETTING.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SET_CUSTOMER_SETTINGS.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GET_CUSTOMER_SETTINGS.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GET_ASSET_CAPABILITY.sql" />
    <Build Include="dbo\Tables\dbo.C_Currency.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_ListCurrencyCodes.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetImagesToPushSellerCloud.sql" />
    <Build Include="reports\Stored Procedures\ReportBuilder\reports.sp_DeleteReportTemplate.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SetRecyclingItemMasterInline.sql" />
    <Build Include="reports\Stored Procedures\ReportBuilder\reports.sp_GetReportTemplate.sql" />
    <Build Include="reports\Stored Procedures\ReportBuilder\reports.sp_ListReportTemplates.sql" />
    <Build Include="reports\Stored Procedures\ReportBuilder\reports.sp_SetReportTemplate.sql" />
    <Build Include="dbo\Stored Procedures\Report\sp_GET_REPORT_RECYCLING_INBOUND_SETTLEMENT.sql" />
    <Build Include="reports\Tables\reports.F_REPORT_TEMPLATE.sql" />
    <Build Include="dbo\Tables\dbo.D_CurrencyExchange.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetListCurrencyExchange.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_DeleteCurrencyExchange.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_UpdateCurrencyExchangeRate.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_AddCurrencyExchange.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_ListCurrenciesNotInExchangeRate.sql" />
    <Build Include="dbo\Functions\fn_int_CompanyCurrencyCodeId.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GET_SALES_ORDERS_FOR_LOCKED.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderCurrency.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetSalesOrderCurrency.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetContactForPortalOrder.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLotInformationForLabel.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingOrderByIdToSettleReport.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingOrderItemServicesReport.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingOrderItemsForSettlementReport.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingOrderOutboundByIdToSettleReport.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCommodityByName.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportsByIds.sql" />
    <Build Include="Locks\sp_Locks.sql" />
    <Build Include="Locks\sp_Locks_All_Running.sql" />
    <Build Include="Locks\sp_Locks_Blocked_Chain.sql" />
    <Build Include="Locks\sp_Locks_Lead_Blocker_Process.sql" />
    <Build Include="dbo\User Defined Types\MasterRoleAvailableService.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportSalesOrderRmaInventory.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetSettlementCost.sql" />
    <Build Include="dbo\Functions\dbo.fn_money_GetAdjustedCost.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetAssetSettlementCosts.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetAssetSettlementCosts.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerAddressForSchedule.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetInboundOrderReadyToBePriced.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_INBOUND_ORDER_IS_READY_TO_BE_PRICED_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_INBOUND_FILE_IS_SHARED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_INBOUND_ORDER_FILE_INFO.sql" />
    <Build Include="recycling\User Defined Types\recycling.AssetExpenses.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SetAssetsExpenses.sql" />
    <Build Include="dbo\User Defined Types\MasterRoleAvailableService.sql" />
    <Build Include="dbo\Tables\dbo.F_DIMENSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_ITEM_DIMENSION.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_UPDATE_ITEM_DIMENSIONS_BY_ITEM_MASTER.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IS_SET_SKU_DIMENSIONS_ALLOWED.sql" />
    <Build Include="recycling\View\recycling.vw_AssetExpenses.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetTakebackOrder.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetTakebackOrdersList.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetTakebackStatusesCounts.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetTakebackStatusList.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderShipping.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetTakebackOrder.sql" />
    <Build Include="recycling\Tables\recycling.F_RecyclingOrderShipping.sql" />
    <Build Include="dbo\Tables\dbo.C_TakebackOrderStatus.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderShipping.sql" />
    <Build Include="dbo\User Defined Types\dbo.TakebackShippingPackage.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetShippingPackageSentLabelDt.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetShippingPackagesLabels.sql" />
    <Build Include="cp\Tables\cp.F_TakebackOrderRequest.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetTakebackOrderRequest.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetTakebackOrderRequestList.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_SetLocaionForInventory.sql" />
    <Build Include="dbo\Functions\fn_bigint_GetDefaultLocationIdByWarehouse.sql" />
    <Build Include="dbo\Functions\dbo.fn_money_GET_AR_OPEN_CUSTOMER_BALANCE.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_RECYCLING_ORDER_OUTBOUND_STATUSES.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCountryList.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSelfCustomerAddressByType.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftApiResult.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftApiLog.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetAssetsForMicrosoftReport.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetInboundOrderForMicrosoftReport.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftApiAssetResult.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftApiAssetResult.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftApiAssetsLog.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftReportSettings.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_SetMicrosoftReportSettings.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftReportSettings.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingReportsPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingReportDetails.sql" />
    <Build Include="cp\Tables\cp.F_TakebackOrderRequestEquipment.sql" />
    <Build Include="cp\Tables\cp.C_TakebackOrderRequestEquipment.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_TakebackOrderRequestEquipments.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetTakebackOrderRequestEquipment.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetTakebackOrderRequestEquipments.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetTakebackOrderRequest.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetEquipmentList.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_DeleteTakebackOrderRequestEquipment.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IS_KIT_ITEM_INVENTORY_NOT_AVAILABLE.sql" />
    <Build Include="dbo\User Defined Types\dbo.AssetImport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.spGetSalesOrderRmaActivity.sql" />
    <Build Include="reports\Stored Procedures\ConcurReport\reports.sp_GetConcurReportsPage.sql" />
    <Build Include="reports\Stored Procedures\ConcurReport\reports.sp_GetPurchaseOrderForConcurReport.sql" />
    <Build Include="reports\Tables\reports.F_ConcurApiResult.sql" />
    <Build Include="reports\Stored Procedures\ConcurReport\reports.sp_AddConcurApiLog.sql" />
    <Build Include="reports\Stored Procedures\ConcurReport\reports.sp_GetPurchaseOrderItemsForConcurReport.sql" />
    <Build Include="recycling\Tables\recycling.F_LocationAuditLot.sql" />
    <Build Include="recycling\Tables\recycling.F_LocationAudit.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLocationAuditStart.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLocationAuditLotStatus.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLocationAuditLot.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLocationAuditComplete.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLocationAuditingData.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLocationAuditedData.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLocationAuditData.sql" />
    <Build Include="dbo\Functions\fn_str_GET_NOTIFICATION_EMAIL_LIST_RR.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RR_SCHEDULED_FROM_QUEUE.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GetCustomerAddressId.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLocationAuditedLogData.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLocationAuditCounts.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetsWithNotCompleteMasterItems.sql" />
    <Build Include="recycling\Tables\recycling.F_AlternativeCommodityName.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SetAlternativeCommodityName.sql" />
    <Build Include="recycling\Functions\Commodities\recycling.fn_nvarchar_GetCommodityName.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomerAddressById.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetsPurchase.sql" />
    <Build Include="recycling\User Defined Types\recycling.AssetsPurchase.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCurrentLocationOfLot.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_RecyclingOrderItemFinalizeBuildUp.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_RecyclingOrderItemMerge.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_RecyclingOrderItemUnmerge.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderItemItemCount.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetSubstituteLotId.sql" />
    <Build Include="recycling\Stored Procedures\Lot\recycling.sp_GetLotsToConsumeInWarehouse.sql" />
    <Build Include="recycling\Stored Procedures\Lot\recycling.sp_SetLotsConsumed.sql" />
    <Build Include="recycling\Stored Procedures\Lot\recycling.sp_GetLotsAutoName.sql" />
    <Build Include="recycling\View\recycling.vw_PurchasedLots.sql" />
    <Build Include="dbo\Functions\dbo.fn_money_GetEstimatedProfit.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_DELETE_ITEM_INVENTORY_IMAGE.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryItemAuditStep.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCompletedInboundOrders.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByWeek.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByMonth.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRevenuePerDay.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTodaysRevenue.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventoryReceivedByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCreatedOutboundOrders.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesOrderProfitabilityByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSkuQualifiedByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldInventoryReceivedByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCreatedInboundOrders.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSettledInboundOrders.sql" />
    <Build Include="bi\Functions\bi.tvf_GetOverallSalesOrderProfitability.sql" />
    <Build Include="bi\Functions\bi.tvf_GetEmployeeProductivity.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesOrderProfitabiolity.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsAuditedByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetItemsSold.sql" />
    <Build Include="Security\bi.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCompanyUtcOffset.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCompanyId.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCompanyDomain.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCompanyCurrency.sql" />
    <Build Include="recycling\Stored Procedures\Inventory\recycling.sp_GetRecyclingInventoryDetailPage.sql" />
    <Build Include="recycling\Stored Procedures\Lot\recycling.sp_GetMergedLotsPage.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetWarehouseIdBySku.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetInventoryCapabilityValueByCapabilityTypeId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetSkuSplitByWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCapabilityTypeSkuEffecting.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSkuSplitByWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCapabilityTypeByName.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetWarehouseCapabilityId.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_CONTRACT_RESALE_PRICING_OTHER_ASSET.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_CleanUnallowedFileNameChars.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLotNoExternalLotsMergedInto.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_ASSET_AUDIT_COMPLETE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_LOADING_COMPLETE_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetInboundOrderAndContractAutoNames.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GetRmaOrderBasicInfo.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_LOT.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_RECYCLING_ORDER_OUTBOUND.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_REPORT_INVENTORY_AUDIT_ITEMS_BY_STATUS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetItemImageSizes.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetRecyclingOrderAssets.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetDataDestruction.sql" />
    <Build Include="recycling\Functions\recycling.fn_bit_IsLotAvailableForAudit.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetItemMasterSubBuildUps.sql" />
    <Build Include="integration\Tables\integration.F_FieldValueSubstitution.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetFieldValueSubstitution.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetFieldValueSubstitutions.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolFieldValueSubstitutions.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_DeleteFieldValueSubstitution.sql" />
    <Build Include="dbo\Views\dbo.vw_InventoryDetailsMain.sql" />
    <Build Include="dbo\Views\dbo.vw_InventoryDetails.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_ITEM_INVENTORY_RECV_COUNT.sql" />
    <Build Include="dbo\Stored Procedures\Attriubtes\dbo.sp_GetAttributeSetAttriubtesFlat.sql" />
    <Build Include="dbo\Stored Procedures\Manufacturer\dbo.sp_GET_MANUFACTURERS_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Manufacturer\dbo.sp_GET_MANUFACTURER_BY_ITEM_IDS.sql" />
    <Build Include="dbo\Stored Procedures\Manufacturer\dbo.sp_GET_MANUFACTURER_ITEM_MASTERS.sql" />
    <Build Include="dbo\Stored Procedures\Capabilities\dbo.sp_GET_INVENTORY_CAPABILITIES_BY_ID.sql" />
    <Build Include="dbo\Stored Procedures\Capabilities\dbo.sp_GET_INVENTORY_CAPABILITY_BY_TYPE_ID.sql" />
    <Build Include="dbo\Stored Procedures\Model\dbo.sp_GET_ITEM_MASTERS_BY_ID.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SetAssetsSettlementFmvPrices.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_GetInboundOrderAssetsByFmvPricingFilter.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_CONTRACT_RESALE_PRICING_CLONE.sql" />
    <Build Include="recycling\Stored Procedures\Inventory\recycling.sp_GetInventoryWorkflowTypeCounts.sql" />
    <Build Include="Scripts\Updates\Fix Grade C with tab char.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetInboundOrderContractFmvTemplate.sql" />
    <Build Include="recycling\Functions\recycling.fn_int_Count0FmvPriceAssetsInInboundOrder.sql" />
    <Build Include="recycling\Stored Procedures\Location\recycling.sp_GetCurrentLocationOfMovableLocation.sql" />
    <Build Include="recycling\Stored Procedures\Location\recycling.sp_RelocateMoveableLocation.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetReceivedFromConsumedLot.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetLotConsumed.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderItemCommodity.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderItemTareWeight.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderItemGrossWeight.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderItem.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingAssetsReportsPage.sql" />
    <Build Include="Security\rzrapi.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryAttributeTypesEx.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommoditiesListEx.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetCustomerAddressLocationName.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftRecyclingAssetsReportData.sql" />
    <Build Include="reports\Tables\reports.F_AssetAdditionalData.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftReportAssetImport.sql" />
    <Build Include="reports\Tables\reports.C_UnitStatus.sql" />
    <Build Include="reports\Tables\reports.C_DispositionType.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.GetUnitStatuses.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.GetDispositionTypes.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderClone.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderItemByAutoName.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_IsUniqueSerialForUid.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolReportField.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolReportFieldsPage.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_DeleteAuditToolReportField.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_UndoRecyclingOrderItemsSorted.sql" />
    <Build Include="rzrapi\Customer\rzrapi.sp_GetCustomer.sql" />
    <Build Include="rzrapi\Customer\rzrapi.sp_SetCustomer.sql" />
    <Build Include="rzrapi\User\rzrapi.sp_GetUserToken.sql" />
    <Build Include="rzrapi\User\rzrapi.sp_SetUserToken.sql" />
    <Build Include="rzrapi\User\rzrapi.sp_GetUser.sql" />
    <Build Include="rzrapi\Customer\Address\rzrapi.sp_GetCustomerAddress.sql" />
    <Build Include="rzrapi\Customer\Address\rzrapi.sp_SetCustomerAddress.sql" />
    <Build Include="rzrapi\Customer\TransactionTerms\rzrapi.sp_GetTransactionTerms.sql" />
    <Build Include="rzrapi\Tax\rzrapi.sp_GetTax.sql" />
    <Build Include="rzrapi\Customer\Tax\rzrapi.sp_GetCustomerTaxes.sql" />
    <Build Include="rzrapi\Customer\Tax\rzrapi.sp_SetCustomerTaxes.sql" />
    <Build Include="rzrapi\Asset\rzrapi.sp_GetAssetBySerial.sql" />
    <Build Include="rzrapi\Asset\rzrapi.sp_GetAssetByUid.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsCustomerInternational.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetScrapItemsInLocation.sql" />
    <Build Include="reports\Tables\reports.F_RecyclingOrderAdditionalData.sql" />
    <Build Include="dbo\Functions\dbo.fn_nvarchar_MakeKIDNumber.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LogRecyclingOrderItemsMerged.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LogInventoryAttributeMerged.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LogMergeItemMasters.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetInvoiceKidNumber.sql" />
    <Build Include="recycling\View\recycling.vw_InboundTransferredLots.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetUsersFullname.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_GetHardDrivesPage.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_GetHardDriveByUidOrSerial.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_SetHardDrives.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_DeleteHardDrives.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.GetPurchaseOrderTypes.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingCreditSummaryReportPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftRecyclingOrderReportData.sql" />
    <Build Include="reports\Tables\reports.C_PurchaseOrderType.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftApiResult.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftReportOrderImport.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftReportInvoiceSummaryImport.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsCategoryExistByFullPath.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsAutoApplySalesTax.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftReportDataBearingDevicesImport.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_UPDATED_USER.sql" />
    <Build Include="rzrapi\Payment\rzrapi.sp_GetInvoicePaymentByTransactionId.sql" />
    <Build Include="rzrapi\Payment\rzrapi.sp_GetPageOfPayments.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLotSla.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_CurrencyExchangeIdByLocalCurrency.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_CompanyHomeCurrencyExchangeId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_MultiplySalesOrderPrices.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_MultiplyPurchaseOrderPrices.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetFormattedCurrencyMark.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCapabilityAllSets.sql" />
    <Build Include="dbo\Stored Procedures\dbo.spGetReportInventoryCount.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftPayments.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftPaymentsPage.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftPaymentNotificationsImport.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_SetMicrosoftPayments.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_DeleteMicrosoftPayment.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftPaymentNotificationsLog.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftPayments.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingDataBearingDevicesReportPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingInvoiceSummaryReportPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftRecyclingDataBearingDeviceReportData.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftRecyclingInvoiceSummaryReportData.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftPaymentNotificationResult.sql" />
    <Build Include="dbo\Stored Procedures\dbo.spGetReportInventory.sql" />
    <Build Include="dbo\Functions\dbo.fn_nvarchar_GetFormattedExchangeRate.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetListCurrencyExchangeHistory.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetCurrencyExchangeInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_MultiplyRecyclingOrderPrices.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetContractCurrency.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_MultiplyOutboundOrderPrices.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_MultiplyContractPrices.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetContractCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetExplicitCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetPurchaseOrderCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetRecyclingOrderCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetSalesOrderCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetInvoiceCurrencyInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCustomerSubAccount.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomerSubAccounts.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_DelCustomerSubAccount.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LogCurrencyUpdate.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerSalesOrdersList.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetCustomerInboundOrders.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCustomerSubAccountValidate.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCustomerWhitelistInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomerWhitelistInfo.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetDataErasuresForExport.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_UpdateAuditToolReport.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryItemLotAutoName.sql" />
    <Build Include="dbo\Tables\dbo.C_DestructionType.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_ListDestructionTypes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListCurrencyExchange.sql" />
    <Build Include="rzrapi\Settings\rzrapi.sp_GetFastSearchSettings.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\dbo.sp_SetInvoiceCreatedDate.sql" />
    <Build Include="integration\Tables\integration.F_DefaultAttributeValues.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolDefaultAttributeValues.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolDefaultAttributeValue.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_CheckAllRequiredAssetsFieldsAreCompleted.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderContractIsFirstApplied.sql" />
    <Build Include="dbo\Views\dbo.vw_F_ITEM_INVENTORY_AVAILABLE_FORSKU_REBUILD.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GetItemStatusIdByAssetWorkflowId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRebuildForSkuGenerateWorkflow.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetCategoryHierarchyFlat.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetItemInventories.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAtpQuantityByIpn.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAtpPageByIpn.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSku.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetItemInventorySubstitutes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetManufacturers.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetEbayCategoryHierarchyFlat.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SetIntegrationOrderShippingInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderCustomerData.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetProductCodes.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetCreditMemoCurrencyInfo.sql" />
    <Build Include="dbo\Views\CreditMemo\dbo.vw_CreditMemoAgainstInvoice.sql" />
    <Build Include="dbo\Views\CreditMemo\dbo.vw_CreditMemoInvoiced.sql" />
    <Build Include="dbo\Views\Payment\dbo.vw_PaymentsByCredits.sql" />
    <Build Include="dbo\Views\CreditMemo\dbo.vw_CreditMemoAgainstRma.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInvoiceCanBePaid.sql" />
    <Build Include="dbo\User Defined Types\dbo.CreditMemoItem.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsSalesOrderCanBeDeleted.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRmaCanBeVoidedData.sql" />
    <Build Include="dbo\Views\CreditMemo\dbo.vw_CreditMemoApplied.sql" />
    <Build Include="dbo\Tables\CreditMemo\dbo.F_CreditMemo.sql" />
    <Build Include="dbo\Tables\CreditMemo\dbo.F_CreditMemoItem.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_SetCreditMemo.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_SetCreditMemoAPInvoice.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_SetCustomerCredit.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_GetRmaCreditMemoData.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_DeleteCreditMemo.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_GetCreditMemo.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_GetCreditMemoItems.sql" />
    <Build Include="dbo\Stored Procedures\CreditMemo\dbo.sp_GetCreditMemoList.sql" />
    <Build Include="dbo\Functions\CreditMemo\dbo.fn_bit_IsCreditMemoCanBeChanged.sql" />
    <Build Include="dbo\Functions\CreditMemo\dbo.fn_money_GetCreditMemoRemainingCreditAmount.sql" />
    <Build Include="reports\Stored Procedures\CreditMemo\reports.sp_GetCreditMemoData.sql" />
    <Build Include="reports\Stored Procedures\CreditMemo\reports.sp_GetCreditMemoItems.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInvoiceCanBeReopen.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSalesOrderItemInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSalesOrderInvoicePaymentInfo.sql" />
    <Build Include="dbo\Functions\CreditMemo\dbo.fn_money_GetCreditMemoAmount.sql" />
    <Build Include="dbo\Views\CreditMemo\dbo.vw_CreditMemo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_CHECK_ORDER_INVOICE_PAID.sql" />
    <Build Include="rzrapi\CreditMemo\rzrapi.sp_GetCreditMemosFromId.sql" />
    <Build Include="rzrapi\CreditMemo\rzrapi.sp_GetCreditMemoItems.sql" />
    <Build Include="rzrapi\Credit\rzrapi.sp_GetCredits.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetClientPortalOrderReportType.sql" />
    <Build Include="dbo\User Defined Types\ClientPortalReport.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInventoryCanBePartRemoved.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInventoryCanBePartAdded.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetInventoryForAddRemoveParts.sql" />
    <Build Include="dbo\User Defined Types\dbo.TubleBigintTinyintBigint.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetPageOfCustomerRecyclingOrders.sql" />
    <Build Include="rzrapi\Invoice\Ar\rzrapi.sp_GetSalesInvoicesFromId.sql" />
    <Build Include="rzrapi\Invoice\Ar\rzrapi.sp_GetSalesInvoiceSoldItems.sql" />
    <Build Include="rzrapi\Invoice\Ap\rzrapi.sp_GetApInvoicesFromId.sql" />
    <Build Include="dbo\Functions\CreditMemo\dbo.fn_money_GetCreditMemoTaxRefundAmount.sql" />
    <Build Include="dbo\Functions\CreditMemo\dbo.fn_money_GetCreditMemoItemsCost.sql" />
    <Build Include="rzrapi\Invoice\Ap\rzrapi.sp_SetCreditMemoApInvoice.sql" />
    <Build Include="rzrapi\Invoice\Ap\rzrapi.sp_GetApInvoiceItems.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftRecyclingReportsCustomers.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventorySoldByCustomerRep.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContacts.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContactAddresses.sql" />
    <Build Include="rzrapi\Customer\Address\rzrapi.sp_GetCustomerAddresses.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAssets.sql" />
    <Build Include="recycling\Tables\recycling.F_ConsumeLaborManagement.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetConsumeLotsManagementPage.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetConsumeLotsManagementPage.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_IsHardDriveSerialNumberUnique.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryUid.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotIds.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotNetWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssets.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssetsWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryUidAvailableInResaleChannels.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByCount.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryPriceAvailableInResaleChannels.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventoryStockAgingReport.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLotAging.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityGroupedByWorkflow.sql" />
    <Build Include="bi\Functions\bi.tvf_GetGroupedCommodity.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetWorkflowList.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfAssetsInProductionByCount.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsInProductionByWorkflow.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersByRepUnsettled.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesOrdersNotInvoiced.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_KIT_RELATED_SKUS.sql" />
    <Build Include="dbo\Stored Procedures\Inventory\dbo.sp_GetInventoryHavingCapabilityValue.sql" />
    <Build Include="dbo\Stored Procedures\ItemMasters\dbo.sp_ListItemMastersOfAttributeSet.sql" />
    <Build Include="Security\queue.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByLocationIds.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByLotIds.sql" />
    <Build Include="queue\Tasks\queue.sp_PutRebuildTask.sql" />
    <Build Include="dbo\Views\dbo.vw_F_ITEM_INVENTORY_FILTERED_FORSKU_REBUILD.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\dbo.sp_INSERT_SKU_AND_SET_INVENTORY_SKU_ID.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\dbo.sp_REBUILD_F_ITEM_SKU_QTY.sql" />
    <Build Include="queue\Tasks\queue.sp_RebuildInventorySkuIds.sql" />
    <Build Include="rzrapi\Account\rzrapi.sp_GetAccounts.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetSkuCapabilities.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderLocationDetails.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderEquipment.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetCustomerAddressLocationDetails.sql" />
    <Build Include="dbo\Stored Procedures\SP_SET_LOG_AFTER_INSERT_INVOICE.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetUserByIdOrLogin.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetShippingPackagesBolForPdfReport.sql" />
    <Build Include="rzrapi\Account\rzrapi.sp_GetEntityAccounts.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_LaborHoursRepresentation.sql" />
    <Build Include="bi\Functions\bi.tvf_GetProductionManagementTotals.sql" />
    <Build Include="bi\Functions\bi.tvf_GetProductionManagement.sql" />
    <Build Include="bi\Functions\bi.tvf_GetEmployeeList.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityList.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByAssetIds.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetCustomerContacts.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetCustomers.sql" />
    <Build Include="Security\clientportal.sql" />
    <Build Include="Storage\Stored Procedures\storage.sp_GetRegisterInfo.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderByIdOrName.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderItemByAutoName.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetUsers.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_SetInboundOrderPriority.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolService.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByInventoryKitIds.sql" />
    <Build Include="queue\queue.sp_RunDwComFullRebuild.sql" />
    <Build Include="queue\queue.sp_RunDwComJob.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_REBUILD_F_ITEM.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_REBUILD_F_ITEM_MASTER_SALES_MEASURE.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_REBUILD_F_ITEM_SKU_ATTRB_VALUE.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_SET_ACTIVE_ITEMS_SALES_BUILD.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_SET_ITEM_SALES_BUILD.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_SYS_HEALTH_TABLE_MAINTENANCE.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by SQL Agent\dbo.sp_REBUILD_C_ITEM_JOB_BY_ITEM.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrders.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_Age.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by SQL Agent\dbo.sp_REBUILD_C_ITEM_JOB_BY_ITEM_MASTER.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by SQL Agent\dbo.sp_REBUILD_C_ITEM_JOB.sql" />
    <Build Include="queue\dbo.sp_BufferDependentRebuild.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_SET_SHIPPING_DATA_FROM_MAGENTO_DATA.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_REBUILD_ALL_F_ITEM_IDS.sql" />
    <Build Include="queue\Queries\queue.sp_GetInventorySkuIdsQualified.sql" />
    <Build Include="queue\Queries\queue.sp_GetSkuIdsQualified.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByItemMasterIds.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByPrimaryCategoryIds.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\invoked by DW_COM\dbo.sp_SET_ITEM_PRICING_REQUEST.sql" />
    <Build Include="bi\Functions\bi.tvf_GetItemsSoldWithAdditionalInfo.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfSoldItemsByCount.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfSoldItemsByWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByWeekWithAdditionalInfo.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByMonthWithAdditionalInfo.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSalesByWeek.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSalesByMonth.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldItemsTotalPrice.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldItemsTotalQty.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldCommodityTotalPrice.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldCommodityTotalWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesOrders.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByCustomer.sql" />
    <Build Include="bi\Functions\bi.tvf_GetOutboundOrdersUnsettled.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_GetCustomerIdByClientPortalContactLogin.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetContractGeneralInfo.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetContractConsignmentRules.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderItems.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContractsGeneralInfo.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContractGeneralInfo.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContractConsignments.sql" />
    <Build Include="bi\Functions\bi.tvf_GetProductivityConsumeLaborManagementByEmployee.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsBySkuIds.sql" />
    <Build Include="dbo\Tables\F_ITEM_LIST_CATEGORY_OPTIONS.sql" />
    <Build Include="dbo\Functions\dbo.fn_varchar_F_ITEM_LIST_COMMON_CATEGORY.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTodaysRevenueByOrderAndCustomer.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRevenuePerDayByOrderAndCustomer.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetLotsByRecyclingOrderId.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetSalesOrderUnallocatedItems.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetSalesOrderData.sql" />
    <Build Include="dbo\Stored Procedures\Invoice\dbo.sp_SET_SENDER_DATA_IVOICE.sql" />
    <Build Include="queue\Queries\Inventory\Split\queue.sp_GetInventoryIdsSplitFromIds.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetSalesOrdersToCreateInvoice.sql" />
    <Build Include="dbo\Views\dbo.vw_LocationItemCounts.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetServiceRequirements.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetOrderScheduledServiceRequirements.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_GetOrderScheduledServiceRequirements.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetSellerCloudOrderItemsToPushInRazor.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SyncCustomerFromSellerCloudOrdersToRazor.sql" />
    <Build Include="dbo\Views\dbo.vw_InventoryKitStatusInfo.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCustomerSettings.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomerSettings.sql" />
    <Build Include="clientportal\User defined Types\clientportal.bigint_ID_ARRAY.sql" />
    <Build Include="dbo\Stored Procedures\sp_CheckBeforeDeletionAuditItems.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetSalesOrdersAdminInfo.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_IsCanDeleteInventoryByAuditSession.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetPageOfUndeletableAssetsByAuditSession.sql" />
    <Build Include="queue\Queries\Inventory\queue.sp_GetInventoryIdsByAuditSessionIds.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsPricingTemplateAssigned.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetResalePricingRulesByIds.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetResalePricingRulesByGlobalPricing.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetContractResalePricingRuleImport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetContractResalePricingRecalculate.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_ListGlobalPricings.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetManufacturersByLabels.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetItemMastersByLabels.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetContractsByResalePricing.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetAttributeIdsByValues.sql" />
    <Build Include="dbo\User Defined Types\dbo.nvarchar_ARRAY.sql" />
    <Build Include="dbo\User Defined Types\dbo.ModelManufacturer.sql" />
    <Build Include="dbo\User Defined Types\dbo.ContractResalePricingRuleImport.sql" />
    <Build Include="dbo\User Defined Types\dbo.AttributeSetAttributeModel.sql" />
    <Build Include="rzrapi\Account\rzrapi.sp_GetAccount.sql" />
    <Build Include="recycling\Tables\recycling.F_AttributeValuePricing.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SaveAttributeValuePricingItem.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAttributeValuePricingData.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAttributeTypeAttributeValues.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAttributeSetListWithStatus.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteAttributeValuePricing.sql" />
    <Build Include="dbo\User Defined Types\dbo.AttributeValuePricingItem.sql" />
    <Build Include="recycling\Functions\recycling.fn_bigint_GetAssetIdByDataDestructionId.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetCancelledSellerCloudOrdersToDelete.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_SyncSalesOrdersFromSellerCloudToRazor.sql" />
    <Build Include="recycling\Stored Procedures\Location\recycling.sp_UnlinkScrapItemsFromLocation.sql" />
    <Build Include="dbo\Stored Procedures\rebuild_sp\dbo.sp_ManuallySetInventorySkuIdAndRebuild.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_GetCustomerMainAddressStringByType.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_IsHeciEnabled.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_ObfuscateEmail.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_ObfuscatePhone.sql" />
    <None Include="Move to Dev\Use local CompanyId, sync Self-customer Id.sql" />
    <None Include="Move to Dev\Disable notifications and change triggers.sql" />
    <None Include="Move to Dev\Obfuscate contact data.sql" />
    <Build Include="dbo\Stored Procedures\sp_SetCustomerClientPortalGeneralSettings.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetOrdersByFMVTemplate.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_SetPortalQuoteFile.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_DeletePortalQuoteFile.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_GetListServiceRequirements.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_DeletePortalQuote.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_GetPortalQuote.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_GetPageOfPortalQuotes.sql" />
    <Build Include="rzrapi\PortalQuote\rzrapi.sp_SetPortalQuote.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAttributeTypeAttributeValue.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_ListAttributeSetAttributeTypes.sql" />
    <Build Include="dbo\User Defined Types\dbo.guid_array.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRestoredWeightOnDelete.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetParentAssetWeightByNestedAsset.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetParentAssetWeight.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetWeightByNestedAsset.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_NOTIFICATIONS_ALL_LOTS_AUDIT_COMPLETE_FROM_QUEUE.sql" />
    <Build Include="dbo\Tables\dbo.F_SHIPPING_PRESETS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetShippingPresets.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SET_SHIPPING_PRESET.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_DELETE_ITEM_SHIPPING_PRESET.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListRecyclingWorkflowTypeWithUses.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListAllowedCommoditiesForInboundOrder.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetLocations.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingOrderSubtotalForReport.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAssetAuditToolReport.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\dbo.sp_UpdateRecyclingOrderItemPriceDataBulk.sql" />
    <Build Include="dbo\User Defined Types\dbo.RecylingOrderPriceParams.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetSalesOrderReps.sql" />
    <Build Include="dbo\Tables\dbo.F_SalesOrderRepUser.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetSalesOrderReps.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetSalesOrderReps.sql" />
    <Build Include="dbo\Functions\fn_str_GET_NOTIFICATION_EMAIL_LIST_REPS.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_ListInboundOrderPriorities.sql" />
    <Build Include="clientportal\Tables\clientportal.F_UserSetting.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_UPD_SALES_ORDER_ITEM_PRICE_BULK.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetAssetsReceivedReport.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderItem.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderItem.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_DeleteRecyclingOrderItemImages.sql" />
    <Build Include="recycling\Functions\recycling.fn_nvarchar_json_GetLotImages.sql" />
    <Build Include="dbo\Stored Procedures\CurrencyInfo\dbo.sp_GetCurrencyExchange.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetContainerLotInformationForLabel.sql" />
    <Build Include="recycling\Tables\recycling.F_RemoteScales.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderServices.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_CUSTOM_INVENTORY_SOLD.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateAsset.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetAssetDataDestruction.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_CreateAsset.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsRedeploymentSalesOrder.sql" />
    <Build Include="dbo\Views\dbo.vw_RedeploymentInventory.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsRedeploymentAllocatedInSalesOrder.sql" />
    <Build Include="recycling\Stored Procedures\RemoteScale\recycling.sp_GetRemoteScales.sql" />
    <Build Include="recycling\Stored Procedures\RemoteScale\recycling.sp_SetRemoteScales.sql" />
    <Build Include="recycling\Stored Procedures\RemoteScale\recycling.sp_DeleteRemoteScales.sql" />
    <Build Include="recycling\Stored Procedures\RemoteScale\recycling.sp_GetRemoteScalesPage.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAvailablePrinters.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_SERVICE_SUMMARY.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_REPORT_SERVICE_SUMMARY.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingInboundOrdersStatusCounts.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderFiles.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetInformationForReturnAssetBySerial.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_DeleteTakebackOrderRequestEquipment.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_GetTakebackOrderRequest.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_GetTakebackOrderRequestEquipments.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_GetTakebackOrderRequestList.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_SetTakebackOrderRequest.sql" />
    <Build Include="clientportal\Stored Procedures\TakebackOrder\clientportal.sp_SetTakebackOrderRequestEquipment.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_AddScheduledOrderFile.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_CreateEmptyRequisitionRequestSalesOrder.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_CreateSalesOrderItemUnallocated.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_DeleteSalesOrder.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_DeleteSalesOrderItemUnallocated.sql" />
    <Build Include="clientportal\Stored Procedures\Outbound\clientportal.sp_GetOutboundOrder.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_DeleteScheduledOrderFile.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContact.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContactAddresses.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContactDashboardInfo.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetPageOfCustomerContacts.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetCustomerContactAddresses.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetCustomerContactCredential.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_UpdateCustomerContact.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_AddNewCustomerAccountNotificationToQueue.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_AddResetCustomerPasswordNotificationToQueue.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetPageOfSalesOrderItems.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetPageOfSalesOrderItemsToSearch.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrder.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderDynamicInfo.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderShippingMethods.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderShippingPayorTypes.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderShippingProviderMethods.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderShippingProviders.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderShippingStatuses.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetSalesOrderStatuses.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_GetScheduledOrder.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_GetScheduledOrderFiles.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_SetSalesOrder.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_SetOrderScheduledServiceRequirements.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_UpdateSalesOrderItemUnallocated.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_SetScheduledOrder.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetContactDefaultAddress.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomer.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAddressById.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAddressList.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAddressLocationDetails.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContactInfoByLogin.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerOnsiteContactList.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetClientPortalContactList.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetClientPortalCustomersList.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetContactByLoginAndEmail.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAddressListForContact.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetPageOfCustomerOpenInvoices.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetPageOfCustomerTransactionLogItems.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerFinancialInfoBlocks.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetAvailableClientPortalReportsForCustomer.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetInboundOrderStatuses.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetShippingStatuses.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetWarehouseList.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetWeightMeasuringSystems.sql" />
    <Build Include="clientportal\Stored Procedures\Settings\clientportal.sp_GetCurrencyInfo.sql" />
    <Build Include="clientportal\Stored Procedures\Settings\clientportal.sp_GetUserSettings.sql" />
    <Build Include="clientportal\Stored Procedures\Settings\clientportal.sp_SetUserSettings.sql" />
    <Build Include="clientportal\Stored Procedures\SalesOrder\clientportal.sp_GetPageOfCustomerResaleOrders.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetOutboundOrderStatuses.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_GetPageOfCustomerRecyclingOrders.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_GetCustomerOrdersAutocomlete.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetAssetCapabilities.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetAsset.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetCustomerAssetVisionStatistic.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAvailablePages.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerOrdersStats.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetPageOfCustomerAssetVisionItems.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetPageOfCustomerInventoryItemsSummary.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetTopCaptegoriesOfAssetsByCount.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetCustomerAvailableGridColumns.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetPageOfCustomerInventoryItems.sql" />
    <Build Include="clientportal\Stored Procedures\ScheduledOrder\clientportal.sp_GetOrderScheduledServiceRequirements.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_GetInventoryStatuses.sql" />
    <Build Include="rzrapi\Invoice\Ap\rzrapi.sp_GetApInvoicesChangedBetween.sql" />
    <Build Include="rzrapi\Invoice\Ar\rzrapi.sp_GetFinalizedSalesInvoicesBetween.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetSalesOrderAllocatedItems.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInboundCommoditiesForReport.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLog.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetDefaultTransferLocationForRecyclingOrders.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrder\recycling.sp_GetInboundOrdersStatus.sql" />
    <Build Include="recycling\Commoidty\Profile\Stored Procedures\Items\recycling.sp_GetCommodityProfileItems.sql" />
    <Build Include="recycling\Commoidty\Profile\Stored Procedures\Items\recycling.sp_SetCommodityProfileItem.sql" />
    <Build Include="recycling\Commoidty\Profile\Stored Procedures\recycling.sp_GetCommodityProfiles.sql" />
    <Build Include="recycling\Commoidty\Profile\Stored Procedures\recycling.sp_SetCommodityProfile.sql" />
    <Build Include="recycling\Commoidty\Version\Tables\recycling.F_CommodityVersion.sql" />
    <Build Include="recycling\Commoidty\Version\Stored Procedures\recycling.sp_SetCommodityVersion.sql" />
    <Build Include="recycling\Commoidty\Profile\Stored Procedures\recycling.sp_DeleteCommodityProfile.sql" />
    <Build Include="dbo\Functions\System\dbo.str_StringMatchStatementByPattern.sql" />
    <Build Include="dbo\Functions\System\dbo.str_ConditionStatement.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetOutboundOrderImportLots.sql" />
    <Build Include="dbo\User Defined Types\dbo.OutboundLotImportModel.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GET_RECYCLING_ORDER_ITEM_TRANSFER.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Input\recycling.sp_GetShredderInputs.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Input\recycling.sp_SetShredderInput.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Location\recycling.sp_SetShredderIoLocation.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Commodity\recycling.sp_GetShredderOutputCommodities.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Commodity\recycling.sp_SetShredderOutputCommodities.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Scales\recycling.sp_GetShredderOutputScales.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\Scales\recycling.sp_SetShredderOutputScales.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\recycling.sp_GetShredderOutputs.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\IO\Output\recycling.sp_SetShredderOutput.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\recycling.sp_GetShredders.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Stored Procedures\Shredder\recycling.sp_SetShredder.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Batch\recycling.F_BatchOfLots.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Batch\recycling.F_IncludedLotInBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Batch\recycling.F_SortedLotFromBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder\recycling.F_Shredder.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder\recycling.F_ShredderInput.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder\recycling.F_ShredderOutput.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder\recycling.F_ShredderOutputCommodity.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\recycling\Tables\Shredder\recycling.F_ShredderOutputScales.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Batch\rzrapi.sp_DeleteBatchOfLots.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Batch\rzrapi.sp_GetPageOfBatches.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Batch\rzrapi.sp_SetBatchOfLots.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots\rzrapi.sp_AdjustOrderForMergedWrapperLot.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots\rzrapi.sp_CheckLotsAreSettled.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots\rzrapi.sp_CreateEmptyMergedWrapperLot.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots\rzrapi.sp_ResetLotsWeightsToZero.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Lots\rzrapi.sp_SwitchLotsWorkflowStep.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_DeleteSortedLotFromBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_ExcludeLotsFromBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_GetPageOfLotsInBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_GetPageOfLotsSortedFromBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_GetPageOfLotsSuitableForBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_IncludeLotsIntoBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_ReprocessLotSortedFromBatch.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_SetBatchLotsNotes.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_SetBatchLotsStatus.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\LotsInBatch\rzrapi.sp_SetSortedLotFromBatch.sql" />
    <Build Include="recycling\Commoidty\Profile\Tables\recycling.F_CommodityProfile.sql" />
    <Build Include="recycling\Commoidty\Profile\Tables\recycling.F_CommodityProfileItem.sql" />
    <Build Include="recycling\Commoidty\Profile\User Defined Types\recycling.udtt_CommodityProfileItem.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_DeleteDemanJobOutputLot.sql" />
    <Build Include="dbo\Views\dbo.vw_LocationDetailsAll.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInventoryHasPartAdded.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSkuImages.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\InputLots\recycling.sp_GetDemanJobInputLots.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\InputLots\recycling.sp_SetDemanJobInputLots.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\InputLots\recycling.sp_SetDemanJobInputLotStatus.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\Job\recycling.sp_DeleteDemanJob.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\Job\recycling.sp_GetDemanJobs.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\Job\recycling.sp_SetDemanJob.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\JobTypes\recycling.sp_ListDemanJobTypes.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\OutputLots\recycling.sp_GetDemanJobOutputLots.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Stored Procedures\OutputLots\recycling.sp_SetDemanJobOutputLot.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\Job\Tables\recycling.C_DemanJobType.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\SortingArea\Stored Procedures\recycling.sp_GetSortingAreas.sql" />
    <Build Include="BusinessLogic\Sorting\Deman\recycling\SortingArea\Stored Procedures\recycling.sp_SetSortingArea.sql" />
    <Build Include="bi\Functions\bi.tvf_GetWarehouseList.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetApInvoice.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_GetPageOfOutboundOrders.sql" />
    <Build Include="BusinessLogic\Sorting\recycling.sp_SetBatchOfLotsEstimatedValues.sql" />
    <Build Include="BusinessLogic\Sorting\recycling.sp_ResetBatchOfLotsEstimatedValues.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetWorkflowListForProductivityTracking.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLogProductivityByUserAndCategory.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLogProductivityByUser.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetWorkflowListForProductivityTrackingSpecific.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLogProductivityByUserSpecific.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLogProductivityByUserAndCategorySpecific.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportRecyclingConsumeActions.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetDataDestructionNesting.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetOnHandInventory.sql" />
    <Build Include="bi\Functions\bi.tvf_GetDetailsOfAssetWorkflowLogProductivityByUserAndCategorySpecific.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetReportWeightDiscrepancy.sql" />
    <Build Include="BusinessLogic\Sorting\Analysis\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfBatchAnalysisInputView.sql" />
    <Build Include="BusinessLogic\Sorting\Analysis\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfBatchAnalysisOutputView.sql" />
    <Build Include="BusinessLogic\Sorting\Analysis\rzrapi\Functions\rzrapi.fn_money_GetLotPartialSalesPriceByWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfSoldItemsByWarehouseCount.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByWeekByWarehouseWithAdditionalInfo.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByMonthAndWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRevenuePerDayByOrderAndCustomerByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSalesByWeekByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSalesByMonthByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetReceivedWeightByMonthAndWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventoryStockAgingReportByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetDemanReceivedWeightByMonthAndWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetSalesByMonthAndWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReceivedWeightByMonthAndWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetDemanReceivedWeightByMonthAndWarehouse.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderServicesInfo.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetRmaNotes.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetNestingItemsInfo.sql" />
    <Build Include="rzrapi\Inventory\Scan\rzrapi.sp_GetItemInventoryCapabilities.sql" />
    <Build Include="rzrapi\Inventory\Scan\rzrapi.sp_GetItemInventoryMainInfo.sql" />
    <Build Include="rzrapi\Inventory\Scan\rzrapi.sp_GetItemInventorySalesInfo.sql" />
    <Build Include="rzrapi\Inventory\Scan\rzrapi.sp_GetItemInventoryMoveHistory.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListContainerSize.sql" />
    <Build Include="rzrapi\Truck\rzrapi.sp_GetListTruckTypes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderLots.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_GetOutboundOrderFiles.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_GetOutboundOrderLoadingProgress.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GetOrderLotsForSettle.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetCommodityGroups.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetSalesOrderExternalId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderExternalId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetInvoiceExternalId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetCreditMemoExternalId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetConsolidatedInvoiceExternalId.sql" />
    <Build Include="rzrapi\Notifications\Queue\rzrapi.sp_GetPageOfNotificationQueue.sql" />
    <Build Include="rzrapi\Notifications\Queue\rzrapi.sp_DeleteNotificationQueue.sql" />
    <Build Include="rzrapi\Notifications\Queue\rzrapi.sp_SetNotificationQueue.sql" />
    <Build Include="rzrapi\Notifications\QueueParams\rzrapi.sp_GetPageOfNotificationQueueParams.sql" />
    <Build Include="rzrapi\Notifications\QueueParams\rzrapi.sp_SetNotificationQueueParams.sql" />
    <Build Include="rzrapi\Notifications\QueueParams\rzrapi.sp_DeleteNotificationQueueParams.sql" />
    <Build Include="rzrapi\Notifications\QueueTypes\rzrapi.sp_GetPageOfNotificationQueueTypes.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSeriesByRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByRangeByWarehouseWithAdditionalInfo.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByRangeAndWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRevenuePerRangeByOrderAndCustomerByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSalesByRangeByWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetReceivedWeightByRangeAndWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetDemanReceivedWeightByRangeAndWarehouse.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersReadyToSettle.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetOutboundOrderStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetOrderNotes.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLocationsSpaceInfo.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_GetSettlementPage.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_GetSettlementPageFilterCounts.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsNestedCapabilitiesStructure.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetCommodityGroup.sql" />
    <Build Include="bi\Functions\bi.tvf_GetReceivedWeightByRangeAndFilter.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityGroupList.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReceivedWeightByRangeAndFilter.sql" />
    <Build Include="recycling\Stored Procedures\HardDrives\recycling.sp_GetHardDriveInventroy.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_SetCompanyDateTimeFormat.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetCompanyDateTimeFormat.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContactDateTimeFormat.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetAssetDistributedCost.sql" />
    <Build Include="reports\Stored Procedures\ResaleOnHandSummary\reports.sp_CollectResaleOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\ResaleOnHandSummary\reports.sp_GetRawResaleOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\ResaleOnHandSummary\reports.sp_GetResaleOnHandSummary.sql" />
    <Build Include="reports\Stored Procedures\ResaleOnHandSummary\reports.sp_GetResaleOnHandSummaryTotals.sql" />
    <Build Include="reports\Stored Procedures\ResaleOnHandSummary\reports.sp_HandleResaleOnHandSummary.sql" />
    <Build Include="reports\Tables\reports.F_ResaleOnHandSummary.sql" />
    <Build Include="reports\Tables\reports.U_SYS_JobReportSettings.sql" />
    <Build Include="reports\User Defined Types\reports.RawResaleOnHandSummary.sql" />
    <Build Include="rzrapi\Truck\rzrapi.sp_GetListTruckTypesWarehouses.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsInboundOrderLotForSettle.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_ResetLotsTareWeightToZero.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_USERS_INFORMATION.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetRecyclingOrderDefaultPrice.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_ListRecyclingOrderBatches.sql" />
    <Build Include="rzrapi\WorkLog\rzrapi.sp_GetWorkLogItems.sql" />
    <Build Include="rzrapi\ReportManifest\rzrapi.sp_GetPageOfReportManifestLocationView.sql" />
    <Build Include="rzrapi\Inventory\rzrapi.sp_GetInventoryCapabilities.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingItemsSortCounts.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingItemsReadyToSettleCounts.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingItemsCounts.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersProfitAndLoss.sql" />
    <Build Include="bi\Functions\bi.tvf_GetDailyAuditReport.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInvoiceReconciliation.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingSalesByMonthAndWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SET_ADDRESS_TAGS.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetInventory.sql" />
    <Build Include="clientportal\Tables\clientportal.F_ContactRole.sql" />
    <Build Include="clientportal\Tables\clientportal.C_Page.sql" />
    <Build Include="clientportal\Tables\clientportal.C_PageSection.sql" />
    <Build Include="clientportal\Tables\clientportal.C_Permission.sql" />
    <Build Include="clientportal\Tables\clientportal.F_RolePage.sql" />
    <Build Include="clientportal\Tables\clientportal.F_RolePageSection.sql" />
    <Build Include="clientportal\Tables\clientportal.F_RolePermission.sql" />
    <Build Include="clientportal\Tables\clientportal.F_RoleInventoryAttribute.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetCustomerContactRole.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetCustomerContactActive.sql" />
    <Build Include="dbo\Views\dbo.vw_RandValue.sql" />
    <Build Include="dbo\Functions\dbo.fn_nvarchar_GetRandomString.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_ResetCustomerContactPassword.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_AddCustomerContact.sql" />
    <Build Include="clientportal\User defined Types\clientportal.CustomerContactCreationInfo.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetPageOfContactRole.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_AddDefaultRolesToCustomer.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetContactRole.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_DeleteContactRole.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetListOfContactsAssociatedWithRole.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetContactRolePermissions.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetContactRolePermissions.sql" />
    <Build Include="clientportal\User defined Types\clientportal.bigint_2_ID_ARRAY.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetItemMasterExternalId.sql" />
    <Build Include="rzrapi\Asset\rzrapi.sp_GetAssetGrading.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderItemsTareWeights.sql" />
    <Build Include="recycling\User Defined Types\recycling.IdTareWeightArray.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_GetOutboundOrderContainer.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_GetOutboundOrderTrucking.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_SetOutboundOrderContainer.sql" />
    <Build Include="rzrapi\OutboundOrder\rzrapi.sp_SetOutboundOrderTrucking.sql" />
    <Build Include="rzrapi\Roles\rzrapi.sp_GetUserRoleActions.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetHardDrivesAdvancedSearchPage.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_DeleteCustomerContact.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsAlternativeNameUnique.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderLot.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_ImportCommodities.sql" />
    <Build Include="recycling\User Defined Types\recycling.ImportCommodities.sql" />
    <Build Include="BusinessLogic\SharedPresets\Tables\dbo.F_SharedPreset.sql" />
    <Build Include="BusinessLogic\SharedPresets\Tables\dbo.F_SharedPresetFavor.sql" />
    <Build Include="BusinessLogic\SharedPresets\Stored Procedures\Favor\rzrapi.sp_DeleteSharedPresetFavor.sql" />
    <Build Include="BusinessLogic\SharedPresets\Stored Procedures\Favor\rzrapi.sp_SetSharedPresetFavor.sql" />
    <Build Include="BusinessLogic\SharedPresets\Stored Procedures\rzrapi.sp_SetSharedPreset.sql" />
    <Build Include="BusinessLogic\SharedPresets\Stored Procedures\rzrapi.sp_DeleteSharedPreset.sql" />
    <Build Include="BusinessLogic\SharedPresets\Stored Procedures\rzrapi.sp_GetSharedPresets.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GET_REPORT_RECYCLING_INBOUND_OUTBOUND_ORDER_SALES.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetReportTrackActivity.sql" />
    <Build Include="dbo\Stored Procedures\Printing\Labels\dbo.sp_GetInventoryLocationLabelModel.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetInboundOrderLogistics.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_LogRecyclingOrderItemsUnmerged.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_CUSTOMER_CONTACT.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_NEW_CUSTOMER_ACCOUNT_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RESET_CUSTOMER_PASSWORD_FROM_QUEUE.sql" />
    <Build Include="dbo\Functions\dbo.fn_money_GetTotalCostWithZeroOut.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingMetricsTotalWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingMetrics.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingAssetsPendingByCategory.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_SetRecyclingService.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_GetRecyclingServiceByExternalId.sql" />
    <Build Include="rzrapi\Commodity\rzrapi.sp_GetCommodityByIds.sql" />
    <Build Include="rzrapi\Commodity\rzrapi.sp_GetCommodityByExternalId.sql" />
    <Build Include="rzrapi\Commodity\rzrapi.sp_SetCommodity.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_GetRecyclingServiceById.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingMetricsForSortCheckIn.sql" />
    <Build Include="dbo\Functions\fn_bit_IS_CLIENT_PORTAL_NOTIFICATION_ALLOWED.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingItemMasterByExternalId.sql" />
    <Build Include="rzrapi\SalesOrder\rzrapi.sp_GetSalesOrderFromIdOrName.sql" />
    <Build Include="rzrapi\SalesOrder\rzrapi.sp_GetSalesOrderItems.sql" />
    <Build Include="rzrapi\SalesOrder\rzrapi.sp_GetSalesOrders.sql" />
    <Build Include="rzrapi\SalesOrder\rzrapi.sp_SetSalesOrderItemExternalId.sql" />
    <Build Include="recycling\Functions\recycling.fn_bit_IsLotNotProcessed.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsAuditedByCategory.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetAssetsAuditedByEmployeeByCategory.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetCompaniesDateOfLastCollectionReceived.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsNestedRepsStructure.sql" />
    <Build Include="dbo\Tables\F_LogProcessor.sql" />
    <Build Include="recycling\Functions\recycling.fn_nvarchar_json_OrderItemTags.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetLastProcessedLogTableId.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetLastProcesedLogTableIdForCompany.sql" />
    <Build Include="recycling\Stored Procedures\Commodities\recycling.sp_GetCommodityBriefById.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\AssetWorkflowTemplates\recycling.sp_GetPageOfAssetWorkflowTemplates.sql" />
    <Build Include="recycling\Stored Procedures\AssetWorkflowTemplates\recycling.sp_SetAssetWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\AssetWorkflowTemplates\recycling.sp_DeleteAssetWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\AssetWorkflowTemplates\recycling.sp_CloneAssetWorkflowTemplate.sql" />
    <Build Include="recycling\Stored Procedures\AssetWorkflowTemplates\recycling.sp_GetRecyclingAssetWorkflowRule.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractCommodityPriceDiscount.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SetCommodityPricingFromContract.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractInstructions.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_SetContractInstructions.sql" />
    <Build Include="dbo\Functions\dbo.fn_bigint_GetLocationLotCount.sql" />
    <Build Include="recycling\Stored Procedures\Location\recycling.sp_CheckLocationForLot.sql" />
    <Build Include="recycling\Stored Procedures\Location\recycling.sp_CheckLocationForTransfer.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateSalesOrderItemUnallocate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateInvoiceAmount.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetShippingStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderShipping.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderItemUnallocate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderItemsStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderItemAllocate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderInvoiceVoided.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderInvoiceStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderInvoice.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderComments.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetPackageTracking.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetBlankLocationResetByInventory.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetAllocateBySerial.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSalesOrderShippings.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAllSku.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_DeleteSalesOrderItemUnallocate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateSalesOrder.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetShippingPackage.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrder.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSystemSettingsDefaults.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesOrderAdminData.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSalesOrderAdminData.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCustomerCurrencyInfo.sql" />
    <Build Include="BusinessLogic\Sorting\Shredding\rzrapi\Stored Procedures\Batch\rzrapi.sp_GetShreddingBatches.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetGradePositionDevaluations.sql" />
    <Build Include="rzrapi\CommodityRule\rzrapi.sp_GetCommodityRules.sql" />
    <Build Include="rzrapi\User Defined Types\rzrapi.CommodityRule.sql" />
    <Build Include="rzrapi\CommodityRule\rzrapi.sp_SetCommodityRules.sql" />
    <Build Include="rzrapi\CommodityRule\rzrapi.sp_DeleteCommodityRules.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetWarehouses.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSkuAll.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftApiPOItemResult.sql" />
    <Build Include="reports\User Defined Types\reports.MicrosoftApiPurchaseOrderItemResult.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftCreditDetailsReportForPOPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftApiPurchaseOrderItemLog.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetPurchaseOrderItemsForMicrosoftCreditDetalsReport.sql" />
    <Build Include="dbo\Functions\dbo.fn_int_GetNumberOfWorkingDaysBetweenTwoDates.sql" />
    <Build Include="dbo\Functions\dbo.fn_int_GetDaysBetweenInboundOrderDueDateBySla.sql" />
    <Build Include="dbo\Functions\dbo.fn_datetime_GetDateAfterNWorkdays.sql" />
    <Build Include="dbo\Stored Procedures\UserSession\dbo.sp_CleanupClosedUserSessions.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateSalesQuote.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_UpdateSalesOrderShipping.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSalesQuote.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSalesQuotes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSalesQuoteItems.sql" />
    <Build Include="rzrapi\Tax\rzrapi.sp_GetTaxes.sql" />
    <Build Include="rzrapi\Invoice\rzrapi.sp_SetIsInSyncWithExteralByIds.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetCapabilitiesUpdatingData.sql" />
    <Build Include="dbo\Tables\dbo.TagToSku.sql" />
    <Build Include="dbo\Tables\dbo.TagToInventory.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetTag.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetSkuTagging.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetInventoryTagging.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSkuTagsBySkuIds.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInventoryTagsByInventoryIds.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAvailableTags.sql" />
    <Build Include="dbo\User Defined Types\dbo.Capabilities.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderReceived.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderItemsReceived.sql" />
    <Build Include="rzrapi\Attributes\rzrapi.sp_GetCapablitiesOfType.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSkuQualifiedWithPriceDataByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSkuQualifiedSoldByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSkuQualifiedAndRevenueByEmployee.sql" />
    <Build Include="search\Views\search.vw_ChangesQtyAndDate.sql" />
    <Build Include="search\Stored Procedures\search.sp_GetDocumentsNestedTagsStructure.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\dbo\Tables\dbo.F_EnvironmentalTrackingItem.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\dbo\Tables\dbo.C_EnvironmentalTrackingMetricsType.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\dbo\Tables\dbo.D_EnvironmentalTrackingMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\dbo\Tables\dbo.D_EnvironmentalTrackingMetricsUom.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\dbo\Tables\dbo.F_EnvironmentalTrackingItemMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfEnvironmentalTrackingMetricsUoms.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetEnvironmentalTrackingMetricsTypes.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetEnvironmentalTrackingItem.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_DeleteEnvironmentalTrackingItem.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_SetEnvironmentalTrackingItem.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_DeleteEnvironmentalTrackingMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_SetEnvironmentalTrackingMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfEnvironmentalTrackingMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_SetEnvironmentalTrackingMetricsUom.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_DeleteEnvironmentalTrackingItemMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfEnvironmentalTrackingItemMetrics.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfEnvironmentalTrackingCategory.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_GetPageOfEnvironmentalTrackingCommodity.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMaster.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMasterAttributes.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMasterCategories.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMasterPrimaryCategory.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMasterDimension.sql" />
    <Build Include="recycling\Functions\recycling.fn_nvarchar_json_OrderItemCommodityTags.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetTagsAssignedEntities.sql" />
    <Build Include="BusinessLogic\EnvironmentalTracking\rzrapi\Stored Procedures\rzrapi.sp_SetEnvironmentalTrackingItemMetrics.sql" />
    <Build Include="dbo\Stored Procedures\sp_CHECK_ORDER_BEFORE_ITEMS_ALLOCATED.sql" />
    <Build Include="recycling\Functions\recycling.fn_nvarchar_json_OrderItemLotTags.sql" />
    <Build Include="recycling\User Defined Types\recycling.UidSerialAssetNumerated.sql" />
    <Build Include="dbo\User Defined Types\bigint_ID_numerated_ARRAY.sql" />
    <Build Include="recycling\Tables\recycling.F_Asset.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetNesting.sql" />
    <Build Include="reports\Tables\reports.F_RecyclingOnHandSummaryLot.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_PopulateRecyclingOnHandSummaryLots.sql" />
    <Build Include="clientportal\Tables\clientportal.F_CustomerPermission.sql" />
    <Build Include="clientportal\Tables\clientportal.F_CustomerPermissionInventoryAttribute.sql" />
    <Build Include="clientportal\Tables\clientportal.F_CustomerPermissionPage.sql" />
    <Build Include="clientportal\Tables\clientportal.F_CustomerPermissionPageSection.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventoryWeightBalanceGroupedByCommodity.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_NOTIFICATION_SET_WORKFLOW_SCOPE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_LOT_MOVED_A_SPECIAL_WORKFLOW_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_CleanLogsTable.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetOutboundOrdersInfo.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_SHIPPING_AUTH_METHOD_ENABLED.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetClientPortalCustomerContact.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetClientPortalContactList.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetLoggedEntityTypes.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetLoggedEntityActions.sql" />
    <Build Include="dbo\Tables\dbo.F_RECYCLING_ORDER_ITEM.sql" />
    <Build Include="dbo\Tables\dbo.F_ITEM_INVENTORY.sql" />
    <Build Include="dbo\Tables\dbo.F_ITEM.sql" />
    <Build Include="dbo\Tables\Partitioned\dbo.F_INVOICE.sql" />
    <Build Include="dbo\Tables\Partitioned\pfn_Invoice_InvoiceTypeId.sql" />
    <Build Include="dbo\Tables\Partitioned\ps_Invoice_InvoiceTypeId.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetRecyclingInitiallyReceivedLots.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderSubtotal.sql" />
    <Build Include="dbo\Functions\fn_str_GET_SALES_ORDER_PART_NUMBER.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\rzrapi.sp_DeleteStateProgram.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\rzrapi.sp_GetStatePrograms.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\rzrapi.sp_SetStateProgram.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftUnitTypes.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftUnitTypes.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_SetMicrosoftCustomerCategoryUnitType.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetVerifyPickListItemsByLocationId.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToCommodity\recycling.sp_SetCommodityStatePrograms.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToCommodity\recycling.sp_GetCommodityStatePrograms.sql.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToContract\recycling.sp_GetContractStatePrograms.sql.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToContract\recycling.sp_SetContractStatePrograms.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToCommodity\rzrapi.sp_GetStateProgramCommodities.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToContract\rzrapi.sp_GetStateProgramContracts.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Tables\recycling.C_BusinessUnit.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Tables\recycling.C_LotWorkflowChangeLogStatus.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Tables\recycling.F_LotWorkflowChangeLog.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Stored Procedures\recycling.sp_SetLotWorkflowChangeLog.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Stored Procedures\recycling.sp_LogClassTrackingOnScrap.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Stored Procedures\recycling.sp_GetBusinessUnits.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Functions\recycling.fn_str_GetCommodityBusinessUnit.sql" />
    <Build Include="BusinessLogic\ClassTracking\recycling\Functions\recycling.fn_str_GetLotBusinessUnit.sql" />
    <Build Include="BusinessLogic\ClassTracking\rzrapi\Stored Procedures\rzrapi.sp_ListLotWorkflowChangeLog.sql" />
    <Build Include="BusinessLogic\ClassTracking\rzrapi\Stored Procedures\rzrapi.sp_SetLotWorkflowChangeLogStatus.sql" />
    <Build Include="BusinessLogic\ClassTracking\rzrapi\Stored Procedures\rzrapi.sp_ListLotWorkflowChangeLogStatuses.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetRecyclingWorkflowTypeIsFinal.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetAdminMenuStructure.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobTimeTrack.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobSubtask.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobExpense.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobComment.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJob.sql" />
    <Build Include="recycling\Tables\recycling.C_InboundOrderJobStatus.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderEquipment.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetInboundOrderFileDetail.sql" />
    <Build Include="dbo\User Defined Types\dbo.TubleBigintIntBit.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobExpenseFile.sql" />
    <Build Include="recycling\Tables\recycling.C_InboundOrderJobSubtaskStatus.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder\recycling.sp_GetInboundOrderStatePrograms.sql.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder\recycling.sp_SetInboundOrderStatePrograms.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder\rzrapi.sp_GetStateProgramInboundOrder.sql" />
    <Build Include="BusinessLogic\StatePrograms\recycling\Tables\recycling.F_CommodityStateProgram.sql" />
    <Build Include="BusinessLogic\StatePrograms\recycling\Tables\recycling.F_ContractStateProgram.sql" />
    <Build Include="BusinessLogic\StatePrograms\recycling\Tables\recycling.F_StateProgram.sql" />
    <Build Include="BusinessLogic\StatePrograms\recycling\Tables\recycling.F_InboundOrderStateProgram.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder\recycling.sp_GetInboundOrderStateProgramOptions.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToInboundOrder\recycling.sp_SetInboundOrderStateProgramOptions.sql" />
    <Build Include="dbo\Stored Procedures\sp_LIST_SEGREGATION_COMMODITIES.sql" />
    <Build Include="BusinessLogic\StatePrograms\rzrapi\Stored Procedures\ToLot\rzrapi.sp_GetStateProgramLots.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftApiSettings.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftApiSettings.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_SetMicrosoftApiSettings.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetEnvironmentalTrackingInboundOrderMetrics.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetEnvironmentalTrackingCompanyMetrics.sql" />
    <Build Include="recycling\Tables\recycling.F_RecyclingOrderReport.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderEmployeeReports.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetInboundOrderEmployeeReports.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetInboundOrderEmployeeReports.sql" />
    <Build Include="recycling\User Defined Types\recycling.OrderReport.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SENDGRID_SETTINGS.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SENDGRID_SETTINGS.sql" />
    <Build Include="dbo\Tables\U_SENDGRID_SETTINGS.sql" />
    <Build Include="recycling\Tables\recycling.F_InboundOrderJobEmployee.sql" />
    <Build Include="dbo\fn_bigint_GetAssetQuantity.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_AddCustomerServiceInternalCost.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_GetServiceInternalCostCustomers.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_DeleteCustomerServiceInternalCost.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrders.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetItemMasterRecyclingMapping.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetItemMasterRecyclingMapping.sql" />
    <Build Include="recycling\Stored Procedures\AuditReport\recycling.sp_GetAuditReportHardDrives.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfInventoryByWeight.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsInProductionByWorkflowInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfAssetsInProductionByCountInDateRange.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportAssetWorkflowLogChangedCountByUser.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLocationTypeList.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetInvoiceReconciliation.sql" />
    <Build Include="dbo\Tables\dbo.F_PermissionEntityToSystemSettings.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetLotRelations.sql" />
    <Build Include="dbo\Tables\dbo.F_ItemMasterGlobalNote.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetItemMasterGlobalNote.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetItemMasterGlobalNotes.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_DeleteItemMasterGlobalNote.sql" />
    <Build Include="dbo\Functions\dbo.fn_datetime_GetSlaDate.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportDailyAudit.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetReportSalesByCustomer.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetReportProfitLoss.sql" />
    <Build Include="dbo\Tables\LotLogs\dbo.F_LotActionLogRaw.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_GetLotRawActionLogs.sql" />
    <Build Include="dbo\Tables\LotLogs\dbo.F_LotActionLog.sql" />
    <Build Include="dbo\Tables\LotLogs\dbo.C_LotAction.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_MarkLotRawActionLogsAsProcessed.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_SetLotActionLogs.sql" />
    <Build Include="dbo\User Defined Types\LotLogs\dbo.udtt_LotActionLog.sql" />
    <Build Include="dbo\Tables\LotLogs\dbo.F_LotActionLogRawHistory.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_CleanupProcessedLotRawActionLogs.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_SetLotActionLogTriggersEnabled.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRmaCreatedCount.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRmaCreatedByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetItemsSoldWithAdditionalInfoRep.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSkuQualifiedByEmployeeBreakdown.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryUidInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotIdsInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotNetWeightInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssetsInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssetsWeightInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryUidAvailableInResaleChannelsInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalInventoryPriceAvailableInResaleChannelsInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByCountInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByWeightInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInventoryStockAgingReportInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLotAgingInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityGroupedByWorkflowInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetGroupedCommodityInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersByRepUnsettledInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesOrdersNotInvoicedInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersFreight.sql" />
    <Build Include="dbo\Stored Procedures\LotLogs\dbo.sp_RecalculateCommodityTotalWeightInWarehouse.sql" />
    <Build Include="dbo\Tables\LotLogs\dbo.F_CommodityTotalWeightChangeLog.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetOrderOverview.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetOrderLotsOverview.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetAssetsByWorkflow.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetLotTransactionLogReport.sql" />
    <Build Include="dbo\Stored Procedures\Magento\dbo.sp_SET_SALES_ORDER_INVOICE_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_ADD_CUSTOMERS_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_ADD_ORDER_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_ADD_ORDER_PRODUCTS_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_ADD_PRODUCT_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_CREATE_SALES_ORDER_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_CUSTOMER_BY_MAGENTO_NAME.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_DATA_FOR_MAGENTO_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_MAGENTO_CUSTOMERS.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_MAGENTO_MAX_ORDER_ID.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_MAGENTO_ORDERS.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_MAGENTO_PRODUCTS.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_MAGENTO_SHIPPING_ADDRESS.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_ORDERS_NEED_INVOICE_IN_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_ORDERS_NEED_TO_MAGENTO_SHIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_ORDERS_NEED_TO_MAGENTO_VOID_SHIPMENT.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_NEED_ADDITIONAL_PRODUCTS_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_CUSTOMER_ADDRESS_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_MAGENTO_CUSTOMER.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_MAGENTO_ORDER_INVOICED.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_MAGENTO_ORDER_MAGENTO_SHIPMENT_ID.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_MAGENTO_ORDER_PROCESSED.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_SET_SHIPMENT_AS_VOIDED_IN_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_UPD_PRODUCT_FROM_MAGENTO.sql" />
    <Build Include="dbo\Stored Procedures\Magento\dbo.sp_GetMagentoEbayOrderData.sql" />
    <Build Include="dbo\Stored Procedures\Magento\dbo.sp_SetSalesOrderFromMagentoWarehouse.sql" />
    <Build Include="dbo\Stored Procedures\Magento\sp_GET_DATA_MAGENTO_ORDER_TO_COMPANY_ORDER.sql" />
    <Build Include="magento\Tables\D_MAGENTO_SHOP.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListSlaBeginningType.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetLotTransactionLogCounters.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_bit_IsContactEmailUnique.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderReferences.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderReferences.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetListRecyclingPONumbers.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_SALES_ORDER_PROFORMA_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_SALES_ORDER_PROFORMA_INVOICE.sql" />
    <Build Include="dbo\Tables\dbo.F_PROFORMA_INVOICE.sql" />
    <Build Include="dbo\Stored Procedures\Printing\Labels\dbo.sp_GetLocationLabelModel.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetUserEmployeesList.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderLocationDetails.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderEquipmentNotes.sql" />
    <Build Include="rzrapi\Customer\Tag\rzrapi.sp_SetCustomerTags.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderRequiredDocuments.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderDocumentNotes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderRequiredDocument.sql" />
    <Build Include="dbo\User Defined Types\dbo.TubleBigintBit.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderDocumentNotes.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderDocumentStructure.sql" />
    <Build Include="bi\Functions\bi.tvf_GetRecyclingSummary.sql" />
    <Build Include="bi\Functions\bi.tvf_GetLotsCountConsumedByCategory.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetTopCategoriesOfAssetsByGrade.sql" />
    <Build Include="bi\Functions\bi.tvf_tvf_GetUnqualifiedItemsAging.sql" />
    <Build Include="bi\Functions\bi.tvf_GetQualifiedItemsAging.sql" />
    <None Include="cp\ApiAccess\Stored Procedures\clientportal.sp_SetCustomerPortalAccess.sql" />
    <Build Include="clientportal\Stored Procedures\ApiAccess\clientportal.sp_GetCustomerPortalAccess.sql" />
    <Build Include="clientportal\Stored Procedures\ApiAccess\clientportal.sp_GetPortalUser.sql" />
    <Build Include="clientportal\Stored Procedures\ApiAccess\clientportal.sp_SetCustomerPortalAccess.sql" />
    <Build Include="clientportal\Stored Procedures\Permissions\clientportal.sp_GetCustomerPermissions.sql" />
    <Build Include="clientportal\Stored Procedures\Permissions\clientportal.sp_SetCustomerPermissions.sql" />
    <Build Include="clientportal\Stored Procedures\Settings\clientportal.sp_SetCustomerClientPortalGeneralSettings.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PICK_LIST_CREATED_FROM_QUEUE.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetAssetsTotalSplitByWorkflowStep.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsDestructionByEmployee.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesByPrimaryCustomers.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSalesBySubCustomers.sql" />
    <Build Include="dbo\User Defined Types\dbo.FreightCarrier.sql" />
    <Build Include="dbo\Tables\dbo.F_CategoryAccount.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_SET_ENTITY_ACCOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\dbo.sp_SetCategoryAccounts.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_DELETE_ACCOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ACCOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ACCOUNT_LOGS.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ACCOUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LIST_ACCOUNT_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LIST_ACCOUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LOG_ACCOUNT_PURCHASING.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LOG_ACCOUNT_SALE.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LOG_ACCOUNT_SALE_APPEND.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_SET_ACCOUNT.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_SET_ACCOUNT_PARENT.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ACCOUNTS_FOR_INVOICE_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ACCOUNTS_FOR_WAREHOUSES.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_GET_ENTITY_ACCOUNTS.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LIST_ACCOUNTS_BY_TYPES.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_LIST_ACCOUNTS_DEFAULTS.sql" />
    <Build Include="dbo\Stored Procedures\Accounts\sp_SET_ACCOUNTS_FOR_WAREHOUSES.sql" />
    <Build Include="Security\shared.sql" />
    <Build Include="shared\Reports\Stored Procedures\shared.sp_GetRecyclingInboundOrderSummary.sql" />
    <Build Include="shared\Reports\Stored Procedures\shared.sp_GetRecyclingDetailedBreakout.sql" />
    <Build Include="shared\Reports\Stored Procedures\shared.sp_GetRecyclingOutboundOrders.sql" />
    <Build Include="shared\Reports\User Defined Types\shared.bigintIdArray.sql" />
    <Build Include="shared\System\Functions\shared.fn_bigintIdArrayToItemsXml.sql" />
    <Build Include="shared\System\Functions\shared.fn_bigintIdArrayToXml.sql" />
    <Build Include="shared\Reports\User Defined Types\shared.bigintIdNameArray.sql" />
    <Build Include="shared\Listings\Views\shared.vw_Warehouses.sql" />
    <Build Include="shared\Listings\Views\shared.vw_Customers.sql" />
    <Build Include="shared\Listings\Views\shared.vw_InboundOrderStatuses.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_GetRecyclingService.sql" />
    <Build Include="dbo\Stored Procedures\sp_UPD_SALES_ORDER_ITEM_AMTOWED.sql" />
    <Build Include="shared\Listings\Views\shared.vw_InvoicesAr.sql" />
    <Build Include="shared\Listings\Views\shared.vw_InvoicesAp.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderItemServicesForPricing.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderContractServices.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_ListItemConditions.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_ListItemMasters.sql" />
    <Build Include="clientportal\Stored Procedures\EnvironmentalReports\clientportal.sp_GetEnvironmentalTrackingInboundOrdersMetrics.sql" />
    <Build Include="clientportal\Stored Procedures\EnvironmentalReports\clientportal.sp_GetAssetsForEnvironmentalTrackingReports.sql" />
    <Build Include="clientportal\Stored Procedures\EnvironmentalReports\clientportal.sp_GetInboundOrdersForEnvironmentalTrackingReports.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsAuditedByEmployeeWithAuditTool.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersByRepUnsettledWithAuditTool.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_PARAMETER.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_GetActiveNotificationSettings.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_CONDITION_COMPLETED.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_CONDITION_LOG.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_SET_NOTIFICATION_CONDITION_LOG.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetCommodityPricingApplyContractPreview.sql" />
    <Build Include="dbo\Functions\dbo.fn_int_BusinessDaysBetween.sql" />
    <Build Include="dbo\Functions\dbo.fn_dateTime_BusinessDaysAdd.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrderSla.sql" />
    <Build Include="dbo\Functions\dbo.fn_str_STRIP_HTML_TAGS.sql" />
    <Build Include="recycling\Functions\recycling.fn_nvarchar_json_GetOrderFreightCarriers.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetRecyclingOrderFreightCarriers.sql" />
    <Build Include="recycling\Functions\recycling.fn_table_GetRecyclingOrderFreightQuoteRate.sql" />
    <Build Include="dbo\Stored Procedures\Logos\dbo.sp_GetSystemImageSizes.sql" />
    <Build Include="dbo\Stored Procedures\Logos\sp_LIST_SYSTEM_IMAGES.sql" />
    <Build Include="dbo\Stored Procedures\Logos\sp_GET_SYSTEM_IMAGE_INFO.sql" />
    <Build Include="dbo\Stored Procedures\Logos\sp_GET_SYSTEM_LOGO.sql" />
    <Build Include="dbo\Stored Procedures\Logos\sp_SET_SYSTEM_IMAGE.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrderLotsAndAssets.sql" />
    <Build Include="rzrapi\ContractServices\rzrapi.sp_GetContractServices.sql" />
    <Build Include="dbo\User Defined Types\dbo.PricingService.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetRecyclingOrderItemServicePricing.sql" />
    <Build Include="dbo\Functions\fn_str_GET_NOTIFICATION_EMAIL_LIST_PL.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetContractTemplates.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetInboundOrderFileDetail.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetRecyclingOrderCommodityPricing.sql" />
    <Build Include="dbo\Functions\dbo.fn_varchar_StripHTML.sql" />
    <Build Include="recycling\Functions\recycling.fn_table_GetInitiallySoldAssets.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetNotificationSetList.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetNotification.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetNotification.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetRecyclingOrderCommodityRestricted.sql" />
    <Build Include="clientportal\Stored Procedures\Asset\clientportal.sp_GetAuditToolReport.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderInboundCertificate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderInboundCertificate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderCertificateTemplates.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetEmailTemplateList.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrdersStatuses.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrderSortAuditStatuses.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrderSortAuditStatusCounts.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_DELETE_RECYCLING_ORDER_ITEM_SERVICES.sql" />
    <Build Include="cp\Tables\cp.F_TakebackOrderRequestShippingEmail.sql" />
    <Build Include="cp\Tables\cp.F_TakebackOrderRequestShipping.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetTakebackOrderRequestEquipments.sql" />
    <Build Include="cp\Stored Procedures\cp.sp_SetBoxProgramShipping.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_float_GetPercentageDifference.sql" />
    <Build Include="clientportal\Functions\clientportal.tvf_GetBoxProgramOverview.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderSettledLots.sql" />
    <Build Include="reports\Tables\reports.F_MicrosoftDocuments.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_SetMicrosoftDocument.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftDocumentsPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_DeleteMicrosoftDocument.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftDocument.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftDocumentLog.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftTransportAndPackagingReportsPage.sql" />
    <Build Include="reports\Tables\reports.F_RecyclingOrderPackagingMaterials.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftTransportReportData.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftPackagingMaterials.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftPackagingReportData.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_DeleteMicrosoftPackagingMaterial.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftTransportAndPackaging.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetInboundOrderFileName.sql" />
    <Build Include="bi\Functions\bi.tvf_GetInboundOrderStatusList.sql" />
    <Build Include="reports\Tables\reports.F_RecyclingOrderMaterialAudit.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftMaterialAuditReportData.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_AddMicrosoftMaterialAuditLog.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftMaterialAuditReportsPage.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_DeleteMicrosoftMaterialAudit.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\recycling.sp_ListMicrosoftCustomerJobs.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_GetMicrosoftMaterialAudits.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetOutboundOrderSettledLots.sql" />
    <Build Include="clientportal\Stored Procedures\Templates\clientportal.sp_GetEmailTemplateByType.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_DeleteBoxProgramOrderRequestEquipments.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_DeleteBoxProgramOrderRequestShippingEmails.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramEquipments.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestById.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestEquipments.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequests.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestsCount.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestShippingEmails.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestsOverview.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequest.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequestEquipment.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequestShipping.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetShippingPackages.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetOrderShipping.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetShippingPackages.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetRecyclingOrderShippings.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetOrderShippings.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_DeleteShipping.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetShippingPackage.sql" />
    <Build Include="rzrapi\User Defined Types\rzrapi.ShippingPackage.sql" />
    <Build Include="dbo\Stored Procedures\sp_GET_PICKLIST_CUSTOMER_ID.sql" />
    <Build Include="dbo\Tables\F_NOTIFICATION_SET_CUSTOMER.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_GetRecyclingOrderCreationParams.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_SetInboundOrder.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetOutboundOrderClone.sql" />
    <Build Include="dbo\Stored Procedures\FreightCarrier\dbo.sp_GetFreightCarrier.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForCustomer.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForUser.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForQuote.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForRecyclingOrder.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForCustomerContact.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForRfq.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetCustomerAddressDuplicate.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetCustomerAddressListExtended.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetCustomerContactDuplicate.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetCustomerDetailsById.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequestEquipments.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequestShippingEmails.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetCustomerContact.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_UpdateBoxProgramOrderRequestStatus.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GetPurchaseOrderSettings.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetPurchaseOrderSettings.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_NEW_PURCHASE_ORDER_RECEIVED.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ADDRESSES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_INVOICE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_VOID_PURCHASE_ORDER_INVOICE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ITEM_INVENTORY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_RECYCLING.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_SPLIT_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_TIED_INVENTORY_RECV.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_RMA_INVOICE_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_UPD_PURCHASE_ORDER_INVOICE_AMOUNT.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_UPD_PURCHASE_ORDER_ITEMS_PRICE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\cp\Stored Procedures\cp.sp_GetCustomerPurchaseOrders.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\bit_IS_REPAIR_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_bigint_GET_REPAIR_PURCHASE_ORDER_ITEM_INVENTORY_ID_BY_SERIAL_OR_UID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_float_GET_PURCHASE_ORDER_TOTAL_COST.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_int_GET_PURCHASE_ORDER_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_int_GET_PURCHASE_ORDER_TOTAL_QTY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_nvarchar_GET_PURCHASE_ORDER_AUTO_NAME.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_str_AUTO_NAME_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_str_GET_PURCHASE_ORDER_INVOICE_DUE_STATUS_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_str_GET_PURCHASE_ORDER_INVOICE_DUE_STATUS_ID_BY_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_str_GET_PURCHASE_ORDER_INVOICE_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Functions\fn_str_GET_PURCHASE_ORDER_PART_NUMBER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GetPurchaseOrderItemsAggregateWithNotes.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GET_PURCHASE_INVOICES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GET_PURCHASE_ORDER_LOGISTICS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetPurchaseOrderExternalId.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetPurchaseOrderCurrency.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_CLOSING_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_CONSIGNMENT.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_COPY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_CUSTOMER_CODE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_FILE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_FILES_SEQUENCE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_FROM_SALES_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ITEM_BY_INVENTORY_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ITEMS_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_ITEMS_STATUS_RECEIVED.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_PRICES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_ORDER_RECYCLING_FROM_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_PURCHASE_PAYMENT.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\api\Stored Procedures\sp_GET_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\api\Stored Procedures\sp_GET_PURCHASE_ORDER_ADDRESSES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\api\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\api\Stored Procedures\sp_LIST_PURCHASE_ORDERS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SET_PURCHASE_ORDER_DELIVERY_DATE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SET_PURCHASE_ORDER_FREIGHT_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SET_PURCHASE_ORDER_LOGISTICS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_UPD_PURCHASE_ORDER_ITEM_SUMMARY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GET_PURCHASE_ORDER_DYNAMIC_INFO.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_DEL_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_DEL_PURCHASE_ORDER_TIED_INVENTORY_RECV.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ADDRESSES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_BY_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_INVOICE_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_INVOICES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEM_DATA.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEM_INVENTORY_ITEM_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_UNPAID_INVOICE_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDERS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_STATUS_COUNTS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PO_NUMBERS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_ORDER_RECEIVE_STATUSES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_ORDERS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_UPD_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_DELETE_PURCHASE_ORDER_FILE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_DEL_PURCHASE_ORDER_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_LIST_PURCHASE_ORDER_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_INVOICES_TO_PAY_TOTALS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_INVOICE_DUE_STATUS_ID_BY_ID.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEM_RECV_CAPABILITY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEM_TEMPLATE_INFO.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS_AGGREGATE_RECEIVE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS_DATA.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS_FOR_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS_FOR_RECEIVE_INVENTORY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_ITEMS_FOR_RECEIVE_INVENTORY_SUMMARY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_ORDER_STATUSES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_PURCHASE_PAYMENT_INVOICE_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE_ORDER_SUMMARY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE_SIMPLE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE_SUMMARY_CATEGORY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE_SUMMARY_COMMODITY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_GET_REPORT_PURCHASE_SUMMARY_CUSTOMER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_IMPORT_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_IMPORT_PURCHASE_ORDER_ITEMS_CAPABILITIES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_INSERT_PURCHASE_ORDER_ADDRESS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_IS_ADD_CONSIGNMENT_TO_PURCHASE_ORDER_AVAILABLE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_IS_PURCHASE_ORDER_ITEM_SERIAL_OR_UID_UNIQUE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_ORDER_FILES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_ORDERS_EXTENDED.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_REFERENCES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LIST_PURCHASE_TIMEFRAMES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LOG_PURCHASE_ORDER_ITEM_DELETED.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LogPurchaseOrderDeleted.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SAVE_PURCHASE_ORDER_TYPES.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_SET_CONSIGNMENT_PURCHASE_ORDER_ITEMS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\C_PURCHASE_ORDER_CLOSING_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\C_PURCHASE_ORDER_FREIGHT_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\C_PURCHASE_ORDER_RECEIVE_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\C_PURCHASE_ORDER_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\C_PURCHASE_TIME_FRAME.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER_ADDRESS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER_FILE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER_ITEM.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER_ITEM_CAPABILITY.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_PURCHASE_ORDER_ITEM_INVOICE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Views\vw_EXPORTABLE_PURCHASE_INVOICE.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Views\vw_EXPORTABLE_PURCHASE_ORDER.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Views\vw_PURCHASE_ORDER_DYNAMIC_INFO.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures\rzrapi.sp_GetPurchaseOrderItems.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures\rzrapi.sp_GetPurchaseOrders.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures\rzrapi.sp_GetUnpaidConsignmentPurchaseOrders.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures\rzrapi.sp_IsOrderItemIncludedInPaidInvoice.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\rzrapi\Stored Procedures\rzrapi.sp_SetPurchaseOrderItem.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\sp_SET_PURCHASE_TAX_INVOICE_STATUS.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GetNotInvoicedPoAmount.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\dbo.sp_SetNotificationSetCustomers.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\dbo.sp_GetNotificationSetCustomers.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\dbo.sp_ListNotificationSetCustomers.sql" />
    <Build Include="bi\Functions\bi.tvf_GetProductivityConsumeLaborManagement.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramShippingDefaults.sql" />
    <Build Include="clientportal\Stored Procedures\Customer\clientportal.sp_GetPostalProvidersAccountNumbers.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_SetContact.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_GetRecyclingOrder.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_GetRecyclingOrderShipping.sql" />
    <Build Include="clientportal\Stored Procedures\RecyclingOrder\clientportal.sp_SetRecyclingOrderShipping.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET.sql" />
    <Build Include="dbo\Functions\fn_bigint_GetNotificationSetForNotificationQueueItem.sql" />
    <Build Include="dbo\User Defined Types\NotifyQueueItemWithNotifySet.sql" />
    <Build Include="rzrapi\Attributes\rzrapi.sp_GetCapabilityTypes.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_SetItemMasterAttributes.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\rzrapi\Stored Procedures\rzrapi.sp_GetIsAutoPriceRulesEnabled.sql" />
    <Build Include="BusinessLogic\AutoPriceRules\rzrapi\Stored Procedures\rzrapi.sp_SetIsAutoPriceRulesEnabled.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetUserData.sql" />
    <Build Include="dbo\Views\dbo.vw_User.sql" />
    <Build Include="dbo\Views\dbo.vw_SalesOrderBillTo.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForRemoteReturnLabel.sql" />
    <Build Include="reports\Stored Procedures\MicrosoftRecyclingReport\reports.sp_UpdateMicrosoftCollectionReportData.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetOtpSystemSettings.sql" />
    <Build Include="dbo\Tables\dbo.F_UserOneTimePassword.sql" />
    <Build Include="dbo\Stored Procedures\OneTimePassword\dbo.sp_ValidateUserOneTimePassword.sql" />
    <Build Include="dbo\Stored Procedures\OneTimePassword\dbo.sp_SaveUserOneTimePassword.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetMfaToUsers.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_SetOtpSystemSettings.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxPrograms.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramCustomerSettings.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_SetBoxProgramCustomerSettings.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetAllocateByInventoryIds.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_DeleteEmptyAdditionalPoInvoices.sql" />
    <Build Include="dbo\Views\dbo.vw_PoItemInvoices.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetPoInvoice.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetPurchaseOrderReference.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetItemInventoryAttachedFiles.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetCustomerContactInfoByContactId.sql" />
    <Build Include="clientportal\Stored Procedures\Commodity\clientportal.sp_GetCommoditiesList.sql" />
    <Build Include="clientportal\Stored Procedures\Commodity\clientportal.sp_SetCommodity.sql" />
    <Build Include="clientportal\Stored Procedures\Lot\clientportal.sp_SetRecyclingOrderItem.sql" />
    <Build Include="clientportal\Stored Procedures\Lists\clientportal.sp_ListRecyclingPackagingTypes.sql" />
    <Build Include="clientportal\Stored Procedures\Warehouse\clientportal.sp_GetUserDefaultWarehouse.sql" />
    <Build Include="clientportal\Stored Procedures\Location\clientportal.sp_GetDefautLocationByWarehouse.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_AddQuoteSubmittedNotificationToQueue.sql" />
    <Build Include="clientportal\Stored Procedures\Lot\clientportal.sp_GetRecyclingOrderItems.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PORTAL_QUOTE_IS_CONVERTED_TO_ORDER_FROM_QUEUE.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RECYCLING_ORDER_WAS_CANCELED_FROM_QUEUE.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramFees.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_SetBoxProgramFees.sql" />
    <Build Include="dbo\User Defined Types\dbo.RangeFee.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramKitOptions.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramCustomerKitOptions.sql" />
    <Build Include="dbo\User Defined Types\dbo.BoxProgramKitOption.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_SetBoxProgramCustomerKitOptions.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderContracts.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_DeleteContractsFromRecyclingOrder.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderContractConflicts.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderContractsItems.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_IsContractIntersectingOrder.sql" />
    <Build Include="rzrapi\WorkLog\rzrapi.sp_GetWorkLogTotals.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.D_BoxProgramCustomerSettings.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.D_BoxProgramFees.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.D_BoxProgramRangeFees.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.F_BoxProgramTransactionLog.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.C_BoxProgramChargeType.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_AddBoxProgramTransaction.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramTransactions.sql" />
    <Build Include="rzrapi\Shipping\Address\rzrapi.sp_GetShippingAddress.sql" />
    <Build Include="rzrapi\Shipping\Address\rzrapi.sp_SetShippingAddress.sql" />
    <Build Include="dbo\Stored Procedures\BoxProgram\dbo.sp_AddBoxProgramShippingTransaction.sql" />
    <Build Include="dbo\Stored Procedures\BoxProgram\dbo.sp_AddBoxProgramAssetTransaction.sql" />
    <Build Include="dbo\Stored Procedures\BoxProgram\dbo.sp_AddBoxProgramRegularTransactions.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetSystemSettingsFieldValidation.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_SetSystemSettingsFieldValidation.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_RMA_CREATED_FROM_QUEUE.sql" />
    <Build Include="rzrapi\Emails\rzrapi.sp_ListEmailsForFreight.sql" />
    <Build Include="dbo\Stored Procedures\Notifications\sp_GET_NOTIFICATIONS_PURCHASE_ORDER_IS_CREATED_FROM_QUEUE.sql" />
    <Build Include="dbo\Tables\dbo.D_TakebackOrderDefault.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetTakebackOrderDefault.sql" />
    <Build Include="dbo\Tables\dbo.C_BoxProgramOrderRequestStatus.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_GetTakebackOrder.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_SetTakebackOrder.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_SetTakebackShipping.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_UpdateProgramTransaction.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetTakebackOrderByLotId.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_IsAllLotsAuditCompleted.sql" />
    <Build Include="dbo\Tables\dbo.F_BoxProgramKitOption.sql" />
    <Build Include="dbo\Tables\dbo.C_BoxProgramKitOption.sql" />
    <Build Include="clientportal\Tables\clientportal.F_BoxProgramInventoryKitOptionModel.sql" />
    <Build Include="clientportal\Tables\clientportal.F_BoxProgramInventoryKitOption.sql" />
    <Build Include="clientportal\Tables\clientportal.F_BoxProgramInventory.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_DeleteBoxProgramOrderRequestInventories.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_DeleteSalesOrderItems.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramInventoryKitOptionModels.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramKitOptions.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestInventories.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramRedeploymentInventory.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramRedeploymentSalesOrder.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetSalesOrderItems.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetSalesOrderShipping.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramInventoryKitOption.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramInventoryKitOptionModels.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetBoxProgramOrderRequestInventory.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetSalesOrderItems.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetSalesOrderShipping.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetCustomerPortalAddress.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_BulkRelocateInventory.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_RELOCATE_ADDED_PARTS.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_RELOCATE_INVENTORY_OR_MOVEABLE_LOCATION.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_SetInventoryKitLocation.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_SetInventoryWarehouseCapabilitiesOnRelocate.sql" />
    <Build Include="BusinessLogic\Relocate\dbo\Stored Procedures\dbo.sp_SetInventoryWarehouseCapabilityOnRelocate.sql" />
    <Build Include="BusinessLogic\Relocate\recycling\Stored Procedures\recycling.sp_BulkRelocateAssets.sql" />
    <Build Include="BusinessLogic\Relocate\recycling\Stored Procedures\recycling.sp_SetLotLocation.sql" />
    <Build Include="dbo\Stored Procedures\dbo.SP_SET_INVENTORY_ITEM_VERIFIED.sql" />
    <Build Include="dbo\Tables\dbo.D_ServiceSubcategory.sql" />
    <Build Include="dbo\Tables\dbo.D_ServiceSubcategoryPosition.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetServiceOptions.sql" />
    <Build Include="dbo\User Defined Types\dbo.AutoPricingService.sql" />
    <Build Include="dbo\User Defined Types\dbo.ServiceSubcategory.sql" />
    <Build Include="dbo\User Defined Types\dbo.ServiceSubcategoryPosition.sql" />
    <Build Include="dbo\Tables\dbo.F_ServiceTemplate.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetServiceTemplatesPage.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetServiceTemplateById.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_SetServiceTemplate.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_DeleteServiceTemplateById.sql" />
    <Build Include="dbo\Tables\dbo.F_ServiceTemplateSubcategoryPositionValue.sql" />
    <Build Include="dbo\User Defined Types\ServiceTemplatePositionValue.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_SetTemplatePositionValues.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetAutoPricingServiceTemplatePositionValues.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_IsServiceTemplatePositionValueUsed.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetSalesOrderShippingAllocatedItems.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetInventoryItemVerified.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Tables\clientportal.F_ContactSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Tables\clientportal.F_CustomerSsoSettings.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Stored Procedures\clientportal.sp_GetContactSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Stored Procedures\clientportal.sp_SetContactSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Stored Procedures\clientportal.sp_GetCustomerSsoSettings.sql" />
    <Build Include="BusinessLogic\Sso\clientportal\Stored Procedures\clientportal.sp_SetCustomerSsoSettings.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Tables\dbo.F_UserSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Tables\dbo.F_SsoSettings.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Stored Procedures\dbo.sp_GetUserSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Stored Procedures\dbo.sp_GetSsoSettings.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Stored Procedures\dbo.sp_SetUserSsoAuthSession.sql" />
    <Build Include="BusinessLogic\Sso\dbo\Stored Procedures\dbo.sp_SetSsoSettings.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetCustomerPortalAddress.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestKitOptionModels.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramOrderRequestKitOptions.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetBoxProgramRedeploymentOrderRequest.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_AddTransactionsToInvoice.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\clientportal\Stored Procedures\clientportal.sp_GetPasswordRecovery.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\clientportal\Stored Procedures\clientportal.sp_SetPasswordRecovery.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\clientportal\Tables\clientportal.F_ContactPasswordRecovery.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\dbo\Tables\dbo.F_UserPasswordRecovery.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\dbo\Stored Procedures\dbo.sp_GetPasswordRecovery.sql" />
    <Build Include="BusinessLogic\PasswordRecovery\dbo\Stored Procedures\dbo.sp_SetPasswordRecovery.sql" />
    <Build Include="dbo\Stored Procedures\dbo.GetHeciCodesByItemMasterId.sql" />
    <Build Include="dbo\Tables\dbo.F_ContractServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_GetContractServiceTemplatePositionValue.sql" />
    <Build Include="dbo\User Defined Types\ContractServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetContractServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Tables\dbo.F_OrderServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_GetOrderServiceTemplatePositionValue.sql" />
    <Build Include="dbo\User Defined Types\OrderServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServiceTemplatePositionValue.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetOrderPricingServiceTemplate.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_SetOrderPricingServiceTemplate.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_AutoPricingServiceList.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetCustomerInvoice.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetCustomerInvoiceItems.sql" />
    <Build Include="reports\Stored Procedures\Picklist\reports.sp_GetPicklist.sql" />
    <Build Include="reports\Stored Procedures\Picklist\reports.sp_GetPicklistItemsUnallocated.sql" />
    <Build Include="reports\Stored Procedures\Picklist\reports.sp_GetPicklistItemsAllocated.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_GetContactById.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_SetServiceOption.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_CreateServiceOption.sql" />
    <Build Include="dbo\Stored Procedures\sp_SET_RMA_FREIGHT.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetItemMasterProductCodes.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderCommodityPricing.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_GetBoxProgramChargeTypes.sql" />
    <Build Include="rzrapi\ItemMaster\rzrapi.sp_GetAllProductCodes.sql" />
    <Build Include="dbo\Stored Procedures\sp_LOG_SALES_ORDER_ITEM_PRICE_UPDATED.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\sp_LOG_PURCHASE_ORDER_ITEM_PRICE_UPDATED.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentItems.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentOrder.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_SetRedeploymentItemStatus.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentItemKits.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentItemKitOptions.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.F_BoxProgramInventoryKitOptionItemInventory.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentItemKitsMatchedInventory.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_SetRedeploymentItemKitOption.sql" />
    <Build Include="rzrapi\SalesOrder\rzrapi.sp_SetSalesOrderItemAllocatedByInventoryId.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_SetRedeploymentItemKit.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_ListAutoPricingServiceTemplatesForContract.sql" />
    <Build Include="recycling\Stored Procedures\PricingService\recycling.sp_GetAutoPricingServiceTemplateAttributeTypeStatus.sql" />
    <Build Include="recycling\Stored Procedures\PricingService\recycling.sp_GetContractAutoPricingServiceTemplate.sql" />
    <Build Include="recycling\Stored Procedures\Contracts\recycling.sp_GetContractAutoPricingServiceAttributeTypeStatus.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetContractServicePositionValueFromTemplate.sql" />
    <Build Include="clientportal\Functions\clientportal.fn_GetOrderScheduleLocationAddress.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentShippingsInfo.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_SetRedeploymentShipping.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_SetOrderRequestStatus.sql" />
    <Build Include="rzrapi\CreditMemo\rzrapi.sp_GetCreditMemos.sql" />
    <Build Include="clientportal\Stored Procedures\CustomerContact\clientportal.sp_IsCustomerContactLoginAvailable.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderPricingServiceAttributeTypeStatus.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionValueFromTemplate.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionValueFromContract.sql" />
    <Build Include="dbo\Functions\fn_float_distributed_cost.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_DeleteRedeploymentItemKitOption.sql" />
    <Build Include="dbo\Stored Procedures\ItemMasters\dbo.sp_ListPartNumbers.sql" />
    <Build Include="dbo\Functions\fn_float_total_consumed_value.sql" />
    <Build Include="dbo\Stored Procedures\ItemMasters\dbo.sp_ListPartNumbers.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Tables\dbo.F_SlaTemplate.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Tables\dbo.F_SlaTemplateCommodity.sql" />
    <Build Include="BusinessLogic\Sla\dbo\User Defined Types\dbo.SlaTemplateCommodity.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Tables\dbo.F_CommoditySlaRules.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_GetContractSla.sql" />
    <Build Include="BusinessLogic\Sla\dbo\User Defined Types\dbo.CommoditySlaRule.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetContractSla.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetDefaultSlaTemplateToContract.sql" />
    <Build Include="dbo\Tables\BoxProgram\dbo.F_BoxProgramTransactionInvoice.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_RemoveTransactionsFromInvoice.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_GetBoxProgramInventoryAttributes.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_ListOpenedCustomerInvoices.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetSystemSettingsInfo.sql" />
    <Build Include="rzrapi\BoxProgram\rzrapi.sp_RebuildCustomerInvoices.sql" />
    <Build Include="recycling\Stored Procedures\Settlement\recycling.sp_SyncCalculatedPricingServicesForRecyclingOrder.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetPricingServicePosition.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_GetAssetPricingServicePositions.sql" />
    <Build Include="recycling\Stored Procedures\Assets\recycling.sp_SetAssetPricingServicePositions.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_GetInboundOrderSla.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetInboundOrderSla.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_GetBoxProgramRemarketingOrderRequest.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_GetBoxProgramRemarketingInventory.sql" />
    <Build Include="dbo\Tables\dbo.F_InboundOrderManifest.sql" />
    <Build Include="dbo\User Defined Types\dbo.InboundOrderManifest.sql" />
    <Build Include="rzrapi\InboundOrderManifest\rzrapi.sp_AddInboundOrderManifests.sql" />
    <Build Include="rzrapi\InboundOrderManifest\rzrapi.sp_GetInboundOrderManifests.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderAllowedCommodityById.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionFromAppliedContract.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionFromDefaultTemplate.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionFromOrder.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetContractUsedServiceTemplate.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetOrderUsedServiceTemplate.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetDefaultSlaToInboundOrder.sql" />
    <Build Include="rzrapi\BoxProgram\Remarketing\rzrapi.sp_GetRemarketingItems.sql" />
    <Build Include="rzrapi\BoxProgram\Remarketing\rzrapi.sp_GetRemarketingOrder.sql" />
    <Build Include="recycling\Functions\recycling.fn_bit_IsAssetWorkflowStepRedeployment.sql" />
    <Build Include="rzrapi\BoxProgram\Redeployment\rzrapi.sp_GetRedeploymentOrderScanCounts.sql" />
    <Build Include="dbo\Tables\dbo.F_CustomerServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_GetCustomerServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetCustomerServiceTemplatePositionValue.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetCustomerServicePositionValueFromTemplate.sql" />
    <Build Include="rzrapi\Customer\PricingService\rzrapi.sp_GetCustomerPricingServiceAttributeTypeStatus.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetPricingTermApplyContractPreview.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_SetOrderServicePositionFromCustomer.sql" />
    <Build Include="recycling\Tables\recycling.F_CategoryStateProgram.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetCategoryStatePrograms.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCommodityAndInboundOrderStatePrograms.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCategoryStatePrograms.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetCategoryAndInboundOrderStatePrograms.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetStatePrograms.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetQuarantine.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_GetAssetsInQuarantine.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_SetAssetQuarantine.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetAssetsInQuarantine.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_CleanupOldAuditToolReports.sql" />
    <Build Include="dbo\Stored Procedures\PricingService\dbo.sp_ResetCustomerServicePositionValueAndTemplate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetGradeTemplates.sql" />
    <Build Include="clientportal\Tables\clientportal.F_BoxProgramRedeploymentSubrequest.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_SetBoxProgramRedeploymentSubrequest.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_GetAuditToolSettings.sql" />
    <Build Include="integration\Stored Procedures\integration.sp_SetAuditToolSettings.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_UpdateSalesOrder.sql" />
    <Build Include="dbo\Tables\dbo.C_SettingsSource.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_GetCommoditySlaRulesForContract.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_SetCommoditySlaRules.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_SetCommoditySlaRulesForContract.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_SetCommoditySlaRulesForCustomer.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_SetCommoditySlaRulesForInboundOrder.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\Template\dbo.sp_GetListOfSlaTemplates.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\Template\dbo.sp_GetPageOfSlaTemplates.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\Template\dbo.sp_SetSlaTemplate.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_GetCommoditySlaRulesForInboundOrder.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\ComoditySlaRules\dbo.sp_GetCommoditySlaRulesForCustomer.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_GetCustomerSla.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetCustomerSla.sql" />
    <Build Include="BusinessLogic\Sla\dbo\Stored Procedures\dbo.sp_SetDefaultSlaTemplateToCustomer.sql" />
    <Build Include="recycling\Tables\recycling.F_AssetQuarantineComment.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAssetQuarantine.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_GetAssetQuarantineCommentsPage.sql" />
    <Build Include="recycling\Stored Procedures\LotAudit\recycling.sp_SetAssetQuarantineComment.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetAssetQuarantine.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_GetAssetQuarantineCommentsPage.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetAssetQuarantineComment.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_GetAssetQuarantine.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_GetAssetQuarantineCommentsPage.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_SetAssetQuarantine.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_SetAssetQuarantineComment.sql" />
    <Build Include="rzrapi\BoxProgram\Takeback\rzrapi.sp_DeleteAssetQuarantineComment.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_SetAssetQuarantine.sql" />
    <Build Include="clientportal\Stored Procedures\BoxProgram\clientportal.sp_DeleteAssetQuarantineComment.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\sp_GET_RECYCLING_ITEM_MASTER_BY_ID.sql" />
    <Build Include="rzrapi\ServiceOptions\rzrapi.sp_GetOrderPricingServiceTemplateList.sql" />
    <Build Include="dbo\Stored Procedures\EmailTemplates\dbo.sp_GetTemplateDataForAssetQuarantineComment.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForAsset.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForComment.sql" />
    <Build Include="rzrapi\EmailTemplates\rzrapi.sp_GetTemplateDataForQuarantine.sql" />
    <Build Include="rzrapi\InboundOrderManifest\rzrapi.sp_DeleteInboundOrderManifest.sql" />
    <Build Include="rzrapi\InboundOrderManifest\rzrapi.sp_SetInboundOrderManifest.sql" />
    <Build Include="rzrapi\InboundOrderManifest\rzrapi.sp_ApplyInboundOrderManifestPricesToAssets.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_SetBoxProgramInboundOrderStatus.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetBoxProgramRequestsByItemInventoryId.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetBoxProgramInboundOrderByRequestId.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetBoxProgramRequestItemCount.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetBoxProgramIsAllProcessed.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderAppliedPricingTemplate.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_SetRecyclingOrderAppliedPricingTemplate.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetAllowedPricingTemplatesForCustomer.sql" />
    <Build Include="rzrapi\Customer\Tag\rzrapi.sp_GetCustomerTags.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobComment.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobContact.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobEmployee.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobExpense.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobExpenseFiles.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_DeleteInboundOrderJobSubtask.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobComments.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobContacts.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobEmployees.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobExpenseFiles.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobExpenses.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobs.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobsTotals.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobSubtasks.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_GetInboundOrderJobTimeTracks.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJob.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobComment.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobContacts.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobContactsList.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobEmployees.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobEmployeesList.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobExpense.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobExpenseFile.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobSignersList.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobStatus.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobSubtask.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobSubtaskStatus.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobTimeTrackEnd.sql" />
    <Build Include="recycling\Stored Procedures\InboundOrderJob\recycling.sp_SetInboundOrderJobTimeTrackStart.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetRecyclingOrderAssetForReport.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetClientPortalReportsForAsset.sql" />
    <Build Include="dbo\Functions\tvf_GET_SNIPPET_DATA_PURCHASE_ORDER_CREATED.sql" />
    <Build Include="recycling\Functions\recycling.fn_bigint_GetAssetRemarketingRequestId.sql" />
    <Build Include="dbo\Tables\dbo.C_RecyclingQuoteStatus.sql" />
    <Build Include="dbo\Tables\dbo.C_RecyclingQuoteTransition.sql" />
    <Build Include="dbo\Tables\dbo.F_RecyclingQuoteTransitionHistory.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_GetRecyclingQuoteStatuses.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_GetRecyclingQuoteById.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_GetRecyclingQuoteStatusTransition.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_SetRecyclingQuoteStatus.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_AddRecyclingQuoteTransitionHistory.sql" />
    <Build Include="rzrapi\RecyclingQuoteStatus\rzrapi.sp_GetQuoteTransitionHistoryById.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_SetInboundOrderInfoFromRecyclingQuote.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_SetRecyclingOrderCommoditiesDestruction.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GET_COMPANY_SALES_ORDER_OPTIONS.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_SET_COMPANY_SALES_ORDER_OPTIONS.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_GetAuditOptions.sql" />
    <Build Include="dbo\Stored Procedures\Settings\dbo.sp_SetAuditOptions.sql" />
    <Build Include="dbo\Stored Procedures\sp_ListSalesOrder.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_ServiceTypeOnsiteOption.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetServiceOnsiteOptions.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_SetRecyclingServiceOnsiteOptions.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.D_ServiceOnsiteOptionHierarchy.sql" />
    <Build Include="dbo\Functions\dbo.fn_varchar_GetInventoryCapability.sql" />
    <Build Include="dbo\Functions\dbo.tvf_GetRecyclingSummaryOrderItemById.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingSummaryOrderItemById.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.D_RecyclingServiceLabel.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.D_RecyclingServiceTypeCategory.sql" />
    <Build Include="dbo\Tables\OnsiteService\Pricing\dbo.F_PricingMatrix.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetPricingMatrix.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_SetRecyclingServiceLabel.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetOnsiteCategories.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi_sp_GetOnsiteLabels.sql" />
    <Build Include="rzrapi\RecyclingService\Pricing\rzrapi.sp_SetPricingMatrix.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_SetServiceCategories.sql" />
    <Build Include="rzrapi\RecyclingService\rzrapi.sp_SetServiceLabels.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.D_RecyclingServiceTypeLabel.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetRecyclingServiceLabels.sql" />
    <Build Include="dbo\User Defined Types\dbo.PricingMatrix.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderOnsiteOption.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderServiceOnsiteOption.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetInboundOrderOnsiteOptions.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetServiceOnsiteOptionsByType.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_SetInboundOrderOnsiteOptions.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderOnsiteCategories.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderOnsiteLabels.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderServiceCategories.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetInboundOrderOnsiteCategories.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_AddInboundOrderOnsiteCategory.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_AddOnsiteCategoryService.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_AddInboundOrderOnsiteCategoryServices.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_UpdateOnsiteCategoryService.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_UpdateOnsiteCategory.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_DeleteOnsiteCategory.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_DeleteOnsiteCategoryService.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetInboundOrderOnsiteLabels.sql" />
    <Build Include="dbo\Tables\OnsiteService\dbo.F_InboundOrderServiceLabels.sql" />
    <Build Include="dbo\Tables\dbo.F_UserRoleToSystemSettingsPage.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetAdminMenuStructureForRole.sql" />
    <Build Include="dbo\Functions\dbo.fn_bit_IsAccessToSettingsPageAllowedForUserId.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_GetInboundOrderServiceOnsiteOptions.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_SetInboundOrderServiceOnsiteOptions.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderServiceLabels.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetInboundOrderServiceCategories.sql" />
    <Build Include="dbo\Functions\fn_money_GET_RECYCLING_SERVICE_PRICE_MATRIX_VALUE.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_AddRecyclingOrderItemServicePricing.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_UpdateRecyclingOrderItemServicePricing.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetRecyclingSummaryOrderItemsForExport.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Tables\F_LinkedPurchaseOrder.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_GetLinkedPurchaseOrders.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetInternalCostFromLinkedPurchaseOrders.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_SetLinkedPurchaseOrder.sql" />
    <Build Include="BusinessLogic\PurchaseOrders\dbo\Stored Procedures\dbo.sp_UnlinkPurchaseOrders.sql" />
    <Build Include="recycling\Stored Procedures\recycling.sp_GetLotHasAssetAndInventoryItems.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetOnsiteServiceQtyFromContractItem.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetOnsiteServiceSinglePriceFromContractItem.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetOnsiteServicePricingTireFromContractItem.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_SetBoxProgramShippingTrackingNumber.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_AddInboundOrderOnsiteLabel.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_UpdateOnsiteLabel.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_DeleteOnsiteLabel.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_AddOnsiteLabelService.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_UpdateOnsiteLabelService.sql" />
    <Build Include="rzrapi\OnsiteService\rzrapi.sp_DeleteOnsiteLabelService.sql" />
    <Build Include="dbo\Functions\fn_money_GET_ITEM_PRICE_BY_TYPE_RESALE.sql" />
    <Build Include="dbo\Stored Procedures\Onsite\sp_GetRecyclingOrderOnsiteServiceDetails.sql" />
    <Build Include="dbo\Stored Procedures\Onsite\sp_GetRecyclingOrderItemService.sql" />
    <Build Include="dbo\Stored Procedures\Procs1\dbo.sp_GetRecyclingOrderServicesForReport.sql" />
    <Build Include="integration\Stored Procedures\SellerCloud\integration.sp_GetCancelledSellerCloudOrdersToCancel.sql" />
    <Build Include="dbo\Functions\dbo.fn_nvarchar_json_GetLinkedContactPersons.sql" />
    <Build Include="clientportal\Stored Procedures\clientportal.sp_GetBoxProgramInventoryIntersections.sql" />
    <Build Include="dbo\Functions\fn_float_GetServicePriceByQty.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetConsignmentItems.sql" />
    <Build Include="rzrapi\InboundOrder\rzrapi.sp_GetRecyclingOrderWorkInstructions.sql" />
    <Build Include="dbo\Functions\fn_money_GET_INVENTORY_VALUATION_TOTAL_VALUE.sql" />
    <Build Include="rzrapi\Inventory\Scan\sp_GetItemInventoryConsigmentContractInfo.sql" />
    <Build Include="rzrapi\Asset\rzrapi.sp_ListAssets.sql" />
    <Build Include="dbo\User Defined Types\dbo.AttachedFile.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsTiedToClosedCharitySalesOrdersInDateRange.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfEcommerceAssetsByWorkflowAndAttribute.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfAssetsAuditedByWorkflow.sql" />
    <Build Include="rzrapi\Stored Procedures\rzrapi.sp_GetBoxProgramCompanyPayor.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotNetWeightInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalLotNetWeightInDateRangeKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssetsWeightInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTotalAssetsWeightInDateRangeKgs.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetAssetsTotalSplitByWorkflowStepConverted.sql" />
    <Build Include="bi\Stored Procedures\bi.sp_GetAssetsTotalSplitByWorkflowStepKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByWeightInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopResaleCategoriesOfInventoryByWeightInDateRangeKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityGroupedByWorkflowInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetCommodityGroupedByWorkflowInDateRangeKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetGroupedCommodityInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetGroupedCommodityInDateRangeKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsInProductionByWorkflowInDateRangeConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetAssetsInProductionByWorkflowInDateRangeKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfSoldItemsByWeightConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetTopCategoriesOfSoldItemsByWeightKgs.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldCommodityTotalWeightConverted.sql" />
    <Build Include="bi\Functions\bi.tvf_GetSoldCommodityTotalWeightKgs.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="EnumTablesData.dcmp" />
    <None Include="Import\EnterasourceMasterItemImport_RSW-7943.sql" />
    <None Include="Import\EnterasourceManufacturersImport_RSW-7943.sql" />
    <None Include="Import\EnterasourceCategoriesImport_RSW-7943.sql" />
    <None Include="Import\CloneCustomerWithContactsAndAddresses.sql" />
    <None Include="Scripts\Updates\Fix page permissions.sql" />
    <Build Include="recycling\Stored Procedures\Services\recycling.sp_SetServiceInternalCost.sql" />
    <Build Include="dbo\Stored Procedures\Location\dbo.sp_GetLocationsMaxWeights.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetLogsBatchByLogTable.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetLogActionDataBatch.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetLogDataBatch.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetIntegrationLogBatch.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetLotActionLogBatch.sql" />
    <Build Include="reports\Stored Procedures\reports.sp_GetOnHandAssets.sql" />
    <Build Include="cp\Tables\F_LogData.sql" />
    <Build Include="dbo\Stored Procedures\dbo.sp_GetPortalLogDataBatch.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="Razor.DbRzr.refactorlog" />
  </ItemGroup>
  <ItemGroup>
    <ArtifactReference Include="$(DacPacRootPath)\Extensions\Microsoft\SQLDB\Extensions\SqlServer\130\SqlSchemas\master.dacpac">
      <HintPath>$(DacPacRootPath)\Extensions\Microsoft\SQLDB\Extensions\SqlServer\130\SqlSchemas\master.dacpac</HintPath>
      <SuppressMissingDependenciesErrors>False</SuppressMissingDependenciesErrors>
      <DatabaseVariableLiteralValue>master</DatabaseVariableLiteralValue>
    </ArtifactReference>
  </ItemGroup>
  <ItemGroup>
    <SqlCmdVariable Include="DW_FINANCE">
      <DefaultValue>DW_FINANCE</DefaultValue>
      <Value>$(SqlCmdVar__1)</Value>
    </SqlCmdVariable>
    <SqlCmdVariable Include="DW_FINANCE_STAGE">
      <DefaultValue>DW_FINANCE_STAGE</DefaultValue>
      <Value>$(SqlCmdVar__3)</Value>
    </SqlCmdVariable>
    <SqlCmdVariable Include="LS_FINANCE">
      <DefaultValue>LS_FINANCE</DefaultValue>
      <Value>$(SqlCmdVar__4)</Value>
    </SqlCmdVariable>
  </ItemGroup>
  <ItemGroup>
    <Content Include="BusinessLogic\Taxes\dbo\Stored Procedures\dbo.sp_GetAvalaraTransaction.sql" />
    <Content Include="BusinessLogic\Taxes\dbo\Tables\dbo.F_AvalaraTransaction.sql" />
  </ItemGroup>
</Project>
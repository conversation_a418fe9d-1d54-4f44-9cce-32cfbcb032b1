CREATE TABLE [dbo].[C_SOURCE_SYS] (
    [SOURCE_SYS_ID]     BIGINT        IDENTITY (1, 1) NOT NULL,
    [SOURCE_SYS_CD]     VARCHAR (50)  NOT NULL,
    [SOURCE_SYS_DESC]   VARCHAR (250) NULL,
    [IS_AGGR_PREFERRED] BIT           CONSTRAINT [DF_C_SOURCE_SYS_IS_AGGR_PREFERRED] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]       BIT           NOT NULL,
    [IS_DELETED]        BIT           NOT NULL,
    [INSERTED_BY]       VARCHAR (50)  NOT NULL,
    [INSERTED_DT]       DATETIME      NOT NULL,
    [UPDATED_BY]        VARCHAR (50)  NULL,
    [UPDATED_DT]        DATETIME      NULL,
    [DELETED_BY]        VARCHAR (50)  NULL,
    [DELETED_DT]        DATETIME      NULL,
    CONSTRAINT [PK_C_SOURCE_SYS] PRIMARY KEY CLUSTERED ([SOURCE_SYS_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The copy of sales channels table', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_SOURCE_SYS';


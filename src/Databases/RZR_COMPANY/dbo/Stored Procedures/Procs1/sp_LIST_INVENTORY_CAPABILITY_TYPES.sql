-- =============================================
-- Author: <Oleg K. Evseev>
-- Create date: <11/15/2013>
-- Description: <lists inventory capability types>
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_INVENTORY_CAPABILITY_TYPES]
	@C_ATTRIBUTE_SET_ID int				= NULL,
	@C_IS_SKU_EFFECTING bit				= NULL,
	@IsSystemReturn		bit				= 0,
	@Term				varchar(max)	= null,
	@Count				int				= null
AS
BEGIN

	set @Term = nullif(nullif(nullif(rtrim(ltrim(@Term)), ''), '%'), '%%');
	set @Count = isnull(@Count, 1000000);

	IF(@C_ATTRIBUTE_SET_ID IS NULL)
	BEGIN

		SELECT TOP(@Count)
			INVENTORY_CAPABILITY_TYPE_ID		AS value,
			INVENTORY_CAPABILITY_TYPE_NAME		AS label,
			INVENTORY_CAPABILITY_LABEL			AS descr
		FROM C_INVENTORY_CAPABILITY_TYPE WITH (NOLOCK)
		WHERE IS_DELETED = 0
			AND IS_INACTIVE = 0
			and (IsSystem = 0 or @IsSystemReturn = 1)
			and (@Term is null or INVENTORY_CAPABILITY_TYPE_NAME like @Term)
		order by INVENTORY_CAPABILITY_TYPE_NAME;

	END
	ELSE
	BEGIN

		SELECT TOP(@Count)
			CT.INVENTORY_CAPABILITY_TYPE_ID		AS value,
			CT.INVENTORY_CAPABILITY_TYPE_NAME	AS label,
			CT.INVENTORY_CAPABILITY_LABEL		AS descr
		FROM F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE	A	WITH (NOLOCK)
		INNER JOIN C_INVENTORY_CAPABILITY_TYPE			CT	WITH (NOLOCK)
			ON CT.INVENTORY_CAPABILITY_TYPE_ID = A.INVENTORY_CAPABILITY_TYPE_ID
			AND CT.IS_DELETED = 0
			AND CT.IS_INACTIVE = 0
		WHERE A.INVENTORY_ATTRIBUTE_TYPE_ID = @C_ATTRIBUTE_SET_ID
			AND A.IS_DELETED = 0
			AND A.IS_INACTIVE = 0
			AND (@C_IS_SKU_EFFECTING IS NULL OR @C_IS_SKU_EFFECTING = 0 OR A.IS_SKU_EFFECTING = @C_IS_SKU_EFFECTING)
			and (ct.IsSystem = 0 or @IsSystemReturn = 1)
			and (@Term is null or INVENTORY_CAPABILITY_TYPE_NAME like @Term)
		ORDER BY A.ITEM_INVENTORY_ORDINATION;

	END
END
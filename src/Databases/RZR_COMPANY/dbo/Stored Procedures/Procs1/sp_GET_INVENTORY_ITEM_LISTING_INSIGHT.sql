-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE  [dbo].[sp_GET_INVENTORY_ITEM_LISTING_INSIGHT]
	@ITEM_ID BIGINT 
AS
BEGIN
	SELECT
		 im.ITEM_NUMBER	as ItemNumber
		, fi.ITEM_ID as ItemId
		, fi.ITEM_MASTER_ID as MasterItemId
		, dc.ITEM_CONDITION_CD	CONDITION
		, 1	MARKETPLACE -- 1 is eBay
	FROM dbo.F_ITEM fi
	INNER JOIN dbo.F_ITEM_MASTER im
		ON fi.ITEM_MASTER_ID = im.ITEM_MASTER_ID
	INNER JOIN dbo.D_ITEM_CONDITION dc WITH (NOLOCK)
		ON fi.CONDITION_ID = dc.ITEM_CONDITION_ID
	WHERE 
		fi.ITEM_ID = @ITEM_ID
END
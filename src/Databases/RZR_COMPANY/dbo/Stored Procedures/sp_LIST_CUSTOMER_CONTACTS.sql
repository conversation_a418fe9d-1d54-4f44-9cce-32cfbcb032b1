CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMER_CONTACTS] 		
	@CUSTOMER_ID	BIGINT	,
	@TERM			NVARCHAR(MAX) = N'',
	@COUNT			INT		    = 20
AS
BEGIN

	SELECT top (@COUNT)
		[value],
		label,
		[desc]
	FROM (
		SELECT
			cc.CUSTOMER_CONTACT_ID												AS value,		
			dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID)	AS label,
			cc.JOB_TITLE														AS [DESC],
			CC.IS_MAIN														
		FROM dbo.F_CUSTOMER_CONTACT cc WITH(NOLOCK)
		WHERE cc.CUSTOMER_ID = @CUSTOMER_ID
		  AND CC.IS_INACTIVE = 0
		  AND CC.IS_DELETED = 0
		  and (isnull(@TERM, '') = '' 
			or dbo.fn_str_GET_CUSTOMER_CONTACT_AUTO_NAME(cc.CUSTOMER_CONTACT_ID) like '%' + @TERM + '%')	
	) T
	ORDER BY IS_MAIN DESC, label ASC
END
CREATE TABLE [dbo].[F_ITEM_IMAGE] (
    [IMAGE_ID]              BIGINT         IDENTITY (1, 1) NOT NULL,
    [ITEM_ID]               BIGINT         NOT NULL,
    [POSITION_INDEX]        INT            NULL,
    [NAME]                  NVARCHAR (256) NOT NULL,
    [DESCR]                 NVARCHAR (256) NULL,
    [GALLEREY_BASE64]       NVARCHAR (MAX) NULL,
    [GALLEREY_BYTE_COUNT]   BIGINT         NULL,
    [BASE_BASE64]           NVARCHAR (MAX) NULL,
    [BASE_BYTE_COUNT]       BIGINT         NULL,
    [BASE_SELECTED]         BIT            CONSTRAINT [DF_F_ITEM_IMAGE_INITIAL_SELECTED] DEFAULT ((0)) NOT NULL,
    [SMALL_BASE64]          NVARCHAR (MAX) NULL,
    [SMALL_BYTE_COUNT]      BIGINT         NULL,
    [SMALL_SELECTED]        BIT            CONSTRAINT [DF_F_ITEM_IMAGE_SMALL_SELECTED] DEFAULT ((0)) NOT NULL,
    [THUMBNAIL_BASE64]      NVARCHAR (MAX) NULL,
    [THUMBNAIL_BYTE_COUNT]  BIGINT         NULL,
    [THUMBNAIL_SELECTED]    BIT            CONSTRAINT [DF_F_ITEM_IMAGE_THUMBNAIL_SELECTED] DEFAULT ((0)) NOT NULL,
    [PARENT_ID]             BIGINT         NULL,
    [MEDIA_GALLERY]         NVARCHAR (MAX) NULL,
    [MEDIA_GALLERY_LABEL]   NVARCHAR (MAX) NULL,
    [BASE_IMAGE]            NVARCHAR (MAX) NULL,
    [BASE_IMAGE_LABEL]      NVARCHAR (MAX) NULL,
    [SMALL_IMAGE]           NVARCHAR (MAX) NULL,
    [SMALL_IMAGE_LABEL]     NVARCHAR (MAX) NULL,
    [THUMBNAIL_IMAGE]       NVARCHAR (MAX) NULL,
    [THUMBNAIL_IMAGE_LABEL] NVARCHAR (MAX) NULL,
    [IS_INACTIVE]           BIT            CONSTRAINT [DF_F_ITEM_IMAGE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]            BIT            CONSTRAINT [DF_F_ITEM_IMAGE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]           NVARCHAR (150) CONSTRAINT [DF_F_ITEM_IMAGE_INSERTED_BY] DEFAULT ((1)) NOT NULL,
    [INSERTED_DT]           DATETIME       CONSTRAINT [DF_F_ITEM_IMAGE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]            NVARCHAR (150) NULL,
    [UPDATED_DT]            DATETIME       NULL,
    [DELETED_BY]            NVARCHAR (150) NULL,
    [DELETED_DT]            DATETIME       NULL,
    [GALLEREY_FILE_ID]      BIGINT         NULL,
    [BASE_FILE_ID]          BIGINT         NULL,
    [SMALL_FILE_ID]         BIGINT         NULL,
    [THUMBNAIL_FILE_ID]     BIGINT         NULL,
    CONSTRAINT [PK_F_ITEM_IMAGE] PRIMARY KEY CLUSTERED ([IMAGE_ID] ASC)
);

GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_IMAGE_ITEM_ID]
    ON [dbo].[F_ITEM_IMAGE]([ITEM_ID] ASC)
    INCLUDE([IMAGE_ID], [NAME], BASE_SELECTED, SMALL_SELECTED, THUMBNAIL_SELECTED);
GO

CREATE UNIQUE NONCLUSTERED INDEX [UK_F_ITEM_IMAGE_BASE_SELECTED]
    ON [dbo].[F_ITEM_IMAGE]([ITEM_ID] ASC) WHERE ([BASE_SELECTED]=(1));


GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_IMAGE_THUMBNAIL_SELECTE]
    ON [dbo].[F_ITEM_IMAGE]([THUMBNAIL_SELECTED] ASC)
    INCLUDE([ITEM_ID], [THUMBNAIL_IMAGE_LABEL]);


GO
CREATE NONCLUSTERED INDEX [IX_F_ITEM_IMAGE_SMALL_SELECTED]
    ON [dbo].[F_ITEM_IMAGE]([SMALL_SELECTED] ASC)
    INCLUDE([ITEM_ID], [SMALL_IMAGE_LABEL]);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UK_F_ITEM_IMAGE_THUMBNAIL_SELECTED]
    ON [dbo].[F_ITEM_IMAGE]([ITEM_ID] ASC) WHERE ([THUMBNAIL_SELECTED]=(1));


GO
CREATE UNIQUE NONCLUSTERED INDEX [UK_F_ITEM_IMAGE_SMALL_SELECTED]
    ON [dbo].[F_ITEM_IMAGE]([ITEM_ID] ASC) WHERE ([SMALL_SELECTED]=(1));
GO


CREATE NONCLUSTERED INDEX [IX__F_ITEM_IMAGE__ITEM_ID__BASE_FILE_ID__GALLEREY_FILE_ID]
     ON [dbo].[F_ITEM_IMAGE]([ITEM_ID] ASC)
     INCLUDE([BASE_FILE_ID],[GALLEREY_FILE_ID]);
 GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of images for item (SKU). It is sent to magento, it has thumbnail, main, etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_IMAGE';


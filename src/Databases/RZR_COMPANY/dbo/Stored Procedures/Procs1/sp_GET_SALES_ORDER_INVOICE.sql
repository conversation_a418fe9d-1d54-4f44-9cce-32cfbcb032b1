CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_INVOICE]
	@INVOICE_ID			BIGINT
AS
BEGIN
	
	SELECT 
		I.INVOICE_ID								as InvoiceId
		,I.AUTO_NAME								as InvoiceAutoName
		,O.SALES_ORDER_ID							as <PERSON><PERSON><PERSON><PERSON>Id
		,<PERSON><PERSON>SALES_ORDER_NUMBER						as <PERSON><PERSON><PERSON>
		,O<PERSON>CUSTOMER_ID								as CustomerId
		,C.CUSTOMER_NAME							AS CustomerCd
		,I.DATE_CREATED								as DateCreated
		,DATEADD(dd, T.DUE_DAYS, I.DATE_CREATED)	AS DateDue
		,CASE
			WHEN I.STATUS_ID = 5 THEN 1
			ELSE 0
		END											AS IsFullyPaid
		,I.PAID_DATE								as PaidDate
		,I.IS_VOIDED								as IsVoided
		,I.REP_USER_ID								AS RepUserId
		,R.UserName									AS RepUserCd
		,I.ORIGINAL_AMOUNT							as Amount
		,I.AMOUNT_DUE								as AmountDue
		,<PERSON><PERSON>LE<PERSON>_ORDER_DATE							AS SalesOrderDate
		,<PERSON><PERSON><PERSON>O_NUMBER								AS SalesOrderPoNumber
		,<PERSON><PERSON><PERSON>N<PERSON><PERSON>								as <PERSON><PERSON><PERSON><PERSON>
	FROM F_INVOICE I WITH(NOLOCK)
	INNER JOIN F_SALES_ORDER O WITH(NOLOCK)
		ON I.ORDER_ID = O.SALES_ORDER_ID
	INNER JOIN F_CUSTOMER C WITH(NOLOCK)
		ON O.CUSTOMER_ID = C.CUSTOMER_ID
	INNER JOIN C_CUSTOMER_TRANSACTION_TERM T WITH(NOLOCK)
		ON O.TERM_ID = T.CUSTOMER_TRANSACTION_TERM_ID
	INNER JOIN tb_User R WITH(NOLOCK)
		ON I.REP_USER_ID = R.UserID
	WHERE I.INVOICE_ID = @INVOICE_ID
			
END
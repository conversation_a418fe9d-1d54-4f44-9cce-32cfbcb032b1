CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_ORDER_SUBTOTAL]
    (@SETTLEMENT_STATE_ID INT,
     @ORDER_ID		    BIGINT)
RETURNS FLOAT
AS
BEGIN
    DECLARE @SUBTOTAL FLOAT;

    SELECT
	   @SUBTOTAL = SUM(T.SUBTOTAL)
	   FROM
	   (
		  SELECT
			 ISNULL([dbo].[fn_money_GET_RECYCLING_ORDER_INBOUND_ITEMS_TOTAL_PRICE](@ORDER_ID, @SETTLEMENT_STATE_ID), 0.0) AS SUBTOTAL		  
		  UNION ALL
		  SELECT			
			 ISNULL(SUM(ISNULL(OS.ITEM_SERVICE_PRICE_FOR_ONE, 0.0) * OS.ITEM_SERVICE_COUNT), 0.0) AS SUBTOTAL -- SUM = NULL if no records
		  FROM F_RECYCLING_ORDER_ITEM_SERVICE		OS	WITH(NOLOCK)
		  WHERE OS.RECYCLING_ORDER_ID = @ORDER_ID
	   ) T	
RETURN @SUBTOTAL;
END
/*
	declare @tIds dbo.bigint_ID_ARRAY
	insert into @tIds
	select distinct top(10) ITEM_MASTER_ID from F_ITEM_MASTER WHERE IS_DELETED = 0 AND IS_INACTIVE = 0 order by 1 desc
	exec [queue].[sp_GetInventoryIdsByItemMasterIds] @tIds
*/
create procedure [queue].[sp_GetInventoryIdsByPrimaryCategoryIds]
	@CategoryIds dbo.bigint_ID_ARRAY readonly
AS

	SELECT distinct
		FII.ITEM_INVENTORY_ID	as ItemInventoryId
	FROM @CategoryIds							ids
	inner join [dbo].[F_ITEM_MASTER_CATEGORY]	FIMC WITH(NOLOCK)
		on fimc.CATEGORY_ID = ids.ID
		and fimc.IS_INACTIVE = 0
		and fimc.IS_DELETED  = 0
		and fimc.IS_PRIMARY = 1
	inner join dbo.F_ITEM_INVENTORY				FII with(nolock)
		on FII.ITEM_MASTER_ID = fimc.ITEM_MASTER_ID
	-- only currently active: the manipulations with the parent never change the state of the inventory
	where FII.IS_DELETED = 0

go

-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_MAGENTO_ORDER_MAGENTO_SHIPMENT_ID]
	@SALES_ORDER_ID BIGINT,
	@M_SHIPMENT_ID	NVARCHAR(50),
	@DATA			XML
AS
BEGIN
	UPDATE MAGENTO_ORDERS SET
		M_SHIPMENT_ID = @M_SHIPMENT_ID
	WHERE SALES_ORDER_ID = @SALES_ORDER_ID
	
	-- process magento track info
	
	DECLARE @idoc INT
	-- read xml list into "@idoc"
    EXEC sp_xml_preparedocument @idoc OUTPUT, @DATA
	-- move it values into the "@caps" table variable
	DECLARE	@temp TABLE ( 
		SOURCE_ID			BIGINT,
		IS_MASTER_TRACK		BIT,
		MAGENTO_TRACK_ID	INT)

    INSERT INTO @temp (
		SOURCE_ID,	
		IS_MASTER_TRACK,
		MAGENTO_TRACK_ID)
    	SELECT -- select from xml doc as table
			x.SOURCE_ID,	
			x.IS_MASTER_TRACK,
			x.MAGENTO_TRACK_ID
		FROM OPENXML (@idoc, '/Root/Items', 1)
		WITH (SOURCE_ID			BIGINT,
			  IS_MASTER_TRACK	BIT,
			  MAGENTO_TRACK_ID	INT) AS x

    -- delete xml doc
	EXEC sp_xml_removedocument @idoc
	
	-- write master track info	
	--UPDATE 	fsos
	--SET
	--	fsos.MAGENTO_TRACK_ID = t.MAGENTO_TRACK_ID
	--FROM [F_SHIPPING] fsos
	--INNER JOIN @temp t
	--  ON t.SOURCE_ID = fsos.SHIPPING_ID AND t.IS_MASTER_TRACK = 1 
	
	-- write all another track info
	UPDATE 	fsp
	SET
		fsp.MAGENTO_TRACK_ID = t.MAGENTO_TRACK_ID
	FROM F_SHIPPING_PACKAGE fsp
	INNER JOIN @temp AS t
		ON t.SOURCE_ID = fsp.PACKAGE_ID -- AND t.IS_MASTER_TRACK = 0 
	
END
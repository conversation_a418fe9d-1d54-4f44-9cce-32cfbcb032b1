CREATE TABLE [dbo].[D_INVENTORY_AUDIT_STATUS] (
    [STATUS_ID]   INT            NOT NULL,
    [STATUS_NAME] NVARCHAR (124) NOT NULL,
    [IS_INACTIVE] BIT            CONSTRAINT [DF_D_INVENTORY_AUDIT_STATUS_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]  BIT            CONSTRAINT [DF_D_INVENTORY_AUDIT_STATUS_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY] VARCHAR (150)  CONSTRAINT [DF_D_INVENTORY_AUDIT_STATUS_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT] DATETIME       CONSTRAINT [DF_D_INVENTORY_AUDIT_STATUS_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]  VARCHAR (150)  NULL,
    [UPDATED_DT]  DATETIME       NULL,
    [DELETED_BY]  VARCHAR (150)  NULL,
    [DELETED_DT]  DATETIME       NULL,
    CONSTRAINT [PK_D_INVENTORY_AUDIT_STATUS] PRIMARY KEY CLUSTERED ([STATUS_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The inventory audit status list, is not changeable ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_INVENTORY_AUDIT_STATUS';


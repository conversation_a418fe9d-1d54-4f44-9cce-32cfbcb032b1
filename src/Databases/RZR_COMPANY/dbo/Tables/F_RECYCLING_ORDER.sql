CREATE TABLE [dbo].[F_RECYCLING_ORDER] (
    [RECYCLING_ORDER_ID]            BIGINT          IDENTITY (1, 1) NOT NULL,
    [USER_ID]                       BIGINT          NOT NULL,
    [SO_TERM_ID]                    INT             NOT NULL,
    [CUSTOMER_ID]                   BIGINT          NOT NULL,
    [DESCR]                         NVARCHAR (MAX)  NULL,
    [INTERNAL_COMMENT]              NVARCHAR (MAX)  NULL,
    [RECEIVING_NOTES]               NVARCHAR (MAX)  NULL,
    [PICKUP_START_DATE]             DATETIME        NULL,
    [PICKUP_END_DATE]               DATETIME        NULL,
    [RECYCLING_ORDER_STATUS_ID]     INT             NULL,
    [BOL_NUMBER]                    NVARCHAR (50)   NULL,
    [PO_NUMBER]                     NVARCHAR (50)   NULL,
    [IS_INBOUND]                    BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_INBOUND] DEFAULT ((0)) NOT NULL,
    [USE_DEFAULT_PRICE]             BIT             NULL,
    [DEFAULT_PRICE]                 FLOAT (53)      NULL,
    [DEFAULT_PRICE_TYPE_ID]         INT             NULL,
    [SETTLE_DATE]                   DATETIME        NULL,
    [IS_PROCESSED_MODE_]            BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_PROCESSED_MODE] DEFAULT ((0)) NULL,
    [RECYCLING_SETTLEMENT_STATE_ID] INT             CONSTRAINT [DF_F_RECYCLING_ORDER_RECYCLING_SETTLEMENT_STATE_ID] DEFAULT ((1)) NULL,
    [WAREHOUSE_ID]                  BIGINT          NULL,
    [STATEMENT_TERM_ID]             BIGINT          NULL,
    [WORK_INSTRUCTIONS]             NVARCHAR (4000) NULL,
    [INTERNAL_COMMENTS]             NVARCHAR (4000) NULL,
    [SETTLEMENT_INSTRUCTIONS]       NVARCHAR (4000) NULL,
    [IS_DISPATCH]                   BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_DISPATCH] DEFAULT ((0)) NULL,
    [IS_INACTIVE]                   BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_TRANSFER]                   BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_TRANSFER] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                    BIT             CONSTRAINT [DF_F_RECYCLING_ORDER_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY_USER]              BIGINT          NULL,
    [INSERTED_BY_IP]                BIGINT          NULL,
    [INSERTED_BY]                   NVARCHAR (250)  CONSTRAINT [DF_F_RECYCLING_ORDER_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                   DATETIME        CONSTRAINT [DF_F_RECYCLING_ORDER_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY_USER]               BIGINT          NULL,
    [UPDATED_BY_IP]                 BIGINT          NULL,
    [UPDATED_BY]                    NVARCHAR (250)  NULL,
    [UPDATED_DT]                    DATETIME        NULL,
    [DELETED_BY]                    NVARCHAR (250)  NULL,
    [DELETED_DT]                    DATETIME        NULL,
    [REFERENCE_1]                   NVARCHAR (75)   NULL,
    [REFERENCE_2]                   NVARCHAR (128)   NULL,
    [REFERENCE_3]                   NVARCHAR (128)   NULL,
    [SMEApprover]                   NVARCHAR (250)  NULL,
    [CBREDisposalRequestWO]         NVARCHAR (250)  NULL,
    [DisposalRequestIDName]         NVARCHAR (250)  NULL,
    [DepartmentTurningEquipment]    NVARCHAR (250)  NULL,
    [IS_HAZARDOUS]                  BIT             NULL,
    [PREV_PURCHASE_ORDER_NUMBER]    NVARCHAR (250)  NULL,
    [PREV_SALES_ORDER_NUMBER]       NVARCHAR (250)  NULL,
    [PREV_INVOICE_AP]               NVARCHAR (250)  NULL,
    [PREV_INVOICE_AR]               NVARCHAR (250)  NULL,
    [DRAFT_ORDER_DATE]              DATETIME        NULL,
    [DRAFT_PO_TERMS_ID]             INT             NULL,
    [DRAFT_WARRANTY_ID]             INT             NULL,
    [DRAFT_SHIPPING]                MONEY           NULL,
    [DRAFT_MISC]                    MONEY           NULL,
    [DRAFT_PURCHASE_ORDER_COMMENT]  NVARCHAR (MAX)  NULL,
    [_CurrencyId]                   INT             NULL,
    [CustomerContractNo]            NVARCHAR (250)  NULL,
    [SettleUserId]                  BIGINT          NULL,
    [CurrencyExchangeId]            BIGINT          NULL,
    [RevenueShareSplit]             DECIMAL (6, 5)  CONSTRAINT [DF_F_RECYCLING_ORDER_RevenueShareSplit] DEFAULT ((0)) NOT NULL,
    [Reference4]                    NVARCHAR (32)   NULL,
    [Reference5]                    NVARCHAR (32)   NULL,
    [Reference6]                    NVARCHAR (32)   NULL,
    [Reference7]                    NVARCHAR (32)   NULL,
    [Reference8]                    NVARCHAR (32)   NULL,
    [ExternalId]                    NVARCHAR (350)  NULL,
    [SourceContractId]              BIGINT          NULL,
    [CSR_REP_ID]                    BIGINT          NULL,
    [PricingServiceTemplateId]      BIGINT          NULL, 
    CONSTRAINT [PK_F_RECYCLING_ORDER] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_CUSTOMER_TRANSACTION_TERM] FOREIGN KEY ([SO_TERM_ID]) REFERENCES [dbo].[C_CUSTOMER_TRANSACTION_TERM] ([CUSTOMER_TRANSACTION_TERM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_CUSTOMER_TRANSACTION_TERM1] FOREIGN KEY ([DRAFT_PO_TERMS_ID]) REFERENCES [dbo].[C_CUSTOMER_TRANSACTION_TERM] ([CUSTOMER_TRANSACTION_TERM_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_RECYCLING_ORDER_STATUS] FOREIGN KEY ([RECYCLING_ORDER_STATUS_ID]) REFERENCES [dbo].[C_RECYCLING_ORDER_STATUS] ([RECYCLING_ORDER_STATUS_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_RECYCLING_PRICE_TYPE] FOREIGN KEY ([DEFAULT_PRICE_TYPE_ID]) REFERENCES [dbo].[C_RECYCLING_PRICE_TYPE] ([PRICE_TYPE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_RECYCLING_SETTLEMENT_STATE] FOREIGN KEY ([RECYCLING_SETTLEMENT_STATE_ID]) REFERENCES [dbo].[C_RECYCLING_SETTLEMENT_STATE] ([RECYCLING_SETTLEMENT_STATE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_C_WARRANTY] FOREIGN KEY ([DRAFT_WARRANTY_ID]) REFERENCES [dbo].[C_WARRANTY] ([ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_D_CurrencyExchange] FOREIGN KEY ([CurrencyExchangeId]) REFERENCES [dbo].[D_CurrencyExchange] ([Id]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_D_WAREHOUSE] FOREIGN KEY ([WAREHOUSE_ID]) REFERENCES [dbo].[D_WAREHOUSE] ([WAREHOUSE_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_F_STATEMENT_TERM] FOREIGN KEY ([STATEMENT_TERM_ID]) REFERENCES [dbo].[F_STATEMENT_TERM] ([STATEMENT_TERM_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_F_RECYCLING_ORDER_tb_User] FOREIGN KEY ([USER_ID]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_tb_User_INSERTED] FOREIGN KEY ([INSERTED_BY_USER]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_tb_User_UPDATED] FOREIGN KEY ([UPDATED_BY_USER]) REFERENCES [dbo].[tb_User] ([UserID]),
	CONSTRAINT [FK_F_RECYCLING_ORDER_F_ServiceTemplate] FOREIGN KEY ([PricingServiceTemplateId]) REFERENCES [dbo].[F_ServiceTemplate] ([Id])
);


GO

CREATE NONCLUSTERED INDEX IX__dbo_F_RECYCLING_ORDER__RECYCLING_ORDER_STATUS_ID__IS_DELETED__INCLUDE__RECYCLING_ORDER_ID__WAREHOUSE_ID
  ON [dbo].[F_RECYCLING_ORDER] (
    [RECYCLING_ORDER_STATUS_ID], [IS_DELETED]
  )
  INCLUDE (
    [RECYCLING_ORDER_ID], [WAREHOUSE_ID]
  )
GO




--this index is used by dbo.sp_GET_RECYCLING_ORDERS_SETTLEMENT
CREATE NONCLUSTERED INDEX [IX__F_RECYCLING_ORDER__IS_TRANSFER__INCLUDE]
	ON [dbo].[F_RECYCLING_ORDER] ([IS_TRANSFER])
	INCLUDE ([RECYCLING_ORDER_ID],[USER_ID],[CUSTOMER_ID],[RECYCLING_ORDER_STATUS_ID],[IS_INBOUND])
GO






GO
CREATE NONCLUSTERED INDEX IX_PICKUP_START_DATE
ON [dbo].[F_RECYCLING_ORDER] ([PICKUP_START_DATE])
INCLUDE ([RECYCLING_ORDER_ID],[USER_ID],[CUSTOMER_ID],[WAREHOUSE_ID], RECYCLING_SETTLEMENT_STATE_ID, WORK_INSTRUCTIONS, IS_TRANSFER, INSERTED_DT, UPDATED_DT) 



GO
CREATE NONCLUSTERED INDEX [IX_IS_INBOUND]
    ON [dbo].[F_RECYCLING_ORDER]([IS_INBOUND] ASC);

GO
CREATE NONCLUSTERED INDEX [IX_RECYCLING_ORDER_STATUS_ID]
    ON [dbo].[F_RECYCLING_ORDER]([RECYCLING_ORDER_STATUS_ID] ASC, IS_TRANSFER)
	INCLUDE(RECYCLING_ORDER_ID, CUSTOMER_ID, PICKUP_START_DATE, WAREHOUSE_ID, USER_ID )
	;



GO
CREATE NONCLUSTERED INDEX [IX_F_RECYCLING_ORDER_CUSTOMER_ID]
    ON [dbo].[F_RECYCLING_ORDER]([CUSTOMER_ID] ASC, RECYCLING_ORDER_STATUS_ID)
	INCLUDE(PO_NUMBER, SETTLE_DATE,WAREHOUSE_ID, REFERENCE_1, RECYCLING_ORDER_ID)

GO
CREATE NONCLUSTERED INDEX [IX_WAREHOUSE_ID]
    ON [dbo].[F_RECYCLING_ORDER]([WAREHOUSE_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [idx_F_RECYCLING_ORDER_IS_TRANSFER]
    ON [dbo].[F_RECYCLING_ORDER]([IS_TRANSFER] ASC)
    INCLUDE([RECYCLING_ORDER_ID]);


GO

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Orders to recieve the equipment for recycling', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_RECYCLING_ORDER';





GO
CREATE TRIGGER [dbo].[trg_F_RECYCLING_ORDER_AFTER_UPDATE_LOG]
    ON  [dbo].[F_RECYCLING_ORDER]
    AFTER UPDATE
AS 
BEGIN

    DECLARE @TAIL NVARCHAR(4) = N'"';
    DECLARE @DEF  NVARCHAR(4) = N'-';

    INSERT INTO F_LOG_DATA(	 
	  [SOURCE]  
	  ,USER_ID
	  ,USER_IP
	  ,OPERATION_NAME
	  ,ENTITY_TYPE_ID
	  ,ENTITY_KEY_VALUE
	  ,ENTITY_AUTO_NAME
	  ,[CHANGES]
    )
    SELECT
	   'trg_F_RECYCLING_ORDER_AFTER_UPDATE_LOG'
	   ,NEW.UPDATED_BY_USER
	   ,NEW.UPDATED_BY_IP
	   ,N'Updated'
	   ,IIF(NEW.IS_INBOUND = 1, 3 ,4) AS ENTITY_TYPE_ID
	   ,NEW.ENTITY_KEY_VALUE
	   ,NEW.ENTITY_AUTO_NAME
	   ,ISNULL(NEW.[CHANGES], '')
    FROM (
	   SELECT
		  UPDATED_BY_USER
		  ,UPDATED_BY_IP
		  ,IS_INBOUND
		  ,ENTITY_KEY_VALUE
		  ,ENTITY_AUTO_NAME
		  ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT 
			  NEW.[USER]		
			 ,NEW.DESCR
			 ,NEW.INTERNAL_COMMENT
			 ,NEW.PICKUP_DATE
			 ,NEW.PO_NUMBER
			 ,NEW.BOL_NUMBER
			 ,NEW.STATUS_CD
			 ,NEW.DEFAULT_PRICE
			 ,NEW.DEFAULT_PRICE_TYPE
			 ,NEW.WORK_INSTRUCTIONS
			 ,NEW.[SettlementState]
			 ,NEW.Customer
			 ,NEW.WAREHOUSE_CD
		  FOR XML
		  PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
	   FROM (
		  SELECT
			 -- Fixed columns
			 I.UPDATED_BY_USER,
			 I.UPDATED_BY_IP,
			 I.IS_INBOUND
			 ,I.RECYCLING_ORDER_ID		AS ENTITY_KEY_VALUE
			 ,dbo.fn_stg_GET_RECYCLING_ORDER_NUMBER(I.RECYCLING_ORDER_ID) as ENTITY_AUTO_NAME
			 -- XML columns		  
			 ,IIF( ISNULL(I.USER_ID, 0) <> ISNULL(D.USER_ID, 0) 
				   ,N'Rep User was "'  + ISNULL(dbo.fn_str_GET_USER_AUTO_NAME(D.USER_ID, 1), @DEF) + N'" is "' + ISNULL(dbo.fn_str_GET_USER_AUTO_NAME(I.USER_ID, 1), @DEF)
				   , NULL		
				)					+ @TAIL  AS [USER]		  
			 ,IIF(ISNULL(I.DESCR, '') <> ISNULL(D.DESCR, '')
				 ,N'Internal Notes were "'	+ ISNULL(D.DESCR, '') + N'" are "' + ISNULL(I.DESCR, '')
			    ,NULL									
			   )						+ @TAIL  AS DESCR		  
			 ,IIF(ISNULL(I.INTERNAL_COMMENT, '') <> ISNULL(D.INTERNAL_COMMENT, '')
				 ,N'Order Comments were "'	+ ISNULL(D.INTERNAL_COMMENT, '') + N'" are "' + ISNULL(I.INTERNAL_COMMENT, '')
			    ,NULL				
			  )						+ @TAIL  AS INTERNAL_COMMENT		  
			 ,IIF(ISNULL(I.PICKUP_START_DATE, N'1/1/2000') <> ISNULL(D.PICKUP_START_DATE, N'1/1/2000')
				  ,N'Pickup Date was "'   + ISNULL([dbo].[fn_str_DATE_TIME_TO_STRING](D.PICKUP_START_DATE), @DEF) + N'" is "' + ISNULL([dbo].[fn_str_DATE_TIME_TO_STRING](I.PICKUP_START_DATE), @DEF)
			    ,NULL
			  )					    + @TAIL  AS PICKUP_DATE
			 ,IIF(ISNULL(I.PO_NUMBER, '') <> ISNULL(D.PO_NUMBER, '')
				,N'PO # was "'	 + ISNULL(D.PO_NUMBER, @DEF) + N'" is "' + ISNULL(I.PO_NUMBER, @DEF)	 + @TAIL				 
				,NULL
			 )																	AS PO_NUMBER
			 ,IIF(ISNULL(I.BOL_NUMBER, '') <> ISNULL(D.BOL_NUMBER, '')
				,N'BOL # was "'+ ISNULL(D.BOL_NUMBER, @DEF) + N'" is "'+ ISNULL(I.BOL_NUMBER, @DEF) + @TAIL				 
				,NULL
			 )																	AS BOL_NUMBER
			 ,IIF(ISNULL(I.RECYCLING_ORDER_STATUS_ID, 0) <> ISNULL(D.RECYCLING_ORDER_STATUS_ID, 0) AND I.IS_INBOUND = 0
				 ,N'Status was "'	   + ISNULL(S_D.STATUS_CD, @DEF) + N'" is "' +  ISNULL(S.STATUS_CD, @DEF)
			    ,NULL
			 )						+ @TAIL  AS STATUS_CD
			 ,IIF(ISNULL(I.DEFAULT_PRICE, 0.0) <> ISNULL(D.DEFAULT_PRICE, 0.0)
				  ,N'Default Price was "'		+
						IIF( I.USE_DEFAULT_PRICE = 1
							,ISNULL(N'$'+ CONVERT(NVARCHAR(20), 
										IIF( PT.PRICE_TYPE_ID = 1 , 
										IIF(ISNULL(I.DEFAULT_PRICE_TYPE_ID, 0) <> ISNULL(D.DEFAULT_PRICE_TYPE_ID, 0), 
										D.DEFAULT_PRICE 
										, D.DEFAULT_PRICE / 0.45359237 ) ,
										IIF( PT_D.PRICE_TYPE_ID = 1,  D.DEFAULT_PRICE / 0.45359237, D.DEFAULT_PRICE  )
										)
							), @DEF) + N'" is "' + ISNULL(N'$' + CONVERT(NVARCHAR(20), 
									IIF( PT.PRICE_TYPE_ID = 1,  I.DEFAULT_PRICE / 0.45359237, I.DEFAULT_PRICE  )
							), @DEF)
						    ,NULL
						)
			    , NULL															
			 )						+ @TAIL  AS DEFAULT_PRICE
			 ,IIF(ISNULL(I.DEFAULT_PRICE_TYPE_ID, 0) <> ISNULL(D.DEFAULT_PRICE_TYPE_ID, 0)
				   ,N'Default Price Type was "'		+ 
						IIF(I.USE_DEFAULT_PRICE = 1
							,ISNULL(PT_D.PRICE_TYPE_DESC, @DEF)  + N'" is "' + ISNULL(PT.PRICE_TYPE_DESC, @DEF)
						    ,NULL
						)
			    ,NULL
			
			 )						+ @TAIL  AS DEFAULT_PRICE_TYPE
			 ,IIF(ISNULL(I.WORK_INSTRUCTIONS, '') <> ISNULL(D.WORK_INSTRUCTIONS, '')
				 ,N'Order Work instructions were "'	+ ISNULL(D.WORK_INSTRUCTIONS, '') + N'" are "' + ISNULL(I.WORK_INSTRUCTIONS, '')
			    ,NULL				
			  )						+ @TAIL  AS WORK_INSTRUCTIONS
			 ,IIF(ISNULL(I.[RECYCLING_SETTLEMENT_STATE_ID], 0) != ISNULL(D.[RECYCLING_SETTLEMENT_STATE_ID], 0)
				,N'Settlement state was "' + ISNULL(SS_D.[RECYCLING_SETTLEMENT_STATE_CD], @DEF) + N'" is "' + ISNULL(SS.[RECYCLING_SETTLEMENT_STATE_CD], @DEF)
				,NULL
			 ) + @TAIL						AS [SettlementState],
			 IIF(ISNULL(I.[CUSTOMER_ID], 0) <> ISNULL(D.[CUSTOMER_ID], 0)
				   ,N'Customer was "'		+ 
						ISNULL(C_D.[CUSTOMER_NAME], 0)  + N'" is "' + ISNULL(C_I.[CUSTOMER_NAME], 0)						   
			    ,NULL) + @TAIL  AS Customer,
			 IIF(ISNULL(IW.WAREHOUSE_ID, 0) <> ISNULL(IW_D.WAREHOUSE_ID, 0)
				   ,N'Warehouse was "'		+ 
						ISNULL(IW_D.WAREHOUSE_CD, 0)  + N'" is "' + ISNULL(IW.WAREHOUSE_CD, 0)						   
			    ,NULL) + @TAIL  AS WAREHOUSE_CD
		  FROM	 INSERTED							I
		  INNER JOIN F_CUSTOMER						C_I		WITH(NOLOCK)
		    ON I.CUSTOMER_ID = C_I.CUSTOMER_ID		  
		  -- common
		  LEFT JOIN C_RECYCLING_ORDER_STATUS		S		WITH(NOLOCK)
		    ON I.RECYCLING_ORDER_STATUS_ID = S.RECYCLING_ORDER_STATUS_ID
				AND I.IS_INBOUND = 0
		  LEFT JOIN C_RECYCLING_PRICE_TYPE			PT		WITH(NOLOCK)
		    ON I.DEFAULT_PRICE_TYPE_ID = PT.PRICE_TYPE_ID
		  LEFT JOIN [dbo].[C_RECYCLING_SETTLEMENT_STATE] AS SS WITH(NOLOCK)
			ON SS.[RECYCLING_SETTLEMENT_STATE_ID] = I.[RECYCLING_SETTLEMENT_STATE_ID]
		  LEFT JOIN D_WAREHOUSE				   IW  WITH(NOLOCK)
		    ON I.WAREHOUSE_ID =  IW.WAREHOUSE_ID

		  INNER JOIN DELETED D
		    ON D.RECYCLING_ORDER_ID = I.RECYCLING_ORDER_ID
		  INNER JOIN F_CUSTOMER						C_D		WITH(NOLOCK)
		    ON C_D.CUSTOMER_ID = D.CUSTOMER_ID
		  -- common
		  LEFT JOIN C_RECYCLING_ORDER_STATUS		S_D		WITH(NOLOCK)
		    ON D.RECYCLING_ORDER_STATUS_ID = S_D.RECYCLING_ORDER_STATUS_ID
				AND D.IS_INBOUND = 0
		  LEFT JOIN C_RECYCLING_PRICE_TYPE			PT_D	WITH(NOLOCK)
		    ON D.DEFAULT_PRICE_TYPE_ID = PT_D.PRICE_TYPE_ID	   	   
		  LEFT JOIN [dbo].[C_RECYCLING_SETTLEMENT_STATE] AS SS_D WITH(NOLOCK)
			ON SS_D.[RECYCLING_SETTLEMENT_STATE_ID] = D.[RECYCLING_SETTLEMENT_STATE_ID]
		  LEFT JOIN D_WAREHOUSE				   IW_D  WITH(NOLOCK)
		    ON D.WAREHOUSE_ID =  IW_D.WAREHOUSE_ID
		  WHERE ISNULL(I.USER_ID, 0)					<> ISNULL(D.USER_ID, 0)
		   OR ISNULL(I.CUSTOMER_ID, 0)					<> ISNULL(D.CUSTOMER_ID, 0)
			OR ISNULL(I.DESCR, '')						<> ISNULL(D.DESCR, '')
			OR ISNULL(I.INTERNAL_COMMENT, '')			<> ISNULL(D.INTERNAL_COMMENT, '')
			OR ISNULL(I.PICKUP_START_DATE, '1/1/2000')	<> ISNULL(D.PICKUP_START_DATE, '1/1/2000')
			OR ISNULL(I.PO_NUMBER, '')					<> ISNULL(D.PO_NUMBER, '')
			OR ISNULL(I.BOL_NUMBER, '')					<> ISNULL(D.BOL_NUMBER, '')
			OR ISNULL(I.RECYCLING_ORDER_STATUS_ID, 0)	<> ISNULL(D.RECYCLING_ORDER_STATUS_ID, 0)
			OR ISNULL(I.DEFAULT_PRICE, 0)				<> ISNULL(D.DEFAULT_PRICE, 0)
			OR ISNULL(I.DEFAULT_PRICE_TYPE_ID, 0)		<> ISNULL(D.DEFAULT_PRICE_TYPE_ID, 0)
			OR ISNULL(I.WORK_INSTRUCTIONS, '')			<> ISNULL(D.WORK_INSTRUCTIONS, '')
			OR ISNULL(I.[RECYCLING_SETTLEMENT_STATE_ID], 0)	<> ISNULL(D.[RECYCLING_SETTLEMENT_STATE_ID], 0)
			OR ISNULL(I.WAREHOUSE_ID, 0)				<> ISNULL(D.WAREHOUSE_ID, 0)
	   ) NEW -- base columns
    ) NEW -- result columns
    WHERE ISNULL(NEW.[CHANGES], '') != ''
END
GO
CREATE TRIGGER [dbo].[trg_F_RECYCLING_ORDER_PUT_CHANGE]
    ON  [dbo].[F_RECYCLING_ORDER]
    AFTER INSERT, UPDATE
AS 
BEGIN

    declare @ids dbo.bigint_id_array

	insert into @ids
	select distinct
		RECYCLING_ORDER_ID
	from 
	(
		select
			isnull(i.RECYCLING_ORDER_ID, d.RECYCLING_ORDER_ID) as RECYCLING_ORDER_ID
		from inserted		i
		full join deleted	d
			on i.RECYCLING_ORDER_ID = d.RECYCLING_ORDER_ID
		where isnull(i.REFERENCE_1, '') <> isnull(d.REFERENCE_1, '')
	) i

	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 14 --InboundOrder
		,@EntityIds = @ids
		,@Invoker = @invoker
END
GO

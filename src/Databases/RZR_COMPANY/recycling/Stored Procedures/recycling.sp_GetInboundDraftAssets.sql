CREATE PROCEDURE [recycling].[sp_GetInboundDraftAssets]
	@RecyclingOrderId BIGINT
AS
	
	SELECT 
		iba.Id,
		iba.RecyclingOrderId,
		iba.ItemMasterId ,
		IM.ITEM_NUMBER as <PERSON><PERSON><PERSON><PERSON><PERSON>,
		IM.MANUFACTURER_ID as ManufacturerId,
		M.<PERSON>FACTURER_CD as Manufacturer,
		iba.ConditionId ConditionId,
		IC.ITEM_CONDITION_CD as Condition,
		iba.QTY as Qty,
		iba.Cost,
		Iba.Total

	
	FROM recycling.F_InboundDraftAsset iba with(nolock)
		LEFT JOIN dbo.F_ITEM_MASTER IM with(nolock) ON IM.ITEM_MASTER_ID =iba.ItemMasterId
		LEFT JOIN dbo.D_MANUFACTURER M with(nolock) ON m.MANUFACTURER_ID = IM.MANUFACTURER_ID
		LEFT JOIN dbo.D_ITEM_CONDITION IC with(nolock) ON IC.ITEM_CONDITION_ID = IBA.ConditionId
	WHERE iba.RecyclingOrderId = @RecyclingOrderId
	ORDER BY iba.Id
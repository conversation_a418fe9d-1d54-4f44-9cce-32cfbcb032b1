-- =============================================
-- Author:		
-- Create date: 10/29/2014
-- Description:	get item addons
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ITEM_ADDONS]
    @ITEM_ID	BIGINT
AS
BEGIN
	SELECT
		IM.ITEM_NUMBER				AS MasterItemNumber,
		COUNT(IM.ITEM_NUMBER)		AS QtyOfAddons
	FROM	   F_ITEM_ADDON			IA	WITH (NOLOCK)
	INNER JOIN F_ITEM_INVENTORY		II	WITH (NOLOCK)
		ON II.ITEM_INVENTORY_ID = IA.ITEM_INVENTORY_ID
	INNER JOIN F_ITEM_MASTER		IM	WITH (NOLOCK)
		ON II.ITEM_MASTER_ID = IM.ITEM_MASTER_ID
	WHERE IA.ITEM_ID = @ITEM_ID
	GROUP BY IM.ITEM_NUMBER
END
-- =============================================
-- Author:		R.SAPRYKIN
-- Create date: 07/29/2014
-- Description:	list document set types
-- =============================================
CREATE PROCEDURE sp_LIST_DOCUMENT_SET_TYPES
	@TERM		 NVARCHAR(MAX)	= N''
	,@COUNT		 INT			= 20
AS
BEGIN
	SET @TERM = LTRIM(RTRIM(@TERM))
		
	SELECT TOP(@COUNT)
		dst.DOCUMENT_SET_TYPE_ID	AS VALUE,
		dst.DOCUMENT_SET_TYPE_CD	AS LABEL
	FROM D_DOCUMENT_SET_TYPE	dst	WITH(NOLOCK)
	WHERE dst.DOCUMENT_SET_TYPE_CD LIKE @TERM
	ORDER BY DOCUMENT_SET_TYPE_CD ASC
END
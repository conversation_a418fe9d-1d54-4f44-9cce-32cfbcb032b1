
-- =============================================
-- Author:		
-- Create date: 
-- Description:
-- =============================================
CREATE function [dbo].[fn_nvarchar_MakeKIDNumber]
(
	@AutoName	nvarchar(250)
)	
RETURNS nvarchar(250)
AS
BEGIN	
		
	declare @kidNumber		nvarchar(250),
			@numberOfPos	int = 9

	set @AutoName = SUBSTRING(@AutoName, PATINDEX('%[\-][0-9]%', @AutoName) + 1, LEN(@AutoName))

	declare @calc table (
		pos		int,
		multi	int,
		number	int,
		res		varchar(10),
		sumval	int
	)

	declare @i int = @numberOfPos;
	WHILE @i > 0
	BEGIN
		INSERT INTO @calc(pos, multi) 
		VALUES (@i, iif(@i % 2 = 0, 1, 2));
		SELECT @i = @i - 1;
	END;

	;WITH cte AS (
		SELECT
			@AutoName					as n,
			SUBSTRING(@AutoName, 1, 1)	as autonum,
			1							as ind
		UNION ALL 
		SELECT
			n,
			SUBSTRING(@AutoName, ind + 1, 1), ind + 1
		FROM cte
		WHERE LEN(n) > ind
	)

	update cl set 
		number	= c.autonum,
		res		= cast(c.autonum * cl.multi as varchar(10))
	from cte			c
	inner join @calc	cl
		on c.ind = cl.pos

	update cl set 		
		sumval = iif(len(res)>1, cast(SUBSTRING(res, 1, 1) as int) + cast(SUBSTRING(res, 2, 1) as int), res)
	from @calc cl	

	declare @pre_sum int = (select sum(sumval) from @calc)

	declare @Modules int = 10

	set @kidNumber = (@Modules - @pre_sum % @Modules)
	set @AutoName = @AutoName + iif(@kidNumber = 10, '0', @kidNumber)

	return concat(replicate('0', @numberOfPos - len(@AutoName)), @AutoName)	 
	
END
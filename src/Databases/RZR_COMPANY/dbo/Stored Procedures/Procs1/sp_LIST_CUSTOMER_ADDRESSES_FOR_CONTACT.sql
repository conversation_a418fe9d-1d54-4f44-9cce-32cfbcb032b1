CREATE PROCEDURE [dbo].[sp_LIST_CUSTOMER_ADDRESSES_FOR_CONTACT] 		
	@CUSTOMER_ID	BIGINT,
	@TERM			VARCHAR(150),
	@COUNT			INT,
	@WITH_WAREHOUSE BIT	   = 0,
	@WAREHOUSE_ID   BIGINT = NULL,
	@USER_ID		BIGINT,
	@ADDRESS_TYPE	INT = 0
AS
BEGIN
	SET @ADDRESS_TYPE = NULLIF(@ADDRESS_TYPE,0)
	IF(@WAREHOUSE_ID IS NULL) BEGIN

		SELECT DISTINCT TOP(@COUNT)
			ca.CUSTOMER_ADDRESS_ID												AS [VALUE],		
			dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(ca.CUSTOMER_ADDRESS_ID)	AS [LABEL],
			ca.STREET_1															AS [DESC]
		FROM dbo.F_CUSTOMER_ADDRESS	ca	WITH (NOLOCK)
		LEFT JOIN F_USER_WAREHOUSE	UW  WITH (NOLOCK)
			ON UW.WAREHOUSE_ID = ca.WAREHOUSE_ID
		-- Get customer addresses
		WHERE  (ca.CUSTOMER_ADDRESS_TYPE_ID = @ADDRESS_TYPE OR @ADDRESS_TYPE IS NULL )
		AND (
		 (@WITH_WAREHOUSE = 0 AND ca.CUSTOMER_ID = @CUSTOMER_ID  AND (
				ca.LOCATION_NAME LIKE @TERM OR 
				ca.STREET_1		 LIKE @TERM OR 
				ca.CITY			 LIKE @TERM OR
				ca.POSTAL_CODE	 LIKE @TERM OR
				ca.COUNTRY		 LIKE @TERM))
				 OR
		-- Get customer addresses with warehouse for current User
			  (@WITH_WAREHOUSE = 1 AND UW.[USER_ID] = @USER_ID AND 
			    ca.WAREHOUSE_ID IS NOT NULL AND ca.CUSTOMER_ID = @CUSTOMER_ID  AND (
				ca.LOCATION_NAME LIKE @TERM OR 
				ca.STREET_1		 LIKE @TERM OR 
				ca.CITY			 LIKE @TERM OR
				ca.POSTAL_CODE	 LIKE @TERM OR
				ca.COUNTRY		 LIKE @TERM))
				)

	END
	ELSE BEGIN

		SELECT DISTINCT TOP(@COUNT)
			ADDRESSES.*
		FROM (
			-- Get customer addresses with current warehouse
			SELECT
				ca.CUSTOMER_ADDRESS_ID												AS [VALUE],		
				dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(ca.CUSTOMER_ADDRESS_ID)	AS [LABEL],
				ca.STREET_1															AS [DESC]
			FROM dbo.F_CUSTOMER_ADDRESS	ca	WITH (NOLOCK)
			WHERE (ca.CUSTOMER_ADDRESS_TYPE_ID = @ADDRESS_TYPE OR @ADDRESS_TYPE IS NULL )
			AND ( ca.WAREHOUSE_ID = @WAREHOUSE_ID AND ca.CUSTOMER_ID = @CUSTOMER_ID  AND (
					ca.LOCATION_NAME LIKE @TERM OR 
					ca.STREET_1		 LIKE @TERM OR 
					ca.CITY			 LIKE @TERM OR
					ca.POSTAL_CODE	 LIKE @TERM OR
					ca.COUNTRY		 LIKE @TERM OR
					dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(ca.CUSTOMER_ADDRESS_ID) LIKE @TERM))
			UNION
			SELECT
				ca.CUSTOMER_ADDRESS_ID												AS [VALUE],		
				dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(ca.CUSTOMER_ADDRESS_ID)	AS [LABEL],
				ca.STREET_1															AS [DESC]
			FROM dbo.F_CUSTOMER_ADDRESS				ca	WITH (NOLOCK)
			
		
			INNER JOIN F_CUSTOMER_ADDRESS_WAREHOUSE CAW WITH (NOLOCK)
				ON CAW.CUSTOMER_ADDRESS_ID = ca.CUSTOMER_ADDRESS_ID
					AND (ca.CUSTOMER_ADDRESS_TYPE_ID = @ADDRESS_TYPE OR @ADDRESS_TYPE IS NULL )
			WHERE ( CAW.WAREHOUSE_ID = @WAREHOUSE_ID AND ca.CUSTOMER_ID = @CUSTOMER_ID  AND (
					ca.LOCATION_NAME LIKE @TERM OR 
					ca.STREET_1		 LIKE @TERM OR 
					ca.CITY			 LIKE @TERM OR
					ca.POSTAL_CODE	 LIKE @TERM OR
					ca.COUNTRY		 LIKE @TERM OR
					dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(ca.CUSTOMER_ADDRESS_ID) LIKE @TERM))
		) ADDRESSES
				
	END
	
END
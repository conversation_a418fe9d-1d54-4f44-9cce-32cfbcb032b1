-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_TO_BASE64]
(
	@STRING VARCHAR(MAX)
)
RETURNS VARCHAR(MAX)
AS
BEGIN
	RETURN (
		SELECT
			CAST(N'' AS XML).value(
				  'xs:base64Binary(xs:hexBinary(sql:column("bin")))'
				, 'VARCHAR(MAX)'
			)   Base64Encoding
		FROM (
			SELECT CAST(@STRING AS VARBINARY(MAX)) AS bin
		) AS bin_sql_server_temp
	)
END
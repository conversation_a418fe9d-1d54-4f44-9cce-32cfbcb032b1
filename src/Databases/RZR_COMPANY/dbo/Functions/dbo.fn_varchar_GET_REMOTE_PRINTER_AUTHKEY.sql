
-- =============================================
-- Author: <PERSON><PERSON><PERSON><PERSON>
-- Create date: 2017/07/04
-- Description: Returns auth key
-- =============================================
 
CREATE FUNCTION [dbo].[fn_varchar_GET_REMOTE_PRINTER_AUTHKEY]
()	
RETURNS VARCHAR(512)
AS
BEGIN
	RETURN
	(
		SELECT TOP(1)
			CONVERT(VARCHAR(512), HASHBYTES('SHA2_512', CAST(UserID AS VARCHAR(255)) + CAST(UserName as VARCHAR(32)) + CAST([Password] AS VARCHAR (128))), 2) AS AuthKey
		FROM dbo.tb_User u WITH (NOLOCK)
		WHERE IS_REMOTE_AGENT = 1 
	      AND u.IS_DELETED  = 0
		  AND u.IS_INACTIVE = 0
		  AND u.IsActive    = 1
	)
END
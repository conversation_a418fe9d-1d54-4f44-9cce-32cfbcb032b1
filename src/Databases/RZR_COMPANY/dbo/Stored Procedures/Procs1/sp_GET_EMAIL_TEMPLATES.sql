-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 05/12/2014
-- Description:	get email template list
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_EMAIL_TEMPLATES]
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'EMAIL_TEMPLATE_ID',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(MAX)	
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		
DECLARE @query NVARCHAR (MAX) = '
			
			WITH m_data AS
			(
				SELECT
					fet.EMAIL_TEMPLATE_ID,
					fet.EMAIL_TEMPLATE_TYPE_ID,
					cett.EMAIL_TEMPLATE_TYPE_CD,
					fet.EMAIL_TEMPLATE_TITLE,
					fet.EMAIL_TEMPLATE_DESC,
					fet.EMAIL_TEMPLATE_SUBJECT,
					(SELECT COUNT(t.EMAIL_TEMPLATE_ID) FROM F_EMAIL_TEMPLATE AS t WHERE t.EMAIL_TEMPLATE_TYPE_ID = fet.EMAIL_TEMPLATE_TYPE_ID) AS [SAME_TYPE_COUNT]
				FROM F_EMAIL_TEMPLATE	fet	WITH (NOLOCK)
				LEFT JOIN C_EMAIL_TEMPLATE_TYPE cett WITH (NOLOCK)
					ON cett.EMAIL_TEMPLATE_TYPE_ID = fet.EMAIL_TEMPLATE_TYPE_ID
			)
		SELECT TOP(1)
			-1							AS RowID,
			COUNT (EMAIL_TEMPLATE_ID)	AS EMAIL_TEMPLATE_ID,
			0							AS EMAIL_TEMPLATE_TYPE_ID,
			NULL						AS EMAIL_TEMPLATE_TYPE_CD,						
			NULL						AS EMAIL_TEMPLATE_TITLE,
			NULL						AS EMAIL_TEMPLATE_DESC,	
			NULL						AS EMAIL_TEMPLATE_SUBJECT,
			0							AS SAME_TYPE_COUNT			
		FROM m_data
		' + @filterCondition + '
		UNION ALL
		SELECT
			TT.RowID,
			TT.EMAIL_TEMPLATE_ID,
			TT.EMAIL_TEMPLATE_TYPE_ID,
			TT.EMAIL_TEMPLATE_TYPE_CD,
			TT.EMAIL_TEMPLATE_TITLE,
			TT.EMAIL_TEMPLATE_DESC,
			TT.EMAIL_TEMPLATE_SUBJECT,
			TT.SAME_TYPE_COUNT
		FROM 
		(
			SELECT *
			FROM
				(SELECT 
						ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
						* 
					FROM m_data	
					' + @filterCondition + ') t	
			WHERE 
				RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100)) + '
		) TT';			

		EXEC sp_executesql @query;
END
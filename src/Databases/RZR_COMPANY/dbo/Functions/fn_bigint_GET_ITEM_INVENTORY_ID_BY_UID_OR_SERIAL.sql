-- =============================================
-- Author:		O.Evseev
-- Create date: 10/06/2016
-- Description:	Returns F_ITEM_INVENTORY.ITEM_INVENTORY_ID of the 1st item having the provided UID or SERIAL
-- =============================================
CREATE FUNCTION [dbo].[fn_bigint_GET_ITEM_INVENTORY_ID_BY_UID_OR_SERIAL]
(@C_ITEM_INVENTORY_SERIAL_OR_UID VARCHAR(200))
RETURNS bigint
AS
BEGIN
	RETURN (
		SELECT TOP(1) ITEM_INVENTORY_ID 
		FROM F_ITEM_INVENTORY WITH (NOLOCK)
		WHERE IS_DELETED = 0 
			AND (ITEM_INVENTORY_UNIQUE_ID = @C_ITEM_INVENTORY_SERIAL_OR_UID
			OR ITEM_INVENTORY_SERIAL = @C_ITEM_INVENTORY_SERIAL_OR_UID)
		)
END
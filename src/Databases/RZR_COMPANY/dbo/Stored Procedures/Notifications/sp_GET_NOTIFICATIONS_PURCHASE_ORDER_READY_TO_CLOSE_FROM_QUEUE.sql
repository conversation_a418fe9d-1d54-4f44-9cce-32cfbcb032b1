CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_PURCHASE_ORDER_READY_TO_CLOSE_FROM_QUEUE]
	@C_COUNT INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID		BIGINT = 22
	DECLARE @IS_ALLOWED					BIT = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID)
	
	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY  = N'sp_GET_NOTIFICATIONS_PURCHASE_ORDER_READY_TO_CLOSE_FROM_QUEUE',
			UPDATED_DT  = GETUTCDATE()
		WHERE IS_INACTIVE		   = 0
			AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP (@C_COUNT)
		NQ.NotificationQueueId							 AS [NOTIFICATION_QUEUE_ID]
		,PO.*
		,(SELECT 
			'(' + CAST(COUNT(POI.PURCHASE_ORDER_ITEM_ID) AS VARCHAR(10)) + ')' + ' Part Number: ' + IMR.ITEM_NUMBER AS PO_ITEM
			FROM F_PURCHASE_ORDER_ITEM				 POI WITH (NOLOCK)
			INNER JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
				ON POI.[PRODUCT_MASTER_ID] = PM.[PRODUCT_MASTER_ID]
			INNER JOIN F_ITEM_MASTER	IMR	WITH (NOLOCK)
				ON PM.[PRODUCT_MASTER_TYPE_ID] = 1
				AND PM.ITEM_MASTER_ID = IMR.ITEM_MASTER_ID
			WHERE POI.PURCHASE_ORDER_ID = QP.PURCHASE_ORDER_ID
			AND POI.RECEIVE_STATUS_ID = 3 -- Received
			GROUP BY IMR.ITEM_NUMBER
			FOR XML AUTO)		AS PO_ITEMS
		,POI.PURCHASE_ORDER_ITEM_COUNT
		,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, PO.[USER_ID], NULL, NULL, NQ.NotificationSetId, NQ.CustomerId) AS EMAILS
	FROM @tNotifyItemWithNotifySet														NQ
	CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId)			QP
	CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_PURCHASE_ORDER] (QP.PURCHASE_ORDER_ID)  PO
	INNER JOIN (
		SELECT
			COUNT(POI.PURCHASE_ORDER_ITEM_ID) AS PURCHASE_ORDER_ITEM_COUNT,
			POI.PURCHASE_ORDER_ID
		FROM F_PURCHASE_ORDER_ITEM	POI WITH(NOLOCK)
		WHERE POI.RECEIVE_STATUS_ID = 3
		GROUP BY POI.PURCHASE_ORDER_ID
	) POI
		ON POI.PURCHASE_ORDER_ID = QP.PURCHASE_ORDER_ID
	WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND QP.PURCHASE_ORDER_ID IS NOT NULL

	

END
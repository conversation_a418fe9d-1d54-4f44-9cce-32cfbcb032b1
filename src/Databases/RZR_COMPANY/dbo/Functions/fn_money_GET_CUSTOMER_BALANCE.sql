-- =============================================
-- Author:	 <Oleg K. Evseev>
-- Create date: <12/22/2014>
-- Description: <Returns the Balance Total of the Customer>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_CUSTOMER_BALANCE]
    (@CUSTOMER_ID BIGINT)
RETURNS MONEY
BEGIN
    RETURN (
	   ISNULL(
		  (SELECT
			 SUM(I.AMOUNT_DUE)
		   from F_INVOICE  I WITH(NOLOCK)
			INNER JOIN F_SALES_ORDER	S WITH(NOLOCK)
				ON S.SALES_ORDER_ID = I.ORDER_ID
			WHERE I.INVOICE_TYPE_ID = 1 AND S.CUSTOMER_ID = @CUSTOMER_ID
			AND I.IS_VOIDED = 0
			AND I.STATUS_ID	< 5					
			AND I.AMOUNT_DUE > 0					
	   )
	   , 0.0) - 
	   ISNULL(
		  (SELECT
			 SUM(I.AMOUNT_DUE)
		  FROM F_INVOICE I WITH(NOLOCK)
		  INNER JOIN F_PURCHASE_ORDER   P WITH(NOLOCK)
		    ON P.PURCHASE_ORDER_ID = I.ORDER_ID
		  WHERE I.INVOICE_TYPE_ID = 2 AND P.CUSTOMER_ID = @CUSTOMER_ID
		    AND I.IS_VOIDED = 0
			AND I.IS_PAID = 0
			AND I.AMOUNT_DUE > 0			    
		    AND I.IS_DELETED = 0
	   )
	   , 0.0))
END
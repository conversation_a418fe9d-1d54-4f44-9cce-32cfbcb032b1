CREATE TABLE [dbo].[F_UserOneTimePassword]
(
	[Id]			    bigint              identity(1,1) not null,
	[UserId]		    bigint              not null,
	[OneTimePassword]	nvarchar(max)       not null,
    [MaxAttempts]       int                 constraint [DF_F_UserOneTimePassword_MaxAttempts] default ((3)) not null,
    [UsedAttempts]      int                 constraint [DF_F_UserOneTimePassword_UsedAttempts] default ((0)) not null,
	[ValidThru]         datetime            not null,
    [FulfilledAt]       datetime            null,
    [InactivatedAt]     datetime            null,
    [InsertedBy]        nvarchar(250)       constraint [DF_F_UserOneTimePassword_InsertedBy] default ('admin') not null,
    [InsertedDate]      datetime            constraint [DF_F_UserOneTimePassword_InsertedDate] default (getutcdate()) not null,
    constraint [PK_F_UserOneTimePassword] primary key clustered ([Id] asc),
    constraint [FK_F_UserOneTimePassword_tb_User] foreign key ([UserId]) references [dbo].[tb_User] ([UserId]) on delete cascade
)

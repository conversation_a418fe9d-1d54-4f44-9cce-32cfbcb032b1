CREATE TABLE [dbo].[F_EMAIL_TEMPLATE] (
    [EMAIL_TEMPLATE_ID]      BIGINT          IDENTITY (1, 1) NOT NULL,
    [EMAIL_TEMPLATE_TYPE_ID] INT             NOT NULL,
    [EMAIL_TEMPLATE_TITLE]   NVARCHAR (256)  NOT NULL,
    [EMAIL_TEMPLATE_DESC]    NVARCHAR (2048) NULL,
    [EMAIL_TEMPLATE_SUBJECT] NVARCHAR (2048) NULL,
    [EMAIL_TEMPLATE_BODY]    NVARCHAR (MAX)  NULL,
    [INSERTED_BY]            VARCHAR (150)   NOT NULL,
    [INSERTED_DT]            DATETIME        NOT NULL,
    [UPDATED_BY]             VARCHAR (150)   NULL,
    [UPDATED_DT]             DATETIME        NULL,
    CONSTRAINT [PK_F_EMAIL_TEMPLATE] PRIMARY KEY CLUSTERED ([EMAIL_TEMPLATE_ID] ASC),
    CONSTRAINT [FK_F_EMAIL_TEMPLATE_C_EMAIL_TEMPLATE_TYPE] FOREIGN KEY ([EMAIL_TEMPLATE_TYPE_ID]) REFERENCES [dbo].[C_EMAIL_TEMPLATE_TYPE] ([EMAIL_TEMPLATE_TYPE_ID])
);




GO
CREATE NONCLUSTERED INDEX [IX_TEMPLATE_TYPE_ID]
    ON [dbo].[F_EMAIL_TEMPLATE]([EMAIL_TEMPLATE_TYPE_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores the email details such as title, description, subject, body etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_EMAIL_TEMPLATE';


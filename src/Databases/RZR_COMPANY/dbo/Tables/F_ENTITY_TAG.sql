CREATE TABLE [dbo].[F_ENTITY_TAG] (
    [ENTITY_TAG_ID]  BIGINT        IDENTITY (1, 1) NOT NULL,
    [TAG_ID]         BIGINT        NOT NULL,
    [TAG_TYPE_ID]    INT           NOT NULL,
    [ENTITY_ID]      BIGINT        NOT NULL,
    [IS_DEFAULT_TAG] BIT           NULL,
    [INSERTED_BY]    VARCHAR (250) CONSTRAINT [DF_F_ENTITY_TAG_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]    DATETIME      CONSTRAINT [DF_F_ENTITY_TAG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_F_ENTITY_TAG] PRIMARY KEY CLUSTERED ([ENTITY_TAG_ID] ASC),
    CONSTRAINT [FK_F_ENTITY_TAG_C_TAG_TYPE] FOREIGN KEY ([TAG_TYPE_ID]) REFERENCES [dbo].[C_TAG_TYPE] ([TAG_TYPE_ID]),
    CONSTRAINT [FK_F_ENTITY_TAG_F_TAG] FOREIGN KEY ([TAG_ID]) REFERENCES [dbo].[F_TAG] ([TAG_ID]) ON DELETE CASCADE ON UPDATE CASCADE
);

GO
CREATE NONCLUSTERED INDEX [IX_TAG_TYPE_ID]
    ON [dbo].[F_ENTITY_TAG]([TAG_TYPE_ID] ASC, [ENTITY_ID] ASC);

GO
CREATE NONCLUSTERED INDEX [IX_TAG_ID_ENTITY_ID]
    ON [dbo].[F_ENTITY_TAG]([TAG_TYPE_ID] ASC, [TAG_ID] ASC, [ENTITY_ID] ASC);

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The map table, it allows to store tag for different entities. The entity id is ENTITY_ID and the type is here TAG_TYPE_ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ENTITY_TAG';

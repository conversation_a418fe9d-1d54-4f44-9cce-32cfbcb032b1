-- =============================================
-- Author:		<PERSON><PERSON><PERSON><PERSON>
-- Create date: 04/07/2014
-- Description:	get roles list
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ROLES]
	@ORDER_COLUMN_NAME	NVARCHAR(250)	= N'UserRoleID',
	@ORDER_DIRECTION	NVARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		NVARCHAR(MAX)	
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		
DECLARE @query NVARCHAR (MAX) = N'			
		WITH m_data AS
		(
			SELECT
				ur.[UserRoleID] AS [ROLE_ID],
				ur.[UserRoleCD] AS [ROLE_NAME],
				ur.[UserRoleDesc] AS [ROLE_DSC],
				(SELECT COUNT(ROLE_ID) FROM F_USER_ROLE AS fur WHERE fur.ROLE_ID = ur.[UserRoleID]) AS USER_COUNT
			FROM tb_UserRole	ur	WITH (NOLOCK)
			WHERE ur.IS_SYSTEM_ROLE = 0		
		)
		SELECT TOP(1)
			-1					AS RowID,
			COUNT (ROLE_ID)		AS ROLE_ID,	
			NULL				AS ROLE_NAME,	
			NULL				AS ROLE_DSC,			
			0					AS USER_COUNT
		FROM m_data
		' + @filterCondition + N'
		UNION ALL
		SELECT
			TT.RowID,
			TT.ROLE_ID,
			TT.ROLE_NAME,
			TT.ROLE_DSC,			
			TT.USER_COUNT
		FROM 
		(
			SELECT *
			FROM
				(SELECT 
						ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
						* 
					FROM m_data	
					' + @filterCondition + N') t	
			WHERE 
				RowID BETWEEN '+ CAST(@startRowNumber AS NVARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS NVARCHAR(100)) + N'
		) TT';			

		EXEC sp_executesql @query;
END
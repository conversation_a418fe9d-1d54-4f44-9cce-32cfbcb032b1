-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 22.04.2014
-- Description:	Return invoices list by customer
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVOICES_BY_CUSTOMER]
	@CUSTOMER_ID BIGINT,
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'SALES_ORDER_INVOICE_ID',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(MAX)	= NULL
AS
BEGIN
	DECLARE @startRowNumber  BIGINT		  = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber	 BIGINT		  = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	DECLARE @filterCondition VARCHAR(MAX) = N'';

	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END	

	DECLARE @query NVARCHAR (MAX) = 
		N'WITH m_data AS
		(
			SELECT 
				SOI.[INVOICE_ID]										AS SALES_ORDER_INVOICE_ID,
				SO.[SALES_ORDER_ID],
				SO.[SALES_ORDER_NUMBER],
				SOI.[DATE_CREATED]										AS [DATE],
				ISNULL(SOI.AMOUNT_DUE + ISNULL(SOI.PAID_AMOUNT, 0), 0)	AS [AMOUNT_SUM],
				SOI.ORIGINAL_AMOUNT,
				ISNULL(SOI.AMOUNT_DUE, 0)								AS AMOUNT_DUE,
				ISNULL(SOI.PAID_AMOUNT, 0)								AS PAID_AMOUNT,
				(SELECT COUNT(P.[INVOICE_ID]) 
					FROM [dbo].[F_INVOICE_PAYMENT]	P	WITH (NOLOCK)
				WHERE P.[INVOICE_ID] = SOI.[INVOICE_ID]
					AND P.[IS_DELETED] = 0)								AS PAYMENT_COUNT
			FROM dbo.F_INVOICE	SOI	WITH(NOLOCK)
			INNER JOIN dbo.F_SALES_ORDER	SO	WITH(NOLOCK)
				ON SOI.[ORDER_ID] = SO.[SALES_ORDER_ID]
			WHERE SOI.INVOICE_TYPE_ID = 1 AND SO.CUSTOMER_ID = ' + CAST(@CUSTOMER_ID AS VARCHAR(1000)) + '
				AND (SOI.STATUS_ID  = 3 OR SOI.STATUS_ID = 4)  -- Final or Partially Paid
				AND SOI.AMOUNT_DUE > 0 AND SOI.[IS_VOIDED] = 0
		)

		SELECT TOP(1)
			-1		AS RowID,
			COUNT(SALES_ORDER_INVOICE_ID)	AS SALES_ORDER_INVOICE_ID,
			0		AS SALES_ORDER_ID,
			NULL	AS SALES_ORDER_NUMBER,
			NULL	AS DATE,		
			0		AS AMOUNT_SUM,
			0		AS ORIGINAL_AMOUNT,
			0		AS AMOUNT_DUE,
			0		AS PAID_AMOUNT,
			0		AS PAYMENT_COUNT,
			NULL	AS PAYMENT_STATUS
		FROM m_data ' + @filterCondition + '
		UNION
		SELECT
			t.RowID,
			t.SALES_ORDER_INVOICE_ID,
			t.SALES_ORDER_ID,
			t.SALES_ORDER_NUMBER,
			t.DATE,
			T.AMOUNT_SUM,
			t.ORIGINAL_AMOUNT,
			t.AMOUNT_DUE,
			t.PAID_AMOUNT,
			T.PAYMENT_COUNT,
			(CASE
				WHEN ISNULL(T.PAYMENT_COUNT, 0) > 0 THEN ''Partially paid'' 
				ELSE ''Unpaid''
			END)						AS	PAYMENT_STATUS
		FROM 
			(
				SELECT 
					ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
					*				 
				FROM m_data M ' + @filterCondition + N' 
			) t			
		WHERE
			RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))				
		
		EXEC sp_executesql @query;
END
-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_AUTO_NAME_CONTRACT]
    (@CONTRACT_ID BIGINT)
RETURNS NVARCHAR(250)
AS
BEGIN
	
	DECLARE 
		@PREFIX NVARCHAR(4) = (
			SELECT TOP(1)
				UPPER(SUBSTRING(CUSTOMER_NAME, 0, 4)) + N'-'
			FROM F_CUSTOMER
			WHERE CUSTOMER_ID = @CONTRACT_ID
		),
		@START	BIGINT = (
			SELECT TOP(1) 
				 STARTING_NUMBER_CONTRACT - 1
			FROM U_SYSTEM_SETTINGS WITH (NOLOCK)),
		@MAX_NUMBER	BIGINT
	
	IF (@PREFIX IS NULL)
	BEGIN
		SET @PREFIX = N'-'
	END
		
	SELECT 
		@MAX_NUMBER = MAX([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](AUTO_NAME))
	FROM dbo.F_CONTRACT WITH (NOLOCK)	
	
	RETURN (
		@PREFIX + CAST(ISNULL(@MAX_NUMBER, @START) + 1 AS VARCHAR(250))
	)
END
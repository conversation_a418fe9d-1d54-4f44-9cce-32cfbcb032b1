-- =============================================
-- Author:		<A<PERSON><PERSON><PERSON><PERSON>>
-- Create date: <04/24/2017>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_GET_SHIPPING_REFERENCE_NO]
    (@C_SALES_ORDER_ID BIGINT)
RETURNS NVARCHAR(512)
AS
BEGIN
	
	DECLARE
		@SHIPPING_REF	NVARCHAR(512) = NULL

	SELECT
		@SHIPPING_REF =  N'soNo: '+ FSO.SALES_ORDER_NUMBER + ISNULL(' poNo: '+ FSO.PO_NUMBER, '')
	FROM F_SALES_ORDER	FSO WITH(NOLOCK)
	WHERE FSO.SALES_ORDER_ID = @C_SALES_ORDER_ID
	
	RETURN @SHIPPING_REF
END
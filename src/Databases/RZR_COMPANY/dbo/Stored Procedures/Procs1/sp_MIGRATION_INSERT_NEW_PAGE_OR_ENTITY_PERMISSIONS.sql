 
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_MIGRATION_INSERT_NEW_PAGE_OR_ENTITY_PERMISSIONS]
	@PAGE_GROUP_NAME		NVARCHAR(512) = NULL,	-- SET ONLY IF NEED TO MAP A PAGE GROUP
	@PAGE_GROUP_ID			INT			  = NULL,	--SELECT * FROM D_PERMISSION_CATEGORY
	@PAGE_OR_ENTITY_NAME	NVARCHAR(512) = NULL,	--SELECT * FROM D_PERMISSION_CATEGORY_ENTITY
	@PAGE_OR_ENTITY_DESC	NVARCHAR(512) = NULL,
	@ENTITY_TYPE_ID			INT			  = 2--,	--SELECT * FROM C_PERMISSION_ENTITY_TYPE: 1-Menu Item (Group of Pages), 2-Menu Subitem (Page), 3-Page Element
	--@ACTION_TYPE_IDS		XML			  = N''
AS
BEGIN
	BEGIN TRANSACTION
		SET @PAGE_GROUP_NAME	 = LTRIM(RTRIM(@PAGE_GROUP_NAME));
		SET @PAGE_OR_ENTITY_NAME = LTRIM(RTRIM(@PAGE_OR_ENTITY_NAME));
		SET @PAGE_OR_ENTITY_DESC = LTRIM(RTRIM(@PAGE_OR_ENTITY_DESC));

		IF(ISNULL(@PAGE_GROUP_ID, 0) <= 0 AND ISNULL(LEN(@PAGE_GROUP_NAME), 0) = 0) BEGIN
			SELECT
				N'You should set @PAGE_GROUP_NAME or @PAGE_GROUP_ID'
			RETURN
		END

		DECLARE 
			@NOW		DATETIME	= GETUTCDATE(),
			@ENTITY_ID	BIGINT		= NULL,
			@SP_NAME	NVARCHAR(50)= N'sp_MIGRATION_INSERT_NEW_PAGE_OR_ENTITY_PERMISSIONS'
	
		-- Insert a page group if need to. Get @PAGE_GROUP_ID
		IF (ISNULL(@PAGE_GROUP_ID, 0) <= 0) BEGIN
		
			-- Such a group may exist
			SELECT 
				@PAGE_GROUP_ID = [PERMISSION_CATEGORY_ID]
			FROM D_PERMISSION_CATEGORY
			WHERE LTRIM(RTRIM([PERMISSION_CATEGORY_CD])) = @PAGE_GROUP_NAME

			-- else create it
			IF (@PAGE_GROUP_ID IS NULL) BEGIN
				INSERT INTO D_PERMISSION_CATEGORY (
					[PERMISSION_CATEGORY_CD],
					[INSERTED_DT],
					[INSERTED_BY]
				) VALUES (
					@PAGE_GROUP_NAME,
					@NOW,
					@SP_NAME
				)
				SET @PAGE_GROUP_ID = SCOPE_IDENTITY()
			END
		END

		-- Such a page/an entity may exist
		SELECT 
			@ENTITY_ID = [PERMISSION_CATEGORY_ENTITY_ID]
		FROM [dbo].[D_PERMISSION_CATEGORY_ENTITY] 
		WHERE LTRIM(RTRIM([PERMISSION_CATEGORY_ENTITY_CD])) = @PAGE_OR_ENTITY_NAME
		  AND [ENTITY_TYPE_ID] = @ENTITY_TYPE_ID
		  AND [PERMISSION_CATEGORY_ID] = @PAGE_GROUP_ID

		IF (@ENTITY_ID IS NULL) BEGIN
			-- Insert a page/an entity
			SET @ENTITY_ID = ISNULL((SELECT MAX(PERMISSION_CATEGORY_ENTITY_ID) FROM D_PERMISSION_CATEGORY_ENTITY), 0) + 1
			
			INSERT INTO D_PERMISSION_CATEGORY_ENTITY (
				[PERMISSION_CATEGORY_ENTITY_ID],
				[PERMISSION_CATEGORY_ID],
				CAPTION_TEXT,
				[ENTITY_TYPE_ID],
				[PERMISSION_CATEGORY_ENTITY_CD],
				[PERMISSION_CATEGORY_ENTITY_DESC],
				[INSERTED_DT],
				[INSERTED_BY]
			) VALUES (
				@ENTITY_ID,
				@PAGE_GROUP_ID,
				@PAGE_OR_ENTITY_NAME,
				@ENTITY_TYPE_ID,
				@PAGE_OR_ENTITY_NAME,
				@PAGE_OR_ENTITY_DESC,
				@NOW,
				1
			)
		END

		DECLARE @idoc INT

		---- read xml list into "@idoc"
		--EXEC sp_xml_preparedocument @idoc OUTPUT, @ACTION_TYPE_IDS
		--	DECLARE	@T_ACTION_IDS TABLE (ACTION_ID BIGINT)
		--	INSERT INTO @T_ACTION_IDS (ACTION_ID)
		--	SELECT
		--		x.ID
		--	FROM OPENXML (@idoc, '/Root/Items', 1)
		--	WITH (ID BIGINT) AS x
		--EXEC sp_xml_removedocument @idoc
		
		--INSERT INTO F_PERMISSION_CATEGORY_ACTIONS (
		--	[PERMISSION_CATEGORY_ID],
		--	[PERMISSION_CATEGORY_ACTION_ID],
		--	[INSERTED_DT],
		--	[INSERTED_BY])
		--SELECT 

	COMMIT TRANSACTION
	SELECT 
		@ENTITY_ID AS [ENTITY_ID]
END
-- =============================================
-- =============================================
CREATE function [dbo].[fn_bigint_GetItemStatusIdByAssetWorkflowId](@AssetWorkflowStepId bigint)
returns bigint
as
begin


	declare @isSkuGenerate	bit = 0 

	select 
        @isSkuGenerate = (aw.[IsSkuGenerate] & ~aw.IsFinal)
    from [recycling].[C_AssetWorkflowStep] as aw with (nolock)
    where Id = @AssetWorkflowStepId

	return iif(@isSkuGenerate = 1,
				14, -- SKU generate
				iif(@AssetWorkflowStepId = 102, -- Resale
					1, -- Pending
					8  -- Recycling
				)
			)
	
end
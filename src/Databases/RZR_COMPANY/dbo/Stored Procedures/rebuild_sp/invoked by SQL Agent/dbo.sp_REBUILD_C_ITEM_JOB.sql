-- =============================================
-- Author:		Unknown
-- Create date: Unknown
-- Description:	Pushes C_ITEM_JOB tasks as DW_COM queue jobs. Invoked by SQL Server Agent job named job_{DbName}_REBUILD_C_ITEM_JOB
-- =============================================
/*
	exec [dbo].[sp_REBUILD_C_ITEM_JOB] 
		@C_ROWS_TO_PROCESS = 4
		,@C_IS_DEBUG = 1
*/
CREATE PROCEDURE [dbo].[sp_REBUILD_C_ITEM_JOB] 
	@C_ROWS_TO_PROCESS  INT = 100
	,@C_IS_DEBUG		BIT = 0
AS

	--EXEC dbo.sp_REBUILD_C_ITEM_JOB_BY_ITEM_MASTER
	--	@C_ROWS_TO_PROCESS   = @C_ROWS_TO_PROCESS
	--	,@C_IS_DEBUG		 = @C_IS_DEBUG

	EXEC dbo.sp_REBUILD_C_ITEM_JOB_BY_ITEM
		@C_ROWS_TO_PROCESS   = @C_ROWS_TO_PROCESS
		,@C_IS_DEBUG		 = @C_IS_DEBUG

GO

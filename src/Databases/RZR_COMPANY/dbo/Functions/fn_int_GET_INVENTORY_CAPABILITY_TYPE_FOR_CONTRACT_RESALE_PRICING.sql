-- =============================================
-- Author:		<PERSON>
-- Create date: 11/27/2015
-- Description:	Gets inventory capability type by contract resale pricing capability type
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_INVENTORY_CAPABILITY_TYPE_FOR_CONTRACT_RESALE_PRICING]
(
	@C_CONTRACT_CAPABILITY_TYPE_ID	INT
)
RETURNS INT
AS
BEGIN
	DECLARE @RESULT INT = -1
	SELECT 
		@RESULT = CAPABILITY_TYPE_ID
	FROM D_CONTRACT_CAPABILITY_TYPE				cct		WITH(NOLOCK)
	WHERE cct.CONTRACT_CAPABILITY_TYPE_ID = @C_CONTRACT_CAPABILITY_TYPE_ID

	RETURN @RESULT
END
create table [dbo].[F_InboundOrderManifest] (
	[Id]							bigint			identity (1, 1) not null,
	[RecyclingOrderId]				bigint			not null,
	[SerialNumber]					varchar (50)	null,
	[ItemNumber]					nvarchar (256)	null,
	[Manufacturer]					varchar (50)	null,
	[Mpn]							nvar<PERSON><PERSON> (256)	null,
	[Ipn]							nvarchar (256)	null,
	[Category]						varchar (250)	null,
	[Condition]						varchar (50)	null,
	[Quantity]						int				constraint [DF__F_InboundOrderManifest__Quantity] default ((1)) not null,
	[FloorPrice]					float			null,
	[PurchasePrice]					float			null,
	[InsertedBy]					nvarchar (200)	not null,
	[InsertedDate]					datetime		not null,
	[InsertedByUserId]				bigint			null,
	[InsertedByUserIp]				bigint			null,
	[UpdatedBy]						nvarchar (200)	null,
	[UpdatedDate]					datetime		null,
	[UpdatedByUserId]				bigint			null,
	[UpdatedByUserIp]				bigint			null,
	[AssetTag]						nvarchar(512)	null,
	constraint [PK__InboundOrderManifest] primary key clustered ([Id] asc),
	constraint [FK__InboundOrderManifest__F_RECYCLING_ORDER] foreign key ([RecyclingOrderId]) references [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) on delete cascade,
	constraint [FK__InboundOrderManifest__tb_USER__InsertedByUserId] foreign key ([InsertedByUserId]) references [dbo].[tb_User] ([UserID]),
	constraint [FK__InboundOrderManifest__tb_USER_UpdatedByUserId] foreign key ([UpdatedByUserId]) references [dbo].[tb_User] ([UserID])
);

CREATE TABLE [dbo].[F_CUSTOMER_MASTER_TAG] (
    [CUSTOMER_MASTER_TAG_ID]  BIGINT         IDENTITY (1, 1) NOT NULL,
    [RECYCLING_ORDER_ID]      BIGINT         NULL,
    [RECYCLING_ORDER_ITEM_ID] BIGINT         NULL,
    [CUSTOMER_ID]             BIGINT         NULL,
    [RECYCLING_MASTER_ID]     BIGINT         NULL,
    [TAG_ID]                  BIGINT         NOT NULL,
    [INSERTED_BY]             NVARCHAR (150) CONSTRAINT [DF_F_CUSTOMER_MASTER_TAG_INSERTED_BY] DEFAULT ((1)) NOT NULL,
    [INSERTED_DT]             DATETIME       CONSTRAINT [DF_F_CUSTOMER_MASTER_TAG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]              NVARCHAR (150) NULL,
    [UPDATED_DT]              DATETIME       NULL,
    CONSTRAINT [PK_F_CUSTOMER_MASTER_TAG] PRIMARY KEY CLUSTERED ([CUSTOMER_MASTER_TAG_ID] ASC),
    CONSTRAINT [FK_F_CUSTOMER_MASTER_TAG_F_CUSTOMER] FOREIGN KEY ([CUSTOMER_ID]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID]),
    CONSTRAINT [FK_F_CUSTOMER_MASTER_TAG_F_RECYCLING_ITEM_MASTER] FOREIGN KEY ([RECYCLING_MASTER_ID]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_CUSTOMER_MASTER_TAG_F_RECYCLING_ORDER] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_CUSTOMER_MASTER_TAG_F_RECYCLING_ORDER_ITEM] FOREIGN KEY ([RECYCLING_ORDER_ITEM_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER_ITEM] ([RECYCLING_ORDER_ITEM_ID]),
    CONSTRAINT [FK_F_CUSTOMER_MASTER_TAG_F_TAG] FOREIGN KEY ([TAG_ID]) REFERENCES [dbo].[F_TAG] ([TAG_ID])
);






GO
CREATE NONCLUSTERED INDEX [IX_ORDER_ID_customer_id_master_id]
    ON [dbo].[F_CUSTOMER_MASTER_TAG]([RECYCLING_ORDER_ID] ASC, [CUSTOMER_ID] ASC, [RECYCLING_MASTER_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The tags for customer, recycling order, recycling model. Many tags are allowed', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CUSTOMER_MASTER_TAG';


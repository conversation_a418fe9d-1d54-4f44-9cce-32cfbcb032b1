CREATE TABLE [dbo].[F_ITEM_SALES_FORECAST] (
    [ITEM_SALES_FORECAST_ID]      BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_ID]                     BIGINT        NOT NULL,
    [ITEM_SALES_DATE_MOST_RECENT] DATETIME      NULL,
    [ITEM_SALES_QTY_MOST_RECENT]  FLOAT (53)    NULL,
    [ITEM_SALES_COST_MOST_RECENT] FLOAT (53)    NULL,
    [ITEM_SALES_ORDER_NEXT_DATE]  DATETIME      NULL,
    [INSERTED_BY]                 VARCHAR (150) NOT NULL,
    [INSERTED_DT]                 DATETIME      NOT NULL,
    [UPDATED_BY]                  VARCHAR (150) NULL,
    [UPDATED_DT]                  DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_SALES_FORECAST] PRIMARY KEY CLUSTERED ([ITEM_SALES_FORECAST_ID] ASC),
    CONSTRAINT [FK_F_ITEM_SALES_FORECAST_F_ITEM] FOREIGN KEY ([ITEM_ID]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]) ON DELETE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems it is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_SALES_FORECAST';


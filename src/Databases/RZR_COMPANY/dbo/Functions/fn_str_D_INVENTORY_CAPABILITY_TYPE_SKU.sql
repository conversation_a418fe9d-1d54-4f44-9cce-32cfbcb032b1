-- SELECT dbo.fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU(6392616)
CREATE FUNCTION [dbo].[fn_str_D_INVENTORY_CAPABILITY_TYPE_SKU]
(
	@C_ITEM_INVENTORY_ID bigint   
)
RETURNS varchar(max)
WITH SCHEMABINDING 
AS
BEGIN

	DECLARE @C_RETURN_VAL varchar(max) = null
	select 
		@C_RETURN_VAL = isnull(@C_RETURN_VAL + '|', '') + 
			CONVERT(varchar(19), t.INVENTORY_CAPABILITY_TYPE_ID) + '-' + 
			CONVERT(varchar(19), t.INVENTORY_CAPABILITY_ID)
	from (
		SELECT 
			fiic.INVENTORY_CAPABILITY_TYPE_ID
			,MAX(fiic.INVENTORY_CAPABILITY_ID) as INVENTORY_CAPABILITY_ID
		FROM [dbo].[vw_InventoryDetails]							FIID    WITH(NOLOCK)
		INNER JOIN dbo.F_ITEM_INVENTORY_CAPABILITY					fiic	WITH(NOLOCK)
			ON FIIC.ITEM_INVENTORY_ID = FIID.Id
			AND fiic.IS_INACTIVE = 0
			AND fiic.IS_DELETED = 0
		INNER JOIN dbo.F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE	fatict	WITH(NOLOCK)
			ON  fatict.INVENTORY_CAPABILITY_TYPE_ID = fiic.INVENTORY_CAPABILITY_TYPE_ID	
			AND fatict.INVENTORY_ATTRIBUTE_TYPE_ID  = FIID.AttributeSetId
			AND fatict.IS_SKU_EFFECTING = 1
		WHERE FIID.Id = @C_ITEM_INVENTORY_ID
		group by
			fiic.INVENTORY_CAPABILITY_TYPE_ID
			,fiic.INVENTORY_CAPABILITY_ID
	) t
	ORDER BY
		t.INVENTORY_CAPABILITY_TYPE_ID
	
	RETURN NULLIF(@C_RETURN_VAL, '')

END

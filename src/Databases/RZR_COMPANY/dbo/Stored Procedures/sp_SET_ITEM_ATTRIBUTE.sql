-- =============================================
-- Author:		V.DRE<PERSON>ZOVA
-- Create date: 05/12/2015
-- Description:
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_ITEM_ATTRIBUTE]
	@ITEM_ATTRIBUTE_ID				BIGINT,
	@ITEM_ATTRIBUTE_CD				VARCHAR(128),
	@ITEM_ATTRIBUTE_DESC			NVARCHAR(512),
	@ITEM_ATTRIBUTE_LABEL			NVARCHAR(512),
	@ITEM_ATTRIBUTE_DEFAULT_VALUE   NVARCHAR(512),
	@INVENTORY_CAPABILITY_TYPE_ID	BIGINT,
	@ITEM_ATTRIBUTE_PAGE_ID			BIGINT,
	@ITEM_ATTRIBUTE_DEVICE_ID		BIGINT,
	@ITEM_ATTRIBUTE_GROUP_ID		BIGINT,
	@ITEM_ATTRIBUTE_FIELD_ID		BIGINT
AS
BEGIN
	
	IF (EXISTS(SELECT TOP(1) 1 FROM [dbo].[C_ITEM_ATTRIBUTE] WITH (NOLOCK) 
		WHERE (ITEM_ATTRIBUTE_CD = @ITEM_ATTRIBUTE_CD OR ITEM_ATTRIBUTE_LABEL = @ITEM_ATTRIBUTE_LABEL)
		AND (@ITEM_ATTRIBUTE_ID IS NULL OR ITEM_ATTRIBUTE_ID <> @ITEM_ATTRIBUTE_ID))) BEGIN

		SELECT 0

	END
	ELSE BEGIN

		UPDATE C_ITEM_ATTRIBUTE
		SET 
			ITEM_ATTRIBUTE_CD				= @ITEM_ATTRIBUTE_CD,
			ITEM_ATTRIBUTE_DESC				= @ITEM_ATTRIBUTE_DESC,
			ITEM_ATTRIBUTE_LABEL			= @ITEM_ATTRIBUTE_LABEL,
			ITEM_ATTRIBUTE_EMPTY_VALUE		= @ITEM_ATTRIBUTE_DEFAULT_VALUE,
			INVENTORY_CAPABILITY_TYPE_ID	= @INVENTORY_CAPABILITY_TYPE_ID,
			ITEM_ATTRIBUTE_PAGE_ID			= @ITEM_ATTRIBUTE_PAGE_ID,
			ITEM_ATTRIBUTE_DEVICE_ID		= @ITEM_ATTRIBUTE_DEVICE_ID,
			ITEM_ATTRIBUTE_GROUP_ID			= @ITEM_ATTRIBUTE_GROUP_ID,
			ITEM_ATTRIBUTE_FIELD_ID			= @ITEM_ATTRIBUTE_FIELD_ID,
			UPDATED_BY						= 'sp_SET_ITEM_ATTRIBUTE',
			UPDATED_DT						= GETUTCDATE()
		WHERE ITEM_ATTRIBUTE_ID		= @ITEM_ATTRIBUTE_ID

		IF (@@ROWCOUNT = 0) BEGIN

			INSERT INTO [dbo].[C_ITEM_ATTRIBUTE]
			(				
				ITEM_ATTRIBUTE_CD,
				ITEM_ATTRIBUTE_DESC,
				ITEM_ATTRIBUTE_LABEL,
				ITEM_ATTRIBUTE_EMPTY_VALUE,
				INVENTORY_CAPABILITY_TYPE_ID,
				ITEM_ATTRIBUTE_PAGE_ID,
				ITEM_ATTRIBUTE_DEVICE_ID,
				ITEM_ATTRIBUTE_GROUP_ID,
				ITEM_ATTRIBUTE_FIELD_ID,
				INSERTED_BY,
				INSERTED_DT
			)
			SELECT				
				@ITEM_ATTRIBUTE_CD,
				@ITEM_ATTRIBUTE_DESC,
				@ITEM_ATTRIBUTE_LABEL,
				@ITEM_ATTRIBUTE_DEFAULT_VALUE,
				@INVENTORY_CAPABILITY_TYPE_ID,
				@ITEM_ATTRIBUTE_PAGE_ID,
				@ITEM_ATTRIBUTE_DEVICE_ID,
				@ITEM_ATTRIBUTE_GROUP_ID,
				@ITEM_ATTRIBUTE_FIELD_ID,
				'sp_SET_ITEM_ATTRIBUTE',
				GETUTCDATE()

		END

		SELECT 1

	END

END
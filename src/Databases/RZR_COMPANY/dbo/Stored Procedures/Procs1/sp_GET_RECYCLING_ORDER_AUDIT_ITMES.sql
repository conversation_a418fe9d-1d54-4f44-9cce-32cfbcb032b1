-- =============================================
-- Author:	 <PERSON>
-- Create date: 04/07/2015
-- Description: Gets recycling order lot items
-- =============================================
-- EXEC dbo.[sp_GET_RECYCLING_ORDER_AUDIT_ITMES] 1218, 0, 20, 'RECYCLING_ORDER_AUDIT_ITEM_ID', 'DESC'
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ORDER_AUDIT_ITMES]
    @RECYCLING_AUDIT_ORDER_ID	BIGINT,
    @PAGE_INDEX					BIGINT	     = 0,
    @ITEMS_PER_PAGE				BIGINT	     = 20,
    @ORDER_COLUMN_NAME			VARCHAR(100) = 'Id',
    @ORDER_DIRECTION			VARCHAR(4)   = 'ASC',
    @FILTER_WHERE				VARCHAR(max) = NULL
AS
BEGIN
    DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
    DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
    DECLARE @filterCondition VARCHAR(MAX) = N'';
    IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
    BEGIN
	   SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
    END				

    DECLARE @query NVARCHAR (MAX) = '			
	   WITH m_data AS
	   (
		  SELECT 
			 froai.[Id]
			 ,froai.[AuditSessionId]
			 ,froai.[RecyclingOrderItemId]
			 ,froai.[ItemNumber]
			 ,froai.[SerialNumber]
			 ,froai.[UniqueId]
			 ,froai.[Reference]
			 ,froai.[InventoryAttributeTypeId]
			 ,froai.[ManufacturerId]
			 ,CASE
				WHEN dm.MANUFACTURER_CD IS NOT NULL THEN dm.MANUFACTURER_CD
				ELSE froai.[ManufacturerName]
			 END															AS ManufacturerCd
			 ,froai.[AuditDate]												AS RecyclingOrderAuditedDate
			 ,froai.[Tag]
			 ,froai.[Weight]												AS WeightLbs
			 ,froai.[Quantity]
			 ,froai.[Notes]
			 ,froai.[CategoryId]
			 ,CASE
				WHEN froai.[CategoryId] IS NOT NULL THEN REPLACE(ch.ITEM_CATEGORY_FULL_PATH,''|'','' > '')
				ELSE froai.[CategoryName]
			  END															AS CategoryName
			 ,CASE
				WHEN froai.[CategoryId] IS NOT NULL THEN ch.CATEGORYNAME
				ELSE froai.[CategoryName]
			 END															AS CategoryFriendlyName
			 ,im.ITEM_MASTER_ID												AS ItemNumberId
			 ,ii.ITEM_INVENTORY_ID											AS ItemInventoryId
			 ,froai.LocationId												AS LocationId
			 ,dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(froai.LocationId)  AS LocationValue
			 ,ic_loc.WAREHOUSE_ID											AS WarehouseId
			 ,froai.[AssetWorkflowStepId]									AS AuditStatusId
			 ,ias.[Label]													AS AuditStatusCd			 
			 ,im2.MANUFACTURER_ID as RealManufacturerId
			 ,iidm.MANUFACTURER_CD as RealManufacturerCd
			 ,im2.ITEM_MASTER_ID as RealItemNumberId
			 ,im2.ITEM_NUMBER as RealItemNumber
			 ,foi.IS_TEST_COMPLETE as LotIsTestComplete
			 ,froai.CosmeticsId			 as ConditionId
			 ,dic.INVENTORY_CAPABILITY_VALUE as Condition
			 ,froai.ConditionId     as RealConditionId
			 ,ic.ITEM_CONDITION_DESC as RealCondition
			 ,ISNULL(DATALENGTH(ic_udef.INVENTORY_CAPABILITY_VALUE),0) AS IsUniqueDefect
			 ,ISNULL(ii.IS_VIRTUAL, 0)		 as IsVirtual
			 ,froai.WorkflowStepId		 as WorkflowId
			 ,froai.RecyclingItemMasterId as CommodityId
			 ,[recycling].[fn_nvarchar_GetCommodityName](froai.[RecyclingItemMasterId], foi.[RECYCLING_ORDER_ID]) AS [CommodityCd]
			 ,ii.ITEM_ID as ItemID
			 ,foi.LOT_AUTO_NAME as LotAutoName
			 ,[dbo].[fn_bit_IsInventoryHasPartAdded](ii.ITEM_INVENTORY_ID)	as HasChildren ';
	 DECLARE @query_1 NVARCHAR (MAX) = '		
		  FROM recycling.F_Asset		 froai WITH(NOLOCK)
		  LEFT JOIN dbo.D_MANUFACTURER			 dm    WITH(NOLOCK)
		    ON froai.ManufacturerId = dm.MANUFACTURER_ID
		  LEFT JOIN D_CATEGORY_HIERARCHY		ch    WITH(NOLOCK)
		    ON  CH.IS_INACTIVE = 0
			AND CH.CATEGORY_ID = froai.CategoryId
		  LEFT JOIN F_ITEM_MASTER			 im    WITH(NOLOCK)
		    ON im.ITEM_MASTER_ID = froai.ItemMasterId AND im.IS_DELETED = 0
		  LEFT JOIN [dbo].[F_ITEM_INVENTORY] ii WITH(NOLOCK)
			ON ii.[AssetId] = froai.[Id]
		  INNER JOIN [recycling].[C_AssetWorkflowStep]  ias WITH(NOLOCK)
			ON froai.AssetWorkflowStepId = ias.Id
		  LEFT JOIN dbo.F_LOCATION		ic_loc		WITH (NOLOCK)
			ON froai.[LocationId] = ic_loc.LOCATION_ID
		  LEFT JOIN [dbo].[F_ITEM_MASTER]		im2		WITH(NOLOCK)
			ON im2.ITEM_MASTER_ID = ii.ITEM_MASTER_ID
		  LEFT JOIN [dbo].D_MANUFACTURER	iidm	WITH(NOLOCK)
			ON im2.MANUFACTURER_ID = iidm.MANUFACTURER_ID
		  LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM AS foi
			ON foi.RECYCLING_ORDER_ITEM_ID = froai.RecyclingOrderItemId
		  LEFT JOIN dbo.D_INVENTORY_CAPABILITY		ic_udef		WITH (NOLOCK)
			ON ii.[DEFECT_UNIQUE_ID] = ic_udef.INVENTORY_CAPABILITY_ID
		  LEFT JOIN dbo.D_INVENTORY_CAPABILITY AS dic
			ON dic.INVENTORY_CAPABILITY_ID = froai.CosmeticsId 
			AND dic.[INVENTORY_CAPABILITY_TYPE_ID] = 8 -- For cosmetics
		  LEFT JOIN dbo.D_ITEM_CONDITION AS ic
			ON ic.ITEM_CONDITION_ID = froai.ConditionId
		  WHERE froai.[AuditSessionId] = ' + CAST(@RECYCLING_AUDIT_ORDER_ID AS VARCHAR(20)) + '
	   )'
	
	DECLARE @query3 NVARCHAR (MAX) = '		
	   SELECT TOP(1)
		  -1						    AS RowID,
		  COUNT (Id) AS RecyclingOrderAuditItemId,	
		  NULL					    AS RecyclingAuditOrderId,	
		  NULL					    AS RecyclingOrderItemId,
		  NULL						AS Reference,
		  NULL					    AS ItemNumber,
		  NULL					    AS SerialNumber,
		  NULL						AS UniqueId,
		  0							AS InventoryAttributeTypeId,
		  NULL					    AS ManufacturerId,
		  NULL					    AS ManufacturerCd,
		  NULL					    AS RecyclingOrderAuditedDate,
		  NULL					    AS AssetTag,
		  0.0					    AS WeightLbs,
		  0							AS Quantity,
		  NULL					    AS Notes,
		  0							AS CategoryId,
		  NULL						AS CategoryName,
		  0							AS ItemNumberId,
		  NULL						AS CategoryFriendlyName,
		  NULL						AS ItemInventoryId,
		  0							AS LocationId,
		  NULL						AS LocationValue,
		  0							AS WarehouseId,
		  0							AS AuditStatusId,
		  NULL						AS AuditStatusCd,
		  0							AS RealManufacturerId,
		  NULL						AS RealManufacturerCd,
		  0							AS RealItemNumberId,
		  NULL						AS RealItemNumber,
		  0							AS LotIsTestComplete,
		  0							AS ConditionId,
		  NULL						AS Condition,
		  0							AS RealConditionId,
		  NULL						AS RealCondition,
		  0							AS IsUniqueDefect,
		  0							AS IsVirtual,
		  0							AS WorkflowId,
		  0							AS CommodityId,
		  NULL						AS CommodityCd,
		  NULL						AS ItemId,
		  NULL						AS LotAutoName,
		  NULL						AS HasChildren

	   FROM m_data
	   ' + @filterCondition + '
	   UNION ALL
	   SELECT
		  t.RowID
		  ,t.[Id] AS RecyclingOrderAuditItemId
		  ,t.[AuditSessionId] AS RecyclingAuditOrderId
		  ,t.[REcyclingOrderItemId] AS RecyclingOrderItemId
		  ,t.[Reference] as Reference
		  ,t.[ItemNumber] AS ItemNumber
		  ,t.[SerialNumber] AS SerialNumber
		  ,t.[UniqueId] as UniqueId
		  ,t.[InventoryAttributeTypeId] as InventoryAttributeTypeId
		  ,t.[ManufacturerId] AS ManufacturerId
		  ,t.[ManufacturerCd] AS ManufacturerCd
		  ,t.[RecyclingOrderAuditedDate] AS RecyclingOrderAuditedDate
		  ,t.[Tag] AS AssetTag
		  ,t.[WeightLbs] AS WeightLbs
		  ,t.[Quantity] AS Quantity
		  ,t.[Notes] AS Notes
		  ,t.[CategoryId] AS CategoryId
		  ,t.[CategoryName] AS CategoryName
		  ,t.[ItemNumberId] AS ItemNumberId
		  ,t.[CategoryFriendlyName] AS CategoryFriendlyName
		  ,t.[ItemInventoryId] as ItemInventoryId
		  ,t.[LocationId]	 as LocationId
		  ,t.[LocationValue] as LocationValue
		  ,t.[WarehouseId] as WarehouseId
		  ,t.[AuditStatusId] as AuditStatusId
		  ,t.[AuditStatusCd] as AuditStatusCd
		  ,t.RealManufacturerId
		  ,t.RealManufacturerCd
		  ,t.RealItemNumberId
		  ,t.RealItemNumber
		  ,t.LotIsTestComplete
		  ,t.ConditionId
		  ,t.Condition
		  ,t.RealConditionId
		  ,t.RealCondition
		  ,t.IsUniqueDefect
		  ,t.IsVirtual
		  ,t.WorkflowId
		  ,t.CommodityId
		  ,t.CommodityCd
		  ,t.ItemId
		  ,t.LotAutoName
		  ,t.HasChildren
		  FROM (SELECT 
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				* 
			 FROM m_data	
			 ' + @filterCondition + ') t	
		  WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))
    select @query = @query + @query_1 + @query3
	--SELECT @query
    EXEC sp_executesql @query;

END

-- =============================================
-- Author:	 <PERSON><PERSON><PERSON>
-- Create date: 07/27/2023
-- Description: get reset customer password notifications from queue
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_RESET_CUSTOMER_PASSWORD_FROM_QUEUE]
    @COUNT INT = 40
AS
BEGIN
    DECLARE @NOTIFICATION_TYPE_ID	 BIGINT  = 43;
    DECLARE @IS_ALLOWED			 BIT	    = dbo.fn_bit_IS_CLIENT_PORTAL_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID);

    IF(@IS_ALLOWED = 0)
    BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
		  IS_INACTIVE = 1,
		  UPDATED_BY = N'sp_GET_NOTIFICATIONS_RESET_CUSTOMER_PASSWORD_FROM_QUEUE: @IS_ALLOWED = 0',
		  UPDATED_DT = GETUTCDATE()
		WHERE IS_INACTIVE = 0
		  AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID 	;
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

		SELECT TOP(@COUNT)
		* 
		FROM (SELECT
			NQ.NotificationQueueId				as NotificationQueueId
			,concat(coalesce(CC.MAIN_EMAIL, CC.CC_EMAIL, CC.OTHER_EMAIL), ', ',
                     [dbo].[fn_str_GET_NOTIFICATION_EMAIL_LIST](@NOTIFICATION_TYPE_ID, C.REP_ID,
                                                                MCC.CUSTOMER_CONTACT_ID, null, NQ.NotificationSetId, NQ.CustomerId)) as EMAILS
			,DUA.CUSTOMER_ID						as CustomerId
			,DUA.CUSTOMER_PRIMARY_CONTACT_ID		as CustomerContactId
			,DUA.CUSTOMER_NAME						as CompanyName
			,DUA.CUSTOMER_SALUTATION				as Salutation
			,DUA.CUSTOMER_FIRST_NAME				as MainContactFirstName
			,DUA.CUSTOMER_MIDDLE_INITIAL			as MainContactMiddleInitial
			,DUA.CUSTOMER_LAST_NAME					as MainContactLastName
			,DUA.CUSTOMER_JOB_TITLE					as MainContactJobTitle
			,DUA.CUSTOMER_PHONE_MAIN				as MainContactPhoneMain
			,DUA.CUSTOMER_PHONE_MOBILE				as MainContactPhoneMobile
			,DUA.CUSTOMER_PHONE_HOME				as MainContactPhoneHome
			,DUA.CUSTOMER_PHONE_WORK				as MainContactPhoneWork
			,DUA.CUSTOMER_FAX						as MainContactFax
			,DUA.CUSTOMER_PRIMARY_EMAIL				as MainContactPrimaryEmail
			,DUA.CUSTOMER_SECONDARY_EMAIL			as MainContactSecondaryEmail
			,DUA.CUSTOMER_OTHER_EMAIL				as MainContactOtherEmail
			,DUA.CUSTOMER_WEBSITE					as MainContactWebsite
			,DUA.[LOGIN]							as CustomerLogin
			,DUA.[PASSWORD]							as CustomerPassword
		FROM @tNotifyItemWithNotifySet		NQ
		CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
		LEFT JOIN F_CUSTOMER_CONTACT	CC  WITH(NOLOCK)
		ON CC.CUSTOMER_CONTACT_ID = QP.CUSTOMER_CONTACT_ID
		LEFT JOIN F_CUSTOMER	C  WITH(NOLOCK)
		ON CC.CUSTOMER_ID = C.CUSTOMER_ID
		left join F_CUSTOMER_CONTACT MCC with(nolock)
        on C.CUSTOMER_ID = MCC.CUSTOMER_ID and MCC.IS_MAIN = 1
		CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER_CONTACT]					(QP.CUSTOMER_CONTACT_ID)   DUA
		WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND CC.IS_INACTIVE = 0 AND CC.IS_DELETED = 0
		AND C.IS_DELETED = 0 AND C.IS_INACTIVE = 0 AND C.IS_LOCK = 0 AND C.CLIENT_PORTAL_IS_ENABLED = 1
		AND (COALESCE(RTRIM(CC.[LOGIN]), '') <> '' AND COALESCE(RTRIM(CC.[PASSWORD]), '') <> '')
		) as T;
END
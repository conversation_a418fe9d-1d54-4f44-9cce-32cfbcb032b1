-- =============================================
-- Author:		<I.Orobets>
-- Create date: <19/05/2015>
-- Description:	<Lists recycling images>
-- =============================================
CREATE PROCEDURE [dbo].[sp_LIST_RECYCLING_ORDER_IMAGES]
	@RECYCLING_ORDER_ID BIGINT,
	@IS_INBOUND			BIT
AS
BEGIN
	IF(@IS_INBOUND = 1)
	BEGIN
		SELECT 
			IM.RECYCLING_ORDER_ITEM_IMAGE_ID								  AS Id,
			ROW_NUMBER() OVER (ORDER BY IM.RECYCLING_ORDER_ITEM_IMAGE_ID ASC) AS PositionIndex,
			IM.[FILE_NAME]													  AS Name,
			IM.BASE_BYTE_COUNT												  AS [Length]
		FROM F_RECYCLING_ORDER					FO  WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER_ITEM		FOI WITH(NOLOCK)
			ON FOI.RECYCLING_ORDER_ID = FO.RECYCLING_ORDER_ID
		INNER JOIN F_RECYCLING_ORDER_ITEM_IMAGE	IM  WITH(NOLOCK)
			ON IM.RECYCLING_ORDER_ITEM_ID = FOI.RECYCLING_ORDER_ITEM_ID
		WHERE FO.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
		ORDER BY 
			RECYCLING_ORDER_ITEM_IMAGE_ID ASC
	END
	ELSE BEGIN
		SELECT 
			IM.RECYCLING_ORDER_ITEM_IMAGE_ID								  AS Id,
			ROW_NUMBER() OVER (ORDER BY IM.RECYCLING_ORDER_ITEM_IMAGE_ID ASC) AS PositionIndex,
			IM.[FILE_NAME]													  AS Name,
			IM.BASE_BYTE_COUNT												  AS [Length]
		FROM F_RECYCLING_ORDER					FO  WITH(NOLOCK)
		INNER JOIN F_RECYCLING_ORDER_ITEM		FOI WITH(NOLOCK)
			ON FOI.OUTBOUND_ORDER_ID = FO.RECYCLING_ORDER_ID
		INNER JOIN F_RECYCLING_ORDER_ITEM_IMAGE	IM  WITH(NOLOCK)
			ON IM.RECYCLING_ORDER_ITEM_ID = FOI.RECYCLING_ORDER_ITEM_ID
		WHERE FO.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
		ORDER BY 
			RECYCLING_ORDER_ITEM_IMAGE_ID ASC
	END
END
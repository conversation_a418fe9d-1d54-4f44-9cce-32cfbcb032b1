

-- =============================================
-- Author:		<<PERSON>>
-- Create date: <06/25/2014>
-- Description:	<>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_MASTERS]
    @ORDER_ID		    BIGINT,
    @ORDER_DATA			XML,
    @USE_DEFAULT_PRICE  BIT			= NULL,
    @DEFAULT_PRICE	    FLOAT		= 0,
    @DEFAULT_PRICE_TYPE INT			= NULL,
    @USER_ID		    BIGINT,
    @IP					BIGINT
AS
BEGIN		
    SET @USE_DEFAULT_PRICE = ISNULL(@USE_DEFAULT_PRICE, 0)
	   
    DECLARE @idoc INT
	
    EXEC sp_xml_preparedocument @idoc OUTPUT, @ORDER_DATA	
    DECLARE @T_ORDER_DATA TABLE ( 		
	   ITEM_MASTER_ID		 BIGINT,
	   PRICE				 FLOAT,
	   PRICE_TYPE_ID		 INT,
	   [WEIGHT]				 FLOAT,
	   PRICE_CHANGE_COMMENT	 NVARCHAR(MAX)
    )
    INSERT INTO @T_ORDER_DATA (		
	   [ITEM_MASTER_ID],
	   [PRICE],
	   [PRICE_TYPE_ID],
	   [WEIGHT],
	   [PRICE_CHANGE_COMMENT]
    )
    SELECT -- select from xml doc as table		
	   X.ITEM_MASTER_ID,
	   X.PRICE,
	   X.PRICE_TYPE_ID,
	   X.[WEIGHT],
	   ISNULL(X.PRICE_CHANGE_COMMENT, OIM.[PriceChangeNotes])
    FROM OPENXML (@idoc, '/Root/Items/Item', 1) 
    WITH ( 		
	   ITEM_MASTER_ID	    BIGINT,
	   PRICE			    FLOAT,
	   PRICE_TYPE_ID	    INT,
	   [WEIGHT]				FLOAT,
	   PRICE_CHANGE_COMMENT NVARCHAR(MAX)	
	   ) AS X	
    LEFT JOIN [recycling].[F_CommodityRule] OIM WITH (NOLOCK)		
	   ON OIM.[RecyclingOrderId] = @ORDER_ID AND X.ITEM_MASTER_ID = OIM.[CommodityId] --AND X.PRICE = OIM.PRICE
			         
    EXEC sp_xml_removedocument @idoc	

    SET XACT_ABORT ON
    BEGIN TRANSACTION 

	   UPDATE [dbo].[F_SALES_ORDER_ITEM] SET
		[CommodityRuleId] = NULL
	   WHERE [CommodityRuleId] IN (SELECT [Id] FROM [recycling].[F_CommodityRule] WITH (NOLOCK) WHERE  [RecyclingOrderId] = @ORDER_ID)

	   UPDATE [dbo].[F_PURCHASE_ORDER_ITEM] SET
		[CommodityRuleId] = NULL
		,UPDATED_BY = N'sp_SET_RECYCLING_ORDER_ITEM_MASTERS'
		,UPDATED_DT = GETUTCDATE()
		,UPDATED_BY_ID = @USER_ID
		,UPDATED_BY_IP = @IP
	   WHERE [CommodityRuleId] IN (SELECT [Id] FROM [recycling].[F_CommodityRule] WITH (NOLOCK) WHERE  RecyclingOrderId = @ORDER_ID)

	   -- Delete the removed ones
	   DELETE M
	   FROM [recycling].[F_CommodityRule]  M
	   LEFT JOIN @T_ORDER_DATA				  T
	     ON T.ITEM_MASTER_ID = M.[CommodityId]
	   WHERE M.[RecyclingOrderId] = @ORDER_ID
	     AND T.ITEM_MASTER_ID IS NULL
	   
	   -- Update the existing ones
	   UPDATE M SET
		   [PRICE]			  = T.[PRICE]
		  ,[PriceTypeId]		  = T.[PRICE_TYPE_ID]
		  ,[DefaultWeight]			  = T.[WEIGHT]
		  ,[PriceChangeNotes]  = T.[PRICE_CHANGE_COMMENT]
		  ,[SourceContractId]			  = CASE
			 WHEN M.[SourceContractId] IS NOT NULL THEN M.[SourceContractId]
			 ELSE (SELECT TOP(1)
				    OC.CONTRACT_ID
				FROM F_RECYCLING_ORDER_CONTRACT	 OC
				INNER JOIN [recycling].[F_CommodityRule]	 CM
				  ON CM.[ContractId] = OC.CONTRACT_ID
				WHERE OC.RECYCLING_ORDER_ID = @ORDER_ID
				  AND CM.[CommodityId] = T.ITEM_MASTER_ID)
		  END
	   FROM [recycling].[F_CommodityRule]	   M
	   INNER JOIN @T_ORDER_DATA			   T
	     ON T.ITEM_MASTER_ID = M.[CommodityId]	   
	   WHERE M.[RecyclingOrderId] = @ORDER_ID

	   -- Add the new ones
	   INSERT INTO [recycling].[F_CommodityRule]
	   (
		  [RecyclingOrderId],
		  [SourceContractId],
		  [CommodityId],
		  [Price],
		  [PriceTypeId],
		  [DefaultWeight],
		  [PriceChangeNotes],
		  [InsertedBy],
		  [InsertedDate]
	   )
	   SELECT 
		  @ORDER_ID,
		  (SELECT TOP(1)
			 OC.CONTRACT_ID
		  FROM F_RECYCLING_ORDER_CONTRACT	   OC
		  INNER JOIN [recycling].[F_CommodityRule]   CM
			 ON CM.[ContractId] = OC.CONTRACT_ID
		  WHERE OC.RECYCLING_ORDER_ID = @ORDER_ID
			 AND CM.[CommodityId] = T.ITEM_MASTER_ID),
		  T.[ITEM_MASTER_ID],
		  T.[PRICE],
		  T.[PRICE_TYPE_ID],
		  T.[WEIGHT],
		  T.[PRICE_CHANGE_COMMENT],
		  'sp_SET_RECYCLING_ORDER_ITEM_MASTERS',
		  GETUTCDATE()
	   FROM @T_ORDER_DATA					  T
	   LEFT JOIN [recycling].[F_CommodityRule]	  M
		ON M.[CommodityId] = T.ITEM_MASTER_ID AND M.[RecyclingOrderId] = @ORDER_ID
	   WHERE M.[CommodityId] IS NULL

	   UPDATE F_RECYCLING_ORDER SET
		  USE_DEFAULT_PRICE		= @USE_DEFAULT_PRICE,
		  DEFAULT_PRICE		 = @DEFAULT_PRICE,
		  DEFAULT_PRICE_TYPE_ID	= @DEFAULT_PRICE_TYPE,
		  UPDATED_BY_USER		= @USER_ID,
		  UPDATED_BY_IP		 = @IP
	   WHERE RECYCLING_ORDER_ID	= @ORDER_ID

	   UPDATE dbo.F_RECYCLING_ORDER_INBOUND SET
		  AMOUNT_OWED	   = (SELECT dbo.fn_money_GET_RECYCLING_AMOUNT_OWED(@ORDER_ID)),
		  UPDATED_BY_USER = @USER_ID,
		  UPDATED_BY_IP   = @IP
	   WHERE RECYCLING_ORDER_ID = @ORDER_ID
	
    COMMIT TRANSACTION

		
END
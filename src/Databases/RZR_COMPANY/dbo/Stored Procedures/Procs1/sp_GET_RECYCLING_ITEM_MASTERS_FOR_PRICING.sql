-- =============================================
-- Author:		<<PERSON>>
-- Create date: <06/25/2014>
-- Description:	<>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_ITEM_MASTERS_FOR_PRICING]
	@SELECTED_MASTERS	XML,
	@COMMODITIES		XML,
	@CATEGORIES			XML,	
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'[RECYCLING_ITEM_MASTER_ID]',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(MAX)	= ''	
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END		

	DECLARE @idoc INT
	
	EXEC sp_xml_preparedocument @idoc OUTPUT, @COMMODITIES	
	DECLARE @T_COMMODITIES TABLE ( 
		ID BIGINT
	)
	INSERT INTO @T_COMMODITIES (
		ID
	)
	SELECT -- select from xml doc as table
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( 
		ID BIGINT
		) AS x		         
	EXEC sp_xml_removedocument @idoc


	EXEC sp_xml_preparedocument @idoc OUTPUT, @CATEGORIES	
	DECLARE @T_CATEGORIES TABLE ( 
		ID INT
	)
	INSERT INTO @T_CATEGORIES (
		ID
	)
	SELECT -- select from xml doc as table
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( 
		ID INT
		) AS x		         
	EXEC sp_xml_removedocument @idoc


	EXEC sp_xml_preparedocument @idoc OUTPUT, @SELECTED_MASTERS	
	CREATE TABLE #T_SELECTED_MASTERS ( 
		ITEM_MASTER_ID BIGINT 
	)
	INSERT INTO #T_SELECTED_MASTERS (
		ITEM_MASTER_ID
	)
	SELECT -- select from xml doc as table
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1) 
	WITH ( 
		ID BIGINT 
		) AS x		         
	EXEC sp_xml_removedocument @idoc


	CREATE TABLE #ITEM_MASTER (
		RECYCLING_ITEM_MASTER_ID		BIGINT,
		[RECYCLING_ITEM_MASTER_NAME]  VARCHAR(250),
		CATEGORY_ID				INT,
		IS_INACTIVE				BIT
	)
		
	DECLARE @ITEMS VARCHAR(300);
	IF (EXISTS(SELECT 1 FROM @T_COMMODITIES) OR 
		EXISTS(SELECT 1 FROM @T_CATEGORIES))
	BEGIN
		SET @ITEMS = N'#ITEM_MASTER';
		INSERT INTO #ITEM_MASTER (
			RECYCLING_ITEM_MASTER_ID,
			[RECYCLING_ITEM_MASTER_NAME],
			CATEGORY_ID,
			IS_INACTIVE
		)
		SELECT DISTINCT
			M.RECYCLING_ITEM_MASTER_ID,
			M.RECYCLING_ITEM_MASTER_NAME,
			CH.CATEGORY_ID,
			M.IS_INACTIVE
		FROM [dbo].[F_RECYCLING_ITEM_MASTER]	M	WITH(NOLOCK)		
		LEFT JOIN [dbo].[D_CATEGORY_HIERARCHY]	CH	WITH(NOLOCK)
			ON  CH.IS_INACTIVE = 0
			AND CH.CATEGORY_ID = M.CATEGORY_ID	
		WHERE (M.RECYCLING_ITEM_MASTER_ID   IN (SELECT ID FROM @T_COMMODITIES)
			   OR M.CATEGORY_ID				IN (SELECT ID FROM @T_CATEGORIES))		
	END
	ELSE BEGIN
		SET @ITEMS = N'[dbo].[F_RECYCLING_ITEM_MASTER]';
	END


	DECLARE @query NVARCHAR (MAX) = '
		WITH m_data AS
		(
			SELECT
				M.RECYCLING_ITEM_MASTER_ID,
				M.RECYCLING_ITEM_MASTER_NAME,
				REPLACE(CH.ITEM_CATEGORY_FULL_PATH, ''|'', ''>'') AS CATEGORY
			FROM ' + @ITEMS + N'				M	WITH(NOLOCK)		
			LEFT JOIN #T_SELECTED_MASTERS		CM	WITH (NOLOCK)
			  ON M.RECYCLING_ITEM_MASTER_ID = CM.ITEM_MASTER_ID
			LEFT JOIN [D_CATEGORY_HIERARCHY]	CH	WITH(NOLOCK)
			  ON  CH.IS_INACTIVE = 0
			  AND CH.CATEGORY_ID = M.CATEGORY_ID
			WHERE CM.ITEM_MASTER_ID IS NULL
			  AND M.IS_INACTIVE = 0
		)
		'+N'
		SELECT TOP(1)
			-1								AS RowID,
			COUNT(RECYCLING_ITEM_MASTER_ID) AS RECYCLING_ITEM_MASTER_ID,	
			NULL							AS RECYCLING_ITEM_MASTER_NAME,
			NULL							AS CATEGORY			
		FROM m_data
		' + @filterCondition + '
		UNION ALL
		SELECT
			t.RowID,
			t.RECYCLING_ITEM_MASTER_ID,	
			t.RECYCLING_ITEM_MASTER_NAME,
			t.CATEGORY
		FROM (SELECT
				ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
				* 
		  FROM m_data
		  ' + @filterCondition + ') t
		WHERE 
			RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))

		--select @query
		EXEC sp_executesql @query;

		DROP TABLE #T_SELECTED_MASTERS
END
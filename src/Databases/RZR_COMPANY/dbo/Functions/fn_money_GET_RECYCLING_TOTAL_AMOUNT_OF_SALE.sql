-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 01-06-2017
-- Description:	Get total amount of sale
-- =============================================
CREATE FUNCTION fn_money_GET_RECYCLING_TOTAL_AMOUNT_OF_SALE
(
	@RECYCLING_ORDER_ID BIGINT
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @PRICE FLOAT = 0.0
	
	SELECT
		@PRICE = ISNULL(
							(
							SELECT 
								SUM(ITEM_PRICE)
							FROM dbo.F_RECYCLING_ORDER_ITEM
							WHERE RECYCLING_ORDER_ID=@RECYCLING_ORDER_ID
							)
					, 0)
	R<PERSON><PERSON>N @PRICE

END
CREATE TABLE [dbo].[D_CONTRACT_ATTRIBUTE_TYPE] (
    [CONTRACT_ATTRIBUTE_TYPE_ID] INT           NOT NULL,
    [ATTRIBUTE_TYPE_ID]          INT           NULL,
    [ATTRIBUTE_TYPE_CD]          VARCHAR (150) NULL,
    [IS_READONLY]                BIT           CONSTRAINT [DF_D_CONTRACT_ATTRIBUTE_TYPE_IS_READONLY] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                VARCHAR (150) CONSTRAINT [DF_D_CONTRACT_ATTRIBUTE_TYPE_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                DATETIME      CONSTRAINT [DF_D_CONTRACT_ATTRIBUTE_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 VARCHAR (150) NULL,
    [UPDATED_DT]                 DATETIME      NULL,
    [IS_TAB_READONLY]            BIT           CONSTRAINT [DF__D_CONTRAC__IS_TA__4C55B112] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_D_CONTRACT_ATTRIBUTE_TYPE] PRIMARY KEY CLUSTERED ([CONTRACT_ATTRIBUTE_TYPE_ID] ASC),
    CONSTRAINT [FK_D_CONTRACT_ATTRIBUTE_TYPE_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID])
);












GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The system page where for attribute type you select system attribute. E.g. what is laptop for current client, what is desktop etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_CONTRACT_ATTRIBUTE_TYPE';


CREATE PROCEDURE [dbo].[sp_SET_SALES_ORDER_INVOICE]
	 @INVOICE_ID			BIGINT
	,@SALES_ORDER_ID		BIGINT
	,@USER_ID				BIGINT
	,@IP					BIGINT
	,@IS_FULLY_PAID			BIT	= 0 -- will create the invoice as fully paid.. hmm the payment will not be created
	,@IS_VOIDED				BIT	= 0
	,@C_IS_SELECT			BIT = 1
AS
BEGIN

	DECLARE
		@UTC_NOW			DATETIME		= GETUTCDATE(),
		@SP_NAME			NVARCHAR(64)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID),
		@ERROR				NVARCHAR(MAX)	= NULL,
		@AMOUNT_DUE			MONEY			= 0,
		@REP_USER_ID		BIGINT,
		@T_INVENTORY_IDS	bigint_ID_ARRAY,
		@TO_Update_items	[bigint_PARE_ARRAY]

	set @REP_USER_ID = (select top(1) [RepUserId]
		from [dbo].[F_SalesOrderRepUser] with (nolock)
		where [SalesOrderId] = @SALES_ORDER_ID
		order by [SalesOrderRepUserId] ASC)

	IF (@IP IS NULL)
	BEGIN
		SET @IP = 1;
	END

	IF (ISNULL(@INVOICE_ID, 0) <= 0)
	BEGIN
		DECLARE @AR_INVOICE_NAME nvarchar(255)= (SELECT TOP(1) AUTO_NAME FROM dbo.vw_AR_INVOICE where ORDER_ID = @SALES_ORDER_ID and IS_VOIDED = 0)
		IF @AR_INVOICE_NAME IS NOT NULL
		BEGIN
			SET @ERROR = N'Cannot create a new invoice. This order has unvoided invoce ' + @AR_INVOICE_NAME
				SELECT 
					@ERROR			AS ERROR
				RETURN;
		END
		SET XACT_ABORT ON;
		BEGIN TRANSACTION;

			-- calculate and save initial amount_due
			SET @AMOUNT_DUE = dbo.fn_money_GET_SALES_ORDER_AMOUNT_DUE(@SALES_ORDER_ID);

			-- increase AccountRecievable
			-- TODO: ACCOUNTS

			INSERT INTO [dbo].[F_INVOICE] (
				[ORDER_ID],
				[DATE_CREATED],
				[AMOUNT_DUE],
				[REP_USER_ID],
				[IS_VOIDED],
				[FINALIZED_DT],
				[PAID_DATE],
				[PAID_AMOUNT],
				[ORIGINAL_AMOUNT],
				[INVOICE_TYPE_ID],
				[INSERTED_BY],
				[INSERTED_DT]
			) VALUES (
				@SALES_ORDER_ID,							-- ORDER_ID
				@UTC_NOW,									-- DATE_CREATED
				IIF(@IS_FULLY_PAID = 1, 0, @AMOUNT_DUE),	-- AMOUNT_DUE
				@REP_USER_ID,								-- REP_USER_ID
				ISNULL(@IS_VOIDED, 0),						-- IS_VOIDED
				IIF(@IS_FULLY_PAID = 1, @UTC_NOW, NULL),	-- FINALIZED_DT
				IIF(@IS_FULLY_PAID = 1, @UTC_NOW, NULL),	-- PAID_DATE
				IIF(@IS_FULLY_PAID = 1, @AMOUNT_DUE, 0),	-- PAID_AMOUNT
				@AMOUNT_DUE,								-- ORIGINAL_AMOUNT
				1,											-- INVOICE_TYPE_ID
				@SP_NAME,									-- INSERTED_BY
				@UTC_NOW									-- INSERTED_DT
			);

			SET @INVOICE_ID = SCOPE_IDENTITY();

			-- SET THE INVENTORY ITEMS "SOLD"

			INSERT INTO @T_INVENTORY_IDS
			SELECT DISTINCT FSOI.ITEM_INVENTORY_ID
			FROM F_SALES_ORDER_ITEM	FSOI	WITH(NOLOCK)
			WHERE FSOI.SALES_ORDER_ID = @SALES_ORDER_ID
			AND FSOI.ITEM_INVENTORY_ID IS NOT NULL
			and not exists(select top(1) 1 from dbo.F_RMA_ITEM RI  
			where RI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID) /*We should not add items witch added to rma*/

			-- We should get PARTS items to update---------------------------
			INSERT INTO @TO_Update_items
			SELECT ID, VALUE FROM dbo.[tvf_GET_ITEM_INVENTORY_ADDED_PARTS](@T_INVENTORY_IDS, 1 ,64)

			--to allocate also inventory kit
			insert into @TO_Update_items
			select
				distinct iikh.ItemInventoryId, 0
			from @T_INVENTORY_IDS i
			inner join [dbo].[F_ItemInventoryKit] iik with (nolock)
				on i.ID = iik.ItemInventoryId
			inner join [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)
				on iik.ItemInventoryKitHeaderId = iikh.Id
			--to allocate also inventory kit

			-- Set IS_DELETE = 1 for empty Pallets
			DECLARE @LOCATION_IDS TABLE(
			   LOCATION_ID BIGINT
			)

			--get locations here, this is sold, location will be reset below
			insert into @LOCATION_IDS
			select distinct
				LII.LOCATION_ID
			from F_ITEM_INVENTORY	LII WITH(NOLOCK)
			inner join @TO_Update_items t
				on LII.ITEM_INVENTORY_ID = t.ID
			inner join F_LOCATION_DETAIL LD WITH(NOLOCK)
				on LD.INVENTORY_CAPABILITY_ID = LII.LOCATION_ID
				--AND LD.LOCATION_TYPE_ID = 3					 -- PALETS ONLY
				AND LD.LOCATION_DETAIL_REMOVE_PALLET_NUMBER = 1  -- PALETS TO REMOVE ONLY

			UPDATE FII SET
				LOCATION_PREV_ID =
					CASE
						WHEN ITEM_STATUS_ID IN (1, 2, 12) THEN FII.LOCATION_ID
						ELSE LOCATION_PREV_ID
					END
				,LOCATION_ID		= NULL
				,ITEM_STATUS_ID		= 3 -- TO "Sold"
				,MODIFIER_USER_ID	= @USER_ID
				,UPDATED_BY_IP		= @IP
				,UPDATED_BY			= @SP_NAME
				,UPDATED_DT			= @UTC_NOW
			FROM @TO_Update_items t
			INNER JOIN dbo.F_ITEM_INVENTORY fii WITH(ROWLOCK) ON t.ID = FII.ITEM_INVENTORY_ID


			UPDATE A SET
				-- save the location
				LocationId		= NULL,
				AssetWorkflowStepId = iif(awparent.IsFinal = 1, awparent.Id, iif(aw.IsFinal = 1, aw.Id, 102)), --resale    		
				UpdatedBy		= @SP_NAME,
				UpdatedDate		= @UTC_NOW,
				UpdatedByUserIp	= @IP,
				UpdatedByUserId	= @USER_ID
			FROM		@TO_Update_items			AS IDS
			INNER JOIN	F_ITEM_INVENTORY			AS I	WITH(NOLOCK)
				ON I.ITEM_INVENTORY_ID = IDS.ID
			INNER JOIN	[recycling].[F_Asset]		AS A	WITH(ROWLOCK)
				ON I.[AssetId] = a.[Id]			
			inner join [recycling].[C_AssetWorkflowStep] aw
				on a.AssetWorkflowStepId = aw.Id
			left join F_ITEM_INVENTORY_ADDED_PART FIIAP with (nolock)
				on 	IDS.ID = FIIAP.PART_ITEM_INVENTORY_ID
			left join dbo.F_ITEM_INVENTORY iParent with (nolock)
				on FIIAP.PARENT_ITEM_INVENTORY_ID = iParent.ITEM_INVENTORY_ID
			left join recycling.F_Asset ap with (nolock)
				on iParent.AssetId = ap.Id
			left join recycling.C_AssetWorkflowStep awparent with (nolock)
				on ap.AssetWorkflowStepId = awparent.Id		
			
			--------------------------- UPDATE ADDED PARTS IS_SOLD --
			
			--Location was reset, check if locations with LOCATION_DETAIL_REMOVE_PALLET_NUMBER is blank		
			IF(exists(select top(1) 1 from @LOCATION_IDS) and not exists(select top(1) 1 from F_ITEM_INVENTORY	II with(nolock)
				where ii.ITEM_STATUS_ID != 8
					  and II.IS_DELETED = 0
					  and II.IS_DROP_SHIP_ITEM = 0
					  and II.IS_VIRTUAL = 0
					  and II.LOCATION_ID IN (select LOCATION_ID from @LOCATION_IDS))
			)
			BEGIN
				UPDATE F_LOCATION WITH(ROWLOCK) SET
					IS_DELETED = 1,
					DELETED_BY = @SP_NAME,
					DELETED_DT = @UTC_NOW
				WHERE LOCATION_ID IN (SELECT LOCATION_ID FROM @LOCATION_IDS)

				UPDATE F_LOCATION_DETAIL WITH(ROWLOCK) SET
					IS_DELETED = 1,
					DELETED_BY = @SP_NAME,
					DELETED_DT = @UTC_NOW
				WHERE INVENTORY_CAPABILITY_ID IN (SELECT LOCATION_ID FROM @LOCATION_IDS)
			END

			-- Process Sale transaction
			EXEC /*@ERROR =*/ dbo.sp_LOG_ACCOUNT_SALE @SALES_ORDER_ID
		COMMIT TRANSACTION
	END
	ELSE BEGIN
	/*	IF (@IS_VOIDED = 1 AND EXISTS (SELECT TOP(1) 1 FROM F_INVOICE_PAYMENT WITH (NOLOCK) WHERE INVOICE_ID = @INVOICE_ID AND IS_DELETED = 0))
		BEGIN			
			SET @ERROR = N'Cannot void the Invoice which has actual payments.'
			SELECT 
				@ERROR		AS ERROR,
				@INVOICE_ID AS ID
			RETURN;
		END*/

		IF (@IS_VOIDED = 1)
		BEGIN

			DECLARE @CAN_VOID_STATE TINYINT = [dbo].[fn_byte_IS_ABLE_TO_VOID_INVOICE](@INVOICE_ID)

			IF @CAN_VOID_STATE != 0
			BEGIN
				SET @ERROR = N'The invoice cannot be voided'
				SELECT 
					@ERROR			AS ERROR,
					@CAN_VOID_STATE	AS [STATE],
					@INVOICE_ID		AS ID
				RETURN;
			END

			select top(1) @SALES_ORDER_ID = ORDER_ID from F_INVOICE with(nolock) where [INVOICE_ID] = @INVOICE_ID;

			if	(@SALES_ORDER_ID is not null)
			begin

				INSERT INTO @T_INVENTORY_IDS
				SELECT DISTINCT FSOI.ITEM_INVENTORY_ID
				FROM F_SALES_ORDER_ITEM	FSOI with(nolock)
				WHERE FSOI.SALES_ORDER_ID = @SALES_ORDER_ID
					AND ITEM_INVENTORY_ID IS NOT NULL
					AND not exists(
						select top(1) 1
						from dbo.F_RMA_ITEM RI with(nolock)
						where RI.SALES_ORDER_ITEM_ID = FSOI.SALES_ORDER_ITEM_ID /*We should not add items witch added to rma*/
					)

				-- We should get PARTS items to update---------------------------
				INSERT INTO @TO_Update_items
				SELECT ID, VALUE FROM dbo.[tvf_GET_ITEM_INVENTORY_ADDED_PARTS](@T_INVENTORY_IDS,1 ,64)

				--to allocate also inventory kit
				insert into @TO_Update_items
				select
					distinct iikh.ItemInventoryId, 0
				from @T_INVENTORY_IDS i
				inner join [dbo].[F_ItemInventoryKit] iik with (nolock)
					on i.ID = iik.ItemInventoryId
				inner join [dbo].[F_ItemInventoryKitHeader] iikh with (nolock)
					on iik.ItemInventoryKitHeaderId = iikh.Id
				--to allocate also inventory kit

				UPDATE FII SET
					-- RESTORE THE LOCATION_ID
					LOCATION_ID = (
						CASE
							WHEN ITEM_STATUS_ID NOT IN (1,2) THEN LOCATION_PREV_ID
							ELSE FII.LOCATION_ID 
						END)
					-- RESET THE LOCATION_PREV_ID
					,LOCATION_PREV_ID = (
						CASE
							WHEN ITEM_STATUS_ID NOT IN (1,2) THEN NULL
							ELSE LOCATION_PREV_ID 
						END)
					,ITEM_STATUS_ID		= IIF(t.Value = 0, 2, 12) -- TO "ALLOCATED" /*12 for part added item*/
					,MODIFIER_USER_ID	= @USER_ID
					,UPDATED_BY_IP		= @IP
					,UPDATED_BY			= @SP_NAME
					,UPDATED_DT			= @UTC_NOW
				FROM @TO_Update_items t
				INNER JOIN dbo.F_ITEM_INVENTORY fii WITH(ROWLOCK) ON t.ID = FII.ITEM_INVENTORY_ID


				UPDATE A SET
					-- save the location
					LocationId		= I.[LOCATION_ID],
					UpdatedBy		= @SP_NAME,
					UpdatedDate		= @UTC_NOW,
					UpdatedByUserIp	= @IP,
					UpdatedByUserId	= @USER_ID
				FROM		@TO_Update_items			AS IDS
				INNER JOIN	F_ITEM_INVENTORY			AS I	WITH(NOLOCK)
					ON I.ITEM_INVENTORY_ID = IDS.ID
				INNER JOIN	[recycling].[F_Asset]		AS A	WITH(ROWLOCK)
					ON I.[AssetId] = a.[Id]			


				DECLARE
					@CreditMemoId BIGINT,
					@cursorBreak bit = 0
				DECLARE @CURSOR CURSOR
				SET @CURSOR  = CURSOR FORWARD_ONLY
				FOR
					select  [Id] 
					from  [dbo].[F_CreditMemo] with (nolock)
					where [InvoiceId] = @INVOICE_ID
				OPEN @CURSOR
					FETCH NEXT FROM @CURSOR INTO @CreditMemoId
					WHILE @@FETCH_STATUS = 0 and @cursorBreak = 0
					BEGIN
						if ([dbo].[fn_bit_IsCreditMemoCanBeChanged](@CreditMemoId) = 0)
						begin
							SET @ERROR = 'The Credit memo can''t be changed or deleted. Its credit is used or the AP invoice is paid.';
							select @cursorBreak = 1;

							SELECT
								@ERROR			AS ERROR,
								0				AS [STATE],
								@INVOICE_ID		AS ID
							RETURN;
						end

						EXEC [dbo].[sp_DeleteCreditMemo]
							@Id			= @CreditMemoId
							,@UserId	= @USER_ID
							,@UserIp	= @IP

						FETCH NEXT FROM @CURSOR INTO @CreditMemoId
					END
				CLOSE @CURSOR;
				DEALLOCATE @CURSOR;

			end
			else
			begin

				update [dbo].[F_BoxProgramTransactionInvoice] with(rowlock)
				set [IsInactive] =1
				where [InvoiceId] = @INVOICE_ID
					and [IsInactive] = 0;

			end

		END

		UPDATE IV SET 
			IV.REP_USER_ID			= @REP_USER_ID
			,AMOUNT_DUE			=
				CASE
					WHEN @IS_FULLY_PAID = 1 THEN 0
					ELSE IV.AMOUNT_DUE
				END
			,PAID_DATE			=
				CASE
					WHEN @IS_FULLY_PAID = 1 AND iv.STATUS_ID != 5 THEN GETUTCDATE()
					ELSE IV.PAID_DATE
				END
			,PAID_AMOUNT			=
				CASE
					WHEN @IS_FULLY_PAID = 1 AND iv.STATUS_ID != 5  THEN ISNULL(ORIGINAL_AMOUNT, AMOUNT_DUE)
					ELSE IV.PAID_AMOUNT
				END		
			,IV.IS_VOIDED			= ISNULL(@IS_VOIDED, ISNULL(IV.IS_VOIDED, 0))
			,IV.UPDATED_BY			= @SP_NAME
			,IV.UPDATED_DT			= @UTC_NOW
		FROM F_INVOICE	IV	WITH(ROWLOCK)
		WHERE INVOICE_ID = @INVOICE_ID

	END

	-- UPDATE THE STATUS OF INVOICE
	IF (@IS_FULLY_PAID = 1)
	BEGIN
		UPDATE F_INVOICE WITH(ROWLOCK) SET
			STATUS_ID	= 5, -- "Paid in Full"
			IS_PAID		= 1,
			UPDATED_BY	= @SP_NAME,
			UPDATED_DT	= @UTC_NOW
		WHERE INVOICE_ID = @INVOICE_ID
	END
	ELSE BEGIN
		EXEC dbo.sp_UPD_SALES_ORDER_INVOICE_STATUS
			@SALES_ORDER_ID = @SALES_ORDER_ID,
			@INVOICE_ID		= @INVOICE_ID,
			@NO_SELECT		= 1
	END
		
	-- CAN UPDATE SALES ORDER HERE
	DECLARE @SHIPPING_STATUS_ID INT = (
		SELECT TOP(1)
			STATUS_ID
		FROM [F_SHIPPING] WITH (NOLOCK)
		WHERE SALES_ORDER_ID = @SALES_ORDER_ID
	)
	
	-- order is completely shipped and the Invoice exists - set order FULFILLED
	IF EXISTS(
		SELECT TOP(1) 1
		FROM F_SHIPPING	WITH(NOLOCK)
		WHERE SALES_ORDER_ID = @SALES_ORDER_ID)
	AND NOT EXISTS(
		SELECT TOP(1) 1 
		FROM F_SHIPPING	WITH(NOLOCK)
		WHERE SALES_ORDER_ID = @SALES_ORDER_ID
		  AND STATUS_ID != 3 --Shipped --SELECT * FROM D_SHIPPING_STATUS
	)
	BEGIN
		UPDATE [dbo].[F_SALES_ORDER] WITH(ROWLOCK) SET
			[STATUS_ID] = 4, -- Fulfilled
			[FulfilledDt] = @UTC_NOW,
			[UPDATED_BY] = @SP_NAME,
			[UPDATED_DT] = @UTC_NOW
		WHERE [SALES_ORDER_ID] = @SALES_ORDER_ID
			AND [STATUS_ID] != 4 -- Fulfilled
	END

	EXEC dbo.SP_SET_LOG_AFTER_INSERT_INVOICE @INVOICE_ID = @INVOICE_ID

	EXEC dbo.sp_UPD_SALES_TAX_INVOICE
		 @SALES_ORDER_ID	= @SALES_ORDER_ID
		,@INVOICE_ID		= @INVOICE_ID

	IF (@C_IS_SELECT = 1)
		SELECT 
			@INVOICE_ID AS ID,
			NULL		AS [STATE],
			NULL		AS ERROR,
			@SALES_ORDER_ID as [SalesOrderId]
END
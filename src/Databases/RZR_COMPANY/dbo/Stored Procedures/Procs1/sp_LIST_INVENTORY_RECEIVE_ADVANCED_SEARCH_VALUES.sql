CREATE PROCEDURE [dbo].[sp_LIST_INVENTORY_RECEIVE_ADVANCED_SEARCH_VALUES] 
	 @SEARCH_VALUE NVARCHAR(max)
	,@COLUMN_NAME  NVARCHAR(250)
	,@ITEM_COUNT   INT			= 20
AS
BEGIN
	
	SET @SEARCH_VALUE = '%' +ISNULL(LTRIM(RTRIM(REPLACE(@SEARCH_VALUE, '%', ''))), '') + '%'

	--IF (@COLUMN_NAME = N'UniqueId')
	--BEGIN	
	--	SELECT DISTINCT TOP(@ITEM_COUNT)
	--		--ITEM_INVENTORY_ID			ID,
	--		ITEM_INVENTORY_UNIQUE_ID	value,
	--		-1						AS	[TYPE]
	--	FROM dbo.F_ITEM_INVENTORY
	--	WHERE ITEM_INVENTORY_UNIQUE_ID LIKE @SEARCH_VALUE
	--	  AND IS_DELETED = 0
	--	ORDER BY 1
	--END	
	--E<PERSON>E IF (@COLUMN_NAME = N'Serial')
	--BEGIN	
	--	SELECT DISTINCT TOP(@ITEM_COUNT)
	--		--ITEM_INVENTORY_ID		ID,
	--		ITEM_INVENTORY_SERIAL	value,
	--		-2					AS	[TYPE]
	--	FROM dbo.F_ITEM_INVENTORY
	--	WHERE ITEM_INVENTORY_SERIAL LIKE @SEARCH_VALUE
	--	  AND IS_DELETED = 0
	--	ORDER BY 1
	--END	
	--ELSE 
	IF (@COLUMN_NAME = N'MasterItemNumber')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			fim.ITEM_NUMBER		value,
			fim.ITEM_NUMBER		label
		FROM dbo.F_ITEM_INVENTORY		fi WITH(NOLOCK)
		INNER JOIN dbo.F_ITEM_MASTER	fim WITH (NOLOCK)
			ON fi.ITEM_MASTER_ID = fim.ITEM_MASTER_ID
		WHERE fim.ITEM_NUMBER LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'SkuId')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			fi.ITEM_ID,
			CAST(fi.ITEM_ID AS NVARCHAR(21)) AS value,
			CAST(fi.ITEM_ID AS NVARCHAR(21)) AS label
		FROM dbo.F_ITEM_INVENTORY		fi WITH (NOLOCK)
		WHERE CAST(fi.ITEM_ID AS NVARCHAR(21)) LIKE @SEARCH_VALUE 
		ORDER BY fi.ITEM_ID
	END
	ELSE IF (@COLUMN_NAME = N'CodeValue')
	BEGIN
		SELECT DISTINCT TOP(@ITEM_COUNT)
			pc.PRODUCT_CODE_VALUE	value,
			pc.PRODUCT_CODE_VALUE	label
		FROM dbo.F_ITEM_INVENTORY fiid WITH (NOLOCK)
		inner join [dbo].[F_PRODUCT_CODE] pc with (nolock)
			on fiid.ProductCodeIdHeci = pc.PRODUCT_CODE_ID
		WHERE pc.PRODUCT_CODE_VALUE LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'CVCode')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			fc.CUSTOMER_NAME	value,
			fc.CUSTOMER_NAME	label
		FROM dbo.F_CUSTOMER				fc WITH (NOLOCK)
		WHERE fc.CUSTOMER_NAME LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'MFGCd')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			dm.MANUFACTURER_CD	value,
			dm.MANUFACTURER_CD	label
		FROM dbo.D_MANUFACTURER			dm WITH (NOLOCK)
		WHERE dm.MANUFACTURER_CD LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'RealCondition')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			di.ITEM_CONDITION_DESC	value,
			di.ITEM_CONDITION_DESC	label
		FROM dbo.D_ITEM_CONDITION		di WITH (NOLOCK)
		WHERE di.ITEM_CONDITION_DESC LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'Conditions')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			di.ITEM_CONDITION_CD	value,
			di.ITEM_CONDITION_CD	label
		FROM dbo.D_ITEM_CONDITION		di WITH (NOLOCK)
		WHERE di.ITEM_CONDITION_DESC LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'Location')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			FL.LOCATION_NAME	value,
			FL.LOCATION_NAME	label
		FROM dbo.f_location				FL WITH (NOLOCK)
		WHERE FL.LOCATION_NAME LIKE @SEARCH_VALUE 
		ORDER BY 1
	END IF (@COLUMN_NAME = N'Status')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			iis.[STATUS_CD]	[value],
			iis.[STATUS_CD]	label
		FROM dbo.F_ITEM_INVENTORY		FIID WITH (NOLOCK)
		inner join [dbo].[D_ITEM_INVENTORY_STATUS] iis with (nolock)
			on fiid.ITEM_STATUS_ID = iis.ITEM_INVENTORY_STATUS_ID
		WHERE FIID.ITEM_INVENTORY_STATUS_CD LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'PurchaseOrderName')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			FPO.AUTO_NAME				value,
			FPO.AUTO_NAME				label
		FROM dbo.F_PURCHASE_ORDER		FPO WITH (NOLOCK)
		WHERE FPO.AUTO_NAME LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'AuthorName')
	BEGIN	
		SELECT DISTINCT TOP(@ITEM_COUNT)
			TU.UserName				value,
			TU.UserName				label
		FROM dbo.tb_User		TU WITH (NOLOCK)
		WHERE TU.UserName LIKE @SEARCH_VALUE 
		ORDER BY 1
	END
	ELSE IF (@COLUMN_NAME = N'CreatedDate')
	BEGIN	
		SELECT
			value,
			value as label
		FROM (
			-- CONVERT TO DATE TIME
			SELECT DISTINCT TOP(@ITEM_COUNT)
				CONVERT(VARCHAR(25), INSERTED_DT, 101)		AS	[value]
			FROM dbo.F_ITEM_INVENTORY		 WITH(NOLOCK)
			WHERE (SELECT CONVERT(VARCHAR(25), INSERTED_DT, 101) AS [MM/DD/YYYY]) LIKE @SEARCH_VALUE			
			ORDER BY 1
		) T
	END
	ELSE IF (@COLUMN_NAME = N'ActivityDate')
	BEGIN
		SELECT
			value,
			value as label
		FROM (
			-- CONVERT TO DATE TIME
			SELECT DISTINCT TOP(@ITEM_COUNT)
				CASE
					WHEN UPDATED_DT IS NULL 
						THEN CONVERT(VARCHAR(25), INSERTED_DT, 101)
					WHEN UPDATED_DT > INSERTED_DT 
						THEN CONVERT(VARCHAR(25), UPDATED_DT, 101)
					ELSE CONVERT(VARCHAR(25), INSERTED_DT, 101)
				END						AS	[value]
			FROM dbo.F_ITEM_INVENTORY	 WITH(NOLOCK)
			WHERE (UPDATED_DT IS NULL
			  AND (SELECT CONVERT(VARCHAR(25), INSERTED_DT, 101) AS [MM/DD/YYYY]) LIKE @SEARCH_VALUE )
			   OR (UPDATED_DT > INSERTED_DT
			  AND ( SELECT CONVERT(VARCHAR(25), UPDATED_DT,  101) AS [MM/DD/YYYY]) LIKE @SEARCH_VALUE )
			   OR ((SELECT CONVERT(VARCHAR(25), INSERTED_DT, 101) AS [MM/DD/YYYY]) LIKE @SEARCH_VALUE )
			ORDER BY 1
		) T
	END

END
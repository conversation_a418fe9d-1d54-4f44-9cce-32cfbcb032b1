CREATE TABLE [dbo].[F_CustomerIpWhitelist]
(
	[CustomerIpWhitelistId]             BIGINT         IDENTITY (1, 1) NOT NULL,
    [CustomerId]                        BIGINT                         NOT NULL,
    [Ip<PERSON>rom]                            NVARCHAR(15)                   NOT NULL,
    [IpTo]                              NVARCHAR(15)                   NOT NULL,
    [InsertedBy]                        NVARCHAR (150)                 CONSTRAINT [DF_F_CustomerIpWhitelist_InsertedBy] DEFAULT (isnull(isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid),'Admin')) NOT NULL,
    [InsertedDt]                        DATETIME                       CONSTRAINT [DF_F_CustomerIpWhitelist_InsertedDt] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_F_CustomerIpWhitelist] PRIMARY KEY CLUSTERED ([CustomerIpWhitelistId] ASC),
    CONSTRAINT [FK_F_CustomerIpWhitelist_F_Customer] FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[F_CUSTOMER] ([CUSTOMER_ID])
)

GO
CREATE NONCLUSTERED INDEX [IX_F_CustomerIpWhitelist]
    ON [dbo].[F_CustomerIpWhitelist]([CustomerId] ASC);
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CATEGORY_TO_EBAY_CATEGORY]
	@EBAY_CATEGORY_IDS XML,
	@CATEGORY_ID BIGINT,
	@IS_MAP_OPERATION BIT
AS
BEGIN
	DECLARE @idoc INT

	-- read xml list into "@idoc"
    EXEC sp_xml_preparedocument @idoc OUTPUT, @EBAY_CATEGORY_IDS
    
	DECLARE	@ids TABLE (CATEGORY_ID BIGINT)

    INSERT INTO @ids (CATEGORY_ID)
	SELECT -- select from xml doc as table
		x.ID
	FROM OPENXML (@idoc, '/Root/Items', 1)
	WITH (ID BIGINT) AS x

    -- delete xml doc
	EXEC sp_xml_removedocument @idoc	
		
	IF (@IS_MAP_OPERATION = 1) BEGIN					
		
		UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
			MAPPED_CATEGORY_ID = @CATEGORY_ID,
			UPDATED_BY = 'sp_SET_CATEGORY_TO_EBAY_CATEGORY',
			UPDATED_DT = GETUTCDATE()
		WHERE [CATEGORY_ID] IN (SELECT CATEGORY_ID FROM @ids)					
		
	END	
	ELSE BEGIN
	
		UPDATE dbo.D_EBAY_CATEGORY_HIERARCHY SET
			MAPPED_CATEGORY_ID = NULL,
			UPDATED_BY = 'sp_SET_CATEGORY_TO_EBAY_CATEGORY',
			UPDATED_DT = GETUTCDATE()
		WHERE [CATEGORY_ID] IN (SELECT CATEGORY_ID FROM @ids)
	
	END			
	
END
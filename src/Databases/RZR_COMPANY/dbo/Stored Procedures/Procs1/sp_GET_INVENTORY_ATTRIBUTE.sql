CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_ATTRIBUTE]
	@ITEM_MASTER_ID			BIGINT  = NULL,
	@ATTRIBUTE_TYPE_ID		INT		= NULL,
	@ITEM_INVENTORY_ID		BIGINT	= NULL,
	@IS_ONLY_SKU_EFFECTING	BIT		= NULL,
	@IS_OMIT_HIDDEN			BIT		= 0
AS
BEGIN

	SET @ATTRIBUTE_TYPE_ID	= ISNULL(@ATTRIBUTE_TYPE_ID, -1)
	SET @ITEM_MASTER_ID		= ISNULL(@ITEM_MASTER_ID, -1)
	SET @ITEM_INVENTORY_ID	= ISNULL(@ITEM_INVENTORY_ID, -1)

	-- getting default attr type if it is not passed
	IF (@ATTRIBUTE_TYPE_ID <= 0)
	BEGIN
		-- take master item attribute type id
		IF (@ITEM_MASTER_ID > 0)
			SET @ATTRIBUTE_TYPE_ID = dbo.fn_bigint_GET_ITEM_MASTER_ATTRIBUTE_SET_ID(@ITEM_MASTER_ID)

		IF (ISNULL(@ATTRIBUTE_TYPE_ID, -1) <= 0 AND @ITEM_INVENTORY_ID > 0)
		BEGIN
			SELECT
				@ATTRIBUTE_TYPE_ID = roi.[InventoryAttributeTypeId]
			FROM F_ITEM_INVENTORY					ii  WITH (NOLOCK)
			INNER JOIN [recycling].F_Asset roi WITH (NOLOCK)
				ON roi.Id = ii.AssetId
			WHERE ii.ITEM_INVENTORY_ID = @ITEM_INVENTORY_ID
		END
	END

	-- take the default one
	IF (@ATTRIBUTE_TYPE_ID <= 0) BEGIN
		SELECT @ATTRIBUTE_TYPE_ID = 1 -- DEFAULT
	END

	SELECT
		atr.[ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE_ID]											AS AttributeId
		,atr.INVENTORY_ATTRIBUTE_TYPE_ID															AS TypeId
		,atr.INVENTORY_CAPABILITY_TYPE_ID															AS CapabilityTypeId
		,ic.INVENTORY_CAPABILITY_VALUE																AS ValueId
		,ic.INVENTORY_CAPABILITY_LABEL																AS Label
		,iif(atr.IS_EDITABLE = 1 or ltrim(rtrim(ic.INVENTORY_CAPABILITY_VALUE)) = 'PACKAGING', 1, 0)AS IsEditable -- packaging always editable
		,atr.IS_REQUIRED																			AS IsRequired
		,ic.ATTRIBUTE_DATA_TYPE_ID																	AS AttributeDataTypeId
		,atr.ITEM_INVENTORY_ORDINATION																AS Ordination
		,dic.INVENTORY_CAPABILITY_ID																AS DefaultValueId
		,dic.INVENTORY_CAPABILITY_VALUE																AS DefaultValueLabel
		,atr.IS_INACTIVE
		,atr.IS_DELETED
		,atr.INSERTED_BY
		,atr.INSERTED_DT
		,atr.IS_SKU_EFFECTING																		AS IsSkuAffeting
	FROM dbo.F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE			atr		WITH (NOLOCK)
	INNER JOIN dbo.C_INVENTORY_CAPABILITY_TYPE					ic		WITH (NOLOCK)
		ON atr.INVENTORY_ATTRIBUTE_TYPE_ID = @ATTRIBUTE_TYPE_ID
		AND atr.IS_INACTIVE = 0
		AND atr.IS_DELETED = 0
		AND atr.INVENTORY_CAPABILITY_TYPE_ID = ic.INVENTORY_CAPABILITY_TYPE_ID
		AND ic.IS_INACTIVE = 0
		AND ic.IS_DELETED = 0
		AND (@IS_OMIT_HIDDEN = 0 or ic.INVENTORY_CAPABILITY_IS_HIDDEN = 0)
		AND (@IS_ONLY_SKU_EFFECTING IS NULL
		  OR atr.IS_SKU_EFFECTING = @IS_ONLY_SKU_EFFECTING)
	LEFT JOIN [dbo].[F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY]	fimatc	WITH (NOLOCK)
		ON  fimatc.ITEM_MASTER_ID = @ITEM_MASTER_ID
		AND fimatc.INVENTORY_ATTRIBUTE_TYPE_ID = atr.INVENTORY_ATTRIBUTE_TYPE_ID
		AND fimatc.INVENTORY_CAPABLITY_TYPE_ID = atr.INVENTORY_CAPABILITY_TYPE_ID
	LEFT JOIN D_INVENTORY_CAPABILITY							dic		WITH (NOLOCK)
		ON  dic.IS_DELETED = 0
		AND dic.IS_INACTIVE = 0
		AND dic.INVENTORY_CAPABILITY_ID = fimatc.INVENTORY_CAPABILITY_ID
	where ic.IsSystem = 0
	ORDER BY Ordination

END
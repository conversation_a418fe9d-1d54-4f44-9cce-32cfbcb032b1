-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <09/27/2013>
-- Description:	<Returns the Amount Due of the sales order>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_SALES_ORDER_AMOUNT_DUE]
(@SALES_ORDER_ID BIGINT)
RETURNS FLOAT
BEGIN
	DECLARE 
		@COST				FLOAT = ISNULL(dbo.sp_CALCULATE_SALES_ORDER_COST(@SALES_ORDER_ID),     0.00),
		@SUBTOTAL			FLOAT = ISNULL(dbo.[fn_float_CALCULATE_SALES_ORDER_SUBTOTAL](@SALES_ORDER_ID), 0.00),
		@TAX				FLOAT = ISNULL(dbo.sp_CALCULATE_SALES_ORDER_TAX(@SALES_ORDER_ID),      0.00),				
		@SHIPPING_COST		FLOAT = 0.00,
		@HANDLING_FEE		FLOAT = 0.00,
		@AMOUNT_DUE			FLOAT = 0.00

	DECLARE 
		@GROSS_PROFIT		FLOAT = @SUBTOTAL - @COST --ISNULL(dbo.sp_CALCULATE_SALES_ORDER_GROSS_PROFIT(@SALES_ORDER_ID), 0.00)

	-- SELECT ITEM PARAMETERS
	SELECT		
		@SHIPPING_COST = ISNULL(O.SHIPPING_COST, 0.00),
		@HANDLING_FEE  = ISNULL(O.HANDLING_FEE, 0.00)
	FROM F_SALES_ORDER O WITH (NOLOCK)
	WHERE O.SALES_ORDER_ID = @SALES_ORDER_ID
	GROUP BY O.SHIPPING_COST, O.HANDLING_FEE

	DECLARE @PAID_AMOUNT FLOAT = ISNULL(
		(SELECT
			SUM(ISNULL(PAID_AMOUNT, 0))
		FROM dbo.F_INVOICE WITH (NOLOCK)
		WHERE INVOICE_TYPE_ID = 1
		  AND ORDER_ID = @SALES_ORDER_ID
		  AND IS_VOIDED = 0)
		, 0.00)

	SET @AMOUNT_DUE = ROUND(@SUBTOTAL + @TAX + @SHIPPING_COST + @HANDLING_FEE - @PAID_AMOUNT, 2)
	
	RETURN ISNULL(@AMOUNT_DUE, 0)
END
 
CREATE PROCEDURE [dbo].[sp_DELETE_CUSTOMER_ADDRESS] 
	@ADDRESS_ID BIGINT	
AS
BEGIN

	IF (EXISTS(
			SELECT TOP(1) 1 
			FROM dbo.F_CUSTOMER_ADDRESS	CA WITH (NOLOCK) 
			WHERE CA.CUSTOMER_ADDRESS_ID = @ADDRESS_ID 
			  AND IS_MAIN = 1 
			  AND CA.CUSTOMER_ADDRESS_TYPE_ID != 1   -- Primary
			  AND CA.CUSTOMER_ADDRESS_TYPE_ID != 4)) -- Other
	BEGIN
		SELECT 'Main Billing and Shipping addresses can''t be deleted.' AS [MESSAGE]
		RETURN;
	END	
	

	BEGIN TRANSACTION
		DELETE D 
		FROM F_RECYCLING_LOCATION_DETAILS	D
		INNER JOIN F_CUSTOMER_ADDRESS		A
		  ON A.LOCATION_DETAILS_ID = D.RECYCLING_LOCATION_DETAILS_ID
		WHERE A.CUSTOMER_ADDRESS_ID = @ADDRESS_ID		

		DELETE FROM dbo.F_CUSTOMER_CONTACT_ADDRESS
		WHERE CUSTOMER_ADDRESS_ID = @ADDRESS_ID

		DELETE FROM dbo.F_CUSTOMER_ADDRESS
		WHERE CUSTOMER_ADDRESS_ID = @ADDRESS_ID	

	COMMIT TRANSACTION

	SELECT NULL AS [MESSAGE]			
						
END
CREATE TABLE [dbo].[D_RecyclingServiceTypeLabel]
(
    [Id]				                INT			    IDENTITY (1, 1) NOT NULL,
    [ServiceTypeId]                     INT             NOT NULL,
    [LabelId]                           INT             NOT NULL,
    CONSTRAINT [PK_D_RecyclingServiceTypeLabel] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [AK_D_RecyclingServiceTypeLabel] UNIQUE ([ServiceTypeId], [LabelId]),
    CONSTRAINT [FK_D_RecyclingServiceTypeLabel_C_RECYCLING_ITEM_SERVICE_TYPE] FOREIGN KEY ([ServiceTypeId]) REFERENCES [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] ([SERVICE_TYPE_ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_D_RecyclingServiceTypeLabel_D_RecyclingServiceLabel] FOREIGN KEY ([LabelId]) REFERENCES [dbo].[D_RecyclingServiceLabel] ([Id]) ON DELETE CASCADE
);
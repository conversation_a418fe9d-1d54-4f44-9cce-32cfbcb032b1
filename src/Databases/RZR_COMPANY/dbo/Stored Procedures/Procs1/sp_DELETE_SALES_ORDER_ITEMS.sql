CREATE PROCEDURE [dbo].[sp_DELETE_SALES_ORDER_ITEMS]
(
	@ORDER_ITEM_IDS XML,
	@SALES_ORDER_ID	BIGINT,
	@USER_ID BIGINT,
	@USER_IP BIGINT
)
AS
BEGIN

	DECLARE @idoc INT;
	DECLARE @spName NVARCHAR(50) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + N'.', N'') + OBJECT_NAME(@@PROCID);
	DECLARE @utcNow	DATETIME = GETUTCDATE();
	DECLARE @SRC_SONO NVARCHAR(256) = (
		SELECT TOP(1) [SALES_ORDER_NUMBER]
		FROM [dbo].[F_SALES_ORDER] AS SO WITH(NOLOCK)
		WHERE SO.SALES_ORDER_ID = @SALES_ORDER_ID
	);
	DECLARE @T_INVENTORY_IDS [dbo].[bigint_ID_ARRAY];
	DECLARE @TO_Update_items [dbo].[bigint_PARE_ARRAY];

	-- read xml list into "@idoc"
	EXEC sp_xml_preparedocument @idoc OUTPUT, @ORDER_ITEM_IDS;

	-- move it values into the "@caps" table variable
	DECLARE @ids TABLE (SALES_ORDER_ITEM_ID BIGINT);

	INSERT INTO @ids (SALES_ORDER_ITEM_ID)
	SELECT x.[ID] -- select from xml doc as table
	FROM OPENXML (@idoc, '/Root/Items', 1)
	WITH (ID BIGINT) AS x;

	-- delete xml doc
	EXEC sp_xml_removedocument @idoc;
	IF (NOT EXISTS(SELECT TOP(1) 1 FROM @ids))
	BEGIN
		INSERT INTO @ids
		SELECT [SALES_ORDER_ITEM_ID]
		FROM [dbo].[F_SALES_ORDER_ITEM] AS I WITH (NOLOCK)
		WHERE I.[SALES_ORDER_ID] = @SALES_ORDER_ID
	END

	INSERT INTO @T_INVENTORY_IDS
	SELECT DISTINCT FSOI.[ITEM_INVENTORY_ID]
	FROM [dbo].[F_SALES_ORDER_ITEM] AS FSOI WITH (NOLOCK)
	INNER JOIN @ids AS X
		ON FSOI.[SALES_ORDER_ITEM_ID] = X.[SALES_ORDER_ITEM_ID]
		-- We can't change status for items that were allocated into other SO
		AND NOT EXISTS(
			SELECT TOP(1) 1
			FROM [dbo].[F_SALES_ORDER_ITEM] AS t WITH (NOLOCK)
			WHERE t.[SALES_ORDER_ID] != @SALES_ORDER_ID
				AND t.[ITEM_INVENTORY_ID] = FSOI.[ITEM_INVENTORY_ID]
		)
	WHERE FSOI.[ITEM_INVENTORY_ID] IS NOT NULL
	UNION
	SELECT FII.[ITEM_INVENTORY_ID]
	FROM [dbo].[F_ITEM_INVENTORY] AS FII WITH (NOLOCK)
	INNER JOIN @ids AS X2
		ON FII.[SalesOrderItemId] = X2.[SALES_ORDER_ITEM_ID];
	
	-- We should get PARTS items to update---------------------------
	INSERT INTO @TO_Update_items
	SELECT [ID], [VALUE]
	FROM [dbo].[tvf_GET_ITEM_INVENTORY_ADDED_PARTS](@T_INVENTORY_IDS, 1, 64);

	DECLARE @isRedeploymentSalesOrder BIT = IIF(@SALES_ORDER_ID IS NULL, 0, [dbo].[fn_bit_IsRedeploymentSalesOrder](@SALES_ORDER_ID));

	UPDATE FII SET
		-- RESTORE THE LOCATION_ID
		[LOCATION_ID] = IIF([ITEM_STATUS_ID] NOT IN (1,2,8,9,10,11,12), [LOCATION_PREV_ID], [LOCATION_ID]),
		-- RESET THE LOCATION_PREV_ID
		[LOCATION_PREV_ID] = IIF([ITEM_STATUS_ID] NOT IN (1,2,8,9,10,11,12), NULL, [LOCATION_PREV_ID]),
		[ITEM_STATUS_ID]	= IIF(T.[VALUE] = 0, IIF(iik.[ItemInventoryId] IS NOT NULL, 13, IIF(RI.InventoryId IS NOT NULL, 8, 1)), 12), -- 12 for part add items, 13 - for Inventory Kit items
		[SalesOrderItemId]	= NULL,
		[UPDATED_BY]		= @spName,
		[UPDATED_DT]		= @utcNow,
		[UPDATED_BY_IP]		= @USER_IP,
		[MODIFIER_USER_ID]	= @USER_ID
	FROM @TO_Update_items AS T
	INNER JOIN [dbo].[F_ITEM_INVENTORY] AS FII WITH(ROWLOCK)
		ON T.[ID] = FII.[ITEM_INVENTORY_ID]
	LEFT JOIN [dbo].[F_ItemInventoryKit] AS IIK WITH (NOLOCK)
		ON FII.[ITEM_INVENTORY_ID] = IIK.[ItemInventoryId]
	LEFT JOIN [dbo].[vw_RedeploymentInventory]	RI		WITH (NOLOCK)
		ON @isRedeploymentSalesOrder = 1
			AND RI.InventoryId = FII.ITEM_INVENTORY_ID
;

	UPDATE A SET
		-- save the location
		[LocationId]		= I.[LOCATION_ID],
		[UpdatedBy]			= @spName,
		[UpdatedDate]		= @utcNow,
		[UpdatedByUserIp]	= @USER_IP,
		[UpdatedByUserId]	= @USER_ID
	FROM		@TO_Update_items			AS IDS
	INNER JOIN	[dbo].[F_ITEM_INVENTORY]	AS I	WITH(NOLOCK)
		ON I.[ITEM_INVENTORY_ID] = IDS.[ID]
	INNER JOIN	[recycling].[F_Asset]		AS A	WITH(ROWLOCK)
		ON I.[AssetId] = A.[Id];


	DECLARE @T_INVENTORY_ITEMS TABLE(SALES_ORDER_ID BIGINT, ITEM_INVENTORY_ID BIGINT);
	
	-- Get items to add log
	DELETE T
	OUTPUT DELETED.[SALES_ORDER_ID], DELETED.[ITEM_INVENTORY_ID] INTO @T_INVENTORY_ITEMS
	FROM [dbo].[F_SALES_ORDER_ITEM] AS T WITH(ROWLOCK)
	INNER JOIN @ids AS IDS
		ON IDS.[SALES_ORDER_ITEM_ID] = T.[SALES_ORDER_ITEM_ID];

	-- Add log
	INSERT INTO F_LOG_DATA(	
		[SOURCE],   
		[USER_ID],
		USER_IP,
		OPERATION_NAME,
		ENTITY_TYPE_ID,
		ENTITY_KEY_VALUE,
		ENTITY_AUTO_NAME,
		[CHANGES]
	)
	SELECT 
		@spName,
		@USER_ID,
		@USER_IP,
		'Inventory item deleted from SO',
		9 /*Receive Inventory*/,
		NEW.ENTITY_KEY_VALUE,
		NEW.ENTITY_AUTO_NAME,
			[dbo].[fn_str_STRIP_XML_TAGS]((SELECT
			NEW.[Changes]
		FOR XML
		PATH('ROOT'),TYPE,ELEMENTS ABSENT))  AS [CHANGES]
	FROM 
	(
		SELECT  t.ITEM_INVENTORY_ID as ENTITY_KEY_VALUE,
			II.ITEM_INVENTORY_UNIQUE_ID as ENTITY_AUTO_NAME,
			-- XML columns
			'Inventory item was deleted from "' + @SRC_SONO + '"' as [Changes]
		FROM @T_INVENTORY_ITEMS t
		INNER JOIN dbo.F_ITEM_INVENTORY II WITH(NOLOCK) 
			ON II.ITEM_INVENTORY_ID = t.ITEM_INVENTORY_ID
			
	) NEW;

	DECLARE @INVOICE_ID BIGINT = (
		SELECT MAX(INVOICE_ID)
		FROM F_INVOICE IV WITH (NOLOCK)
		WHERE IV.INVOICE_TYPE_ID = 1 AND IV.ORDER_ID = @SALES_ORDER_ID AND IS_VOIDED = 0 AND STATUS_ID < 4
	);
	
	IF (@INVOICE_ID IS NOT NULL)
	BEGIN	
							
		UPDATE IV SET 						
			 AMOUNT_DUE			=  dbo.fn_money_GET_SALES_ORDER_AMOUNT_DUE(@SALES_ORDER_ID)
			,ORIGINAL_AMOUNT	=  dbo.fn_money_GET_SALES_ORDER_AMOUNT_DUE(@SALES_ORDER_ID) + ISNULL(PAID_AMOUNT, 0)
			,IV.UPDATED_BY		= 'sp_DELETE_SALES_ORDER_ITEMS'
			,IV.UPDATED_DT		=  GETUTCDATE()
		FROM F_INVOICE	IV WITH(ROWLOCK)
		WHERE INVOICE_ID = @INVOICE_ID

	END

	EXEC [dbo].[sp_RECALC_SHIPPING_PACKAGE_DIMENSIONS]
		 @SALES_ORDER_ID = @SALES_ORDER_ID;

END
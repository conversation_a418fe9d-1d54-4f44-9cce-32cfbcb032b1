CREATE TABLE [dbo].[C_INVENTORY_CAPABILITY_TYPE] (
    [INVENTORY_CAPABILITY_TYPE_ID]      INT           IDENTITY (1, 1) NOT NULL,
    [INVENTORY_CAPABILITY_TYPE_NAME]    VARCHAR (150) NOT NULL,
    [PHISICAL_PARAMETER_TYPE_ID]        INT           NULL,
    [INVENTORY_CAPABILITY_VALUE]        VARCHAR (150) NOT NULL,
    [INVENTORY_CAPABILITY_LABEL]        VARCHAR (150) NOT NULL,
    [INVENTORY_CAPABILITY_IS_KEY]       BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_IS_KEY] DEFAULT ((0)) NOT NULL,
    [INVENTORY_CAPABILITY_CAN_SEARCH]   BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_CAN_SEARCH] DEFAULT ((0)) NOT NULL,
    [INVENTORY_CAPABILITY_IS_HIDDEN]    BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_IS_HIDDEN] DEFAULT ((0)) NOT NULL,
    [INVENTORY_CAPABILITY_IS_INDEX]     BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_IS_INDEX] DEFAULT ((0)) NOT NULL,
    [INVENTORY_CAPABILITY_FORMATTER_ID] INT           NULL,
    [INVENTORY_CAPABILITY_SORT_TYPE_ID] INT           NULL,
    [INVENTORY_CAPABILITY_WIDTH]        INT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_WIDTH] DEFAULT ((50)) NOT NULL,
    [INVENTORY_CAPABILITY_ORDER_DIR]    VARCHAR (150) NULL,
    [INVENTORY_CAPABILITY_IS_AVAILABLE] BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INVENTORY_CAPABILITY_IS_AVAILABLE] DEFAULT ((1)) NOT NULL,
    [IS_RUNTIME_EDITABLE]               BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_IS_RUNTIME_EDITABLE] DEFAULT ((1)) NOT NULL,
    [SRC_INVENTORY_ATTRIBUTE_TYPE_ID]   INT           NULL,
    [ATTRIBUTE_DATA_TYPE_ID]            BIGINT        NULL,
    [IS_INACTIVE]                       BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                        BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                       VARCHAR (150) NOT NULL,
    [INSERTED_DT]                       DATETIME      CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                        VARCHAR (150) NULL,
    [UPDATED_DT]                        DATETIME      NULL,
    [DELETED_BY]                        VARCHAR (150) NULL,
    [DELETED_DT]                        DATETIME      NULL,
    [IsSystem]                          BIT           CONSTRAINT [DF_C_INVENTORY_CAPABILITY_TYPE_IsSystem] DEFAULT ((0)) NULL,
    [SpecificName]                      VARCHAR (150) NULL,
    CONSTRAINT [PK_C_INVENTORY_CAPABILITY_TYPE] PRIMARY KEY CLUSTERED ([INVENTORY_CAPABILITY_TYPE_ID] ASC),
    CONSTRAINT [FK_C_INVENTORY_CAPABILITY_TYPE_C_INVENTORY_ATTRIBUTE_DATA_TYPE] FOREIGN KEY ([ATTRIBUTE_DATA_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_DATA_TYPE] ([ATTRIBUTE_DATA_TYPE_ID]),
    CONSTRAINT [FK_C_INVENTORY_CAPABILITY_TYPE_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([SRC_INVENTORY_ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_C_INVENTORY_CAPABILITY_TYPE_C_PHISICAL_PARAMETER_TYPE] FOREIGN KEY ([PHISICAL_PARAMETER_TYPE_ID]) REFERENCES [dbo].[C_PHISICAL_PARAMETER_TYPE] ([PHISICAL_PARAMETER_TYPE_ID])
);

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The list of capabilities in the system such as speed, cpu. These capabilities are assigned to the appropriate attribute type.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_INVENTORY_CAPABILITY_TYPE';

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'!Not used yet! Governs the ability to set "editable" property for this attribute in any Attribute Set', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_INVENTORY_CAPABILITY_TYPE', @level2type = N'COLUMN', @level2name = N'IS_RUNTIME_EDITABLE';


GO
CREATE NONCLUSTERED INDEX [IX__C_INVENTORY_CAPABILITY_TYPE__INVENTORY_CAPABILITY_LABEL__IS_DELETED__IsSystem]
    ON [dbo].[C_INVENTORY_CAPABILITY_TYPE] ([INVENTORY_CAPABILITY_LABEL],[IS_DELETED],[IsSystem])

GO
CREATE NONCLUSTERED INDEX [IX_VALUE]
    ON [dbo].[C_INVENTORY_CAPABILITY_TYPE]([INVENTORY_CAPABILITY_VALUE] ASC);

GO
CREATE NONCLUSTERED INDEX [IX__C_INVENTORY_CAPABILITY_TYPE__GRID_MODEL]
    ON [dbo].[C_INVENTORY_CAPABILITY_TYPE]([IS_INACTIVE] ASC, [IS_DELETED] ASC, [INVENTORY_CAPABILITY_TYPE_ID] ASC)
    INCLUDE([INVENTORY_CAPABILITY_CAN_SEARCH], [INVENTORY_CAPABILITY_IS_HIDDEN], [INVENTORY_CAPABILITY_IS_INDEX], [INVENTORY_CAPABILITY_IS_KEY], [INVENTORY_CAPABILITY_LABEL], [INVENTORY_CAPABILITY_ORDER_DIR], [INVENTORY_CAPABILITY_VALUE], [INVENTORY_CAPABILITY_WIDTH]);

GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_ATTRIBUTE_PUT_CHANGE]
    ON  [dbo].[C_INVENTORY_CAPABILITY_TYPE]
    AFTER INSERT, UPDATE, DELETE
AS 
BEGIN									

	declare @ids dbo.bigint_id_array

	insert into @ids
	select distinct
		fiic.ITEM_INVENTORY_ID
	from (
		select
			isnull(i.INVENTORY_CAPABILITY_TYPE_ID, d.INVENTORY_CAPABILITY_TYPE_ID) INVENTORY_CAPABILITY_TYPE_ID
		from inserted		i
		full join deleted	d
			on i.INVENTORY_CAPABILITY_TYPE_ID = d.INVENTORY_CAPABILITY_TYPE_ID	
		where isnull(i.INVENTORY_CAPABILITY_LABEL, '') <> isnull(d.INVENTORY_CAPABILITY_LABEL, '')
			or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
			or isnull(i.IS_DELETED, 0) <> isnull(d.IS_DELETED, 0)
	) t
	inner join D_INVENTORY_CAPABILITY		dic with(nolock)
		on T.INVENTORY_CAPABILITY_TYPE_ID = dic.INVENTORY_CAPABILITY_TYPE_ID
	inner join F_ITEM_INVENTORY_CAPABILITY	fiic with(nolock)
		on dic.INVENTORY_CAPABILITY_ID = fiic.INVENTORY_CAPABILITY_ID
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 1 --Inventory
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

GO
CREATE TRIGGER [dbo].[trg_INVENTORY_CAPABILITY_AFTER_NOTIFICATION]
    ON  [dbo].[C_INVENTORY_CAPABILITY_TYPE]
    AFTER INSERT
AS 
BEGIN
	DECLARE @params dbo.NOTIFICATION_QUEUE_PARAMS
		
	INSERT INTO @params (NAME, VALUE) 
	SELECT
		'INVENTORY_CAPABILITY_TYPE_ID'
		,INVENTORY_CAPABILITY_TYPE_ID
	FROM INSERTED

	----add notifications about item create
	EXEC sp_ADD_NOTIFICATION_TO_QUEUE
		@C_NOTIFICATION_TYPE_ID = 29 -- Attribute Is Created
		,@C_PARAMS = @params	

END
-- =============================================
-- Author:	    <Author,,Name>
-- Create date: <Create Date,,>
-- Description: Inventory items that can be used to allocate the provided Sales Order item
-- =============================================
/*
	exec [dbo].[sp_GET_ALLOCATE_INVENTORY] 
		@SALES_ORDER_ITEM_ID	= 575326
		,@CONDITION_ID			= 1
		,@WAREHOUSE_ID			= -1
		,@SKU					= null
		,@ORDER_COLUMN_NAME		= 'INSERTED_DT'
		,@ORDER_DIRECTION		= 'asc'
*/
CREATE PROCEDURE [dbo].[sp_GET_ALLOCATE_INVENTORY] 
	 @SALES_ORDER_ITEM_ID 	BIGINT 
	,@CONDITION_ID			BIGINT 
	,@WAREHOUSE_ID			BIGINT			= -1
	,@SKU					BIGINT			= NULL
	,@PRODUCT_CODE_ID		BIGINT			= NULL
	,@REVISION_ID			BIGINT			= NULL
	,@SHOW_SUBS				BIT				= 0
	,@ORDER_COLUMN_NAME		NVARCHAR(150)   = N'INSERTED_DT'
	,@ORDER_DIRECTION		VARCHAR(20)	    = N'ASC'
	,@ITEMS_PER_PAGE		INT				= 20
	,@PAGE_INDEX			INT				= 0
	,@FILTER_WHERE			NVARCHAR(2000)  = N''
	,@C_IS_DEBUG			BIT				= 0
AS
BEGIN

	DECLARE
		@ITEM_MASTER_ID BIGINT,
		@CUSTOMER_ID	BIGINT,
		@LOCATION_ID	BIGINT,
		@salesOrderId	BIGINT

	SELECT TOP(1)
		@ITEM_MASTER_ID = FPM.ITEM_MASTER_ID,
		@CUSTOMER_ID	= FSOI.CUSTOMER_ID,
		@LOCATION_ID	= FSOI.LOCATION_ID,
		@salesOrderId	= FSOI.SALES_ORDER_ID
	FROM F_SALES_ORDER_ITEM		FSOI	WITH(NOLOCK)
	INNER JOIN F_PRODUCT_MASTER	FPM		WITH(NOLOCK)
		ON  FPM.PRODUCT_MASTER_ID	   = FSOI.PRODUCT_MASTER_ID
		AND FPM.PRODUCT_MASTER_TYPE_ID = 1 -- ITEM_MASTER
	WHERE SALES_ORDER_ITEM_ID = @SALES_ORDER_ITEM_ID


	DECLARE @IS_LOCATION_AND_CUSTOMER_GROUPING BIT = ISNULL((SELECT TOP(1) USS.IS_EXTRA_COLUMNS_FOR_UNALLOCATED FROM U_SYSTEM_SETTINGS USS WITH(NOLOCK)), 0)
	DECLARE @FILTER NVARCHAR(MAX) = CASE
		WHEN @IS_LOCATION_AND_CUSTOMER_GROUPING = 0 THEN N''
		ELSE N'
			AND (@CUSTOMER_ID IS NULL OR FII.CustomerId = @CUSTOMER_ID)
			AND (@LOCATION_ID IS NULL OR FII.LocationId = @LOCATION_ID)
		'
	END


	SET @WAREHOUSE_ID	 = NULLIF(@WAREHOUSE_ID,	-1)
	SET @PRODUCT_CODE_ID = NULLIF(@PRODUCT_CODE_ID, -1)
	SET @REVISION_ID	 = NULLIF(@REVISION_ID,		-1)

	DECLARE
		@startRowNumber  BIGINT		=  @PAGE_INDEX      * @ITEMS_PER_PAGE + 1,
		@endRowNumber    BIGINT		= (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE,	
		@filterCondition NVARCHAR(2006) = CASE
				WHEN ISNULL(@FILTER_WHERE, N'') != N'' AND @FILTER_WHERE != 'null' THEN N' WHERE ('+ @FILTER_WHERE +N')'
				ELSE N''
			END,
		@skuCondition bit = 1;
    
	IF(@SKU IS NULL)
	BEGIN
		SET @skuCondition = 0; 
		SET @SKU = -1;
	END

	SET XACT_ABORT ON
	-- source model
	CREATE TABLE #t_MODELS (ID BIGINT PRIMARY KEY)	
		INSERT INTO  #t_MODELS
		SELECT @ITEM_MASTER_ID

		-- can take substitute models
		IF (@SHOW_SUBS = 1)
		BEGIN		  
			INSERT INTO #t_MODELS	
			SELECT 
				SUBSTITUTE_ITEM_MASTER_ID
			FROM dbo.vw_F_ITEM_MASTER_SUBSTITUTES_ALL WITH(NOLOCK)
			WHERE ITEM_MASTER_ID = @ITEM_MASTER_ID
		END
	
	-- models from bundle 
	CREATE TABLE #t_SKUS(ITEM_ID BIGINT)
	INSERT INTO #t_SKUS (ITEM_ID)	
	SELECT @SKU
	UNION ALL
	SELECT 
		IL.ITEM_ID_MASTER
	FROM F_ITEM						I	WITH (NOLOCK)
	INNER JOIN [dbo].[F_ITEM_LOT]	IL	WITH (NOLOCK)
		ON I.ITEM_ID = IL.ITEM_ID
	WHERE I.ITEM_ID = @SKU;

	CREATE INDEX idx_ITEM_ID ON  #t_SKUS(ITEM_ID)

	DECLARE @query1 NVARCHAR (max) = N'
		DECLARE @isRedeploymentSalesOrder BIT = IIF(@salesOrderId IS NULL, 0, [dbo].[fn_bit_IsRedeploymentSalesOrder](@salesOrderId));
		WITH m_data AS (	
			SELECT DISTINCT
				FII.Id															AS ITEM_INVENTORY_ID
				,FII.[ItemMasterId]											AS ITEM_MASTER_ID
				,FIM.ITEM_NUMBER	    
				,FII.[UniqueId]												AS ITEM_INVENTORY_UNIQUE_ID
				,FII.[Serial]													AS ITEM_INVENTORY_SERIAL
				,DIC.ITEM_CONDITION_DESC										AS ITEM_CONDITION
				,dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(FII.[LocationId])	AS ITEM_LOCATION
				,fii.Notes														AS ITEM_DESCRIPTION
				,FII.SkuId														AS [SKU]
				,CASE
				WHEN FII.Qty > 1 THEN 1
				ELSE 0
				END										AS IsBreakDown
				,FII.[Qty]									AS ItemQuantity
				,FII.CreatedDate			AS INSERTED_DT
				,DW.WAREHOUSE_ID							AS WAREHOUSE_ID
				,DW.WAREHOUSE_CD							AS WAREHOUSE_CD
				,C.CUSTOMER_ID
				,C.CUSTOMER_NAME							AS CUSTOMER
				,FII.GradeLevelMark
				,ISNULL(FPO.PurchaseOrderList, ''[]'')							AS PurchaseOrderList
			FROM #t_MODELS							M		WITH (NOLOCK)
			INNER JOIN F_ITEM_MASTER					FIM		WITH (NOLOCK)
				ON FIM.ITEM_MASTER_ID = M.ID
			INNER JOIN (
				SELECT
					IDM.Id
					,IDM.SkuId
					,IDM.UniqueId
					,IDM.CustomerId
					,IDM.Serial
					,IDM.ItemMasterId
					,IDM.ConditionId
					,IDM.LocationId
					,IDM.Notes
					,IDM.Qty
					,IDM.CreatedDate
					,IDM.ProductCodeIdHeci
					,IDM.RevisionId
					,FA.GradeLevelMark
				FROM [dbo].[vw_InventoryDetailsMain]		IDM WITH (NOLOCK)
				LEFT JOIN [recycling].[F_AssetGrading]		FA WITH (NOLOCK)
					ON FA.AssetId = IDM.AssetId
				WHERE IDM.[IsAvailable] = 1
					AND IDM.[IsVirtual] = 0
				UNION
				SELECT
					ir.ITEM_INVENTORY_ID						AS Id
					,ir.ITEM_ID									AS SkuId					
					,ir.ITEM_INVENTORY_UNIQUE_ID				AS UniqueId
					,ir.CUSTOMER_ID								AS CustomerId					
					,ir.ITEM_INVENTORY_SERIAL					AS Serial								
					,ir.ITEM_MASTER_ID							AS ItemMasterId									
					,ir.CONDITION_ID							AS ConditionId					
					,ir.LOCATION_ID								AS LocationId			
					,ir.ITEM_INVENTORY_NOTES					as Notes																					
					,ir.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED	AS Qty
					,ir.INSERTED_DT								as CreatedDate					
					,ir.ProductCodeIdHeci				
					,ir.RevisionId
					,FA.GradeLevelMark
				FROM [dbo].[F_ITEM_INVENTORY]				IR WITH (NOLOCK)
				INNER JOIN [dbo].[vw_RedeploymentInventory]	RI WITH (NOLOCK)
					ON  RI.InventoryId = IR.ITEM_INVENTORY_ID
					AND RI.IsAvailable = 1
				LEFT JOIN [recycling].[F_AssetGrading]		FA WITH (NOLOCK)
					ON FA.AssetId = IR.AssetId
				WHERE @isRedeploymentSalesOrder = 1
					and IR.[IS_VIRTUAL] = 0
						
			)											FII
			    ON FII.[ItemMasterId] = M.ID
				' + @FILTER + N'
			INNER JOIN D_ITEM_CONDITION				DIC		WITH (NOLOCK)
				ON DIC.ITEM_CONDITION_ID = FII.[ConditionId]
			LEFT JOIN F_LOCATION						FL		WITH (NOLOCK)
				ON FL.LOCATION_ID = FII.LocationId
			LEFT JOIN D_WAREHOUSE 					DW		WITH (NOLOCK)
				ON DW.WAREHOUSE_ID = FL.WAREHOUSE_ID
			LEFT JOIN F_CUSTOMER						C		WITH(NOLOCK)
				ON C.CUSTOMER_ID = FII.CustomerId				
			OUTER APPLY (
				SELECT
					JSON_QUERY((	
						-- Get purchase orders related to either the sales order item or the inventory item
						SELECT DISTINCT
							IIF(PO.PURCHASE_ORDER_ID IS NOT NULL, PO.PURCHASE_ORDER_ID, NPO.PURCHASE_ORDER_ID) AS PurchaseOrderId,
							IIF(PO.PURCHASE_ORDER_ID IS NOT NULL, PO.AUTO_NAME, NPO.AUTO_NAME) AS PurchaseOrderName
						FROM dbo.F_PURCHASE_ORDER_ITEM POI WITH(NOLOCK)
						LEFT JOIN dbo.F_PURCHASE_ORDER PO WITH(NOLOCK)
							ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
							AND PO.IS_DELETED = 0
							AND POI.IS_DELETED = 0
							AND POI.SALES_ORDER_ITEM_ID = @SALES_ORDER_ITEM_ID
							AND POI.INVENTORY_ITEM_ID = FII.Id
						LEFT JOIN dbo.F_PURCHASE_ORDER_ITEM NPOI WITH(NOLOCK)
							ON NPOI.INVENTORY_ITEM_ID = FII.Id
							AND NPOI.IS_DELETED = 0
						LEFT JOIN dbo.F_PURCHASE_ORDER NPO WITH(NOLOCK)
							ON NPO.PURCHASE_ORDER_ID = NPOI.PURCHASE_ORDER_ID
							AND NPO.IS_DELETED = 0
						WHERE (PO.PURCHASE_ORDER_ID IS NOT NULL OR NPO.PURCHASE_ORDER_ID IS NOT NULL)
						FOR JSON PATH
					)) AS PurchaseOrderList
			) AS FPO			
			WHERE  (@PRODUCT_CODE_ID IS NULL OR FII.ProductCodeIdHeci	= @PRODUCT_CODE_ID)
				AND (@REVISION_ID     IS NULL OR fii.RevisionId		= @REVISION_ID)
				AND (@WAREHOUSE_ID    IS NULL OR DW.WAREHOUSE_ID		= @WAREHOUSE_ID)				
				AND ( @skuCondition = 0 and FII.[ConditionId] = @CONDITION_ID
				OR @skuCondition = 1 AND (FII.[SkuId] IN (SELECT ITEM_ID FROM #t_SKUS)					 
				OR @SHOW_SUBS = 1    AND M.ID != @ITEM_MASTER_ID ))
		)'
		
		DECLARE @query2 NVARCHAR(MAX) = N'
		   SELECT TOP(1)
			  -1						AS RowId
			  ,COUNT(ITEM_INVENTORY_ID) AS ItemInventoryId
			  ,NULL						AS MasterItemId
			  ,NULL						AS ItemNumber
			  ,NULL						AS UniqueIdentifier
			  ,NULL						AS Serial
			  ,NULL						AS Condition
			  ,NULL						AS Location
			  ,NULL						AS Description
			  ,NULL						AS Sku
			  ,0						AS IsBreakDown
			  ,0						AS ItemQuantity
			  ,NULL						AS CreatedDate
			  ,NULL						AS WarehouseId
			  ,NULL						AS Warehouse
			  ,NULL						AS CustomerId
			  ,NULL						AS Customer
			  ,NULL						AS FinalGrade
			  ,NULL						AS PurchaseOrderList
		   FROM m_data ' + @filterCondition + '
		   UNION'
		
		SET @query2 = @query2 + N'
		   SELECT
			  t.RowID						AS RowId
			  ,t.ITEM_INVENTORY_ID			AS ItemInventoryId
			  ,t.ITEM_MASTER_ID				AS MasterItemId
			  ,t.ITEM_NUMBER				AS ItemNumber
			  ,t.ITEM_INVENTORY_UNIQUE_ID	AS UniqueIdentifier
			  ,t.ITEM_INVENTORY_SERIAL		AS Serial
			  ,t.ITEM_CONDITION				AS Condition
			  ,t.ITEM_LOCATION				AS Location
			  ,t.ITEM_DESCRIPTION			AS Description
			  ,t.[SKU]						AS Sku
			  ,t.IsBreakDown				AS IsBreakDown
			  ,t.ItemQuantity				AS ItemQuantity
			  ,t.INSERTED_DT				AS CreatedDate
			  ,t.WAREHOUSE_ID				AS WarehouseId
			  ,t.WAREHOUSE_CD				AS Warehouse
			  ,t.CUSTOMER_ID				AS CustomerId
			  ,t.CUSTOMER					AS Customer
			  ,t.GradeLevelMark				AS FinalGrade
			  ,t.PurchaseOrderList'
		
		SET @query2 = @query2 + N'
		   FROM (		
			  SELECT 
				 ROW_NUMBER() OVER (ORDER BY ' + @ORDER_COLUMN_NAME + N' ' + @ORDER_DIRECTION + N') AS RowID,
				 * 
			  FROM m_data ' + @filterCondition + N') t
		   WHERE RowID BETWEEN @startRowNumber AND @endRowNumber ';
		
		SET @query1 = @query1 + @query2
		

		IF (@C_IS_DEBUG = 1)
		BEGIN
			SET @query1 = REPLACE(@query1, '@PRODUCT_CODE_ID',	ISNULL(CAST(@PRODUCT_CODE_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@REVISION_ID',		ISNULL(CAST(@REVISION_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@WAREHOUSE_ID',		ISNULL(CAST(@WAREHOUSE_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@CONDITION_ID',		ISNULL(CAST(@CONDITION_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@skuCondition',		ISNULL(CAST(@skuCondition AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@ITEM_MASTER_ID',	ISNULL(CAST(@ITEM_MASTER_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@SHOW_SUBS',		ISNULL(CAST(@SHOW_SUBS AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@startRowNumber',	ISNULL(CAST(@startRowNumber AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@endRowNumber',		ISNULL(CAST(@endRowNumber AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@CUSTOMER_ID',		ISNULL(CAST(@CUSTOMER_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@LOCATION_ID',		ISNULL(CAST(@LOCATION_ID AS NVARCHAR(MAX)), 'NULL'));
			SET @query1 = REPLACE(@query1, '@salesOrderId',		ISNULL(CAST(@salesOrderId AS NVARCHAR(MAX)), 'NULL'));

			PRINT(CAST(@query1 AS NTEXT));

			SELECT id as ITEM_MASTER_ID FROM #t_MODELS
			SELECT ITEM_ID				FROM #t_SKUS
		END
		ELSE BEGIN
			EXEC sp_executesql @query1
			,N'@PRODUCT_CODE_ID	BIGINT
			  ,@REVISION_ID		BIGINT
			  ,@WAREHOUSE_ID	BIGINT
			  ,@CONDITION_ID	BIGINT
			  ,@skuCondition	bit
			  ,@ITEM_MASTER_ID	BIGINT
			  ,@SHOW_SUBS		BIT
			  ,@startRowNumber	BIGINT
			  ,@endRowNumber	BIGINT
			  ,@CUSTOMER_ID		BIGINT
			  ,@LOCATION_ID		BIGINT
			  ,@salesOrderId	BIGINT
			  ,@SALES_ORDER_ITEM_ID BIGINT
			'
			,@PRODUCT_CODE_ID	= @PRODUCT_CODE_ID
			,@REVISION_ID		= @REVISION_ID
			,@WAREHOUSE_ID		= @WAREHOUSE_ID
			,@CONDITION_ID		= @CONDITION_ID
			,@skuCondition		= @skuCondition
			,@ITEM_MASTER_ID	= @ITEM_MASTER_ID
			,@SHOW_SUBS			= @SHOW_SUBS
			,@startRowNumber	= @startRowNumber
			,@endRowNumber		= @endRowNumber
			,@CUSTOMER_ID		= @CUSTOMER_ID
			,@LOCATION_ID		= @LOCATION_ID
			,@salesOrderId		= @salesOrderId
			,@SALES_ORDER_ITEM_ID = @SALES_ORDER_ITEM_ID
		END
		DROP TABLE #t_MODELS
		DROP TABLE #t_SKUS
END
-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE FUNCTION [dbo].[fn_float_GET_ITEM_QUANTITY_AVAILABLE]
(
	@ITEM_ID		FLOAT
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @LOCKED FLOAT = dbo.fn_float_GET_ITEM_QUANTITY_LOCKED(@ITEM_ID)
	
	RETURN 
		(SELECT		
				CASE  
					WHEN QUANTITY < @LOCKED THEN 0 --QUANTITY - ON HAND
					ELSE QUANTITY - @LOCKED 
				END				 
		FROM dbo.F_ITEM
		WHERE ITEM_ID = @ITEM_ID)
END
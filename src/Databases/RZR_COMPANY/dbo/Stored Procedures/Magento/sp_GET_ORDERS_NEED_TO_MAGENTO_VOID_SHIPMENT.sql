-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_ORDERS_NEED_TO_MAGENTO_VOID_SHIPMENT]
	@MAGENTO_SHOP_ID INT
AS
BEGIN
	SELECT 
		mo.[M_SHIPMENT_ID] AS [MShipmentId],
		fsos.[MAGENTO_TRACK_ID] AS [MTrackId]
	FROM [dbo].[F_SHIPPING] AS fsos WITH (NOLOCK)
	INNER JOIN [dbo].[MAGENTO_ORDERS] AS mo WITH (NOLOCK)
		ON mo.[SALES_ORDER_ID] = fsos.[SALES_ORDER_ID] AND mo.[SHOP_ID] = @MAGENTO_SHOP_ID
	WHERE 
		mo.[M_SHIPMENT_ID] IS NOT NULL AND
		fsos.[NEEDS_MAGENTO_VOID] = 1 AND
		fsos.[MAGENTO_TRACK_ID] IS NOT NULL
	UNION
	SELECT 
		mo.[M_SHIPMENT_ID],
		fsp.[MAGENTO_TRACK_ID]
	FROM [dbo].[F_SHIPPING] AS fsos WITH (NOLOCK)
	INNER JOIN [dbo].[MAGENTO_ORDERS] AS mo WITH (NOLOCK)
		ON mo.[SALES_ORDER_ID] = fsos.[SALES_ORDER_ID] AND mo.[SHOP_ID] = @MAGENTO_SHOP_ID
	INNER JOIN [dbo].[F_SHIPPING_PACKAGE] fsp WITH (NOLOCK)
		ON fsp.[SHIPPING_ID] = fsos.[SHIPPING_ID] AND fsp.[MAGENTO_TRACK_ID] IS NOT NULL
	WHERE 
		mo.[M_SHIPMENT_ID] IS NOT NULL AND
		fsos.[NEEDS_MAGENTO_VOID] = 1
END
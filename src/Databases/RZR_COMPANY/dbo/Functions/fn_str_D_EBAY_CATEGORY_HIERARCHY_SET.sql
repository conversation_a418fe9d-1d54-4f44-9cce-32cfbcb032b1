-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date, ,>
-- Description:	<Description, ,>
-- =============================================
-- SELECT dbo.fn_str_D_CATEGORY_HIERARCHY_SET('FSP300-60BTV')
CREATE FUNCTION [dbo].[fn_str_D_EBAY_CATEGORY_HIERARCHY_SET]
(
	@ITEM_ID BIGINT
)
RETURNS NVARCHAR(MAX)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @C_RETURN_VAL NVARCHAR(MAX) = NULL
	
	-- Triyng to get the direct master item eBay category
	SELECT 
		@C_RETURN_VAL =  
			CAST(ech.CATEGORY_ID AS NVARCHAR(20))
	FROM F_ITEM I
	INNER JOIN F_ITEM_MASTER M WITH(NOLOCK)
		ON M.ITEM_MASTER_ID = I.ITEM_MASTER_ID
	INNER JOIN 	dbo.D_EBAY_CATEGORY_HIERARCHY ech
		ON M.EBAY_CATEGORY_ID = ech.EBAY_CATEGORY_HIERARCHY_ID
	WHERE I.ITEM_ID = @ITEM_ID
	  AND M.EBAY_CATEGORY_ID IS NOT NULL
	
	IF (DATALENGTH(@C_RETURN_VAL) > 0)
	BEGIN
		RETURN @C_RETURN_VAL;
	END

	-- Triyng to get the master item eBay category via owr category
	SELECT			
		@C_RETURN_VAL = COALESCE(@C_RETURN_VAL + '||', '') + CAST(ech.CATEGORY_ID AS NVARCHAR(20))
	FROM F_ITEM I
	INNER JOIN F_ITEM_MASTER M WITH(NOLOCK)
		ON M.ITEM_MASTER_ID = I.ITEM_MASTER_ID
	INNER JOIN F_ITEM_MASTER_CATEGORY MC WITH(NOLOCK)
		ON M.ITEM_MASTER_ID = MC.ITEM_MASTER_ID	
	INNER JOIN D_EBAY_CATEGORY_HIERARCHY ech WITH(NOLOCK)
		ON MC.CATEGORY_ID = ech.MAPPED_CATEGORY_ID		
	WHERE
		I.ITEM_ID = @ITEM_ID
	
	RETURN ISNULL(@C_RETURN_VAL, '')

END
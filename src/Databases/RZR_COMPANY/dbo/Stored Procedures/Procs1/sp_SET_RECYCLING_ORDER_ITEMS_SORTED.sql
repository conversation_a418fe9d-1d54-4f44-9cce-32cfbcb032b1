-- =============================================
-- Author:		<O. Evseev>
-- Create date: <02/11/2014>
-- Description:	<Sets "Processed" flag for recycling order items>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEMS_SORTED]
	@LOT_IDS					bigint_ID_ARRAY READONLY,
	@APPEND_TO_CHILDREN			BIT,
	@USER_ID					BIGINT,
	@IP							BIGINT,
	@C_USE_INTERNAL_TRANSACTION	BIT	= 1
AS
BEGIN

	DECLARE
		@UTC_NOW	DATETIME	= GETUTCDATE(),
		@PROCESS_CD	VARCHAR(250)= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
	
	IF @C_USE_INTERNAL_TRANSACTION = 1
	BEGIN
		SET XACT_ABORT ON
		BEGIN TRAN
	END

		IF (@APPEND_TO_CHILDREN = 1)
		BEGIN
			UPDATE I SET
				I.NOTES = ISNULL(I.NOTES, '') + ' ' + LL.NOTES
			FROM @LOT_IDS						L	
			INNER JOIN F_RECYCLING_ORDER_ITEM	I	WITH(ROWLOCK)
				ON I.PARENT_ID = L.ID
			INNER JOIN 	F_RECYCLING_ORDER_ITEM	LL	WITH(ROWLOCK)
				ON L.ID = LL.RECYCLING_ORDER_ITEM_ID		
			WHERE ROUND(LL.WEIGHT_REMAIN, 2) != 0			
		END			

		UPDATE F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
			WorkflowStepPrevId			= iif(WorkflowStepPrevId is null, WORKFLOW_STEP_ID, WorkflowStepPrevId),
			WORKFLOW_STEP_ID			= 8,
			UPDATED_BY					= @PROCESS_CD,
			UPDATED_DT					= @UTC_NOW,
			UPDATED_BY_USER				= @USER_ID,
			UPDATED_BY_IP				= @IP		
		WHERE [RECYCLING_ORDER_ITEM_ID] IN (SELECT ID FROM @LOT_IDS)

    declare @orderIds bigint_ID_ARRAY;
    declare @orderId bigint;

    declare ordersCursor cursor for
        select distinct [FROI].[RECYCLING_ORDER_ID]
        from [dbo].[F_RECYCLING_ORDER_INBOUND] FROI with (nolock)
                 inner join [dbo].[F_RECYCLING_ORDER_ITEM] FROIT with (nolock)
                            on FROI.[RECYCLING_ORDER_ID] = FROIT.[RECYCLING_ORDER_ID]
                 inner join @LOT_IDS LOTS
                            on FROIT.[RECYCLING_ORDER_ITEM_ID] = LOTS.[ID]
        where FROI.[StatusId] in (5, 8);

    open ordersCursor;
    fetch next from ordersCursor into @orderId;
    while @@fetch_status = 0
        begin
            insert into @orderIds (ID)
            values (@orderId);
            print @orderId;

            fetch next from ordersCursor into @orderId;
        end

    close ordersCursor;
    deallocate ordersCursor;

    exec [dbo].[sp_UpdateReadyToBePricedOrders] @AffectedRecyclingOrders = @orderIds;

	IF @C_USE_INTERNAL_TRANSACTION = 1
		COMMIT TRAN

    declare @lotsToLog [dbo].[bigint_ID_ARRAY];
    insert into @lotsToLog
    select FROI_CHILD.[RECYCLING_ORDER_ITEM_ID]
    from [dbo].[F_RECYCLING_ORDER_ITEM] FROI with (nolock)
             inner join @LOT_IDS LOTS
                        on LOTS.[ID] = FROI.[RECYCLING_ORDER_ITEM_ID]
             inner join [dbo].[F_RECYCLING_ORDER_ITEM] FROI_CHILD with (nolock)
                        on FROI_CHILD.[PARENT_ID] = FROI.[RECYCLING_ORDER_ITEM_ID]
             inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT with (nolock)
                        on FROI.[WORKFLOW_STEP_ID] = CRWT.[WORKFLOW_TYPE_ID]
             inner join [dbo].[C_RECYCLING_WORKFLOW_TYPE] CRWT_CHILD with (nolock)
                        on FROI_CHILD.[WORKFLOW_STEP_ID] = CRWT_CHILD.[WORKFLOW_TYPE_ID]
             inner join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
                        on FROI.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID]
             inner join [dbo].[F_RECYCLING_ORDER_INBOUND] FROIN with (nolock)
                        on FRO.[RECYCLING_ORDER_ID] = FROIN.[RECYCLING_ORDER_ID]
             inner join [dbo].[F_CUSTOMER] FC with (nolock)
                        on FRO.[CUSTOMER_ID] = FC.[CUSTOMER_ID]
    where coalesce(FROI.BusinessUnitId, CRWT.[BusinessUnitId], FROIN.[BusinessUnitId], FC.[BusinessUnitId]) <>
          coalesce(FROI_CHILD.BusinessUnitId, CRWT_CHILD.[BusinessUnitId], FROIN.[BusinessUnitId],
                   FC.[BusinessUnitId])
      and FROI.[IS_EXCESS_FROM_SCRAP] = 0
      and CRWT_CHILD.IsFinal = 1

    exec [recycling].[sp_SetLotWorkflowChangeLog]
         @LotIds = @lotsToLog,
         @Activity = N'Sort Complete',
         @StatusId = 1, -- pending
         @UserId = @USER_ID
    
END
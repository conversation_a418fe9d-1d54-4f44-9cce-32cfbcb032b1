CREATE PROCEDURE [recycling].[sp_SetGradeCategoryPositionDevaluation]
	@GradeTemplateId			BIGINT,
	@AttributeTypeId			BIGINT,
	@GradePositionDevaluation	[dbo].[InventoryGradePositionDevaluation] READONLY,
	@UserId						BIGINT,
	@UserIp						BIGINT,
	@UseInternalTran			BIT = 1,
	@OuterSpName				NVARCHAR(150) = NULL
AS
BEGIN
	
    SET XACT_ABORT ON;

	DECLARE
		 @spName NVARCHAR(50)	= ISNULL(OBJECT_SCHEMA_NAME(@@procid) + '.', '') + OBJECT_NAME(@@procid)
		,@utcNow DATETIME		= GETUTCDATE();

	IF (@OuterSpName IS NOT NULL) BEGIN
		SELECT @spName = @spName + N' (' + @OuterSpName + N')';
	END

	BEGIN TRY
		
		IF (@UseInternalTran = 1) BEGIN
			BEGIN TRANSACTION SetGradingPositionDevaluations;
		END


		;WITH m_t AS (
			SELECT pd.*
			FROM [recycling].[F_GradeCategoryPositionDevaluation] pd WITH (NOLOCK)
			WHERE pd.[GradeTemplateId] = @GradeTemplateId
			AND pd.[AttributeTypeId] = @AttributeTypeId
		)
		MERGE m_t AS t
		USING @GradePositionDevaluation AS s
		ON t.[Id] = s.[Id]
		WHEN MATCHED 
			THEN UPDATE SET
				t.[LevelPoint]					= s.[LevelPoint]
				,t.[IsAffectingOnGradeLevel]	= s.[IsAffectingOnGradeLevel]
				,t.[DevaluationValue]			= s.[DevaluationValue]
				,t.[IsDevaluationValueAbsolute] = s.[IsDevaluationValueAbsolute]
				,t.[IsSelected]					= s.[IsSelected]
				,t.[UpdatedBy]					= @spName
				,t.[UpdatedDate]				= @utcNow
				,t.[UpdatedByUserId]			= @UserId
				,t.[UpdatedByUserIp]			= @UserIp
		WHEN NOT MATCHED BY TARGET
			THEN INSERT (
				[AttributeTypeId]
				,[GradeTemplateId]
				,[GradePositionId]
				,[LevelPoint]
				,[IsAffectingOnGradeLevel]
				,[DevaluationValue]
				,[IsDevaluationValueAbsolute]
				,[IsSelected]
				,[InsertedBy]
				,[InsertedDate]
				,[InsertedByUserId]
				,[InsertedByUserIp]
			)
			VALUES (
				@AttributeTypeId
				,@GradeTemplateId
				,s.PositionId
				,s.LevelPoint
				,s.IsAffectingOnGradeLevel
				,s.DevaluationValue
				,s.IsDevaluationValueAbsolute
				,s.IsSelected
				,@spName
				,@utcNow
				,@UserId
				,@UserIp
			)
		WHEN NOT MATCHED BY SOURCE THEN
			DELETE;		


		IF (@UseInternalTran = 1) BEGIN
			COMMIT TRANSACTION SetGradingPositionDevaluations;
		END
		
	END TRY
	BEGIN CATCH
		 DECLARE @ErrorNumer INT = ERROR_NUMBER() 
				,@ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
				,@ErrorSeverity INT = ERROR_SEVERITY()
				,@ErrorState INT = ERROR_STATE();

		 IF (XACT_STATE() <> 0 AND @UseInternalTran = 1)
		 BEGIN
		 	ROLLBACK TRANSACTION SetGradingPositionDevaluations;
		 END

		;THROW

	END CATCH

END
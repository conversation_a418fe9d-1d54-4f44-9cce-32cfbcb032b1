CREATE TABLE [dbo].[F_ITEM_LIST_CATEGORY_OPTIONS](
	[ITEM_LIST_CATEGORY_OPTIONS_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[ITEM_MASTER_ID] [bigint] NOT NULL,
	[ITEM_NUMBER] [varchar](50) NOT NULL,
	[SOURCE_SYS_ID] [bigint] NOT NULL,
	[ITEM_LIST_COMMON_CATEGORY] [varchar](max) NULL,
	[ITEM_LIST_COMMON_CATEGORY_COUNT] [bigint] NULL,
	[ITEM_LIST_COMMON_CATEGORY_RANK] [bigint] NULL,
	[IS_INACTIVE] [bit] NOT NULL,
	[IS_DELETED] [bit] NOT NULL,
	[INSERTED_BY] [varchar](50) NOT NULL,
	[INSERTED_DT] [datetime] NOT NULL,
	[UPDATED_BY] [varchar](50) NULL,
	[UPDATED_DT] [datetime] NULL,
	[DELETED_BY] [varchar](50) NULL,
	[DELETED_DT] [datetime] NULL,
 CONSTRAINT [PK_F_ITEM_LIST_CATEGORY_OPTIONS] PRIMARY KEY CLUSTERED 
(
	[ITEM_LIST_CATEGORY_OPTIONS_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'It seems it is not used' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'F_ITEM_LIST_CATEGORY_OPTIONS'
GO

CREATE TABLE [dbo].[F_NOTIFICATION_CONDITION_LOG] (
    [NotificationConditionLogId]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [IsNotified]                        BIT           CONSTRAINT [DF_F_NOTIFICATION_CONDITION_LOG_IS_NOTIFIED] DEFAULT ((0)) NOT NULL,
    [EntityTypeId]                      BIGINT        NOT NULL,
    [EntityId]                          BIGINT        NOT NULL,
    [ContractExpirationDate]            DATETIME      NULL,
    [NotificationTypeId]                BIGINT        NOT NULL,
    [InsertedBy]                        VARCHAR (150) CONSTRAINT [DF_F_NOTIFICATION_CONDITION_LOG_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [InsertedDate]                      DATETIME      CONSTRAINT [DF_F_NOTIFICATION_CONDITION_LOG_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UpdatedBy]                         VARCHAR (150) NULL,
    [UpdatedDate]                       D<PERSON>ETIME      NULL,
    [DeletedBy]                         VARCHAR (150) NULL,
    [DeletedDate]                       DATETIME      NULL,
    CONSTRAINT [PK_F_NOTIFICATION_CONDITION_LOG] PRIMARY KEY CLUSTERED ([NotificationConditionLogId] ASC),
    CONSTRAINT [FK_F_NOTIFICATION_CONDITION_LOG_C_NOTIFICATION_TYPE] FOREIGN KEY ([NotificationTypeId]) REFERENCES [dbo].[C_NOTIFICATION_TYPE] ([NOTIFICATION_TYPE_ID]),
    CONSTRAINT [FK_F_NOTIFICATION_CONDITION_LOG_F_CONTRACT_ID] FOREIGN KEY ([EntityId]) REFERENCES [dbo].[F_CONTRACT] ([CONTRACT_ID]),
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'information about if notification with condition has been completed', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_NOTIFICATION_CONDITION_LOG';


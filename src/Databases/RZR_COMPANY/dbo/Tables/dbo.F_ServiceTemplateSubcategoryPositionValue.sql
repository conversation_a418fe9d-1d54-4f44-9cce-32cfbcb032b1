CREATE TABLE [dbo].[F_ServiceTemplateSubcategoryPositionValue] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [AttributeTypeId]                           INT             NOT NULL,
    [ServiceTemplateId]                         BIGINT          NULL,
    [ServiceSubcategoryPositionId]              BIGINT          NOT NULL,
    [ChargeValue]                               DECIMAL (18, 4) CONSTRAINT [DF_F_ServiceTemplateSubcategoryPositionValue_ChargeValue] DEFAULT ((0)) NOT NULL,
    [Pos]                                       INT             NOT NULL,
    [IsRequired]                                BIT             CONSTRAINT [DF_F_ServiceTemplateSubcategoryPositionValue_IsRequired] DEFAULT ((0)) NOT NULL,
    [IsSelected]                                BIT             CONSTRAINT [DF_F_ServiceTemplateSubcategoryPositionValue_IsSelected] DEFAULT ((0)) NOT NULL,
    [IsRadioButton]                             BIT             CONSTRAINT [DF_F_ServiceTemplateSubcategoryPositionValue_IsRadioButton] DEFAULT ((0)) NOT NULL,
    [IsDeleted]                                 BIT             CONSTRAINT [DF_F_ServiceTemplateSubcategoryPositionValue_IsDeleted] DEFAULT ((0)) NOT NULL,
    [InsertedBy]                 NVARCHAR (150)  NOT NULL,
    [InsertedDate]               DATETIME        NOT NULL,
    [InsertedByUserId]           BIGINT          NULL,
    [InsertedByUserIp]           BIGINT          NULL,
    [UpdatedBy]                  NVARCHAR (150)  NULL,
    [UpdatedDate]                DATETIME        NULL,
    [UpdatedByUserId]            BIGINT          NULL,
    [UpdatedByUserIp]            BIGINT          NULL,
    [DeletedBy]                  NVARCHAR (150)  NULL,
    [DeletedDate]                DATETIME        NULL,
    [DeletedByUserId]            BIGINT          NULL,
    [DeletedByUserIp]            BIGINT          NULL,
    CONSTRAINT [PK_F_ServiceTemplateSubcategoryPositionValue] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([AttributeTypeId]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID]),
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_D_ServiceSubCategoryPosition] FOREIGN KEY ([ServiceSubcategoryPositionId]) REFERENCES [dbo].[D_ServiceSubcategoryPosition] ([Id]),
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_F_ServiceTemplate] FOREIGN KEY ([ServiceTemplateId]) REFERENCES [dbo].[F_ServiceTemplate] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_tb_User] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_tb_User1] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_ServiceTemplateSubcategoryPositionValue_tb_User2] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);








GO
CREATE NONCLUSTERED INDEX [idx_ServiceTemplateId_AttributeTypeId]
    ON [dbo].[F_ServiceTemplateSubcategoryPositionValue]([ServiceTemplateId] ASC, [AttributeTypeId] ASC);


GO
CREATE NONCLUSTERED INDEX [idx_ServiceSubcategoryPositionId]
    ON [dbo].[F_ServiceTemplateSubcategoryPositionValue]([ServiceSubcategoryPositionId] ASC);


CREATE TABLE [dbo].[TagToSku] (
    [Id]             BIGINT   IDENTITY (1, 1) NOT NULL,
    [TagId]          BIGINT   NOT NULL,
    [SkuId]          BIGINT   NOT NULL,
    [InsertedDate]   DATETIME NOT NULL,
    [InsertedUserId] BIGINT   NOT NULL,
    [InsertedUserIp] BIGINT   NOT NULL,
    CONSTRAINT [PK_TagToSku] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TagToSku_F_ITEM] FOREIGN KEY ([SkuId]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID]),
    CONSTRAINT [FK_TagToSku_F_TAG] FOREIGN KEY ([TagId]) REFERENCES [dbo].[F_TAG] ([TAG_ID]),
    CONSTRAINT [FK_TagToSku_tb_User] FOREIGN KEY ([InsertedUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);








GO



CREATE trigger [dbo].[trg_TagToSku_PUT_CHANGE]
    ON  [dbo].[TagToSku]
    AFTER INSERT, DELETE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select distinct SkuId
	from 
	(
		select i.SkuId
		from inserted		i
		union
		select d.SkuId
		from deleted	d		
	)  s	
	-- assuming that it is fast enough to update the records in elastic each time 

	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker	
	
	exec search.sp_SetEntityChanged
		@TypeId		= 11 --sku
		,@EntityIds = @ids
		,@Invoker	= @invoker


END
GO

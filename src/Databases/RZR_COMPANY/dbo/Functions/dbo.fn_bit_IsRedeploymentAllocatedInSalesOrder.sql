-- =============================================
-- Create date: 11/14/2022
-- Description:	Checks if sales order has allocated redeployment inventory (assets with redeployment workflow)
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IsRedeploymentAllocatedInSalesOrder]
(
	@SalesOrderId					BIGINT
)
RETURNS BIT
AS
BEGIN
	DECLARE @result BIT = 0

	IF (EXISTS(SELECT TOP(1) 1
		FROM F_SALES_ORDER_ITEM							FSOI	WITH(NOLOCK)
		INNER JOIN [dbo].[vw_RedeploymentInventory]		RI		WITH (NOLOCK)
			ON RI.InventoryId = FSOI.ITEM_INVENTORY_ID
		WHERE FSOI.SALES_ORDER_ID = @SalesOrderId))
	BEGIN
		SELECT @result = 1
	END
	
	RETURN 
		@result
END
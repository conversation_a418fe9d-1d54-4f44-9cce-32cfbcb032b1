CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_CAPABILITY_TYPE]
AS
BEGIN
	SELECT 
		CT.INVENTORY_CAPABILITY_TYPE_ID	 AS TypeId
		,CT.INVENTORY_CAPABILITY_LABEL	 AS Caption
		,AT.INVENTORY_ATTRIBUTE_TYPE_ID	 AS SrcAttrSetId
		,AT.INVENTORY_ATTRIBUTE_NAME	 AS SrcAttrSetName
		,CT.INVENTORY_CAPABILITY_IS_AVAILABLE AS IsAvailable
	FROM dbo.C_INVENTORY_CAPABILITY_TYPE	CT WITH(NOLOCK)
	LEFT JOIN C_INVENTORY_ATTRIBUTE_TYPE	AT WITH(NOLOCK)
	  ON CT.SRC_INVENTORY_ATTRIBUTE_TYPE_ID = AT.INVENTORY_ATTRIBUTE_TYPE_ID
	  AND AT.IS_INACTIVE = 0
	  AND AT.IS_DELETED = 0
	WHERE CT.INVENTORY_CAPABILITY_IS_HIDDEN = 0	 
	  AND CT.IS_INACTIVE = 0
	  AND CT.IS_DELETED = 0
	  and ct.IsSystem = 0
	ORDER BY INVENTORY_CAPABILITY_LABEL
END
CREATE TABLE [dbo].[D_CATEGORY_HIERARCHY] (
    [CATEGORY_ID]                 INT            IDENTITY (1, 1) NOT NULL,
    [CATEG<PERSON>YNAME]                VARCHAR (250)  NOT NULL,
    [FRIENDLY_NAME]               VARCHAR (250)  NULL,
    [CATEGORY_DESC]               NVARCHAR (MAX) NULL,
    [PAGE_TITLE]                  NVARCHAR (250) NULL,
    [META_KEYWORDS]               NVARCHAR (MAX) NULL,
    [META_DESC]                   NVARCHAR (MAX) NULL,
    [ITEM_CATEGORY_FULL_PATH]     VARCHAR (MAX)  NULL,
    [ITEM_CATEGORY_URL_PATH]      VARCHAR (MAX)  NULL,
    [ITEM_CATEGORY_URL_PATH_FULL] VARCHAR (MAX)  NULL,
    [PARENT_ID]                   INT            NOT NULL,
    [INVENTORY_ATTRIBUTE_TYPE_ID] INT            NULL,
    [CATEGORY_KEY]                VARCHAR (MAX)  NULL,
    [CATEGORY_LEVEL]              INT            NULL,
    [FOCUS_MATERIAL]              BIT            CONSTRAINT [DF_D_CATEGORY_HIERARCHY_FOCUS_MATERIAL] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]                 BIT            NOT NULL,
    [INSERTED_BY]                 VARCHAR (150)  NOT NULL,
    [INSERTED_DT]                 DATETIME       NOT NULL,
    [UPDATED_BY]                  VARCHAR (150)  NULL,
    [UPDATED_DT]                  DATETIME       NULL,
    [DELETED_BY]                  VARCHAR (150)  NULL,
    [DELETED_DT]                  DATETIME       NULL,
    [SellerCloudProductType]      VARCHAR (150)  NULL,
    [InsertedByUserId]            BIGINT         NULL,
    [InsertedByUserIp]            BIGINT         NULL,
    [UpdatedByUserId]             BIGINT         NULL,
    [UpdatedByUserIp]             BIGINT         NULL,
    [HddManagement]               BIT            CONSTRAINT [DF_D_CATEGORY_HddManagement] DEFAULT ((0)) NOT NULL,
    [CommodityCode]               VARCHAR (250)  NULL,
    [ExternalId]                  NVARCHAR (68)  NULL,
    [TaxCode]                     NVARCHAR (16)  NULL,
    CONSTRAINT [PK_D_CATEGORY_HIERARCHY] PRIMARY KEY CLUSTERED ([CATEGORY_ID] ASC),
    CONSTRAINT [FK_D_CATEGORY_HIERARCHY_C_INVENTORY_ATTRIBUTE_TYPE] FOREIGN KEY ([INVENTORY_ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[C_INVENTORY_ATTRIBUTE_TYPE] ([INVENTORY_ATTRIBUTE_TYPE_ID])
);










GO
CREATE NONCLUSTERED INDEX [IX_ATTRIBUTE_TYPE_ID]
    ON [dbo].[D_CATEGORY_HIERARCHY]([INVENTORY_ATTRIBUTE_TYPE_ID] ASC);
	
GO
CREATE NONCLUSTERED INDEX [IX_PARENT_ID]
    ON [dbo].[D_CATEGORY_HIERARCHY]([PARENT_ID] ASC)
    INCLUDE([CATEGORY_KEY]);

GO
CREATE NONCLUSTERED INDEX [IX_D_CATEGORY_HIERARCHY__IS_INACTIVE__INCLUDE__CATEGORY_ID__INVENTORY_ATTRIBUTE_TYPE_ID]
    ON [dbo].[D_CATEGORY_HIERARCHY]([IS_INACTIVE] ASC)
    INCLUDE([CATEGORY_ID], [INVENTORY_ATTRIBUTE_TYPE_ID], CATEGORYNAME);
	
GO
CREATE NONCLUSTERED INDEX [IX_LEVEL]
    ON [dbo].[D_CATEGORY_HIERARCHY]([CATEGORY_LEVEL] ASC)
    INCLUDE([CATEGORY_KEY]);
		
GO
CREATE NONCLUSTERED INDEX [IX_CATEGORY_ID_NAME]
    ON [dbo].[D_CATEGORY_HIERARCHY]([CATEGORY_ID] ASC, [CATEGORYNAME] ASC)
	INCLUDE(ITEM_CATEGORY_FULL_PATH);


GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_FILL_URL_PATH] ON  [dbo].[D_CATEGORY_HIERARCHY]
   AFTER INSERT
AS 
BEGIN
	
	SET NOCOUNT ON;
    
	UPDATE D_CATEGORY_HIERARCHY SET 	
		ITEM_CATEGORY_URL_PATH =
			LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CATEGORYNAME, 'Root Category|', ''), 'Resale|', ''), ' & ', ''), '|', '/'), ',', ''), ';', ''), ':', ''), ' ', ''), '"', ''), '&', '-')),
		ITEM_CATEGORY_URL_PATH_FULL =
			LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(ITEM_CATEGORY_FULL_PATH, 'Root Category|', ''), 'Resale|', ''), ' & ', ''), '|', '/'), ',', ''), ';', ''), ':', ''), ' ', ''), '"', ''), '&', '-')) + '.html'	
	WHERE [CATEGORY_ID] IN (SELECT CATEGORY_ID FROM INSERTED)

END

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The categories of the system, it stores like hierarchy', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'D_CATEGORY_HIERARCHY';


GO
-- =============================================
-- Create date: 10/21/2015
-- Description:	<Description,,>
-- =============================================
CREATE TRIGGER [dbo].[trg_CATEGORY_HIERARCHY_PUT_CHANGE]
    ON  [dbo].[D_CATEGORY_HIERARCHY]
    AFTER INSERT, UPDATE
AS 
BEGIN									

    declare @ids dbo.bigint_id_array

	insert into @ids
	select
		isnull(i.CATEGORY_ID, d.CATEGORY_ID)
	from inserted		i
	full join deleted	d
		on i.CATEGORY_ID = d.CATEGORY_ID
	where isnull(i.CATEGORYNAME, '') <> isnull(d.CATEGORYNAME, '')
		or isnull(i.IS_INACTIVE, 0) <> isnull(d.IS_INACTIVE, 0)
		or isnull(i.INVENTORY_ATTRIBUTE_TYPE_ID, 0) <> isnull(d.INVENTORY_ATTRIBUTE_TYPE_ID, 0)
	-- assuming that it is fast enough to update the records in elastic each time
    
	declare @invoker nvarchar(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)--,@Invoker = @invoker
	exec search.sp_SetEntityChanged
		@TypeId = 10 --MasterItemCategory
		,@EntityIds = @ids
		,@Invoker = @invoker

END
GO

GO
-- =============================================
-- Author:             
-- Author update:      
-- Create date:        
-- Update date:        
-- Reason for update:  
-- Description:	       
-- =============================================
CREATE TRIGGER [dbo].[trg_D_CATEGORY_HIERARCHY_AFTER_UPDATE]
    ON  [dbo].[D_CATEGORY_HIERARCHY]
    AFTER INSERT, UPDATE
AS 
BEGIN
		
	DECLARE
		 @PROCESS_CD NVARCHAR(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
	
	DECLARE @logData TABLE
	(
		[SOURCE]				NVARCHAR(250)
		,[USER_ID]				BIGINT
		,[USER_IP]				BIGINT
		,[OPERATION_NAME]		NVARCHAR(50)
		,[ENTITY_TYPE_ID]		BIGINT
		,[ENTITY_KEY_VALUE]		BIGINT
		,[ENTITY_AUTO_NAME]		NVARCHAR(250)
		,[CHANGES]				NVARCHAR(MAX)
	)

	INSERT INTO @logData
	SELECT @PROCESS_CD
               ,NEW.[USER_ID]
               ,NEW.[USER_IP]
               ,NEW.[OPERATION_NAME]
               ,18 --N'Master Item category add',
               ,NEW.[ENTITY_KEY_VALUE]
               ,NEW.[ENTITY_AUTO_NAME]
	           ,[dbo].[fn_str_STRIP_XML_TAGS]((SELECT NEW.category_name, NEW.parent_name                                                
	                                          FOR XML PATH('ROOT'), TYPE, ELEMENTS ABSENT))		AS [CHANGES]
           FROM (
                     SELECT -- Fixed columns		  
                            
							case
								when D.CATEGORY_ID is null then 'Inserted'	
								when I.IS_INACTIVE = 1 then 'Deleted'										
								else N'Updated'													
							end AS [OPERATION_NAME]
                           ,ISNULL(I.[UpdatedByUserId], I.[InsertedByUserId])			AS [USER_ID]
                           ,ISNULL(I.[UpdatedByUserIp], I.[InsertedByUserIp])			AS [USER_IP]
                           ,ISNULL(I.[CATEGORY_ID], D.[CATEGORY_ID])					AS [ENTITY_KEY_VALUE]
                           ,ch.ITEM_CATEGORY_FULL_PATH									AS [ENTITY_AUTO_NAME]
                           -- XML columns														
                           ,case 
						   when I.IS_INACTIVE = 1 then 'Category ' + isnull(D.[CATEGORYNAME], '') + ' was deleted'
						   else 
						   case
							when D.[CATEGORYNAME] is null then '' 
							else N'Category was ' + isnull(D.[CATEGORYNAME], '') 
						   end + 
						   ' Category is ' + isnull(I.[CATEGORYNAME], '')			
						   end				AS category_name   						  
						   ,case 
						   when I.IS_INACTIVE = 1 then '' 
						   else 
						   case
						    when chd.ITEM_CATEGORY_FULL_PATH is null then '' 
						    else N' Parent was ' + isnull(chd.ITEM_CATEGORY_FULL_PATH, '') 
						   end + 
						   ' Parent is ' + isnull(ch.ITEM_CATEGORY_FULL_PATH	, '')	end		AS parent_name                          													
                       FROM INSERTED                        AS I
                left JOIN DELETED                         AS D                   
					ON I.[CATEGORY_ID] = D.[CATEGORY_ID]
                left join [dbo].[D_CATEGORY_HIERARCHY]		ch with (nolock)
					on I.PARENT_ID = ch.[CATEGORY_ID]	
				left join [dbo].[D_CATEGORY_HIERARCHY]		chd with (nolock)
					on d.PARENT_ID = chd.[CATEGORY_ID]	
				where isnull(I.[CATEGORYNAME], '') <> isnull(D.[CATEGORYNAME], '')
					or isnull(I.[FRIENDLY_NAME], '') <> isnull(D.[FRIENDLY_NAME], '')
					or isnull(I.PARENT_ID, 0) <> isnull(D.PARENT_ID, 0)		
					or isnull(I.IS_INACTIVE, 0) <> isnull(D.IS_INACTIVE, 0)			
                ) NEW
    
    INSERT INTO F_LOG_DATA 	WITH(ROWLOCK)
	(	
                [SOURCE]
               ,[USER_ID]
               ,[USER_IP]
               ,[OPERATION_NAME]
               ,[ENTITY_TYPE_ID]
               ,[ENTITY_KEY_VALUE]
               ,[ENTITY_AUTO_NAME]
               ,[CHANGES])
         SELECT
				[SOURCE]
               ,[USER_ID]
               ,[USER_IP]
               ,[OPERATION_NAME]
               ,[ENTITY_TYPE_ID]
               ,[ENTITY_KEY_VALUE]
               ,[ENTITY_AUTO_NAME]
               ,[CHANGES]
		from @logData

END
GO

CREATE TABLE [dbo].[F_InboundOrderServiceCategories] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [ServiceTypeId]                             INT             NOT NULL,
    [CategoryId]                                INT             NOT NULL,
    [ActualQty]                                 INT             NULL,
    [ExpectedQty]                               INT             NULL,
    [Notes]                                     NVARCHAR (MAX)  NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderServiceCategories_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderServiceCategories_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceCategories_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderServiceCategories_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderServiceCategories] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderServiceCategories_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceCategories_C_RECYCLING_ITEM_SERVICE_TYPE] FOREIGN KEY ([ServiceTypeId]) REFERENCES [dbo].[C_RECYCLING_ITEM_SERVICE_TYPE] ([SERVICE_TYPE_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderServiceCategories_D_CATEGORY_HIERARCHY] FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderServiceCategories]([RecyclingOrderId])
    INCLUDE([ServiceTypeId], [CategoryId]);
GO

CREATE NONCLUSTERED INDEX [IX_ServiceTypeId]
    ON [dbo].[F_InboundOrderServiceCategories]([ServiceTypeId])
    INCLUDE([RecyclingOrderId], [CategoryId]);
GO

CREATE NONCLUSTERED INDEX [IX_CategoryId]
    ON [dbo].[F_InboundOrderServiceCategories]([CategoryId])
    INCLUDE ([RecyclingOrderId], [ServiceTypeId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Categories for an Service Type in Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderServiceCategories';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_ServiceTypeId_CategoryId]
    ON [dbo].[F_InboundOrderServiceCategories]([RecyclingOrderId] ASC, [ServiceTypeId] ASC, [CategoryId] ASC);
       
GO
CREATE UNIQUE NONCLUSTERED INDEX [UX_RecyclingOrderId_ServiceTypeId_CategoryId]
    ON [dbo].[F_InboundOrderServiceCategories] ([RecyclingOrderId], [ServiceTypeId], [CategoryId]);


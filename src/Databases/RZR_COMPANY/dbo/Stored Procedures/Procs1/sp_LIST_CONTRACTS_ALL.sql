-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 09/04/2014
-- Description:	list all contacts
-- =============================================
CREATE PROCEDURE sp_LIST_CONTRACTS_ALL
	@TERM		 NVARCHAR(MAX)	= N''
	,@COUNT		 INT			= 20
AS
BEGIN
	SET @TERM = LTRIM(RTRIM(@TERM))
		
	SELECT TOP(@COUNT)
		C.CONTRACT_ID										AS VALUE,
		[dbo].[fn_str_GET_CONTRACT_ALIAS](C.CONTRACT_ID)	AS LABEL
	FROM F_CONTRACT		C	WITH(NOLOCK)
	WHERE C.STATUS_ID != 3 --  not templates
		AND (C.AUTO_NAME LIKE @TERM 
		OR C.CONTRACT_NAME LIKE @TERM
		OR [dbo].[fn_str_GET_CONTRACT_ALIAS](C.CONTRACT_ID) LIKE @TERM)
	ORDER BY AUTO_NAME ASC
END
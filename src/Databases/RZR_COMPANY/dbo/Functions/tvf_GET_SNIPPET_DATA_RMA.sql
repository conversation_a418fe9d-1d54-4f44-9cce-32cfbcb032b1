CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_RMA]
  (
    @C_RMA_ID BIGINT
  )
RETURNS @T_RESULT TABLE
(
  RMA_ID	BIGINT,
  STATUS_ID	INT,
  [USER_ID] BIGINT,
  CreateDate Date
)
AS
BEGIN
    INSERT INTO @T_RESULT
      (
        RMA_ID,
        STATUS_ID,
		[USER_ID],
        CreateDate
      )
    SELECT
		SOR.RMA_ID,
        SOR.RMA_STATUS_ID,
		SOR.MODIFIER_USER_ID,
        SOR.INSERTED_DT
    FROM dbo.F_SALES_ORDER_RMA		SOR  WITH (NOLOCK)
    WHERE SOR.RMA_ID = @C_RMA_ID

    RETURN;
END
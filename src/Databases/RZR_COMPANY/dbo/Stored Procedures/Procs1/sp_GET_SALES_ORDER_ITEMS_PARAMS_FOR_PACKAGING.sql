-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <10/02/2013>
-- Description: <Gets sales order items parameters, required for packaging>
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_ITEMS_PARAMS_FOR_PACKAGING] 
     @SALES_ORDER_ITEM_IDS	bigint_ID_ARRAY READONLY
	,@ORDER_TYPE_ID			INT = 1
AS
BEGIN
   

	SELECT 
		NULL													AS PackageId,
		NULL													AS RowId,
		SI.SALES_ORDER_ITEM_ID									AS SalesOrderItemId,
		LTRIM(RTRIM(I.ITEM_INVENTORY_UNIQUE_ID))				AS UniqueId,
		LTRIM(RTRIM(I.ITEM_INVENTORY_SERIAL))					AS Serial,
		I.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED					AS Qty,
		C.ITEM_CONDITION_DESC									AS Condition,
		dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(ISNULL(I.LOCATION_ID, I.LOCATION_PREV_ID)) AS [Location],
		ISNULL(IMD.DIMENSION_LENGTH,     0.00) 	    AS [Length],
		ISNULL(IMD.DIMENSION_WIDTH,      0.00)	    AS Width,
		ISNULL(IMD.DIMENSION_HEIGHT,     0.00)	    AS Height,
		ISNULL(IMD.DIMENSION_WEIGHT,	 0.00)		AS [Weight],
		SI.ITEM_PRICE											AS Price
	FROM @SALES_ORDER_ITEM_IDS			IDS
	INNER JOIN	F_SALES_ORDER_ITEM		SI  WITH(NOLOCK)
		ON IDS.ID = SI.SALES_ORDER_ITEM_ID
	INNER JOIN [dbo].[F_PRODUCT_MASTER] PM	WITH (NOLOCK)
		ON SI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
	INNER JOIN F_ITEM_INVENTORY			I   WITH(NOLOCK)
		ON I.ITEM_INVENTORY_ID = SI.ITEM_INVENTORY_ID
		
	LEFT JOIN dbo.F_ITEM				FI  WITH(NOLOCK)
		ON FI.ITEM_ID = I.ITEM_ID						
	LEFT JOIN [dbo].[F_DIMENSION]		IMD	WITH(NOLOCK)
		ON IMD.DIMENSION_ID = FI.DIMENSION_ID	

	LEFT JOIN D_ITEM_CONDITION			C   WITH(NOLOCK)
		ON C.ITEM_CONDITION_ID = I.CONDITION_ID
	WHERE SI.ITEM_INVENTORY_ID IS NOT NULL 
		AND SI.IS_DELETED  = 0 
		AND SI.IS_INACTIVE = 0 
		
END
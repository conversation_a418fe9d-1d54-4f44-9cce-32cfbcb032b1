

CREATE PROCEDURE [dbo].[sp_GET_SALES_PAYMENT_INVOICE_ITEMS]
    @INVOICE_IDS			bigint_ID_ARRAY READONLY    
AS
BEGIN	

    SELECT 
	   poi.SALES_ORDER_ITEM_ID				PurchaseOrderItemId,
	   INV.ID								InvoiceId,
	   PO.SALES_ORDER_NUMBER				PurchaseOrderAutoName,
	   ISNULL(RO.AUTO_NAME, OO.AUTO_NAME)	InboundOrderAutoName,
	   PO.SALES_ORDER_DATE					OrderDate,
	   CUST.CUSTOMER_NAME					CustomerName,
	   CP.CHECK_NUMBER						CheckNumber,
	   CP.CUSTOMER_PAYMENT_ID				CustomerPaymentId,
	   CP.PAYMENT_DATE						PaymentDate,
	   CIPT.INVOICE_PAYMENT_TYPE_NAME		PaymentMethod,
	   TT.TRANSACTION_TERM_VALUE		    TermsName,
	   CASE
		  WHEN poi.RECYCLING_ORDER_ITEM_ID IS NULL THEN
			 CASE
				WHEN ROIM.[CommodityId] IS NULL THEN (SELECT st.SERVICE_TYPE_CD FROM dbo.C_RECYCLING_ITEM_SERVICE_TYPE st WHERE st.SERVICE_TYPE_ID = pm.[SERVICE_MASTER_ID])
				ELSE (SELECT RECYCLING_ITEM_MASTER_NAME FROM dbo.F_RECYCLING_ITEM_MASTER WITH (NOLOCK) WHERE RECYCLING_ITEM_MASTER_ID = ROIM.[CommodityId])
			 END						
		  ELSE im.RECYCLING_ITEM_MASTER_NAME
	   END									ItemNumber,		
	   NULL									Mfg,	
	   poi.RECEIVED_PRICE					Price,
	   poi.ITEM_QUANTITY					Qty,
	   poi.ITEM_TITLE					    Descr,
	   PT.PRICE_TYPE_DESC					PriceTypeDesc,		
	   CASE
		  WHEN poi.RECYCLING_ORDER_ITEM_ID IS NULL THEN 1
		  ELSE 0
	   END									IsService,
	   poi.IS_FLAT_FEE						IsFlatFee,
	   NULL									Sku,
	   NULL									Serial		
    FROM dbo.F_SALES_ORDER_ITEM					 poi	WITH (NOLOCK)	
    INNER JOIN F_SALES_ORDER					 PO	WITH (NOLOCK)
	   ON poi.SALES_ORDER_ITEM_ID = po.SALES_ORDER_ID
	INNER JOIN dbo.F_INVOICE as I WITH(NOLOCK)
		ON I.ORDER_ID = po.SALES_ORDER_ID
    INNER JOIN @INVOICE_IDS						 INV
	   ON I.INVOICE_ID = INV.ID	
    INNER JOIN [dbo].[F_INVOICE_PAYMENT]			 IP	WITH (NOLOCK)
	   ON INV.ID = IP.INVOICE_ID
    INNER JOIN [dbo].[F_CUSTOMER_PAYMENT]			 CP	WITH (NOLOCK)
	   ON IP.PAYMENT_ID = CP.CUSTOMER_PAYMENT_ID
	  AND CP.TYPE_BY_ACCOUNT_TYPE_ID = 2 --AR
	INNER JOIN C_INVOICE_PAYMENT_TYPE				CIPT WITH(NOLOCK)
	  ON CIPT.INVOICE_PAYMENT_TYPE_ID = CP.PAYMENT_TYPE_ID
    
    INNER JOIN F_CUSTOMER						 CUST WITH(NOLOCK)
	   ON PO.CUSTOMER_ID = CUST.CUSTOMER_ID
    INNER JOIN [dbo].[F_PRODUCT_MASTER]				 pm	WITH (NOLOCK)
	   ON pm.PRODUCT_MASTER_TYPE_ID IN (2, 3)
	   AND poi.[PRODUCT_MASTER_ID] = pm.[PRODUCT_MASTER_ID]
    LEFT JOIN F_RECYCLING_ORDER_INBOUND				 RO	WITH(NOLOCK)
	   ON RO.RECYCLING_ORDER_ID = PO.RECYCLING_ORDER_ID
    LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			 OO	WITH (NOLOCK)
		ON PO.RECYCLING_ORDER_ID = OO.RECYCLING_ORDER_ID
    LEFT JOIN C_CUSTOMER_TRANSACTION_TERM			 TT	WITH (NOLOCK)
	   ON TT.CUSTOMER_TRANSACTION_TERM_ID = PO.TERM_ID
    LEFT JOIN dbo.F_RECYCLING_ORDER_ITEM			 oi	WITH (NOLOCK)
	   ON poi.RECYCLING_ORDER_ITEM_ID = oi.RECYCLING_ORDER_ITEM_ID
    LEFT JOIN 	dbo.F_RECYCLING_ITEM_MASTER			 im	WITH (NOLOCK)
	   ON oi.RECYCLING_ITEM_MASTER_ID = im.RECYCLING_ITEM_MASTER_ID							
    LEFT JOIN 	dbo.C_RECYCLING_PRICE_TYPE			 PT	WITH (NOLOCK)																									
	   ON POI.PRICE_TYPE_ID = PT.PRICE_TYPE_ID
    LEFT JOIN [recycling].[F_CommodityRule]		 ROIM WITH (NOLOCK) 
	   ON POI.[CommodityRuleId] = ROIM.[Id]									
    UNION ALL
    SELECT 
	   SI.SALES_ORDER_ITEM_ID					PurchaseOrderItemId,
	   INV.INVOICE_ID						    InvoiceId,
	   SO.SALES_ORDER_NUMBER				    PurchaseOrderAutoName,
	   ISNULL(RO.AUTO_NAME, OO.AUTO_NAME)		InboundOrderAutoName,
	   SO.SALES_ORDER_DATE					    OrderDate,
	   CUST.CUSTOMER_NAME						CustomerName,
	   CP.CHECK_NUMBER							CheckNumber,
	   CP.CUSTOMER_PAYMENT_ID					CustomerPaymentId,
	   CP.PAYMENT_DATE							PaymentDate,		
	   CIPT.INVOICE_PAYMENT_TYPE_NAME			PaymentMethod,
	   TT.TRANSACTION_TERM_VALUE				TermsName,
	   CASE
		  WHEN PM.[ITEM_MASTER_ID] IS NULL THEN
			 CASE
				WHEN ROIM.[CommodityId] IS NULL THEN (SELECT st.SERVICE_TYPE_CD FROM dbo.C_RECYCLING_ITEM_SERVICE_TYPE st WHERE st.SERVICE_TYPE_ID = pm.[SERVICE_MASTER_ID])
				ELSE (SELECT RECYCLING_ITEM_MASTER_NAME FROM dbo.F_RECYCLING_ITEM_MASTER WITH (NOLOCK) WHERE RECYCLING_ITEM_MASTER_ID = ROIM.[CommodityId])
			 END					
		  ELSE im.ITEM_NUMBER
	   END										ItemNumber,		
	   m.MANUFACTURER_CD						Mfg,	
	   SI.RECEIVED_PRICE						Price,
	   SI.ITEM_QUANTITY							Qty,	
	   SI.ITEM_TITLE							Descr,
	   'Unit'									PriceTypeDesc,					
	   CASE
		  WHEN pm.[PRODUCT_MASTER_TYPE_ID] = 2 THEN 1
		  ELSE 0
	   END										IsService,
	   SI.IS_FLAT_FEE							IsFlatFee,	
	   i.ITEM_ID								Sku,
	   null as	 Serial
	FROM F_INVOICE							  INV	 WITH(NOLOCK)
	INNER JOIN F_SALES_ORDER_ITEM			  SI	 WITH(NOLOCK)
		ON INV.ORDER_ID = SI.SALES_ORDER_ID
	INNER JOIN F_SALES_ORDER				  SO	 WITH (NOLOCK)
		ON SI.SALES_ORDER_ID = SO.SALES_ORDER_ID
	LEFT JOIN [dbo].[F_INVOICE_PAYMENT]		  IP	 WITH (NOLOCK)
	    ON INV.INVOICE_ID = IP.INVOICE_ID
	LEFT JOIN [dbo].[F_CUSTOMER_PAYMENT]	  CP	 WITH (NOLOCK)
	    ON IP.PAYMENT_ID = CP.CUSTOMER_PAYMENT_ID
	   AND CP.TYPE_BY_ACCOUNT_TYPE_ID = 2 --AR
	INNER JOIN C_INVOICE_PAYMENT_TYPE			CIPT WITH(NOLOCK)
	  ON CIPT.INVOICE_PAYMENT_TYPE_ID = CP.PAYMENT_TYPE_ID
	LEFT JOIN dbo.F_ITEM_INVENTORY			  ii	 WITH (NOLOCK)
	   ON SI.ITEM_INVENTORY_ID = ii.ITEM_INVENTORY_ID
	LEFT JOIN dbo.F_ITEM					  i		 WITH (NOLOCK)
	   ON ii.ITEM_ID = i.ITEM_ID	
	LEFT JOIN F_CUSTOMER					  CUST	 WITH(NOLOCK)
	    ON SO.CUSTOMER_ID = CUST.CUSTOMER_ID
	LEFT JOIN F_RECYCLING_ORDER_ITEM	RI	WITH(NOLOCK)
		ON SI.RECYCLING_ORDER_ITEM_ID = RI.RECYCLING_ORDER_ITEM_ID
	LEFT JOIN F_RECYCLING_ITEM_MASTER   MI	WITH(NOLOCK)
		ON RI.RECYCLING_ITEM_MASTER_ID = MI.RECYCLING_ITEM_MASTER_ID
	LEFT JOIN [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
		ON SI.PRODUCT_MASTER_ID = PM.PRODUCT_MASTER_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND			RO	 WITH(NOLOCK)
	    ON RO.RECYCLING_ORDER_ID = SO.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND		OO	 WITH (NOLOCK)
		ON SO.RECYCLING_ORDER_ID = OO.RECYCLING_ORDER_ID
	LEFT JOIN C_CUSTOMER_TRANSACTION_TERM		TT	 WITH (NOLOCK)
	    ON TT.CUSTOMER_TRANSACTION_TERM_ID = SO.TERM_ID
	LEFT JOIN dbo.F_ITEM_MASTER					im	 WITH (NOLOCK)	
	    ON PM.[ITEM_MASTER_ID] = im.ITEM_MASTER_ID
	LEFT JOIN dbo.D_MANUFACTURER				m	 WITH (NOLOCK)	
	    ON im.MANUFACTURER_ID = m.MANUFACTURER_ID
	LEFT JOIN F_ITEM_MASTER IMR WITH (NOLOCK)
		ON PM.[PRODUCT_MASTER_TYPE_ID] = 1 AND PM.ITEM_MASTER_ID = IMR.ITEM_MASTER_ID
	LEFT JOIN C_RECYCLING_ITEM_SERVICE_TYPE		ST	 WITH(NOLOCK)
		ON PM.SERVICE_MASTER_ID = ST.SERVICE_TYPE_ID
	LEFT JOIN [recycling].[F_CommodityRule] ROIM WITH (NOLOCK) 
		ON SI.[CommodityRuleId] = ROIM.[Id]
	WHERE INV.INVOICE_ID IN (SELECT ID FROM @INVOICE_IDS)
	
END
CREATE PROCEDURE [dbo].[sp_GetAttribute<PERSON>et<PERSON>ttriubtesFlat]
AS
BEGIN

     SELECT DISTINCT
		 FATICT.INVENTORY_ATTRIBUTE_TYPE_ID				AS AttributeSetId
		,CIAT.INVENTORY_ATTRIBUTE_NAME					AS AttributeSetName
        ,FATICT.INVENTORY_CAPABILITY_TYPE_ID			AS TypeId
		,CICT.INVENTORY_CAPABILITY_LABEL				AS Caption
		,FATICT.ITEM_INVENTORY_ORDINATION				AS Ordination
     FROM F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE	FATICT	WITH (NOLOCK)
     INNER JOIN C_INVENTORY_CAPABILITY_TYPE				CICT	WITH (NOLOCK)
       ON  CICT.INVENTORY_CAPABILITY_TYPE_ID = FATICT.INVENTORY_CAPABILITY_TYPE_ID
	   AND CICT.IS_DELETED = 0
	   AND CICT.IS_INACTIVE = 0
	   AND CICT.IsSystem = 0 
	   AND FATICT.IS_DELETED = 0
	   AND FATICT.IS_INACTIVE = 0
	 INNER JOIN C_INVENTORY_ATTRIBUTE_TYPE				CIAT	WITH(NOLOCK)
	   ON CIAT.INVENTORY_ATTRIBUTE_TYPE_ID = FATICT.INVENTORY_ATTRIBUTE_TYPE_ID   
	   AND CIAT.IS_DELETED = 0
	   AND CIAT.IS_INACTIVE = 0
	 ORDER BY 
		AttributeSetName
		,Ordination

END

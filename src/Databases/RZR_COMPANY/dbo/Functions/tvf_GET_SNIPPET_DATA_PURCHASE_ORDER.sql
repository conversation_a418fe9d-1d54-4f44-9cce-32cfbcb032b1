CREATE FUNCTION [dbo].[tvf_GET_SNIPPET_DATA_PURCHASE_ORDER]
  (
    @C_PURCHASE_ORDER_ID BIGINT
  )
RETURNS @T_RESULT TABLE
(
  PURCHASE_ORDER_ID			BIGINT,
  PURCHASE_ORDER_AUTO_NAME	VARCHAR(250),
  [USER_ID]					BIGINT,
  CUSTOMER_ID				BIGINT,
  IS_RECYCLING				BIT,
  PURCHASE_ORDER_STATUS_CD	NVARCHAR(64),
  REFERENCE					NVARCHAR(250),
  VENDOR_NAME				VARCHAR(150),
  PURCHASE_ORDER_REP_NAME	VARCHAR(256)
)
AS
BEGIN
    INSERT INTO @T_RESULT
      (
        PURCHASE_ORDER_ID,
        PURCHASE_ORDER_AUTO_NAME,
		[USER_ID],
		CUSTOMER_ID,
		IS_RECYCLING,
		PURCHASE_ORDER_STATUS_CD,
		R<PERSON><PERSON>ENCE,
		VENDOR_NAME,
		PURCHASE_ORDER_REP_NAME
      )
    SELECT
      PO.PURCHASE_ORDER_ID,
	  PO.AUTO_NAME,
	  PO.[USER_ID],
	  PO.CUSTOMER_ID,
	  CASE  
			WHEN (PO.STATUS_ID = 2 OR PO.STATUS_ID = 3 OR PO.STATUS_ID = 6) -- PartiallyReceived, Received, AwaitingConfirm
			THEN CASE WHEN (PO.RECYCLING_ORDER_ID IS NULL)
					  THEN 0
					  ELSE 1
				 END
			ELSE 0
	  END	AS IS_RECYCLING,
	  POS.PURCHASE_ORDER_STATUS_CD,
	  PO.REFERENCE,
	  C.CUSTOMER_NAME,
	  U.LastName + ' ' + U.FirstName
    FROM dbo.F_PURCHASE_ORDER		   PO WITH (NOLOCK)
	INNER JOIN C_PURCHASE_ORDER_STATUS POS	WITH (NOLOCK)
		ON POS.PURCHASE_ORDER_STATUS_ID = PO.STATUS_ID
	LEFT JOIN F_CUSTOMER			   C WITH (NOLOCK)
		ON PO.CUSTOMER_ID = C.CUSTOMER_ID
	LEFT JOIN tb_User				   U WITH (NOLOCK)
		ON PO.[USER_ID] = U.UserID
    WHERE PO.PURCHASE_ORDER_ID = @C_PURCHASE_ORDER_ID

    RETURN;
END
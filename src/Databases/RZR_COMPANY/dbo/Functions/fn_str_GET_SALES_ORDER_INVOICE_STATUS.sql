-- =============================================
-- Author:		<Oleg K. Evseev>
-- Create date: <09/27/2013>
-- Description:	<Returns the status of the last sales order invioce>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_GET_SALES_ORDER_INVOICE_STATUS]
	(@SALES_ORDER_ID BIGINT)
RETURNS NVARCHAR(50)
WITH SCHEMABINDING
BEGIN

	RETURN
		ISNULL(
			(SELECT TOP(1)				
				--CASE
				--	WHEN FI.INVOICE_ID IS NULL 
				--		THEN 'None'
				--	WHEN ISNULL(FI.IS_VOIDED, 0) = 1 
				--		THEN 'Voided'
				--	WHEN FI.STATUS_ID <= 3 OR FI.STATUS_ID = 5
				--		THEN CSOIS.STATUS_NAME			
				--	WHEN FI.STATUS_ID = 4 
				--	AND FI.DATE_CREATED IS NOT NULL 
				--	AND DATEDIFF(Day, FI.DATE_CREATED, GETUTCDATE()) <= 
				--		(
				--			SELECT
				--				CCTT.DUE_DAYS 
				--			FROM dbo.F_SALES_ORDER						FSO WITH (NOLOCK) 
				--			INNER JOIN  dbo.C_CUSTOMER_TRANSACTION_TERM	CCTT WITH(NOLOCK)
				--				ON FSO.TERM_ID = CCTT.CUSTOMER_TRANSACTION_TERM_ID
				--			WHERE FSO.SALES_ORDER_ID = @SALES_ORDER_ID
				--		)
				--		THEN 'Current'
				--	ELSE 'Open'			
				--END
				
				CASE
					WHEN FI.IS_VOIDED = 1 
						THEN 'Voided'

					-- open/current is a due status, not "CURRENT" from C_SALES_ORDER_INVOICE_STATUS
					ELSE CSOIS.STATUS_NAME
				END
			
			FROM dbo.F_INVOICE								FI		WITH(NOLOCK)				
			LEFT JOIN dbo.C_SALES_ORDER_INVOICE_STATUS		CSOIS	WITH(NOLOCK)
				ON FI.STATUS_ID		 = CSOIS.INVOICE_STATUS_ID			
			WHERE FI.INVOICE_TYPE_ID = 1
			  AND ORDER_ID			 = @SALES_ORDER_ID

			ORDER BY INVOICE_ID DESC
		), 'None')
END
-- sp_GET_INVENTORY_CAPABILITIES_BY_ATTRIBUTE_TYPE 23
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_CAPABILITIES_BY_ATTRIBUTE_TYPE]
	@ATTRIBUTE_TYPE_ID INT,
	@isSystemReturn		bit = 0
AS
BEGIN

     SELECT DISTINCT
         FATICT.INVENTORY_CAPABILITY_TYPE_ID			AS TypeId
		,CICT.INVENTORY_CAPABILITY_LABEL				AS Caption
		,FATICT.IS_EDITABLE								AS Editable
		,FATICT.IS_REQUIRED								AS [Required]
		,FATICT.IS_SKU_EFFECTING						AS IsSkuEffecting
		,CICT.SRC_INVENTORY_ATTRIBUTE_TYPE_ID			AS SrcAttrSetId
		,CIAT.INVENTORY_ATTRIBUTE_NAME					AS SrcAttrSetName
		,FATICT.ITEM_INVENTORY_ORDINATION				AS Ordination
		,CICT.INVENTORY_CAPABILITY_IS_AVAILABLE			AS IsAvailable
     FROM F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE	FATICT	WITH (NOLOCK)
     INNER JOIN C_INVENTORY_CAPABILITY_TYPE				CICT	WITH (NOLOCK)
       ON CICT.INVENTORY_CAPABILITY_TYPE_ID = FATICT.INVENTORY_CAPABILITY_TYPE_ID
	   AND CICT.IS_DELETED = 0
	   AND CICT.IS_INACTIVE = 0	  
	 LEFT JOIN C_INVENTORY_ATTRIBUTE_TYPE				CIAT	WITH(NOLOCK)
	   ON CIAT.INVENTORY_ATTRIBUTE_TYPE_ID = CICT.SRC_INVENTORY_ATTRIBUTE_TYPE_ID	   
	   AND CIAT.IS_DELETED = 0
	   AND CIAT.IS_INACTIVE = 0
     WHERE FATICT.INVENTORY_ATTRIBUTE_TYPE_ID = @ATTRIBUTE_TYPE_ID
	   AND FATICT.IS_DELETED = 0
	   AND FATICT.IS_INACTIVE = 0
	   and (CICT.IsSystem = 0 or CICT.IsSystem = @isSystemReturn)

	ORDER BY Ordination
END
-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE FUNCTION [dbo].[fn_str_AUTO_NAME_OUTBOUND_ORDER]
()
RETURNS NVARCHAR(250)
AS
BEGIN
	DECLARE 
		@MAX_NUMBER		BIGINT,		
		@PREFIX		VARCHAR(10),
		@START		BIGINT
	
	SELECT TOP(1) 
		 @PREFIX = PREFIX_OUTBOUND_ORDER + N'-',
		 @START	= STARTING_NUMBER_OUTBOUND_ORDER - 1
	FROM U_SYSTEM_SETTINGS WITH (NOLOCK)
			
	SELECT 
		@MAX_NUMBER = MAX([dbo].[fn_bigint_GET_AUTO_NUMBER_VALUE](AUTO_NAME))
	FROM dbo.F_RECYCLING_ORDER_OUTBOUND WITH (TABLOCK HOLDLOCK)	
	
	RETURN (
		@PREFIX + CAST(ISNULL(@MAX_NUMBER, @START) + 1 AS VARCHAR(250))
	)
END
 
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_HARD_DRIVE_SHRED] 
	@ORDER_ID BIGINT
AS
BEGIN

	SELECT 
		 RS.RECYCLING_ORDER_ID
		,RS.IS_PROVIDED
		,R.IS_OFFSITE
		,R.ESTIMATED_COUNT
		,R.PRICE_PER_UNIT		AS PRICE
	FROM dbo.F_RECYCLING_ORDER_SERVICE RS WITH(NOLOCK)
	LEFT JOIN dbo.F_RECYCLING_HARD_DRIVE_SHRED	R	WITH(NOLOCK)
		ON RS.RECYCLING_ORDER_ID = R.RECYCLING_ORDER_ID
	WHERE RS.RECYCLING_ORDER_ID = @ORDER_ID AND RECYCLING_SERVICE_ID = 1
					
END
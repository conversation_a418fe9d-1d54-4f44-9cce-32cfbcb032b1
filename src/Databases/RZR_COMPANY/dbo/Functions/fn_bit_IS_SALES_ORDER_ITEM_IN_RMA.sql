-- =============================================
-- Author:	 
-- Create date: 
-- Description: must return 1 item having no RMA or an incomplete RMA
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IS_SALES_ORDER_ITEM_IN_RMA] 
(
    @SALES_ORDER_ITEM_ID BIGINT
)
RETURNS BIT
AS
BEGIN   
	-- 
    RETURN 
		ISNULL(
			(	SELECT TOP(1) 1
				FROM [dbo].[F_RMA_ITEM] rma WITH (NOLOCK)
				INNER JOIN [dbo].[F_SALES_ORDER_RMA] so WITH (NOLOCK)
					ON rma.[RMA_ID] = so.RMA_ID
				WHERE rma.[SALES_ORDER_ITEM_ID] = @SALES_ORDER_ITEM_ID
				  AND so.RMA_STATUS_ID IN (1, 2)
			), 0)

END
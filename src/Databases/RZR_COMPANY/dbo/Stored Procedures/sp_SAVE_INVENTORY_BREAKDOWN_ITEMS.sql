CREATE PROCEDURE [dbo].[sp_SAVE_INVENTORY_BREAKDOWN_ITEMS]
	@items BreakdownItem   READONLY,
	@parentItemInventoryId bigint,
	@USER_ID			   BIGINT = NULL,
	@IP					   BIGINT = NULL
AS
BEGIN

	DECLARE @SP_NAME NVARCHAR(250)	= N'sp_SAVE_INVENTORY_BREAKDOWN_ITEMS'
	DECLARE @UTC_NOW DATETIME		= GETUTCDATE()

    DECLARE @ITEMSCANNOTDELETE TABLE(
	   ITEMINVENTORYID	   BIGINT, 
	   OLDITEMINVENTORYID  BIGINT, 
	   ORDERI<PERSON>			   BIGINT, 
	   ORDERAUTONAME	   NVARCHAR(250), 
	   AD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ITEMID BIGINT, 
	   SERIAL			   NVARCHAR(250), 
	   ITEMNUMBER		   NVARCHAR(250),
	   MANUFACTURERCD	   VARCHAR(200),
	   HASADDONS		   BIT)

    DECLARE @ITEMSFORDELETE TABLE(ITEMID BIGINT)

    DECLARE @CUSTOMERID			BIGINT,
			@CUSTOMER_NAME		VARCHAR(250),
			@PURCHASE_ORDER_ID  BIGINT,
			@RESYCLING_ORDER_ID BIGINT,
			@UID				NVARCHAR(250)

    SET XACT_ABORT ON
    BEGIN TRAN

	   SELECT TOP 1 
			@CUSTOMERID			= II.CUSTOMER_ID,
			@CUSTOMER_NAME		= C.CUSTOMER_NAME,
			@PURCHASE_ORDER_ID  = POI.PURCHASE_ORDER_ID,
			@RESYCLING_ORDER_ID	= ISNULL(UI.RECYCLING_ORDER_ID, PO.RECYCLING_ORDER_ID),
			@UID				= II.ITEM_INVENTORY_UNIQUE_ID
	   FROM [dbo].[F_ITEM_INVENTORY]	II  WITH (NOLOCK)
	   LEFT JOIN F_CUSTOMER				C   WITH (NOLOCK)
			ON C.CUSTOMER_ID = II.CUSTOMER_ID
	   LEFT JOIN F_PURCHASE_ORDER_ITEM	POI WITH (NOLOCK)
			ON POI.INVENTORY_ITEM_ID = II.ITEM_INVENTORY_ID
	   LEFT JOIN F_GENERATED_UNIQUE_IDENTIFIER UI WITH(NOLOCK)
			ON UI.UNIQUE_IDENTIFIER_VALUE = II.ITEM_INVENTORY_UNIQUE_ID
	   LEFT JOIN F_PURCHASE_ORDER		PO  WITH (NOLOCK)
			ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID
	   WHERE ITEM_INVENTORY_ID = @parentItemInventoryId

	   IF(@RESYCLING_ORDER_ID IS NULL) BEGIN

			SELECT
				@RESYCLING_ORDER_ID = ISNULL(ROI.RECYCLING_ORDER_ID, OAH.[RecyclingOrderId])
			FROM F_ITEM_INVENTORY						II WITH(NOLOCK)
			LEFT JOIN F_RECYCLING_ORDER_ITEM ROI WITH(NOLOCK)
				ON ROI.RECYCLING_ORDER_ITEM_ID = II.RECYCLING_ORDER_ITEM_ID
			LEFT JOIN [recycling].F_Asset		OAI WITH(NOLOCK)
				ON OAI.Id = II.AssetId
			LEFT JOIN [recycling].[F_AuditSession]	OAH WITH(NOLOCK)
				ON OAH.[Id] = OAI.[AuditSessionId]
			WHERE II.ITEM_INVENTORY_ID = @parentItemInventoryId

	   END

	   /* FIND PARENT ORDERS */
	   INSERT INTO @ITEMSCANNOTDELETE (ITEMINVENTORYID, ORDERID, ORDERAUTONAME)
	   SELECT II.ITEM_INVENTORY_ID, SOI.SALES_ORDER_ID, SO.SALES_ORDER_NUMBER
	   FROM [dbo].[F_ITEM_INVENTORY]		AS II WITH (NOLOCK)
	   INNER JOIN [dbo].F_SALES_ORDER_ITEM	AS SOI WITH (NOLOCK)
		  ON SOI.ITEM_INVENTORY_ID =  II.ITEM_INVENTORY_ID
	   INNER JOIN [dbo].F_SALES_ORDER		AS SO  WITH (NOLOCK)
		  ON SO.SALES_ORDER_ID = SOI.SALES_ORDER_ID
	   WHERE II.PARENT_ITEM_INVENTORY_ID = @parentItemInventoryId 
		  AND II.[IS_DELETED] = 0
		  AND II.ITEM_INVENTORY_ID NOT IN (SELECT ITEMINVENTORYID FROM @items)

	   /* FIND PARENT ADDONS */
	   INSERT INTO @ITEMSCANNOTDELETE (ITEMINVENTORYID, ADDONTOINVENTORYITEMID, SERIAL, ITEMNUMBER, MANUFACTURERCD)
	   SELECT II.ITEM_INVENTORY_ID,  AII.ITEM_INVENTORY_ID , AII.ITEM_INVENTORY_SERIAL, IM.ITEM_NUMBER, M.MANUFACTURER_CD
	   FROM [dbo].[F_ITEM_INVENTORY] AS II WITH (NOLOCK)
	   INNER JOIN [dbo].F_ITEM_INVENTORY_INTERNAL AS IA  WITH (NOLOCK)
		  ON IA.ITEM_INVENTORY_ID = II.ITEM_INVENTORY_ID
	   INNER JOIN [dbo].[F_ITEM_INVENTORY] AS AII  WITH (NOLOCK)
		  ON AII.ITEM_INVENTORY_ID = IA.ITEM_INVENTORY_MAIN_ID
	   LEFT JOIN [dbo].F_ITEM_MASTER AS IM  WITH (NOLOCK)
		  ON IM.ITEM_MASTER_ID = AII.ITEM_MASTER_ID
	   LEFT JOIN [dbo].D_MANUFACTURER AS M  WITH (NOLOCK)
		  ON M.MANUFACTURER_ID = IM.MANUFACTURER_ID
	   WHERE II.PARENT_ITEM_INVENTORY_ID = @parentItemInventoryId 
		  AND II.[IS_DELETED] = 0
		  AND II.ITEM_INVENTORY_ID NOT IN (SELECT ITEMINVENTORYID FROM @items)

	   /* FIND CHILD ADDONS */
	   INSERT INTO @ITEMSCANNOTDELETE (ITEMINVENTORYID, HASADDONS)
	   SELECT II.ITEM_INVENTORY_ID, 1
	   FROM [dbo].[F_ITEM_INVENTORY] AS II WITH (NOLOCK)
	   INNER JOIN [dbo].F_ITEM_INVENTORY_INTERNAL AS IA  WITH (NOLOCK)
		  ON IA.ITEM_INVENTORY_MAIN_ID = II.ITEM_INVENTORY_ID
	   WHERE II.PARENT_ITEM_INVENTORY_ID = @parentItemInventoryId 
		  AND II.[IS_DELETED] = 0
		  AND II.ITEM_INVENTORY_ID NOT IN (SELECT ITEMINVENTORYID FROM @items)
	   GROUP BY II.ITEM_INVENTORY_ID

	   --PRINT('NOT DELETE')
	   --SELECT * FROM @ITEMSCANNOTDELETE

	   INSERT INTO @ITEMSFORDELETE (ITEMID)
	   SELECT  II.ITEM_INVENTORY_ID
	   FROM [dbo].[F_ITEM_INVENTORY] AS II WITH (NOLOCK)
	   WHERE II.PARENT_ITEM_INVENTORY_ID = @parentItemInventoryId 
		  AND II.[IS_DELETED] = 0
		  AND II.ITEM_INVENTORY_ID NOT IN (SELECT ITEMINVENTORYID FROM @items)
		  AND II.ITEM_INVENTORY_ID NOT IN (SELECT ITEMINVENTORYID FROM @ITEMSCANNOTDELETE)

	   --PRINT('DELETE')
	   --SELECT * FROM @ITEMSFORDELETE

	   -- Delete PO_Items that removing from Break Down window
	   DECLARE @PO_ITEM_IDS bigint_ID_ARRAY;
	   INSERT INTO @PO_ITEM_IDS (ID)
	   SELECT 
			POI.PURCHASE_ORDER_ITEM_ID
	   FROM F_PURCHASE_ORDER_ITEM POI WITH(NOLOCK)
	   LEFT JOIN F_ITEM_INVENTORY II  WITH(NOLOCK)
	   		ON II.ITEM_INVENTORY_ID = POI.INVENTORY_ITEM_ID
	   WHERE II.ITEM_INVENTORY_ID IN (SELECT ITEMID FROM @ITEMSFORDELETE)

	   EXEC sp_DEL_PURCHASE_ORDER_ITEMS
				@PURCHASE_ORDER_ITEM_IDS = @PO_ITEM_IDS,
				@DELETE_INVENTORY		 = 0,
				@USER_ID				 = @USER_ID,
				@IP						 = @IP
	   -- End deleting PO_Items

	   DECLARE @CURRENTITEMID BIGINT
	   WHILE((SELECT COUNT(ITEMID) FROM @ITEMSFORDELETE) > 0)
	   BEGIN
		  SELECT TOP 1 @CURRENTITEMID = ITEMID FROM @ITEMSFORDELETE

		  EXEC SP_DEL_INVENTORY_RECV
			 @INVENTORY_RECV_ID = @CURRENTITEMID
			,@USER_ID			= @USER_ID
			,@IP				= @IP
			,@USE_TRANSACTION	= 0

		  DELETE
		  FROM @ITEMSFORDELETE
		  WHERE ITEMID = @CURRENTITEMID 
	   END

	   UPDATE [dbo].[F_ITEM_INVENTORY] WITH (ROWLOCK) SET
		  ITEM_MASTER_ID						= II.ITEMMASTERID, 		
		  ITEM_INVENTORY_SERIAL					= II.SERIAL, 
		  ITEM_INVENTORY_UNIQUE_ID				= II.UNIQUEID,
		  ITEM_INVENTORY_NOTES					= II.[DESCRIPTION], 
		  ITEM_INVENTORY_UNIT_COST_ORIGINAL		= II.COST, 
		  ITEM_INVENTORY_CHILD_QTY_UNALLOCATED	= II.QTY, 
		  CONDITION_ID							= II.CONDITIONID,
		  LOCATION_ID							= II.LOCATIONID,
		  UPDATED_BY							= @SP_NAME,
		  UPDATED_DT							= @UTC_NOW,
		  UPDATED_BY_IP							= @IP,
		  MODIFIER_USER_ID						= @USER_ID
	   FROM @items AS II
	   WHERE II.ITEMINVENTORYID = ITEM_INVENTORY_ID

	   MERGE F_ITEM_INVENTORY WITH(ROWLOCK) AS TARGET
	   USING
	   (
	   SELECT 
		  ITEMINVENTORYID
		  ,ITEMMASTERID
		  ,MANUFACTURERID
		  ,SERIAL
		  ,[DESCRIPTION]
		  ,COST
		  ,QTY
		  ,CONDITIONID
		  ,LOCATIONID
	   FROM @items
	   WHERE ITEMINVENTORYID <= 0
	   ) AS SOURCE(
		  ITEMINVENTORYID
		  ,ITEMMASTERID
		  ,MANUFACTURERID
		  ,SERIAL
		  ,[DESCRIPTION]
		  ,COST
		  ,QTY
		  ,CONDITIONID
		  ,LOCATIONID
	   )
	   ON 0=1

	   WHEN NOT MATCHED BY TARGET THEN
	   INSERT  ( 
		  ITEM_STATUS_ID, 
		  AUDIT_STATUS_ID, 
		  ITEM_MASTER_ID, 		 
		  ITEM_INVENTORY_SERIAL,
		  ITEM_INVENTORY_UNIQUE_ID, 
		  ITEM_INVENTORY_NOTES, 
		  ITEM_INVENTORY_UNIT_COST_ORIGINAL, 
		  ITEM_INVENTORY_CHILD_QTY_UNALLOCATED, 
		  CONDITION_ID,
		  LOCATION_ID,
		  AUTHOR_USER_ID,
		  CUSTOMER_ID,
		  PARENT_ITEM_INVENTORY_ID,
		  INSERTED_BY,
		  INSERTED_DT)
	   VALUES(
		   1 -- UNALLOCATED
		  ,1 -- PENDING
		  ,SOURCE.ITEMMASTERID		
		  ,SOURCE.SERIAL
		  ,[dbo].[fn_str_GENERATE_INVENTORY_UID](@CUSTOMER_NAME)
		  ,SOURCE.[DESCRIPTION]
		  ,SOURCE.COST
		  ,SOURCE.QTY
		  ,SOURCE.CONDITIONID
		  ,SOURCE.LOCATIONID
		  ,@USER_ID
		  ,@CUSTOMERID
		  ,@parentItemInventoryId
		  ,@SP_NAME
		  ,@UTC_NOW
	   )
	   OUTPUT INSERTED.ITEM_INVENTORY_ID, SOURCE.ITEMINVENTORYID  INTO @ITEMSCANNOTDELETE (ITEMINVENTORYID, OLDITEMINVENTORYID);

	   INSERT INTO @ITEMSCANNOTDELETE (ITEMINVENTORYID, OLDITEMINVENTORYID)
	   SELECT ITEMINVENTORYID, ITEMINVENTORYID
	   FROM @items
	   WHERE ITEMINVENTORYID > 0

	   IF (@RESYCLING_ORDER_ID IS NOT NULL) BEGIN

			INSERT INTO [dbo].[F_GENERATED_UNIQUE_IDENTIFIER]
			    ([CUSTOMER_NAME]
			    ,[CUSTOMER_ID]
			    ,[RECYCLING_ORDER_ID]
			    ,[UNIQUE_IDENTIFIER_VALUE]
			    ,[INSERTED_DT])
			SELECT 
				@CUSTOMER_NAME,
				@CUSTOMERID,
				@RESYCLING_ORDER_ID,
				II.ITEM_INVENTORY_UNIQUE_ID,
				@UTC_NOW
			FROM @ITEMSCANNOTDELETE i
			LEFT JOIN F_ITEM_INVENTORY				  II WITH(NOLOCK)
					ON i.ITEMINVENTORYID = II.ITEM_INVENTORY_ID
			WHERE i.OLDITEMINVENTORYID < 0

	   END

	   -- Create child PO with items if parent inventory item include in PO
	   IF(@PURCHASE_ORDER_ID IS NOT NULL) BEGIN

			DECLARE @CHILD_PO_ID BIGINT;

			SELECT 
				@CHILD_PO_ID = PO.PURCHASE_ORDER_ID
			FROM F_PURCHASE_ORDER			PO  WITH(NOLOCK)
			LEFT JOIN F_PURCHASE_ORDER_ITEM	POI WITH(NOLOCK)
				ON POI.PURCHASE_ORDER_ID = PO.PURCHASE_ORDER_ID
			LEFT JOIN F_ITEM_INVENTORY		II  WITH(NOLOCK)
				ON II.ITEM_INVENTORY_ID = POI.INVENTORY_ITEM_ID
			WHERE PARENT_PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID
			AND II.PARENT_ITEM_INVENTORY_ID = @parentItemInventoryId

			IF(@CHILD_PO_ID IS NULL) BEGIN

				DECLARE @T TABLE (ID BIGINT)

				DECLARE
					@PO_CUSTOMER_ID			BIGINT,
					@PO_USER_ID				BIGINT,
					@PO_TERM_ID				INT,
					@PO_DATE				DATETIME,
					@PO_ISSUED_DT			DATETIME,
					@PO_CONFIRM_BY_DT		DATETIME,
					@PO_EST_DELIVERY_DT		DATETIME,
					@PO_NEED_BY_DT			DATETIME,
					@PO_SHIP_BY_DT			DATETIME,
					@PO_DELIVERY_DT			DATETIME,
					@PO_REFERENCE			NVARCHAR(250),
					@PO_COMMENTS			NVARCHAR(MAX),
					@PO_INTERNAL_COMMENTS	NVARCHAR(MAX),
					@PO_WAREHOUSE_ID		BIGINT,
					@PO_PROVIDER_ID			INT,
					@PO_SHIPPING_METHOD_ID	INT,
					@PO_WARRANTY_ID			INT,
					@PO_AUTO_NAME			NVARCHAR(250),
					@CurrencyExchangeId		BIGINT,
					@COUNT					INT

				SELECT
					@PO_CUSTOMER_ID			= CUSTOMER_ID
					,@PO_USER_ID			= [USER_ID]				
					,@PO_TERM_ID			= PO_TERM_ID				
					,@PO_DATE				= [DATE]			
					,@PO_ISSUED_DT			= ISSUED_DT			
					,@PO_CONFIRM_BY_DT		= CONFIRM_BY_DT		
					,@PO_EST_DELIVERY_DT	= EST_DELIVERY_DT		
					,@PO_NEED_BY_DT			= NEED_BY_DT			
					,@PO_SHIP_BY_DT			= SHIP_BY_DT			
					,@PO_DELIVERY_DT		= DELIVERY_DT			
					,@PO_REFERENCE			= REFERENCE			
					,@PO_COMMENTS			= CASE WHEN (COMMENTS IS NOT NULL)
												   THEN COMMENTS + ' UNIT ' + @UID
												   ELSE 'UNIT ' + @UID
											  END
					,@PO_INTERNAL_COMMENTS	= INTERNAL_COMMENTS	
					,@PO_WAREHOUSE_ID		= WAREHOUSE_ID		
					,@PO_PROVIDER_ID		= PROVIDER_ID			
					,@PO_SHIPPING_METHOD_ID	= SHIPPING_METHOD_ID	
					,@PO_WARRANTY_ID		= WARRANTY_ID			
					,@PO_AUTO_NAME			= AUTO_NAME
					,@CurrencyExchangeId	= CurrencyExchangeId
				FROM F_PURCHASE_ORDER WITH(NOLOCK)
				WHERE PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID

				SELECT 
					@COUNT = COUNT(PURCHASE_ORDER_ID)
				FROM F_PURCHASE_ORDER WITH(NOLOCK)
				WHERE PARENT_PURCHASE_ORDER_ID = @PURCHASE_ORDER_ID

				SELECT @PO_AUTO_NAME = @PO_AUTO_NAME + N'-' + 
					CASE WHEN @COUNT < 10 
						THEN N'0' 
						ELSE N''
					END + CAST((@COUNT + 1) AS VARCHAR(250))
					
				INSERT INTO @T
				EXEC [dbo].[sp_SET_PURCHASE_ORDER]
					@ORDER_ID			= -1,
					@CUSTOMER_ID		= @PO_CUSTOMER_ID,
					@USER_ID			= @PO_USER_ID,
					@PO_TERM_ID			= @PO_TERM_ID,
					@DATE				= @PO_DATE,
					@ISSUED_DT			= @PO_ISSUED_DT,
					@CONFIRM_BY_DT		= @PO_CONFIRM_BY_DT,
					--@EST_DELIVERY_DT	= @PO_EST_DELIVERY_DT,
					@NEED_BY_DT			= @PO_NEED_BY_DT,
					--@SHIP_BY_DT			= @PO_SHIP_BY_DT,
					--@DELIVERY_DT		= @PO_DELIVERY_DT,
					@REFERENCE			= @PO_REFERENCE,
					@COMMENTS			= @PO_COMMENTS,
					@INTERNAL_COMMENTS	= @PO_INTERNAL_COMMENTS,
					@WAREHOUSE_ID		= @PO_WAREHOUSE_ID,
					@PROVIDER_ID		= @PO_PROVIDER_ID,
					@SHIPPING_METHOD_ID	= @PO_SHIPPING_METHOD_ID,
					@WARRANTY_ID		= @PO_WARRANTY_ID,
					@STATUS_ID			= 3, -- Received
					@AUTO_NAME			= @PO_AUTO_NAME,
					@PARENT_PURCHASE_ORDER_ID	= @PURCHASE_ORDER_ID,
					@CURRENCY_EXCHANGE_ID		= @CurrencyExchangeId

				SELECT @CHILD_PO_ID = ID FROM @T

			END

			-- Adding Items to PO
			DECLARE @PURCHASE_ORDER_ITEM_ID	BIGINT,
					@II_ITEM_MASTER_ID	    BIGINT,
					@II_MANUFACTURER_ID		BIGINT,
					@II_SERIAL				NVARCHAR(200),
					@II_UNIQUE_ID			NVARCHAR(200),
					@II_DESCRIPTION			NVARCHAR(500),
					@II_COST				MONEY,
					@II_QTY					BIGINT,
					@II_CONDITION_ID		BIGINT,
					@MASTER_ITEM_ID			BIGINT

			DECLARE Items CURSOR LOCAL FORWARD_ONLY FOR
				SELECT
					ITEMINVENTORYID,
					OLDITEMINVENTORYID
				FROM @ITEMSCANNOTDELETE
			OPEN Items
			DECLARE 
				@ITEM_INVENTORY_ID		BIGINT,
				@OLD_ITEM_INVENTORY_ID	BIGINT	
			FETCH NEXT FROM Items INTO @ITEM_INVENTORY_ID, @OLD_ITEM_INVENTORY_ID
			WHILE @@FETCH_STATUS = 0
			BEGIN

				SELECT 
					@PURCHASE_ORDER_ITEM_ID = PURCHASE_ORDER_ITEM_ID
				FROM F_PURCHASE_ORDER_ITEM POI WITH(NOLOCK)
				WHERE INVENTORY_ITEM_ID = @ITEM_INVENTORY_ID

				SELECT
					@II_ITEM_MASTER_ID	= II.ITEM_MASTER_ID,					
					@II_SERIAL			= II.ITEM_INVENTORY_SERIAL,
					@II_UNIQUE_ID		= II.ITEM_INVENTORY_UNIQUE_ID,
					@II_DESCRIPTION		= II.ITEM_INVENTORY_NOTES,
					@II_COST			= II.ITEM_INVENTORY_UNIT_COST_ORIGINAL,
					@II_QTY				= II.ITEM_INVENTORY_CHILD_QTY_UNALLOCATED,
					@II_CONDITION_ID	= II.CONDITION_ID
				FROM F_ITEM_INVENTORY II WITH(NOLOCK)
				WHERE ITEM_INVENTORY_ID = @ITEM_INVENTORY_ID


				SELECT TOP(1) -- TOP(1) Because select without ProductCodeId
					@MASTER_ITEM_ID = PM.PRODUCT_MASTER_ID
				FROM [dbo].[F_PRODUCT_MASTER] PM WITH (NOLOCK)
				WHERE PM.PRODUCT_MASTER_TYPE_ID = 1 AND PM.ITEM_MASTER_ID = @II_ITEM_MASTER_ID

				IF(ISNULL(@OLD_ITEM_INVENTORY_ID, 0) < 0) BEGIN -- Create new PO_Item
									
					INSERT INTO F_PURCHASE_ORDER_ITEM
					(
						INVENTORY_ITEM_ID,
						PURCHASE_ORDER_ID,								
						PRODUCT_MASTER_ID,	
						PRODUCT_MASTER_TYPE_ID,
						PRICE,
						RECEIVED_PRICE,
						QTY,
						ITEM_CONDITION_ID,
						RECEIVE_STATUS_ID,	
						DESCR,			
						SERIAL,	
						UNIQUE_ID,
						INSERTED_BY,
						INSERTED_DT,
						INSERTED_BY_ID,
						INSERTED_BY_IP
					)
					VALUES
					(
						@ITEM_INVENTORY_ID,
						@CHILD_PO_ID,				
						@MASTER_ITEM_ID,	
						1,		
						@II_COST,
						@II_COST,
						@II_QTY,
						@II_CONDITION_ID,
						3, -- Received
						@II_DESCRIPTION,	
						@II_SERIAL,
						@II_UNIQUE_ID,		
						@SP_NAME,
						@UTC_NOW,
						@USER_ID,
						@IP
					)

					SELECT @PURCHASE_ORDER_ITEM_ID = SCOPE_IDENTITY()
          
				END
				ELSE BEGIN
				
					UPDATE 	F_PURCHASE_ORDER_ITEM WITH(ROWLOCK) SET	
						PRODUCT_MASTER_ID	= @MASTER_ITEM_ID,
						PRICE				= @II_COST,
						RECEIVED_PRICE		= @II_COST,
						QTY					= @II_QTY,
						ITEM_CONDITION_ID	= @II_CONDITION_ID,
						DESCR				= @II_DESCRIPTION,
						SERIAL				= @II_SERIAL,	
						UNIQUE_ID			= @II_UNIQUE_ID,
						UPDATED_BY			= @SP_NAME,
						UPDATED_DT			= @UTC_NOW,
						UPDATED_BY_ID		= @USER_ID,
						UPDATED_BY_IP		= @IP
					WHERE PURCHASE_ORDER_ITEM_ID = @PURCHASE_ORDER_ITEM_ID

				END
				
				-- Creating and updating of Invoice, don't want yet.
				--IF (NOT EXISTS(SELECT TOP(1) 1 
				--				FROM dbo.F_INVOICE I WITH (NOLOCK) 
				--				INNER JOIN dbo.F_PURCHASE_ORDER_ITEM_INVOICE II WITH (NOLOCK)
				--					ON I.INVOICE_ID = II.PURCHASE_ORDER_INVOICE_ID
				--				WHERE II.PURCHASE_ORDER_ITEM_ID = @PURCHASE_ORDER_ITEM_ID AND I.IS_VOIDED = 0 AND I.IS_ADDITIONAL = 0)) BEGIN
						
				--	EXEC [dbo].[sp_SET_PURCHASE_ORDER_INVOICE] @CHILD_PO_ID, @PURCHASE_ORDER_ITEM_ID, 0		
							
				--END
				--ELSE BEGIN
				--	EXEC [dbo].[sp_SET_PURCHASE_ORDER_INVOICE] @CHILD_PO_ID, @PURCHASE_ORDER_ITEM_ID, 1
				--END
	
			FETCH NEXT FROM Items INTO @ITEM_INVENTORY_ID, @OLD_ITEM_INVENTORY_ID
			END
			CLOSE Items
			DEALLOCATE Items		

	   END
	   -- End PO creating

  
      INSERT INTO F_LOG_DATA (SOURCE, USER_ID, USER_IP, OPERATION_NAME, ENTITY_TYPE_ID, ENTITY_KEY_VALUE, ENTITY_AUTO_NAME, CHANGES)
      SELECT 
        @SP_NAME				as SOURCE,
        @USER_ID				as USER_ID,
        @IP						as USER_IP,
        'Removed'				as OPERATION_NAME,
        9						as ENTITY_TYPE_ID,
        @parentItemInventoryId	as ENTITY_KEY_VALUE,
        ''						as ENTITY_AUTO_NAME,
        im.ManufacturerCd + ' ' + im.ItemNumber + ' Serial ' + im.Serial + ' was Removed from this unit' as CHANGES
      FROM @items im

  
	   DECLARE @ITEMS_COUNT INT
	   SELECT
			@ITEMS_COUNT = COUNT(1)
		FROM @items
	   UPDATE F_ITEM_INVENTORY WITH(ROWLOCK) SET
		  ITEM_STATUS_ID = CASE WHEN @ITEMS_COUNT > 0 
				THEN 10-- BREAKDOWN
				ELSE 1 --Unallocated
			END,
		  UPDATED_BY = @SP_NAME,
		  UPDATED_DT = @UTC_NOW,
		  UPDATED_BY_IP = @IP,
		  MODIFIER_USER_ID = @USER_ID
	   WHERE ITEM_INVENTORY_ID = @parentItemInventoryId

          

  
    COMMIT TRAN

    SELECT * FROM @ITEMSCANNOTDELETE

END
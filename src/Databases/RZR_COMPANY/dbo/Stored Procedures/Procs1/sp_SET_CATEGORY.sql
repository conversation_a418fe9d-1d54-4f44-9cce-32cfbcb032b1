CREATE PROCEDURE [dbo].[sp_SET_CATEGORY] 
	@CATEGORY_ID	BIGINT				 = null,	-- 1,    3
	@PARENT_ID		BIGINT				 = null,	--    2, 3
	@CATEGORY_NAME	VARCHAR(250),					-- 1, 2, 3
	@FRIENDLY_NAME	VARCHAR(250)		 = null,	-- 1, 2
	@IS_INACTIVE	BIT					 = 0,		-- 1, 2
	@FOCUS_MATERIAL BIT					 = 0,		-- 1, 2
	@ATTRIBUTE_TYPE_ID INT				 = null,
	@CATEGORY_DESC  NVARCHAR(MAX)		 = null,
	@PAGE_TITLE		NVARCHAR(250)		 = null,
	@META_KEYWORDS  NVARCHAR(MAX)		 = null,
	@META_DESC		NVARCHAR(MAX)		 = null,	
	@SellerCloudProductType	VARCHAR(150) = null,
	@HddManagement	BIT					 = 0,
	@IS_UPDATE_NAME	BIT					 = 0,		-- 1, 2, 3
	/*
		1 - for					(@IS_UPDATE_NAME == 1)
		2 - for create			(@IS_UPDATE_NAME != 1 & @CATEGORY_ID IS NULL)
		3 - for changing parent (@IS_UPDATE_NAME != 1 & @CATEGORY_ID IS NOT NULL)
	*/
	@CommodityCode			varchar(250)	= null,
	@ExternalId				varchar(68)		= null,
	@TaxCode				nvarchar(100)	= null,
	@UserId					bigint = 1,
	@UserIp					bigint = 1
AS
BEGIN
	SET @CATEGORY_NAME	= LTRIM(RTRIM(@CATEGORY_NAME))
	SET @FRIENDLY_NAME	= LTRIM(RTRIM(@FRIENDLY_NAME))
	SET @CATEGORY_DESC	= LTRIM(RTRIM(@CATEGORY_DESC))
	SET @PAGE_TITLE		= LTRIM(RTRIM(@PAGE_TITLE))
	SET @META_KEYWORDS	= LTRIM(RTRIM(@META_KEYWORDS))
	SET @META_DESC		= LTRIM(RTRIM(@META_DESC))
	SET @SellerCloudProductType = LTRIM(RTRIM(@SellerCloudProductType))
		
	DECLARE
		 @PROCESS_CD NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID)
		,@UTC_NOW	 DATETIME		= GETUTCDATE();
				
	SET XACT_ABORT ON
	BEGIN TRANSACTION

		IF (@IS_UPDATE_NAME = 1)
		BEGIN
			
			--print('updating names')
			UPDATE DBO.D_CATEGORY_HIERARCHY WITH(ROWLOCK) SET
				[CATEGORYNAME]				= @CATEGORY_NAME,
				[FRIENDLY_NAME]				= @FRIENDLY_NAME,
				CATEGORY_DESC				= @CATEGORY_DESC,
				PAGE_TITLE					= @PAGE_TITLE,
				META_KEYWORDS				= @META_KEYWORDS,
				META_DESC					= @META_DESC,
				IS_INACTIVE					= @IS_INACTIVE,
				FOCUS_MATERIAL				= @FOCUS_MATERIAL,
				INVENTORY_ATTRIBUTE_TYPE_ID = @ATTRIBUTE_TYPE_ID,
				UPDATED_DT					= @UTC_NOW,
				UPDATED_BY					= @PROCESS_CD,
				SellerCloudProductType		= @SellerCloudProductType,
				CommodityCode				= @CommodityCode,
				ExternalId					= @ExternalId,
				TaxCode					    = @TaxCode,
				[HddManagement]				= @HddManagement,
				[UpdatedByUserId]			= @UserId,
				[UpdatedByUserIp]			= @UserIp
			WHERE CATEGORY_ID = @CATEGORY_ID

		END
		ELSE BEGIN

			IF (ISNULL(@CATEGORY_ID, -1) <= 0) 
			BEGIN

				--print('creating the category')		
				INSERT INTO DBO.D_CATEGORY_HIERARCHY (
					[CATEGORYNAME],
					[FRIENDLY_NAME],
					CATEGORY_DESC,	
					PAGE_TITLE,		
					META_KEYWORDS,	
					META_DESC,		
					[ITEM_CATEGORY_FULL_PATH], 
					[PARENT_ID],
					[CATEGORY_KEY],
					[CATEGORY_LEVEL],
					IS_INACTIVE,
					FOCUS_MATERIAL,
					INVENTORY_ATTRIBUTE_TYPE_ID,
					[INSERTED_BY],
					[INSERTED_DT],
					SellerCloudProductType,
					CommodityCode,
					ExternalId,
					TaxCode,
					[HddManagement],
					[InsertedByUserId],
					[InsertedByUserIp]
				)
				SELECT
					@CATEGORY_NAME,
					@FRIENDLY_NAME,
					@CATEGORY_DESC,  
					@PAGE_TITLE,		
					@META_KEYWORDS,  
					@META_DESC,		
					'',
					@PARENT_ID,
					'',
					-1,
					@IS_INACTIVE,
					@FOCUS_MATERIAL,
					@ATTRIBUTE_TYPE_ID,
					@PROCESS_CD,
					@UTC_NOW,
					@SellerCloudProductType,
					@CommodityCode,
					@ExternalId,
					@TaxCode,
					@HddManagement,
					@UserId,
					@UserIp

				SET @CATEGORY_ID = SCOPE_IDENTITY()

			END
			ELSE BEGIN

				--print('updating the parent')
				UPDATE dbo.D_CATEGORY_HIERARCHY WITH(ROWLOCK) SET
					PARENT_ID					= @PARENT_ID
					,UPDATED_DT					= @UTC_NOW
					,UPDATED_BY					= @PROCESS_CD
					,[UpdatedByUserId]			= @UserId
					,[UpdatedByUserIp]			= @UserIp
				WHERE CATEGORY_ID = @CATEGORY_ID

			END

		END
				
		--print('rebuilding the path')
		EXEC dbo.sp_REBUILD_CATEGORY_PATH
			@CATEGORY_ID = @CATEGORY_ID
			,@USER_ID	 = @UserId
			,@USER_IP	 = @UserIp

		--print('rebuilding child paths')
		EXEC dbo.sp_REBUILD_CATEGORY_CHILD_PATHS
			@CATEGORY_ID = @CATEGORY_ID
			,@USER_ID	 = @UserId
			,@USER_IP	 = @UserIp

		-- Sync assets Attribute Set Id
		EXEC [recycling].[sp_SetAssetCorrectCategoryId]
			@CategoryId			= @CATEGORY_ID,
			@UserId				= @UserId,
			@UserIp				= @UserIp,
			@UseInternalTran	= 0,
			@OuterSpName		= @PROCESS_CD;

	COMMIT TRANSACTION

	SELECT @CATEGORY_ID AS CATEGORY_ID
					
END

-- Alter Procedure sp_GET_NOTIFICATIONS_CUSTOMER_CREATION_FROM_QUEUE
-- =============================================
-- Author:		I.Orobets
-- Create date: 06/08/2015
-- Description: Get Item not in Contract notifications from queue
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_COMMODITY_IS_NOT_IN_CONTRACT_FROM_QUEUE] 
	@COUNT		 INT = 40,
	@IS_ADD_ITEM BIT = 0
AS
BEGIN
    DECLARE @NOTIFICATION_TYPE_ID BIGINT = CASE
	   WHEN @IS_ADD_ITEM = 1 THEN 10	-- An Item is not in Contract Add
	   ELSE 11							-- An Item is not in Contract Remove
    END;

    DECLARE @IS_ALLOWED		     BIT    = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID);

	IF(@IS_ALLOWED = 0)
	BEGIN
	   UPDATE F_NOTIFICATION_QUEUE SET
		  IS_INACTIVE = 1,
		  UPDATED_BY  = N'sp_GET_NOTIFICATIONS_SETTLEMENT_COMPLETE_FROM_QUEUE: @IS_ALLOWED = 0',
		  UPDATED_DT  = GETUTCDATE()
	   WHERE IS_INACTIVE = 0
	     AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID 	
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP(@COUNT)
		  * 
	FROM (SELECT
		  NQ.NotificationQueueId		AS [NOTIFICATION_QUEUE_ID]
		  ,DC.*
		  ,DU.*
		  ,DO.*
		  ,DQ.*
		  ,DQC.*
		  ,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, O.[USER_ID], NULL, NULL, NQ.NotificationSetId, nq.CustomerId) AS EMAILS 	   
	   FROM @tNotifyItemWithNotifySet		NQ
	   CROSS APPLY tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
	   INNER JOIN F_RECYCLING_ORDER	O   WITH (NOLOCK)
	   	ON O.RECYCLING_ORDER_ID = QP.ORDER_ID
	   -- To filter out the orders being not under a contract
	   INNER JOIN F_RECYCLING_ORDER_CONTRACT ROC WITH(NOLOCK)
	     ON ROC.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID		
	   INNER JOIN F_CONTRACT			OC   WITH(NOLOCK)
	     ON OC.CONTRACT_ID = ROC.CONTRACT_ID
	   ------------------------------------------------------
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER]					(O.CUSTOMER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)   DC
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER]						(O.[USER_ID])						    DU
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_ORDER]				(QP.ORDER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)	    DO
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE]				(QP.ORDER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)	    DQ
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE_ONSITE_CONTACT]	(QP.ORDER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)	    DQC
	   WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND QP.ORDER_ID IS NOT NULL
	   UNION -- ORDERS OF THE NOTIFICATION CUSTOMERS
	   SELECT
		  NQ.NotificationQueueId				AS [NOTIFICATION_QUEUE_ID]
		  ,DC.*
		  ,DU.*
		  ,DO.*
		  ,DQ.*
		  ,DQC.*
		  ,[dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, O.[USER_ID], NULL, NULL, NQ.NotificationSetId, NQ.CustomerId) AS EMAILS 
	   FROM F_NOTIFICATION_GROUP_SCOPE		   NGS WITH(NOLOCK)
	   INNER JOIN F_NOTIFICATION_SET_GROUP	   NSG WITH(NOLOCK)
	     ON NSG.NOTIFICATION_GROUP_ID = NGS.NOTIFICATION_GROUP_ID AND NGS.CUSTOMER_ID	  IS NOT NULL
	   INNER JOIN F_NOTIFICATION			   N   WITH(NOLOCK)
	     ON N.NOTIFICATION_GROUP_ID = NSG.NOTIFICATION_GROUP_ID AND N.NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
	   INNER JOIN F_CUSTOMER				   C   WITH(NOLOCK)
	     ON C.CUSTOMER_ID = NGS.CUSTOMER_ID
	   INNER JOIN F_RECYCLING_ORDER		   O   WITH (NOLOCK)
	   	ON O.CUSTOMER_ID = C.CUSTOMER_ID
	   INNER JOIN @tNotifyItemWithNotifySet		   NQ
	   CROSS APPLY tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId) QP
	     ON QP.ORDER_ID = O.RECYCLING_ORDER_ID	   
	   -- To filter out the orders being not under a contract
	   INNER JOIN F_RECYCLING_ORDER_CONTRACT ROC WITH(NOLOCK)
	     ON ROC.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID		
	   INNER JOIN F_CONTRACT			OC   WITH(NOLOCK)
	     ON OC.CONTRACT_ID = ROC.CONTRACT_ID
	   ------------------------------------------------------
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_CUSTOMER]					(O.CUSTOMER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)		  DC
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_USER]						(O.[USER_ID])								  DU
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_ORDER]				(O.RECYCLING_ORDER_ID, @NOTIFICATION_TYPE_ID, 1, NQ.NotificationSetId)	  DO
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE]				(QP.ORDER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)			  DQ
	   CROSS APPLY [dbo].[tvf_GET_SNIPPET_DATA_RECYCLING_QUOTE_ONSITE_CONTACT]	(QP.ORDER_ID, @NOTIFICATION_TYPE_ID, 0, NQ.NotificationSetId)			  DQC
	   WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND QP.ORDER_ID	  IS NOT NULL
		AND NGS.CUSTOMER_ID	  IS NOT NULL
		AND NSG.NOTIFICATION_SET_ID = NQ.NotificationSetId
		) AS T;
END

-- =============================================
-- Author:		I.Orobets
-- Create date: 07/11/2016
-- Description: Is sales order Item include into RMA
-- =============================================
CREATE FUNCTION [dbo].[fn_bit_IS_SALES_ORDER_ITEM_INCLUDE_INTO_RMA] 
(
	@C_SALES_ORDER_ITEM_ID BIGINT
)
RETURNS BIT
AS
BEGIN

	IF(EXISTS(SELECT TOP(1) 1 
			  FROM F_RMA_ITEM WITH(NOLOCK)
			  WHERE SALES_ORDER_ITEM_ID = @C_SALES_ORDER_ITEM_ID)) 
	BEGIN
		RETURN 1;
	END
    RETURN 0;

END
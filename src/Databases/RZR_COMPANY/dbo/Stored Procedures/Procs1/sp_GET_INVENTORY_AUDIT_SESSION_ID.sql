-- =============================================
-- Author:		Oleg K. Evseev
-- Create date: 08/07/2013
-- Description:	Sets condition value to the listed inventory items
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_INVENTORY_AUDIT_SESSION_ID] 
	@LOCATION_ID	BIGINT
	,@USER_ID		BIGINT
AS
BEGIN
	BEGIN TRANSACTION
		DECLARE 
			@SESSION_ID BIGINT = (
				SELECT 
					MAX(SESSION_ID)
				FROM F_INVENTORY_AUDIT_SESSION
				WHERE LOCATION_ID = @LOCATION_ID
				  AND ISNULL(IS_COMPLETE, 0) = 0)

		IF (ISNULL(@SESSION_ID, 0) = 0)
		BEGIN
			INSERT INTO F_INVENTORY_AUDIT_SESSION (
				LOCATION_ID 
				,[USER_ID] 
				,INSERTED_BY
			) VALUES (
				@LOCATION_ID
				,@USER_ID
				,'sp_GET_INVENTORY_AUDIT_SESSION_ID'
			)
			SET @SESSION_ID = SCOPE_IDENTITY()
		END
	COMMIT TRANSACTION
		
	SELECT @SESSION_ID AS SESSION_ID
END
CREATE TABLE [dbo].[F_NOTIFICATION_QUEUE_PARAMS] (
    [NOTIFICATION_QUEUE_PARAM_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [PARAM_TYPE_NAME]             NVARCHAR (MAX) NOT NULL,
    [PARAM_VALUE]                 BIGINT         NOT NULL,
    [NOTIFICATION_QUEUE_ID]       BIGINT         NOT NULL,
    CONSTRAINT [PK_F_NOTIFICATION_QUEUE_PARAMS] PRIMARY KEY CLUSTERED ([NOTIFICATION_QUEUE_PARAM_ID] ASC),
    CONSTRAINT [FK_F_NOTIFICATION_QUEUE_PARAMS_F_NOTIFICATION_QUEUE] FOREIGN KEY ([NOTIFICATION_QUEUE_ID]) REFERENCES [dbo].[F_NOTIFICATION_QUEUE] ([NOTIFICATION_QUEUE_ID])
);
GO


CREATE NONCLUSTERED INDEX IX__dbo_F_NOTIFICATION_QUEUE_PARAMS__NOTIFICATION_QUEUE_ID__INCLUDE__PARAM_TYPE_NAME__PARAM_VALUE
  ON [dbo].[F_NOTIFICATION_QUEUE_PARAMS] (
    [NOTIFICATION_QUEUE_ID]
  )
  INCLUDE (
    [PARAM_TYPE_NAME], [PARAM_VALUE]
  )

GO
CREATE NONCLUSTERED INDEX [IX_F_NOTIFICATION_QUEUE_PARAMS_NOTIFICATION_QUEUE_ID] ON 
	[dbo].[F_NOTIFICATION_QUEUE_PARAMS] ([NOTIFICATION_QUEUE_ID]) INCLUDE ([NOTIFICATION_QUEUE_PARAM_ID]);

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'the parameters values for notifications. The stored procedure will be executed with these params. For each notifications own set of parameters', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_NOTIFICATION_QUEUE_PARAMS';


-- =============================================
-- Author: <PERSON>
-- Create date: 12/14/2015
-- Description:	Gets count of sales order RMA 
-- =============================================
CREATE FUNCTION [dbo].[fn_int_GET_SALES_ORDER_RMA_COUNT]
(
	@C_SALES_ORDER_ID	BIGINT
)
RETURNS INT
AS
BEGIN
	DECLARE @RESULT INT

	SELECT
		@RESULT = ISNULL(COUNT(rma.RMA_ID), 0)
	FROM [dbo].[F_SALES_ORDER_RMA]			rma		WITH(NOLOCK)
	WHERE rma.SALES_ORDER_ID = @C_SALES_ORDER_ID
		AND rma.IS_DELETED = 0

	-- Return the result of the function
	RETURN @RESULT

END
CREATE PROCEDURE [dbo].[sp_GET_NOTIFICATIONS_SALES_ORDER_IS_APPROVED_FROM_QUEUE]
	@C_COUNT INT = 40
AS
BEGIN
	DECLARE @NOTIFICATION_TYPE_ID		BIGINT = 24
	DECLARE @IS_ALLOWED					BIT = dbo.fn_bit_IS_NOTIFICATION_ALLOWED(@NOTIFICATION_TYPE_ID)	
	IF (@IS_ALLOWED = 0)
	BEGIN
		UPDATE F_NOTIFICATION_QUEUE SET
			IS_INACTIVE = 1,
			UPDATED_BY  = N'sp_GET_NOTIFICATIONS_SALES_ORDER_IS_APPROVED_FROM_QUEUE',
			UPDATED_DT  = GETUTCDATE()
		WHERE IS_INACTIVE		   = 0
		  AND NOTIFICATION_TYPE_ID = @NOTIFICATION_TYPE_ID
	END

	DECLARE @tNotifyItemWithNotifySet [dbo].NotifyQueueItemWithNotifySet;

	INSERT INTO @tNotifyItemWithNotifySet 
	SELECT * FROM [dbo].[tvf_GET_NOTIFICATION_QUEUE_ITEM_WITH_NOTIFICATION_SET](@NOTIFICATION_TYPE_ID);

	SELECT TOP (@C_COUNT)
		NQ.NotificationQueueId			AS [NOTIFICATION_QUEUE_ID]
		,SO.*
		,CONCAT([dbo].fn_str_GET_NOTIFICATION_EMAIL_LIST(@NOTIFICATION_TYPE_ID, NULL, SO.CUSTOMER_PRIMARY_CONTACT_ID, NULL, NQ.NotificationSetId, NQ.CustomerId),
			[dbo].[fn_str_GET_NOTIFICATION_EMAIL_LIST_REPS](@NOTIFICATION_TYPE_ID, SO.SALES_ORDER_ID, NQ.NotificationSetId))	AS EMAILS		
	FROM @tNotifyItemWithNotifySet														NQ
	CROSS APPLY	tvf_GET_NOTIFICATION_QUEUE_PARAMS(NQ.NotificationQueueId)			QP
	CROSS APPLY dbo.tvf_GET_SNIPPET_DATA_SALES_ORDER (QP.SALES_ORDER_ID)			SO
	WHERE NQ.NotificationTypeId = @NOTIFICATION_TYPE_ID
		AND QP.SALES_ORDER_ID IS NOT NULL
END
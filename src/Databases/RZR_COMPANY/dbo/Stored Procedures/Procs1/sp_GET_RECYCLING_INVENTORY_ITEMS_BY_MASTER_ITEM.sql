-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 27.02.2014
-- Description:	Return recycling inventory items by master item
-- =============================================
-- EXEC [dbo].[sp_GET_RECYCLING_INVENTORY_ITEMS_BY_MASTER_ITEM] @C_IS_DEBUG = 1, @MASTER_ITEM_ID = 300
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_INVENTORY_ITEMS_BY_MASTER_ITEM]
	@WAREHOUSE_ID			BIGINT			= NULL,
	@MASTER_ITEM_ID			BIGINT,
	@WORKFLOW_STEP_ID		INT				= NULL,	
	@ORDER_COLUMN_NAME		VARCHAR(250)	= N'LOT_NUM',
	@ORDER_DIRECTION		VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE			INT				= 20,
	@PAGE_INDEX				INT				= 0,
	@C_IS_DEBUG				BIT				= 0
AS
BEGIN
	
	SET @WAREHOUSE_ID		= ISNULL(@WAREHOUSE_ID		,0);
	SET @MASTER_ITEM_ID		= ISNULL(@MASTER_ITEM_ID	,0);
	SET @WORKFLOW_STEP_ID	= ISNULL(@WORKFLOW_STEP_ID	,0);

	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	
	DECLARE @query NVARCHAR (MAX) = '			
	WITH m_data AS (
		SELECT 
			roi.RECYCLING_ORDER_ITEM_ID									AS [LOT_NUM],
			rim.RECYCLING_ITEM_MASTER_ID								AS [MASTER_ID],
			rim.RECYCLING_ITEM_MASTER_NAME								AS [ITEM],
			roi.WEIGHT_RECEIVED											AS [WEIGHT],
			roi.WEIGHT_TARE												AS [TARE],
			(roi.WEIGHT_RECEIVED - roi.WEIGHT_TARE)						AS [NET],
			rpt.PACKAGING_TYPE_DESC										AS [PACKAGING],
			roi.ITEM_COUNT												AS [ITEM_COUNT],	
			dbo.fn_str_GET_LOCATON_NAME_WITH_SECONDARY(roi.LOCATION_ID) AS [LOCATION_CD],
			WORKFLOW_TYPE_DESC											AS [WORKFLOW],
			roi.IsCertifiedDestruction									AS [IsCertifiedDestruction]
		FROM dbo.F_RECYCLING_ORDER_ITEM				roi	WITH(NOLOCK)	
		INNER JOIN F_RECYCLING_ORDER				RO	WITH(NOLOCK)
			ON roi.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
		INNER JOIN dbo.F_RECYCLING_ITEM_MASTER		rim	WITH(NOLOCK)	
			ON roi.RECYCLING_ITEM_MASTER_ID = rim.RECYCLING_ITEM_MASTER_ID
		LEFT JOIN dbo.D_RECYCLING_PACKAGING_TYPE	rpt	WITH(NOLOCK)	
			ON roi.PACKAGING_TYPE_ID = rpt.RECYCLING_PACKAGING_TYPE_ID	
		LEFT JOIN dbo.C_RECYCLING_WORKFLOW_TYPE		rwt	WITH(NOLOCK)	
			ON roi.WORKFLOW_STEP_ID = rwt.WORKFLOW_TYPE_ID

		--for transfer orders RSW-8950
		LEFT JOIN 	dbo.F_LOCATION					loc  WITH(NOLOCK)	
			ON roi.LOCATION_ID = loc.LOCATION_ID
		LEFT JOIN F_RECYCLING_ORDER_ITEM_TRANSFER	ROIT WITH(NOLOCK)
			ON  ROIT.RECYCLING_ORDER_ITEM_ID = roi.RECYCLING_ORDER_ITEM_ID
			AND ROIT.OUTBOUND_ORDER_ID		 = roi.OUTBOUND_ORDER_ID -- only the actual one
		LEFT JOIN dbo.F_RECYCLING_ORDER				ROT WITH(NOLOCK)
			ON ROIT.INBOUND_ORDER_ID = ROT.RECYCLING_ORDER_ID
		LEFT JOIN F_RECYCLING_ORDER					OO  WITH(NOLOCK)
			ON roi.OUTBOUND_ORDER_ID = OO.RECYCLING_ORDER_ID
			AND OO.RECYCLING_ORDER_STATUS_ID != 4
		WHERE (' + CAST(@WAREHOUSE_ID AS VARCHAR(100)) + ' = 0 OR COALESCE(ROT.WAREHOUSE_ID, loc.WAREHOUSE_ID, RO.WAREHOUSE_ID) = ' + CAST(@WAREHOUSE_ID AS VARCHAR(100)) + ') 
		  AND (/*' + CAST(@MASTER_ITEM_ID AS VARCHAR(100)) + ' = 0 OR */roi.RECYCLING_ITEM_MASTER_ID = ' + CAST(@MASTER_ITEM_ID AS VARCHAR(100)) + ')
		  AND (' + CAST(@WORKFLOW_STEP_ID AS VARCHAR(100)) + ' = 0 AND (roi.IS_CONSUMED_OR_PROCESSED = 0) OR (' + CAST(@WORKFLOW_STEP_ID AS VARCHAR(100))+ ' = 8 AND roi.IS_CONSUMED_OR_PROCESSED = 1) OR roi.WORKFLOW_STEP_ID = ' + CAST(@WORKFLOW_STEP_ID AS VARCHAR(100)) + ')
		  AND (OO.RECYCLING_ORDER_ID IS NULL
			OR ROIT.OUTBOUND_ORDER_ID IS NOT NULL AND ROIT.INBOUND_ORDER_ID IS NOT NULL) --for transfer orders RSW-8950
		  AND roi.IS_INACTIVE = 0
		)
		SELECT TOP(1)
			-1								AS RowID,
			COUNT ([LOT_NUM])				AS [LOT_NUM],
			0								AS [MASTER_ID],
			NULL							AS [ITEM],
			NULL							AS [WEIGHT],
			NULL							AS [TARE],
			NULL							AS [NET],
			NULL							AS [PACKAGING],
			NULL							AS [ITEM_COUNT],
			NULL							AS [LOCATION_CD],
			NULL							AS [WORKFLOW],
			NULL							AS [IsCertifiedDestruction]					
		FROM m_data		
		UNION ALL
		SELECT
			t.RowID,
			t.[LOT_NUM],
			t.[MASTER_ID],
			t.[ITEM],
			t.[WEIGHT],
			t.[TARE],
			t.[NET],
			t.[PACKAGING],
			t.[ITEM_COUNT],
			t.[LOCATION_CD],
			t.[WORKFLOW],
			t.[IsCertifiedDestruction]
		FROM (SELECT 
					ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
					* 
				FROM m_data) t	
		WHERE 
			RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))

		IF @C_IS_DEBUG = 1
			PRINT CAST(@query as ntext)

		EXEC sp_executesql @query;
END

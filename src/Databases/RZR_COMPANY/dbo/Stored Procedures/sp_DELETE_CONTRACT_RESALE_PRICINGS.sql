-- Create Procedure sp_DELETE_CONTRACTS
-- =============================================
-- Author:		I.Orobets
-- Create date: 11/25/2015
-- Description: Delete Contracts Resale Pricing
-- =============================================
CREATE PROCEDURE [dbo].[sp_DELETE_CONTRACT_RESALE_PRICINGS]
	@TEMPLATE_IDS bigint_ID_ARRAY READONLY
AS
BEGIN

	DECLARE @TEMPLATES nvarchar_LABEL_ARRAY
	
	DECLARE Items CURSOR LOCAL FORWARD_ONLY FOR
		SELECT
			ID
		FROM @TEMPLATE_IDS
				
	OPEN Items
	DECLARE @FMV_TEMPLATE_ID BIGINT
		
	FETCH NEXT FROM Items INTO @FMV_TEMPLATE_ID
	WHILE @@FETCH_STATUS = 0
	BEGIN

		SET XACT_ABORT ON
		BEGIN TRAN

			-- Delete Template if it is't assigned to Contract
			IF(NOT EXISTS(
				SELECT TOP(1) 1
				FROM F_CONTRACT WITH(NOLOCK)
				WHERE FMV_TEMPLATE_ID = @FMV_TEMPLATE_ID))
			BEGIN
				
				DECLARE @CONTRACT_ATTRIBUTE_TYPE_ID bigint_ID_ARRAY;
				INSERT INTO @CONTRACT_ATTRIBUTE_TYPE_ID(ID)
				SELECT
					CONTRACT_ATTRIBUTE_TYPE_ID
				FROM F_CONTRACT_ATTRIBUTE_TYPE WITH(NOLOCK)
				WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID

				DELETE FROM F_CONTRACT_CAPABILITY
				WHERE CONTRACT_ATTRIBUTE_TYPE_ID IN (SELECT ID FROM @CONTRACT_ATTRIBUTE_TYPE_ID)

				DELETE FROM [dbo].[F_CONTRACT_ATTRIBUTE_TYPE]
				WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID
			
				update oa set
					[ParentResalePricingRuleId] = null
				from [dbo].[F_CONTRACT_RESALE_PRICING_OTHER_ASSET] oa with (rowlock)
				where [ParentResalePricingRuleId] in
					(
						select CONTRACT_RESALE_PRICING_OTHER_ASSET_ID
						FROM F_CONTRACT_RESALE_PRICING_OTHER_ASSET with (nolock)	
						WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID			
					)
			
				DELETE FROM F_CONTRACT_RESALE_PRICING_OTHER_ASSET
				WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID

				update rp set
					 GlobalPricingId = null
				from [dbo].[F_CONTRACT_RESALE_PRICING] rp with (rowlock)
				where GlobalPricingId = @FMV_TEMPLATE_ID

				DELETE FROM F_CONTRACT_RESALE_PRICING
				WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID
			
			END
			ELSE BEGIN

				INSERT INTO @TEMPLATES (LABEL)
				SELECT 
					CONTRACT_RESALE_PRICING_NAME
				FROM F_CONTRACT_RESALE_PRICING
				WHERE CONTRACT_RESALE_PRICING_ID = @FMV_TEMPLATE_ID

			END
		
		COMMIT TRAN
			
	FETCH NEXT FROM Items INTO @FMV_TEMPLATE_ID
	END
	CLOSE Items
	DEALLOCATE Items


	SELECT * FROM @TEMPLATES

END
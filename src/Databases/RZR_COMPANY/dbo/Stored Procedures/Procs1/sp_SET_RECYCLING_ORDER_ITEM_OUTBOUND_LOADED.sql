-- =============================================
-- Author:		<O.Evseev>
-- Create date: <02/18/2014>
-- Description:	<Loads a lot into the outbound order>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_LOADED]
	@OUTBOUND_ORDER_ID			BIGINT,
	@RECYCLING_ORDER_ITEM_ID	BIGINT,
	@PACKAGING_TYPE_ID			INT				= NULL,
	@WORKFLOW_TYPE_ID			INT				= NULL,
	@NOTES						NVARCHAR(MAX)	= NULL,
	@LOADING_DATE				DATETIME,
	@USER_ID					BIGINT,
	@IP							BIGINT,
	@USE_INTERNAL_TRANSACTION	BIT = 1
AS
BEGIN

	DECLARE 
		@PROCESS_CD NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID)+'.','')+OBJECT_NAME(@@PROCID),
		@UTC_NOW	DATETIME		= GETUTCDATE()

	IF @USE_INTERNAL_TRANSACTION = 1
	BEGIN
		SET XACT_ABORT ON
		BEGIN TRAN
	END

		DECLARE @IS_TRANSFER BIT = (
			SELECT TOP(1)
				IS_TRANSFER 
			FROM dbo.F_RECYCLING_ORDER WITH(NOLOCK)
			WHERE RECYCLING_ORDER_ID = @OUTBOUND_ORDER_ID)

		UPDATE OU SET
			LOADING_DT = 
				CASE 
					WHEN O.RECYCLING_ORDER_STATUS_ID = 1 THEN ISNULL(@LOADING_DATE, @UTC_NOW)
					ELSE OU.LOADING_DT
				END,
			OU.UPDATED_BY_USER	= @USER_ID,
			OU.UPDATED_BY_IP	= @IP
		FROM dbo.F_RECYCLING_ORDER_OUTBOUND	OU	WITH(ROWLOCK)
		INNER JOIN dbo.F_RECYCLING_ORDER	O	WITH(ROWLOCK)
			ON OU.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
		WHERE OU.RECYCLING_ORDER_ID = @OUTBOUND_ORDER_ID
		
		UPDATE TOP(1) F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
			OUTBOUND_ORDER_ID	= @OUTBOUND_ORDER_ID,
			PACKAGING_TYPE_ID	= ISNULL(@PACKAGING_TYPE_ID, PACKAGING_TYPE_ID),
			WORKFLOW_STEP_ID	= ISNULL(@WORKFLOW_TYPE_ID,  WORKFLOW_STEP_ID),
			NOTES				= ISNULL(@NOTES,			 NOTES),
			EXPORTED_DT			= @UTC_NOW,
			UPDATED_BY			= @PROCESS_CD,
			UPDATED_DT			= @UTC_NOW,
			UPDATED_BY_USER		= @USER_ID,
			UPDATED_BY_IP		= @IP
		WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID

		
		UPDATE F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
			OUTBOUND_ORDER_ID			= @OUTBOUND_ORDER_ID,		
			EXPORTED_DT					= @UTC_NOW,
			UPDATED_BY					= @PROCESS_CD,
			UPDATED_DT					= @UTC_NOW,
			UPDATED_BY_USER				= @USER_ID,
			UPDATED_BY_IP				= @IP		
		WHERE RECYCLING_ORDER_ITEM_ID IN (SELECT RECYCLING_ORDER_ITEM_ID FROM F_RECYCLING_ORDER_ITEM_MERGE WITH (NOLOCK) WHERE RECYCLING_ORDER_ITEM_PRIMARY_ID = @RECYCLING_ORDER_ITEM_ID)

		
		UPDATE TOP(1) dbo.F_RECYCLING_ORDER WITH(ROWLOCK) SET
			RECYCLING_ORDER_STATUS_ID = 
				CASE
					WHEN ISNULL(RECYCLING_ORDER_STATUS_ID, 0) = 1 THEN 6 --Partially Loaded
					ELSE RECYCLING_ORDER_STATUS_ID
				END,
			UPDATED_BY_USER	= @USER_ID,
			UPDATED_BY_IP	= @IP
		WHERE RECYCLING_ORDER_ID = @OUTBOUND_ORDER_ID	

		-- For Transfer outbound orders. Adding it to F_RECYCLING_ORDER_ITEM_TRANSFER and temporary add to F_RECYCLING_ORDER_ITEM.
		-- After LOAD COMPLETE it will be delete from F_RECYCLING_ORDER_ITEM by deleting link(OUTBOUND_ORDER_ID = NULL)
		IF (@IS_TRANSFER = 1
		AND NOT EXISTS(
			SELECT TOP(1) 1
			FROM F_RECYCLING_ORDER_ITEM_TRANSFER WITH(NOLOCK)
			WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID 
			AND OUTBOUND_ORDER_ID = @OUTBOUND_ORDER_ID))
		BEGIN

			INSERT INTO F_RECYCLING_ORDER_ITEM_TRANSFER (
				RECYCLING_ORDER_ITEM_ID,
				OUTBOUND_ORDER_ID,
				INSERTED_BY,
				INSERTED_DT
			) VALUES (
				@RECYCLING_ORDER_ITEM_ID,
				@OUTBOUND_ORDER_ID,
				@PROCESS_CD,
				@UTC_NOW
			)

		END

	
	IF @USE_INTERNAL_TRANSACTION = 1
	BEGIN		
		COMMIT TRAN
	END
	
END
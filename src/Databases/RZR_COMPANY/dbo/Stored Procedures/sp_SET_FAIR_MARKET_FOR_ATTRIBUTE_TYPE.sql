
-- =============================================
-- Author:		<I.Orobets>
-- Create date: <12/01/2015>
-- Description:	<Save FairMarket for row OR Add capability and attribute for row(if it doesn't exist)>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_FAIR_MARKET_FOR_ATTRIBUTE_TYPE]
	@C_CONTRACT_ATTRIBUTE_TYPE_ID BIGINT = NULL,
	@C_CONTRACT_CAPABILITY_ID     BIGINT = NULL,
	@C_INVENTORY_CAPABILITY_ID    BIGINT = NULL,
	@C_CONTRACT_RESALE_PRICING_ID BIGINT = NULL,
	@С_ATTRIBUTE_TYPE			  INT	 = NULL,
	@C_FAIR_MARKET                MONEY
AS	                    
BEGIN

	DECLARE @NAME VARCHAR(150) = N'sp_SET_FAIR_MARKET_FOR_ATTRIBUTE_TYPE',
			@DATE DATETIME = GETUTCDATE();

	IF(@C_CONTRACT_ATTRIBUTE_TYPE_ID IS NOT NULL)
	BEGIN

		UPDATE [dbo].[F_CONTRACT_ATTRIBUTE_TYPE] SET
			[FAIR_MARKET] = @C_FAIR_MARKET
			,[UPDATED_BY] = @NAME
			,[UPDATED_DT] = @DATE
		WHERE CONTRACT_ATTRIBUTE_TYPE_ID = @C_CONTRACT_ATTRIBUTE_TYPE_ID

		UPDATE [dbo].[F_CONTRACT_CAPABILITY] SET 
			[FAIR_MARKET] = @C_FAIR_MARKET
			,[UPDATED_BY] = @NAME
			,[UPDATED_DT] = @DATE
		WHERE CONTRACT_ATTRIBUTE_TYPE_ID = @C_CONTRACT_ATTRIBUTE_TYPE_ID

	END
	ELSE IF(@C_CONTRACT_CAPABILITY_ID IS NOT NULL) BEGIN

		UPDATE [dbo].[F_CONTRACT_CAPABILITY] SET 
			[FAIR_MARKET] = @C_FAIR_MARKET
			,[UPDATED_BY] = @NAME
			,[UPDATED_DT] = @DATE
		WHERE CONTRACT_CAPABILITY_ID = @C_CONTRACT_CAPABILITY_ID

	END
	ELSE IF(@C_INVENTORY_CAPABILITY_ID IS NOT NULL) BEGIN

		DECLARE @ATTR_TYPE_NAME NVARCHAR(512),
				@ATTR_TYPE_ID   INT;

		SELECT
			@C_CONTRACT_ATTRIBUTE_TYPE_ID = CAT.CONTRACT_ATTRIBUTE_TYPE_ID,
			@ATTR_TYPE_NAME				  = IC.INVENTORY_CAPABILITY_VALUE,
			@ATTR_TYPE_ID				  = DCAT.ATTRIBUTE_TYPE_ID
		FROM D_INVENTORY_CAPABILITY				IC	WITH(NOLOCK)
		LEFT JOIN F_CONTRACT_CAPABILITY			CC	WITH (NOLOCK)
			ON CC.INVENTORY_CAPABILITY_ID = IC.INVENTORY_CAPABILITY_ID
		LEFT JOIN F_CONTRACT_ATTRIBUTE_TYPE		CAT WITH(NOLOCK)
			ON CAT.CONTRACT_ATTRIBUTE_TYPE_ID = CC.CONTRACT_ATTRIBUTE_TYPE_ID
			AND CAT.CONTRACT_RESALE_PRICING_ID = @C_CONTRACT_RESALE_PRICING_ID
		LEFT JOIN D_CONTRACT_ATTRIBUTE_TYPE		DCAT WITH(NOLOCK)
			ON DCAT.CONTRACT_ATTRIBUTE_TYPE_ID = @С_ATTRIBUTE_TYPE
		WHERE IC.INVENTORY_CAPABILITY_ID = @C_INVENTORY_CAPABILITY_ID

		IF(@C_CONTRACT_ATTRIBUTE_TYPE_ID IS NULL) BEGIN

			INSERT INTO [dbo].[F_CONTRACT_ATTRIBUTE_TYPE]
				([ATTRIBUTE_TYPE_NAME]
				,[FAIR_MARKET]
				,[ATTRIBUTE_TYPE_ID]
				,[INVENTORY_ATTRIBUTE_TYPE_ID]
				,[CONTRACT_RESALE_PRICING_ID]
				,[INSERTED_BY]
				,[INSERTED_DT])
			 VALUES
				(@ATTR_TYPE_NAME
				,@C_FAIR_MARKET
				,@С_ATTRIBUTE_TYPE
				,@ATTR_TYPE_ID
				,@C_CONTRACT_RESALE_PRICING_ID
				,@NAME 
				,@DATE)

			SET @C_CONTRACT_ATTRIBUTE_TYPE_ID = SCOPE_IDENTITY();

			INSERT INTO [dbo].[F_CONTRACT_CAPABILITY]
				([INVENTORY_CAPABILITY_ID]
				,[CONTRACT_ATTRIBUTE_TYPE_ID]
				,[FAIR_MARKET]
				,[INSERTED_BY]
				,[INSERTED_DT])
			VALUES
				(@C_INVENTORY_CAPABILITY_ID
				,@C_CONTRACT_ATTRIBUTE_TYPE_ID
				,@C_FAIR_MARKET
				,@NAME
				,@DATE)

		END
		ELSE BEGIN

			UPDATE [dbo].[F_CONTRACT_ATTRIBUTE_TYPE] SET
				[FAIR_MARKET] = @C_FAIR_MARKET
				,[UPDATED_BY] = @NAME
				,[UPDATED_DT] = @DATE
			WHERE CONTRACT_ATTRIBUTE_TYPE_ID = @C_CONTRACT_ATTRIBUTE_TYPE_ID

			UPDATE [dbo].[F_CONTRACT_CAPABILITY] SET 
				[FAIR_MARKET] = @C_FAIR_MARKET
				,[UPDATED_BY] = @NAME
				,[UPDATED_DT] = @DATE
			WHERE CONTRACT_ATTRIBUTE_TYPE_ID = @C_CONTRACT_ATTRIBUTE_TYPE_ID

		END

	END

END
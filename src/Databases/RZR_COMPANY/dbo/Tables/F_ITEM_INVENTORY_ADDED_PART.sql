CREATE TABLE [dbo].[F_ITEM_INVENTORY_ADDED_PART] (
    [ITEM_INVENTORY_ADDED_PART_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [PARENT_ITEM_INVENTORY_ID]     BIGINT         NOT NULL,
    [PART_ITEM_INVENTORY_ID]       BIGINT         NOT NULL,
    [INSERTED_BY]                  NVARCHAR (250) NOT NULL,
    [INSERTED_DT]                  DATETIME       NOT NULL,
    [INSERTED_BY_ID]               BIGINT         NOT NULL,
    [INSERTED_BY_IP]               BIGINT         NOT NULL,
    [DELETED_BY]                   NVARCHAR (250) NULL,
    [DELETED_DT]                   DATETIME       NULL,
    [DELETED_BY_ID]                BIGINT         NULL,
    [DELETED_BY_IP]                BIGINT         NULL,
    CONSTRAINT [PK_F_ITEM_INVENTORY_ADDED_PART] PRIMARY KEY CLUSTERED ([ITEM_INVENTORY_ADDED_PART_ID] ASC),
    CONSTRAINT [FK_F_ITEM_INVENTORY_ADDED_PART_F_ITEM_INVENTORY] FOREIGN KEY ([PARENT_ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID]),
    CONSTRAINT [FK_F_ITEM_INVENTORY_ADDED_PART_F_ITEM_INVENTORY1] FOREIGN KEY ([PART_ITEM_INVENTORY_ID]) REFERENCES [dbo].[F_ITEM_INVENTORY] ([ITEM_INVENTORY_ID])
);








GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The inventory can be built using other inventory. This is the table to store this info', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_INVENTORY_ADDED_PART';


GO
CREATE NONCLUSTERED INDEX [IX_PartItemInventoryId]
    ON [dbo].[F_ITEM_INVENTORY_ADDED_PART]([PART_ITEM_INVENTORY_ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ParentItemInventoryId]
    ON [dbo].[F_ITEM_INVENTORY_ADDED_PART]([PARENT_ITEM_INVENTORY_ID] ASC);


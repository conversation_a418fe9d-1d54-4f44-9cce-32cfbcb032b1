-- =============================================
-- Author:	    <O.Evseev>
-- Create date: <2016.05.16>
-- Description: <Create consignment POs for the list of Sold Inventory items>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_CONSIGNMENT_PURCHASE_ORDER_ITEMS]
	@ITEMS			ConsignmentSmallModel READONLY,
	@USER_ID		BIGINT,
	@IP				BIGINT,
	@C_IS_DEBUG	BIT	= 0
AS
BEGIN

	DECLARE @T_PO_ITEMS TABLE(ID BIGINT);
	DECLARE @T_POS		TABLE(value BIGINT, label NVARCHAR(MAX), isFlagged BIT);

	DECLARE 
		@INVOICE_SUM MONEY = 0,
		@INVOICE_ID	 BIGINT = 0;

	DECLARE
		-- FOR C_CREATE_CUSTOMER_PO
		@PO_CUSTOMER_ID		BIGINT,
		@PO_ID				BIGINT,
		@PO_ITEM_ID			BIGINT,
		@PO_TERM_ID			BIGINT = (
			SELECT TOP(1) CUSTOMER_TRANSACTION_TERM_ID
			FROM C_CUSTOMER_TRANSACTION_TERM WITH(NOLOCK)
			WHERE TRANSACTION_TERM_TYPE_ID = 2 -- PO type
				AND TRANSACTION_TERM_VALUE  = N'Due on Receipt'
		),
		@IS_RMA_USED		BIT,
		@UTC_NOW			DATETIME = GETUTCDATE(),
		@SP_NAME			NVARCHAR(56) = N'sp_SET_CONSIGNMENT_PURCHASE_ORDER_ITEMS',
		-- FOR C_CREATE_PO_ITEM
		@ITEM_INVENTORY_ID	BIGINT,
		@AMT_OWED			FLOAT,
		@ITEM_NUMBER		NVARCHAR(MAX),
		@MANUFACTURER_ID	BIGINT,
		@CONDITION_ID		BIGINT,
		@QTY				FLOAT,
		@SALES_ORDER_ID		BIGINT,
		@CurrencyExchangeId bigint = [dbo].[fn_bigint_CompanyHomeCurrencyExchangeId](),
		@CONSIGNMENT_ID		bigint;

	SET XACT_ABORT ON;
	BEGIN TRAN;

		-- AMTs to be returned under the consignment for the sold inventory items
		DECLARE @T_ITEM_AMOUNTS T_CONSIGNMENT_ITEMS_AMOUNTS;
		INSERT INTO @T_ITEM_AMOUNTS
		EXEC dbo.[sp_GET_CONSIGNMENT_ITEMS_AMOUNTS]
			@C_ITEMS						= @ITEMS,
			@C_NOT_UNDER_CONSIGNMENT_ONLY	= 1;


		IF (@C_IS_DEBUG = 1)
		BEGIN
			SELECT DISTINCT
				'All items'	as What,
				*
			FROM @T_ITEM_AMOUNTS;
		END


		IF (@C_IS_DEBUG = 1)
		BEGIN
			SELECT DISTINCT
				'Cursor steps'	as What,
				CUSTOMER_ID,
				PURCHASE_ORDER_ID
		  FROM @T_ITEM_AMOUNTS;
		  --COMMIT RETURN;
		END

		DECLARE @IS_RECYCLING_CONSIGNMENT BIT = 0;
		DECLARE C_CREATE_CUSTOMER_PO CURSOR LOCAL FORWARD_ONLY FOR
			-- Inventory items sold to one customer will go to one PO
			SELECT DISTINCT
				CUSTOMER_ID,
				PURCHASE_ORDER_ID,
				IIF(RMA_ITEM_ID IS NULL, 0, 1) AS IS_RMA_USED,
				CONSIGNMENT_ID
			FROM @T_ITEM_AMOUNTS;

		OPEN C_CREATE_CUSTOMER_PO;
		FETCH NEXT FROM C_CREATE_CUSTOMER_PO INTO @PO_CUSTOMER_ID, @PO_ID, @IS_RMA_USED, @CONSIGNMENT_ID;
		
		WHILE (@@FETCH_STATUS = 0)
		BEGIN

			SET @IS_RECYCLING_CONSIGNMENT = IIF(@PO_ID IS NULL, 1, 0);

			-- A recycling Consignment - create the PO for the selected items ------------------------------
			IF (@IS_RECYCLING_CONSIGNMENT = 1 AND @IS_RMA_USED = 0)
			BEGIN
			-- Do not create new PO if 'Add to PO' was done for RMA Item
			-- Do not add PO Items to PO if 'Add to PO' was done for RMA Item

				INSERT INTO F_PURCHASE_ORDER WITH(ROWLOCK) (
					STATUS_ID,
					--AUTO_NAME,
					[CUSTOMER_ID],
					[PO_TERM_ID],
					[USER_ID],
					[DATE],
					COMMENTS,
					INTERNAL_COMMENTS,
					IS_CONSIGNMENT_PO,
					INSERTED_BY,
					INSERTED_DT,
					CurrencyExchangeId,
					CONSIGNMENT_ID
				) VALUES (
					3, -- RECEIVED
					--[dbo].[fn_str_AUTO_NAME_PURCHASE_ORDER](),
					@PO_CUSTOMER_ID,
					@PO_TERM_ID,
					@USER_ID,
					@UTC_NOW,
					NULL,
					NULL,
					1,
					N'Consignment PO',
					@UTC_NOW,
					@CurrencyExchangeId,
					@CONSIGNMENT_ID
				);

				SET @PO_ID = SCOPE_IDENTITY();

				INSERT INTO F_ORDER_ORDER_SUBJECT_TYPE (ORDER_ID, ENTITY_TYPE_ID, [ENTITY_SUBJECT_TYPE_ID], INSERTED_BY, INSERT_DT)
				VALUES (@PO_ID, 3, 10, @SP_NAME, @UTC_NOW);

				-- create vendor address
				EXEC [dbo].[sp_INSERT_PURCHASE_ORDER_ADDRESS]
					 @PO_ID		 = @PO_ID
					,@VENDOR_ID	 = @PO_CUSTOMER_ID
					,@USER_ID	 = @USER_ID;


				-- Inserting PO items for each inventory item
				DECLARE C_CREATE_PO_ITEM CURSOR LOCAL FORWARD_ONLY FOR
					SELECT
						 T.ITEM_INVENTORY_ID
						,T.AMT_OWED
						,IM.ITEM_NUMBER	
						,IM.MANUFACTURER_ID
						,II.CONDITION_ID
						,T.QTY
						,T.SALES_ORDER_ID
					FROM @T_ITEM_AMOUNTS			T
					INNER JOIN F_ITEM_INVENTORY		II WITH(NOLOCK)
						ON II.ITEM_INVENTORY_ID = T.ITEM_INVENTORY_ID
					INNER JOIN F_ITEM_MASTER		IM WITH(NOLOCK)
						ON IM.ITEM_MASTER_ID = II.ITEM_MASTER_ID
					WHERE II.CUSTOMER_ID = @PO_CUSTOMER_ID -- bought from the customer receiving the consignment
						AND ISNULL(T.CONSIGNMENT_ID, 0) = ISNULL(@CONSIGNMENT_ID, 0)
						AND T.PURCHASE_ORDER_ID IS NULL;
			 
				OPEN C_CREATE_PO_ITEM;
				FETCH NEXT FROM C_CREATE_PO_ITEM INTO
					@ITEM_INVENTORY_ID
					,@AMT_OWED
					,@ITEM_NUMBER
					,@MANUFACTURER_ID
					,@CONDITION_ID
					,@QTY
					,@SALES_ORDER_ID;
				WHILE (@@FETCH_STATUS = 0)
				BEGIN
						
					DELETE FROM @T_PO_ITEMS;

					INSERT INTO @T_PO_ITEMS
					EXEC [dbo].[sp_SET_PURCHASE_ORDER_ITEM]
						@PURCHASE_ORDER_ID	= @PO_ID
						,@ITEM_NUMBER		= @ITEM_NUMBER
						,@MANUFACTURER_ID	= @MANUFACTURER_ID
						,@IS_SERVICE		= 0
						,@PRICE				= @AMT_OWED
						,@QTY				= @QTY
						,@ITEM_CONDITION_ID	= @CONDITION_ID
						,@USER_ID			= @USER_ID
						,@IP				= @IP;

					UPDATE POI SET
						INVENTORY_ITEM_ID	= @ITEM_INVENTORY_ID
						,RECEIVE_STATUS_ID	= 3 -- Received
						,CONSIGNMENT_SALES_ORDER_ID = @SALES_ORDER_ID
						,UPDATED_BY			= @SP_NAME
						,UPDATED_DT			= @UTC_NOW
						,UPDATED_BY_ID		= @USER_ID
						,UPDATED_BY_IP		= @IP
					FROM @T_PO_ITEMS				 IDS
					INNER JOIN F_PURCHASE_ORDER_ITEM POI WITH(ROWLOCK)
						ON POI.PURCHASE_ORDER_ITEM_ID = IDS.ID
						AND POI.IS_DELETED = 0;

					UPDATE dbo.F_ITEM_INVENTORY WITH(ROWLOCK) SET
						ITEM_INVENTORY_UNIT_COST_ORIGINAL	= ISNULL(@AMT_OWED, ITEM_INVENTORY_UNIT_COST_ORIGINAL)
						,UPDATED_BY							= @SP_NAME
						,UPDATED_DT							= @UTC_NOW
						,UPDATED_BY_IP						= @IP
						,MODIFIER_USER_ID					= @USER_ID
					WHERE ITEM_INVENTORY_ID = @ITEM_INVENTORY_ID;
						
					FETCH NEXT FROM C_CREATE_PO_ITEM INTO
						@ITEM_INVENTORY_ID
						,@AMT_OWED		
						,@ITEM_NUMBER	
						,@MANUFACTURER_ID
						,@CONDITION_ID	
						,@QTY
						,@SALES_ORDER_ID;

				END
				CLOSE C_CREATE_PO_ITEM;
				DEALLOCATE C_CREATE_PO_ITEM;

			END
			-- PO Consignment
			ELSE BEGIN -- Also make Update for Recycling consignment (RMA)

				-- overwrite the price by what we have to return to the Vendor
				UPDATE FPOI SET
					 PRICE			= TI.AMT_OWED
					,RECEIVED_PRICE	= TI.AMT_OWED
					,QTY			= TI.QTY
					,CONSIGNMENT_SALES_ORDER_ID = TI.SALES_ORDER_ID
				FROM F_PURCHASE_ORDER_ITEM		FPOI WITH(ROWLOCK)
				INNER JOIN @T_ITEM_AMOUNTS		TI
					ON TI.ITEM_INVENTORY_ID = FPOI.INVENTORY_ITEM_ID
					AND FPOI.IS_DELETED = 0
				WHERE FPOI.PURCHASE_ORDER_ID = @PO_ID;
					
			END
			------------------------------------------------------------------------------------------------


			-- create(PO for Recycling consignment) the invoice or find(consignment PO) --------------------
			IF (@IS_RECYCLING_CONSIGNMENT = 1 AND @IS_RMA_USED = 0)
			BEGIN -- Make Invoice for Recycling consignment (not RMA)
				
				-- consignment sum of the selected items
				SET @INVOICE_SUM = ISNULL((
					SELECT
						SUM(TI.AMT_OWED * TI.QTY)
					FROM dbo.F_PURCHASE_ORDER_ITEM		FPOI WITH(NOLOCK) -- THE po was just created
					INNER JOIN @T_ITEM_AMOUNTS			TI
						ON TI.ITEM_INVENTORY_ID = FPOI.INVENTORY_ITEM_ID
						AND FPOI.IS_DELETED = 0
					WHERE FPOI.PURCHASE_ORDER_ID = @PO_ID
				), 0.0);

				INSERT INTO dbo.F_INVOICE WITH(ROWLOCK) (
					ORDER_ID,
					DATE_CREATED,
					AMOUNT_DUE,
					ORIGINAL_AMOUNT,
					INVOICE_TYPE_ID,
					IS_PAID,
					IS_VOIDED,
					IS_ADDITIONAL,
					SCHEDULE_DT,
					INSERTED_BY,
					INSERTED_DT
				)
				SELECT
					@PO_ID,
					@UTC_NOW,
					@INVOICE_SUM,
					@INVOICE_SUM,
					2,
					0,
					0,
					0,
					@UTC_NOW,
					@SP_NAME,
					@UTC_NOW;

				SET @INVOICE_ID = SCOPE_IDENTITY();
				
				-- bind PO items to the invoice
				INSERT INTO dbo.F_PURCHASE_ORDER_ITEM_INVOICE WITH(ROWLOCK) (PURCHASE_ORDER_INVOICE_ID, PURCHASE_ORDER_ITEM_ID)
				SELECT
					@INVOICE_ID,
					PURCHASE_ORDER_ITEM_ID
				FROM  dbo.F_PURCHASE_ORDER_ITEM	WITH (NOLOCK)
				WHERE PURCHASE_ORDER_ID = @PO_ID
				  AND IS_DELETED = 0;
			END
			-- PO Consignment
			ELSE BEGIN -- Also make Invoice for Recycling consignment (RMA)

				-- consignment sum of the selected items
				SET @INVOICE_SUM = ISNULL((
					SELECT
						SUM(ISNULL(AMT_OWED, 0) * QTY)
					FROM @T_ITEM_AMOUNTS				TI
					WHERE PURCHASE_ORDER_ID = @PO_ID
				), 0.0);


				SET @INVOICE_ID = (
					SELECT TOP(1)
						FI.INVOICE_ID
					FROM dbo.F_INVOICE			FI  WITH(NOLOCK)
					WHERE FI.INVOICE_TYPE_ID	= 2 -- PO invoice type
						AND FI.IS_PAID			= 0
						AND FI.IS_VOIDED		= 0
						AND FI.IS_ADDITIONAL	= 0
						AND FI.ORDER_ID			= @PO_ID
				);

				IF (@INVOICE_ID IS NULL)
				BEGIN
					INSERT INTO dbo.F_INVOICE WITH(ROWLOCK) (
						ORDER_ID,
						DATE_CREATED,
						AMOUNT_DUE,
						ORIGINAL_AMOUNT,
						INVOICE_TYPE_ID,
						IS_PAID,
						IS_VOIDED,
						IS_ADDITIONAL,
						SCHEDULE_DT,
						INSERTED_BY,
						INSERTED_DT
					)
					SELECT
						@PO_ID,
						@UTC_NOW,
						@INVOICE_SUM,
						@INVOICE_SUM,
						2,
						0,
						0,
						0,
						@UTC_NOW,
						@SP_NAME,
						@UTC_NOW;
					SET @INVOICE_ID = SCOPE_IDENTITY();
				END
				ELSE BEGIN
					UPDATE F_INVOICE WITH(ROWLOCK) SET
						AMOUNT_DUE		= AMOUNT_DUE	  + @INVOICE_SUM,
						ORIGINAL_AMOUNT	= ORIGINAL_AMOUNT + @INVOICE_SUM,
						UPDATED_BY		= @SP_NAME,
						UPDATED_DT		= @UTC_NOW
					WHERE INVOICE_ID = @INVOICE_ID;
				END

				IF (@C_IS_DEBUG = 1)
				BEGIN
					SELECT
						@INVOICE_ID AS INVOICE_ID,
						FPOI.PURCHASE_ORDER_ITEM_ID
					FROM @T_ITEM_AMOUNTS					TI
					INNER JOIN dbo.F_PURCHASE_ORDER_ITEM	FPOI WITH (NOLOCK)
						ON FPOI.INVENTORY_ITEM_ID = TI.ITEM_INVENTORY_ID
						AND FPOI.IS_DELETED = 0
					WHERE FPOI.PURCHASE_ORDER_ID = @PO_ID;
				END

				IF(@IS_RMA_USED = 0)
				BEGIN -- If 'Add to PO' for RMA we needn't add Item here, it already added
					-- bind PO items to the invoice
					INSERT INTO dbo.F_PURCHASE_ORDER_ITEM_INVOICE WITH(ROWLOCK) (PURCHASE_ORDER_INVOICE_ID, PURCHASE_ORDER_ITEM_ID)
					SELECT DISTINCT
						@INVOICE_ID,
						FPOI.PURCHASE_ORDER_ITEM_ID
					FROM @T_ITEM_AMOUNTS					TI
					INNER JOIN dbo.F_PURCHASE_ORDER_ITEM	FPOI WITH (NOLOCK)
						ON FPOI.INVENTORY_ITEM_ID = TI.ITEM_INVENTORY_ID
						AND FPOI.IS_DELETED = 0
					LEFT JOIN F_PURCHASE_ORDER_ITEM_INVOICE POII WITH (NOLOCK)
						ON POII.PURCHASE_ORDER_ITEM_ID = FPOI.PURCHASE_ORDER_ITEM_ID
					WHERE FPOI.PURCHASE_ORDER_ID = @PO_ID
						AND POII.PURCHASE_ORDER_ITEM_ID IS NULL;
				END

			END
			------------------------------------------------------------------------------------------------

			UPDATE FRI SET
				IS_ADDED_TO_PO = 1
			FROM F_RMA_ITEM FRI WITH(ROWLOCK)
			INNER JOIN @T_ITEM_AMOUNTS TI
				ON TI.RMA_ITEM_ID = FRI.RMA_ITEM_ID;

			INSERT INTO @T_POS (value, label, isFlagged)
			SELECT TOP(1)
				PO.PURCHASE_ORDER_ID,
				PO.AUTO_NAME,
				@IS_RECYCLING_CONSIGNMENT
			 FROM F_PURCHASE_ORDER PO WITH(NOLOCK)
			 WHERE PO.PURCHASE_ORDER_ID = @PO_ID;

			-- Preparing for the next step
			FETCH NEXT FROM C_CREATE_CUSTOMER_PO INTO @PO_CUSTOMER_ID, @PO_ID, @IS_RMA_USED, @CONSIGNMENT_ID;
		END

		CLOSE C_CREATE_CUSTOMER_PO;
		DEALLOCATE C_CREATE_CUSTOMER_PO;

	COMMIT TRAN;

	SELECT * FROM @T_POS;

END

-- =============================================
-- Author:		<<PERSON>>
-- Create date: <>
-- Description:	<>
-- =============================================
CREATE FUNCTION [dbo].[fn_money_GET_RECYCLING_AMOUNT_OWED]
	(
		@ORDER_ID BIGINT
	)
RETURNS FLOAT
BEGIN
	DECLARE @PRICE FLOAT = 0.0
	
	SELECT
		@PRICE = ISNULL(
					(SELECT 
						SUM(ISNULL(ITEM_SERVICE_PRICE_FOR_ONE, 0.0) * ITEM_SERVICE_COUNT)
					FROM dbo.F_RECYCLING_ORDER_ITEM_SERVICE WITH (NOLOCK)
					WHERE RECYCLING_ORDER_ID = @ORDER_ID
					), 0) + 
				ISNULL(
					(
						SELECT	SUM([dbo].fn_money_GET_RECYCLING_ITEM_PRICE(OI.RECYCLING_ORDER_ITEM_ID, RO.RECYCLING_SETTLEMENT_STATE_ID))				
						FROM F_RECYCLING_ORDER_ITEM		OI	WITH(NOLOCK)
						INNER JOIN F_RECYCLING_ORDER	RO  WITH(NOLOCK)
							ON OI.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
						WHERE OI.RECYCLING_ORDER_ID = @ORDER_ID
					), 0)

	RETURN @PRICE
END
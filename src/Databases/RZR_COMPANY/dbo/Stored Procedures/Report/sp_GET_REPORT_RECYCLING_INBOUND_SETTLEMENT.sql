CREATE PROCEDURE [dbo].[sp_GET_REPORT_RECYCLING_INBOUND_SETTLEMENT]
	@CUSTOMER_IDs		bigint_ID_ARRAY READONLY,
	@REP_USER_IDs		bigint_ID_ARRAY READONLY,
	@WAREHOUSE_IDs		bigint_ID_ARRAY READONLY,
	@DATE_FROM			DATETIME		= NULL,
	@DATE_TO			DATETIME		= NULL,
	@DateFilter			NVARCHAR(150)	= N'CompletedDate',
	@ORDER_COLUMN_NAME	varchar(150)	= N'ReceiveDate',
	@ORDER_DIRECTION	varchar(20)		= N'DESC',
	@ITEMS_PER_PAGE		int				= 25,
	@PAGE_INDEX			int				= 0,
	@FILTER_WHERE		varchar(2000)	= N'',
	@SEPARATOR			NVARCHAR(8)		= N'}}{{',
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN
	DECLARE @HAS_CUSTOMERS	BIT = IIF(EXISTS(SELECT TOP(1) 1 FROM @CUSTOMER_IDs),  1, 0)
	DECLARE @HAS_USERS		BIT	= IIF(EXISTS(SELECT TOP(1) 1 FROM @REP_USER_IDs),  1, 0)
	DECLARE @HAS_WAREHOUSES BIT = IIF(EXISTS(SELECT TOP(1) 1 FROM @WAREHOUSE_IDs), 1, 0)

	SET @FILTER_WHERE = LTRIM(RTRIM(@FILTER_WHERE))
	SET @FILTER_WHERE = IIF(ISNULL(@FILTER_WHERE, '') != '' AND @FILTER_WHERE != 'null', @FILTER_WHERE, '(0=0)');
	SET @ORDER_COLUMN_NAME = IIF(@ORDER_COLUMN_NAME = 'NetWeight', 'ISNULL(m.GrossWeight, 0.0) - ISNULL(m.TareWeight, 0.0)', @ORDER_COLUMN_NAME);

	DECLARE @dateCondition NVARCHAR(2000) = CASE
		  WHEN @DATE_FROM IS NOT NULL 
		  THEN CASE @DateFilter 
				WHEN 'CompletedDate' THEN N' froin.COMPLETED_DT BETWEEN @DateFrom AND @DateTo AND'
				WHEN 'PickupDate' THEN N' fro.PICKUP_START_DATE BETWEEN @DateFrom AND @DateTo AND'
				ELSE N' froin.COMPLETED_DT BETWEEN @DateFrom AND @DateTo AND' -- default CompletedDate
			END 
		  ELSE N''
	   END
	
	DECLARE @Q_ORDERS NVARCHAR (MAX) = N'
		DECLARE @SEPARATOR_LEN	INT = LEN(@SEPARATOR);
		;WITH m_data AS (
			SELECT
			 fro.RECYCLING_ORDER_ID										AS RecyclingOrderId,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN froin.AUTO_NAME
				ELSE froot.AUTO_NAME
			 END													    AS AutoName,
			 c.CUSTOMER_ID												AS CustomerId,
			 c.CUSTOMER_NAME											AS CustomerCd,
			 cc.WORK_PHONE_NUMBER										AS MainPhone,
			 u.UserName													AS UserCd,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(froin.CUSTOMER_ADDRESS_ID)
				ELSE dbo.fn_str_GET_CUSTOMER_ADDRESS_AUTO_NAME(frot.PICKUP_LOCATION_ID) 
			 END														AS Location,
			 fro.PICKUP_START_DATE										AS PickupDate,									
			 froin.COMPLETED_DT											AS ReceiveDate,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN 
				    CAST(DATEDIFF(hh, froin.RECIEVE_DATE, GETUTCDATE()) / 24 AS VARCHAR(100)) + ''.'' + CAST(DATEDIFF(hh, froin.RECIEVE_DATE, GETUTCDATE()) % 24 AS VARCHAR(100))
				ELSE 
				    CAST(DATEDIFF(hh, froot.LOADED_DT, GETUTCDATE()) / 24 AS VARCHAR(100)) + ''.'' + CAST(DATEDIFF(hh, froot.LOADED_DT, GETUTCDATE()) % 24 AS VARCHAR(100))
			 END													    AS [Age],
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN 
					inbs.[Name]		-- inbound order status name
				ELSE
					os.STATUS_CD	-- outbound order status name
			 END														AS Status,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN [dbo].[fn_money_GET_RECYCLING_ORDER_SUBTOTAL](fro.[RECYCLING_SETTLEMENT_STATE_ID], froin.RECYCLING_ORDER_ID)
				ELSE [dbo].[fn_money_GET_RECYCLING_ORDER_OUTBOUND_SUBTOTAL](fro.RECYCLING_ORDER_ID)
			 END													    AS AmountOwed,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN [dbo].[fn_float_GET_RECYCLING_ORDER_WEIGHT_RECEIVED](fro.RECYCLING_SETTLEMENT_STATE_ID, fro.RECYCLING_ORDER_ID)
				ELSE IO.GROSS_WEIGHT
			 END													    AS GrossWeight,
			 CASE
				WHEN fro.IS_INBOUND = 1 THEN [dbo].[fn_float_GET_RECYCLING_ORDER_WEIGHT_TARE](fro.RECYCLING_SETTLEMENT_STATE_ID, fro.RECYCLING_ORDER_ID)
				ELSE IO.TARE_WEIGHT
			 END													    AS TareWeight,			 
			 W.WAREHOUSE_CD												AS Warehouse,
			 fro.IS_INBOUND												as IsInbound,
			 ([dbo].[fn_str_CONCATENATE_RECYCLING_ORDER_ITEM_TAGS](fro.RECYCLING_ORDER_ID	,null))	 AS Tags
		  FROM dbo.F_RECYCLING_ORDER				   fro	 WITH (NOLOCK)	
			LEFT JOIN C_RECYCLING_ORDER_STATUS		   os	 WITH (NOLOCK)
				ON fro.RECYCLING_ORDER_STATUS_ID = os.RECYCLING_ORDER_STATUS_ID
			LEFT JOIN dbo.F_RECYCLING_ORDER_INBOUND	   froin	 WITH (NOLOCK)
				ON fro.RECYCLING_ORDER_ID = froin.RECYCLING_ORDER_ID
			LEFT JOIN [recycling].C_InboundOrderStatus	inbs WITH (NOLOCK)
				ON froin.[StatusId] = inbs.[Id]
			LEFT JOIN dbo.F_RECYCLING_ORDER_OUTBOUND	   froot   WITH(NOLOCK)
				ON fro.RECYCLING_ORDER_ID = froot.RECYCLING_ORDER_ID	
			LEFT JOIN dbo.F_RECYCLING_OUTBOUND_TRUCKING   frot    WITH(NOLOCK)
				ON froot.RECYCLING_OUTBOUND_TRUCKING_ID = frot.RECYCLING_OUTBOUND_TRUCKING_ID		
			INNER JOIN 	dbo.F_CUSTOMER C WITH (NOLOCK)	
				ON fro.CUSTOMER_ID = C.CUSTOMER_ID		
			INNER JOIN 	dbo.tb_User				   u		 WITH (NOLOCK)
				ON fro.USER_ID = u.UserID ';

	SET @Q_ORDERS = @Q_ORDERS + N'
			LEFT JOIN 	dbo.F_CUSTOMER_CONTACT		   cc	 WITH (NOLOCK)
				ON c.CUSTOMER_ID = cc.CUSTOMER_ID AND cc.IS_MAIN = 1
			LEFT JOIN 	dbo.F_CUSTOMER_ADDRESS		   ca	 WITH (NOLOCK)
				ON  c.CUSTOMER_ID = ca.CUSTOMER_ID 
				AND ca.CUSTOMER_ADDRESS_TYPE_ID = 2 
				AND ca.IS_MAIN = 1		 
			LEFT JOIN dbo.D_WAREHOUSE			 W WITH(NOLOCK)
				ON W.WAREHOUSE_ID = fro.WAREHOUSE_ID
			LEFT JOIN
			(
					SELECT OUTBOUND_ORDER_ID, SUM(WEIGHT_RECEIVED) AS GROSS_WEIGHT, SUM(WEIGHT_TARE) AS TARE_WEIGHT
					FROM dbo.F_RECYCLING_ORDER_ITEM WITH (NOLOCK)
					WHERE IS_MERGED = 0
					GROUP BY OUTBOUND_ORDER_ID
			) IO ON fro.RECYCLING_ORDER_ID = IO.OUTBOUND_ORDER_ID
			WHERE ' + @dateCondition + N'
			  (@HAS_CUSTOMERS  = 0 OR EXISTS(SELECT TOP(1) 1 FROM @CUSTOMER_IDs  T WHERE T.ID = C.CUSTOMER_ID))
			  AND (@HAS_WAREHOUSES = 0 OR EXISTS(SELECT TOP(1) 1 FROM @WAREHOUSE_IDs T WHERE T.ID = W.WAREHOUSE_ID))
			  AND (@HAS_USERS	  = 0 OR EXISTS(SELECT TOP(1) 1 FROM @REP_USER_IDs  T WHERE T.ID = u.UserID))
		)
	'
	DECLARE @Q_select NVARCHAR(MAX) = N'		
			SELECT TOP(1) 
				-1 AS RowId
				,count(1) RecyclingOrderId	
				,null as AutoName	
				,null as CustomerId	
				,null as CustomerCd	
				,null as MainPhone	
				,null as UserCd	
				,null as Location	
				,null as PickupDate	
				,null as ReceiveDate	
				,null as Age	
				,null as Status	
				,null as AmountOwed	
				,null as GrossWeight
				,null as TareWeight
				,null as NetWeight
				,null as Warehouse
				,null as Tags
				,null as OrderLotWorkflow
				,null as OrderLotWorkflowOrderName
				,null as OrderLotWorkflowValue
				,null as ItemCount
				,null as PackageCount
			FROM m_data
			WHERE ' + @FILTER_WHERE + '
			UNION ALL
			SELECT
				ORDERS.*
			FROM (
			SELECT
					ROW_NUMBER() OVER(ORDER BY '+ @ORDER_COLUMN_NAME + ' '+ @ORDER_DIRECTION + ')	AS RowId
					,m.RecyclingOrderId	
					,m.AutoName	
					,m.CustomerId	
					,m.CustomerCd	
					,m.MainPhone	
					,m.UserCd	
					,m.Location	
					,m.PickupDate	
					,m.ReceiveDate	
					,m.Age	
					,m.Status	
					,m.AmountOwed	
					,m.GrossWeight
					,m.TareWeight
					,ISNULL(m.GrossWeight, 0.0) - ISNULL(m.TareWeight, 0.0)	as NetWeight
					,m.Warehouse
					,m.Tags
					,ISNULL(t.OrderLotWorkflow,'''') as OrderLotWorkflow
					,ISNULL(t.OrderLotWorkflowOrderName, '''') as OrderLotWorkflowOrderName 
					,ISNULL(t.OrderLotWorkflowValue, '''') as OrderLotWorkflowValue
					,CASE
						WHEN m.IsInbound  = 0 THEN 
						(SELECT SUM(I.[ITEM_COUNT]) 
							FROM [dbo].[F_RECYCLING_ORDER_ITEM] I WITH(NOLOCK) 
							WHERE I.[OUTBOUND_ORDER_ID]	= m.RecyclingOrderId)
						ELSE  
						(SELECT SUM(I.[ITEM_COUNT]) 
							FROM [dbo].[F_RECYCLING_ORDER_ITEM] I WITH(NOLOCK) 
							WHERE I.[RECYCLING_ORDER_ID]	= m.RecyclingOrderId)
					END																as ItemCount
					,CASE
						WHEN m.IsInbound  = 0 THEN 
						(SELECT COUNT(I.[OUTBOUND_ORDER_ID]) 
							FROM [dbo].[F_RECYCLING_ORDER_ITEM] I WITH(NOLOCK) 
							WHERE I.[OUTBOUND_ORDER_ID] = m.RecyclingOrderId)
						ELSE
						(SELECT COUNT(I.[RECYCLING_ORDER_ID]) 
							FROM [dbo].[F_RECYCLING_ORDER_ITEM] I WITH(NOLOCK) 
							WHERE I.[RECYCLING_ORDER_ID]	= m.RecyclingOrderId)
					END																as PackageCount
				FROM m_data M ';

	SET @Q_select = @Q_select + N'
				OUTER APPLY (
					SELECT	 
						LTRIM(STUFF(
							(
								SELECT 
									@SEPARATOR + crwt.WORKFLOW_TYPE_DESC
								FROM					
									F_RECYCLING_ORDER_ITEM froi WITH (NOLOCK)
									INNER JOIN C_RECYCLING_WORKFLOW_TYPE crwt WITH (NOLOCK)
										ON froi.WORKFLOW_STEP_ID = crwt.WORKFLOW_TYPE_ID
								WHERE
									froi.RECYCLING_ORDER_ID = m.RecyclingOrderId
								GROUP BY crwt.WORKFLOW_TYPE_DESC

								FOR XML PATH(''''), TYPE
							).value(''.'',''varchar(max)''), 1, @SEPARATOR_LEN, ''''))	AS OrderLotWorkflow
						,LTRIM(STUFF(
							(
								SELECT 
									@SEPARATOR + cast(RecyclingOrderId  as nvarchar(20))
								FROM					
									F_RECYCLING_ORDER_ITEM froi WITH (NOLOCK)
									INNER JOIN C_RECYCLING_WORKFLOW_TYPE crwt WITH (NOLOCK)
										ON froi.WORKFLOW_STEP_ID = crwt.WORKFLOW_TYPE_ID
								WHERE
									froi.RECYCLING_ORDER_ID = m.RecyclingOrderId
								GROUP BY crwt.WORKFLOW_TYPE_DESC
								FOR XML PATH(''''), TYPE
							).value(''.'',''varchar(max)''), 1, @SEPARATOR_LEN, ''''))	AS OrderLotWorkflowOrderName
						,LTRIM(STUFF(
							(
								SELECT 
									@SEPARATOR + ''True''
								FROM					
									F_RECYCLING_ORDER_ITEM froi WITH (NOLOCK)
									INNER JOIN C_RECYCLING_WORKFLOW_TYPE crwt WITH (NOLOCK)
										ON froi.WORKFLOW_STEP_ID = crwt.WORKFLOW_TYPE_ID
								WHERE
									froi.RECYCLING_ORDER_ID = m.RecyclingOrderId
								GROUP BY crwt.WORKFLOW_TYPE_DESC
								FOR XML PATH(''''), TYPE
							).value(''.'',''varchar(max)''), 1, @SEPARATOR_LEN, ''''))	AS OrderLotWorkflowValue
				)	t
				WHERE ' + @FILTER_WHERE + '				
				ORDER BY RowId
				OFFSET		@PAGE_INDEX * @ITEMS_PER_PAGE	ROWS
				FETCH NEXT	@ITEMS_PER_PAGE					ROWS ONLY
			) ORDERS		
	'
	SET @Q_ORDERS = @Q_ORDERS + @Q_select

	IF (@C_IS_DEBUG = 1)
		PRINT(CAST(@Q_ORDERS AS NTEXT))
	else
	EXEC sp_ExecuteSql @Q_ORDERS,
		N'@PAGE_INDEX		INT
		,@ITEMS_PER_PAGE	INT
		,@DateFrom			DATETIME
		,@DateTo  			DATETIME
		,@HAS_CUSTOMERS		BIT
		,@HAS_WAREHOUSES	BIT
		,@HAS_USERS			BIT
		,@CUSTOMER_IDs		bigint_ID_ARRAY READONLY
		,@WAREHOUSE_IDs		bigint_ID_ARRAY READONLY
		,@REP_USER_IDs		bigint_ID_ARRAY READONLY
		,@SEPARATOR			NVARCHAR(20)'
		,@PAGE_INDEX		= @PAGE_INDEX	
		,@ITEMS_PER_PAGE	= @ITEMS_PER_PAGE
		,@DateFrom			= @DATE_FROM		
		,@DateTo  			= @DATE_TO  		
		,@HAS_CUSTOMERS		= @HAS_CUSTOMERS	
		,@HAS_WAREHOUSES	= @HAS_WAREHOUSES
		,@HAS_USERS			= @HAS_USERS		
		,@CUSTOMER_IDs		= @CUSTOMER_IDs	
		,@WAREHOUSE_IDs		= @WAREHOUSE_IDs	
		,@REP_USER_IDs		= @REP_USER_IDs	
		,@SEPARATOR			= @SEPARATOR
END
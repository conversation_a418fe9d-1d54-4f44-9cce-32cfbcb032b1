CREATE TABLE [dbo].[C_INVENTORY_CAPABILITY_SIZE] (
    [INVENTORY_CAPABILITY_SIZE_ID]        INT           NOT NULL,
    [PHISICAL_PARAMETER_TYPE_ID]          INT           NOT NULL,
    [INVENTORY_CAPABILITY_SIZE_SUFFIX]    NVARCHAR (50) NOT NULL,
    [INVENTORY_CAPABILITY_SIZE_MULIPLIER] FLOAT (53)    NOT NULL,
    CONSTRAINT [PK_C_INVENTORY_CAPABILITY_SIZE] PRIMARY KEY CLUSTERED ([INVENTORY_CAPABILITY_SIZE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Is not used it seems', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_INVENTORY_CAPABILITY_SIZE';


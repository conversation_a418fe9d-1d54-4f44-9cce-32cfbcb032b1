-- =============================================
-- Author:		<O.Evseev>
-- Create date: <04/16/2014>
-- =============================================
CREATE PROCEDURE [dbo].[sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID]
    (@WAREHOUSE_ID			BIGINT
	,@RECYCLING_ORDER_ID	BIGINT OUTPUT)
AS
BEGIN
	SET XACT_ABORT ON
	BEGIN TRAN
		
		SELECT
			@RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
		FROM dbo.F_RECYCLING_ORDER RO WITH (NOLOCK)
		INNER JOIN dbo.F_RECYCLING_ORDER_INBOUND ROI  WITH (NOLOCK)
			ON ROI.RECYCLING_ORDER_ID = RO.RECYCLING_ORDER_ID
		WHERE ROI.IsFromConsumable = 1
		  AND (WAREHOUSE_ID = @WAREHOUSE_ID
			OR @WAREHOUSE_ID IS NULL AND WAREHOUSE_ID IS NULL)

	
		IF (@RECYCLING_ORDER_ID IS NULL)
		BEGIN
			DECLARE
				@homeCurrencyExchangeId bigint = [dbo].[fn_bigint_CompanyHomeCurrencyExchangeId]()
				
			INSERT INTO dbo.F_RECYCLING_ORDER (
				[USER_ID],
				SO_TERM_ID,
				CUSTOMER_ID,
				WAREHOUSE_ID,
				IS_INBOUND,
				CurrencyExchangeId,
				INSERTED_BY,
				INSERTED_DT				
			) VALUES (
				dbo.fn_bigint_GET_USER_ADMIN_ID(),
				1,
				dbo.fn_bigint_GET_MAIN_CUSTOMER_ID(),
				@WAREHOUSE_ID,
				1,
				@homeCurrencyExchangeId,
				'sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID', 
				GETUTCDATE()
			)
			
			SET @RECYCLING_ORDER_ID = SCOPE_IDENTITY()

			DECLARE @ORDER_START		BIGINT		  = [dbo].[fn_str_AUTO_NUMBER_INBOUND_ORDER_STARTING_NUMBER]() - 1
			DECLARE @MAX_NUMBER			BIGINT		  = (
				SELECT
					MAX(CAST(SUBSTRING(AUTO_NAME, PATINDEX('%[0-9]%', AUTO_NAME), LEN(AUTO_NAME)) AS INT))
				FROM dbo.F_RECYCLING_ORDER_INBOUND WITH (NOLOCK)
				WHERE IS_QUOTE = 0 
				  AND IS_REVISION = 0)
			DECLARE @AUTO_NAME			NVARCHAR(100) = [dbo].[fn_str_AUTO_NUMBER_INBOUND_ORDER_PREFIX]() + N'-' + CAST(ISNULL(@MAX_NUMBER, @ORDER_START) + 1 AS VARCHAR(250))

            INSERT INTO dbo.F_RECYCLING_ORDER_INBOUND (
                RECYCLING_ORDER_ID,
                IsFromConsumable,
				IS_QUOTE,
                IS_REVISION,
                AUTO_NAME,
                INSERTED_BY,
                INSERTED_DT
            ) VALUES (
                @RECYCLING_ORDER_ID,
				1,
                0,
                0,
                @AUTO_NAME,
                'sp_bigint_GET_FROM_CONSUMED_RECYCLING_ORDER_ID', 
                GETUTCDATE()
            )                
		END

	COMMIT TRAN
END
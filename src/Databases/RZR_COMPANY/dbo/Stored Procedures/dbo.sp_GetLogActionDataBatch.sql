create procedure [dbo].[sp_GetLogActionDataBatch] @LastProcessedEntityId bigint,
                                                  @BatchSize int = 100
as
begin
    declare @LastEntity bigint;
    select @LastEntity = max(LOG_ACTION_DATA_ID) from F_LOG_ACTION_DATA with (nolock);
    if @LastEntity is null
        begin
            select @LastEntity = max(LOG_ACTION_DATA_HISTORY_ID) from F_LOG_ACTION_DATA_HISTORY with (nolock);
        end
    if @LastEntity is null or @LastProcessedEntityId = @LastEntity
        begin
            return;
        end

    declare @NextEntityId bigint = null;
    declare @FromHistoryTable bit = 1;

    declare @minId bigint = null;
    declare @maxId bigint = null;

    if @LastProcessedEntityId is null or @LastProcessedEntityId = 0
        begin
            if exists(select 1 from [dbo].[F_LOG_ACTION_DATA_HISTORY] with (nolock))
                begin
                    set @NextEntityId = (select top (1) [MIN_ID]
                                         from (select min(LOG_ACTION_DATA_HISTORY_ID) as [MIN_ID]
                                               from [dbo].[F_LOG_ACTION_DATA_HISTORY] with (nolock)) as IDS
                                         order by [MIN_ID] asc);
                end
            else
                begin
                    set @NextEntityId = (select top (1) [MIN_ID]
                                         from (select min(LOG_ACTION_DATA_ID) as [MIN_ID]
                                               from [dbo].[F_LOG_ACTION_DATA] with (nolock)) as IDS
                                         order by [MIN_ID] asc);
                    set @FromHistoryTable = 0;
                end
        end
    else
        begin
            select @minId = min([LOG_ACTION_DATA_HISTORY_ID]),
                   @maxId = max([LOG_ACTION_DATA_HISTORY_ID])
            from [dbo].[F_LOG_ACTION_DATA_HISTORY] with (nolock)

            if (exists(select 1
                       from [dbo].[F_LOG_ACTION_DATA_HISTORY] with (nolock)
                       where [LOG_ACTION_DATA_HISTORY_ID] = @LastProcessedEntityId) or
                @LastProcessedEntityId between @minId and @maxId)
                begin
                    select top (1) @NextEntityId = [LOG_ACTION_DATA_HISTORY_ID]
                    from [dbo].[F_LOG_ACTION_DATA_HISTORY] with (nolock)
                    where [LOG_ACTION_DATA_HISTORY_ID] > @LastProcessedEntityId

                    if @NextEntityId is null
                        begin
                            select @NextEntityId = min([LOG_ACTION_DATA_ID])
                            from [dbo].[F_LOG_ACTION_DATA] with (nolock);
                            set @FromHistoryTable = 0;
                        end
                end
            else
                begin
                    select @minId = min([LOG_ACTION_DATA_ID]),
                           @maxId = max([LOG_ACTION_DATA_ID])
                    from [dbo].[F_LOG_ACTION_DATA] with (nolock)

                    if (exists(select 1
                               from [dbo].[F_LOG_ACTION_DATA] with (nolock)
                               where [LOG_ACTION_DATA_ID] = @LastProcessedEntityId) or
                        @LastProcessedEntityId between @minId and @maxId)
                        begin
                            select top (1) @NextEntityId = [LOG_ACTION_DATA_ID]
                            from [dbo].[F_LOG_ACTION_DATA] with (nolock)
                            where [LOG_ACTION_DATA_ID] > @LastProcessedEntityId
                            set @FromHistoryTable = 0;
                        end
                end
        end

    if @NextEntityId is null
        begin
            declare @Message nvarchar(max) =
                N'Cannot determine Next Entity Id for F_LOG_ACTION_DATA, last processed Id was: ' +
                cast(@LastProcessedEntityId as varchar(20));
            ;
            throw 52000, @Message, 1;
        end

    if @FromHistoryTable = 0
        begin
            select top (@BatchSize) FLAD.[LOG_ACTION_DATA_ID]                                            as [LogActionDataId],
                                    FLAD.[ACTION_ID]                                                     as [ActionId],
                                    CA.[ACTION_NAME]                                                     as [ActionName],
                                    FLAD.[ENTITY_TYPE_ID]                                                as [EntityTypeId],
                                    CLET.[ENTITY_TYPE_NAME]                                              as [EntityTypeName],
                                    FLAD.[ENTITY_KEY_VALUE]                                              as [EntityKeyValue],
                                    FLAD.[ENTITY_AUTO_NAME]                                              as [EntityAutoName],
                                    FLAD.[CHANED_PROPERTY_PREV_ID]                                       as [ChangedPropertyPrevId],
                                    FLAD.[CHANED_PROPERTY_PREV_VALUE]                                    as [ChangedPropertyPrevValue],
                                    FLAD.[CHANED_PROPERTY_CURRENT_ID]                                    as [ChangedPropertyCurrentId],
                                    FLAD.[CHANED_PROPERTY_CURRENT_VALUE]                                 as [ChangedPropertyCurrentValue],
                                    FLAD.[USER_ID]                                                       as [UserId],
                                    TBU.[UserName]                                                       as [UserLogin],
                                    TBU.[Email]                                                          as [Email],
                                    ltrim(rtrim(concat(TBU.[FirstName], N' ', TBU.[LastName])))          as [UserName],
                                    FLAD.[USER_IP]                                                       as [UserIp],
                                    FLAD.[LOGGED_DT]                                                     as [LoggedAt],
                                    FLAD.[INSERTED_DT]                                                   as [InsertedAt],
                                    FLAD.[SOURCE]                                                        as [Source],
                                    case
                                        when FLAD.[ENTITY_TYPE_ID] = 9 then FROI.[RECYCLING_ORDER_ITEM_ID]
                                        when FLAD.[ENTITY_TYPE_ID] = 5 then FROI2.[RECYCLING_ORDER_ITEM_ID]
                                        end                                                              as [LotId],
                                    case
                                        when FLAD.[ENTITY_TYPE_ID] = 9 then FROI.[LOT_AUTO_NAME]
                                        when FLAD.[ENTITY_TYPE_ID] = 5 then FROI2.[LOT_AUTO_NAME]
                                        end                                                              as [LotAutoName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 17, FA.[SerialNumber],
                                        FII.[ITEM_INVENTORY_SERIAL])                                     as [Serial],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 5,
                                        [dbo].[fn_str_GET_LOCATON_NAME_WITH_SECONDARY](FROI2.[LOCATION_ID]),
                                        coalesce(FL.[LOCATION_NAME], FDET.[LOCATION_SYSTEM_NAME]))       as [Location],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 17, isnull(FA.ItemNumber, FA_FIM.ITEM_NUMBER),
                                        FIM.[ITEM_NUMBER])                                               as [Model],
                                    coalesce(FROI.[RECYCLING_ITEM_MASTER_ID],
                                             FROI2.[RECYCLING_ITEM_MASTER_ID])                           as [CommodityId],
                                    FRIM.[RECYCLING_ITEM_MASTER_NAME]                                    as [CommodityName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 3, FLAD.[ENTITY_KEY_VALUE],
                                        coalesce(FROI.[RECYCLING_ORDER_ID], FROI2.[RECYCLING_ORDER_ID])) as [InboundOrderId],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 3, FLAD.[ENTITY_AUTO_NAME],
                                        FROIN.[AUTO_NAME])                                               as [InboundOrderAutoName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 4, FLAD.[ENTITY_KEY_VALUE],
                                        coalesce(FROI.[OUTBOUND_ORDER_ID], FROI2.[OUTBOUND_ORDER_ID]))   as [OutboundOrderId],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 4, FLAD.[ENTITY_AUTO_NAME],
                                        FROOUT.[AUTO_NAME])                                              as [OutboundOrderAutoName],
                                    DW.[WAREHOUSE_ID]                                                    as [WarehouseId],
                                    DW.[WAREHOUSE_CD]                                                    as [WarehouseName]
            from [dbo].[F_LOG_ACTION_DATA] FLAD with (nolock)
                     left join [dbo].[tb_User] TBU with (nolock)
                               on FLAD.[USER_ID] = TBU.[UserID]

                     left join [dbo].[C_ACTION] CA with (nolock)
                               on FLAD.[ACTION_ID] = CA.[ACTION_ID]

                     left join [dbo].[C_LOGGED_ENTITY_TYPE] CLET with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = CLET.[ENTITY_TYPE_ID]

                     left join [dbo].[F_ITEM_INVENTORY] FII with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = 9 and FLAD.[ENTITY_KEY_VALUE] = FII.[ITEM_INVENTORY_ID]
                     left join [dbo].[F_RECYCLING_ORDER_ITEM] FROI with (nolock)
                               on FII.[RECYCLING_ORDER_ITEM_ID] = FROI.[RECYCLING_ORDER_ITEM_ID]
                     left join [dbo].[F_LOCATION] FL_FII with (nolock)
                               on FII.[LOCATION_ID] = FL_FII.[LOCATION_ID]
                     left join [dbo].[F_ITEM_MASTER] FIM with (nolock)
                               on FII.[ITEM_MASTER_ID] = FIM.[ITEM_MASTER_ID]

                     left join [dbo].[F_RECYCLING_ORDER_ITEM] FROI2 with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = 5 and
                                  FLAD.[ENTITY_KEY_VALUE] = FROI2.[RECYCLING_ORDER_ITEM_ID]
                     left join [dbo].[F_LOCATION] FL_FROI with (nolock)
                               on FROI2.[LOCATION_ID] = [FL_FROI].LOCATION_ID

                     left join [dbo].[F_RECYCLING_ITEM_MASTER] FRIM with (nolock)
                               on FROI.[RECYCLING_ITEM_MASTER_ID] = FRIM.[RECYCLING_ITEM_MASTER_ID] or
                                  FROI2.[RECYCLING_ITEM_MASTER_ID] = FRIM.[RECYCLING_ITEM_MASTER_ID]

                     left join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
                               on FROI.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID] or
                                  FROI2.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID] or
                                  FLAD.[ENTITY_TYPE_ID] in (3, 4) and FRO.[RECYCLING_ORDER_ID] = FLAD.[ENTITY_KEY_VALUE]

                     left join [dbo].[F_RECYCLING_ORDER_INBOUND] FROIN with (nolock)
                               on FRO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID or
                                  FROIN.[RECYCLING_ORDER_ID] = FROI2.[RECYCLING_ORDER_ID]

                     left join [dbo].[F_RECYCLING_ORDER_OUTBOUND] FROOUT with (nolock)
                               on FROI.[OUTBOUND_ORDER_ID] = FROOUT.[RECYCLING_ORDER_ID] or
                                  FROOUT.[RECYCLING_ORDER_ID] = FROI2.[OUTBOUND_ORDER_ID]

                     left join [dbo].[F_CUSTOMER_ADDRESS] FCA with (nolock)
                               on FCA.[CUSTOMER_ADDRESS_ID] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 21
                     left join [dbo].[F_LOCATION_DETAIL] FCADET with (nolock)
                               on FCA.[LOCATION_DETAILS_ID] = FCADET.[LOCATION_DETAIL_ID]

                     left join [recycling].[F_Asset] FA with (nolock)
                               on FA.[Id] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 17
                     left join [dbo].[F_ITEM_MASTER] FA_FIM with (nolock)
                               on FA.[ItemMasterId] = FA_FIM.[ITEM_MASTER_ID]
                     left join [dbo].[F_LOCATION] FL_FA with (nolock)
                               on FA.[LocationId] = FL_FA.[LOCATION_ID]

                     left join [dbo].[D_WAREHOUSE] DW with (nolock)
                               on
                                   FLAD.[ENTITY_TYPE_ID] in (3, 4) and FRO.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 5 and FL_FROI.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 9 and FL_FII.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 17 and FL_FA.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID]

                     left join [dbo].[F_SALES_ORDER_ITEM] FSOI with (nolock)
                               on FSOI.[SALES_ORDER_ITEM_ID] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 2

                     left join [dbo].[F_LOCATION] LL with (nolock)
                               on ll.LOCATION_ID = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 25

                     left join [dbo].[F_LOCATION] FL with (nolock)
                               on FL.[LOCATION_ID] = coalesce(FA.[LocationId], FSOI.[LOCATION_ID], LL.[LOCATION_ID],
                                                              FII.[LOCATION_ID], FROI2.[LOCATION_ID],
                                                              FCADET.[INVENTORY_CAPABILITY_ID])
                     left join [dbo].[F_LOCATION_DETAIL] FDET with (nolock)
                               on FL.[LOCATION_ID] = FDET.[INVENTORY_CAPABILITY_ID]

            where FLAD.[LOG_ACTION_DATA_ID] >= @NextEntityId
            order by FLAD.[LOG_ACTION_DATA_ID] asc
        end
    else
        begin
            select top (@BatchSize) FLAD.[LOG_ACTION_DATA_HISTORY_ID]                                    as [LogActionDataId],
                                    FLAD.[ACTION_ID]                                                     as [ActionId],
                                    CA.[ACTION_NAME]                                                     as [ActionName],
                                    FLAD.[ENTITY_TYPE_ID]                                                as [EntityTypeId],
                                    CLET.[ENTITY_TYPE_NAME]                                              as [EntityTypeName],
                                    FLAD.[ENTITY_KEY_VALUE]                                              as [EntityKeyValue],
                                    FLAD.[ENTITY_AUTO_NAME]                                              as [EntityAutoName],
                                    FLAD.[CHANED_PROPERTY_PREV_ID]                                       as [ChangedPropertyPrevId],
                                    FLAD.[CHANED_PROPERTY_PREV_VALUE]                                    as [ChangedPropertyPrevValue],
                                    FLAD.[CHANED_PROPERTY_CURRENT_ID]                                    as [ChangedPropertyCurrentId],
                                    FLAD.[CHANED_PROPERTY_CURRENT_VALUE]                                 as [ChangedPropertyCurrentValue],
                                    FLAD.[USER_ID]                                                       as [UserId],
                                    TBU.[UserName]                                                       as [UserLogin],
                                    TBU.[Email]                                                          as [Email],
                                    ltrim(rtrim(concat(TBU.[FirstName], N' ', TBU.[LastName])))          as [UserName],
                                    FLAD.[USER_IP]                                                       as [UserIp],
                                    FLAD.[LOGGED_DT]                                                     as [LoggedAt],
                                    FLAD.[INSERTED_DT]                                                   as [InsertedAt],
                                    FLAD.[SOURCE]                                                        as [Source],
                                    case
                                        when FLAD.[ENTITY_TYPE_ID] = 9 then FROI.[RECYCLING_ORDER_ITEM_ID]
                                        when FLAD.[ENTITY_TYPE_ID] = 5 then FROI2.[RECYCLING_ORDER_ITEM_ID]
                                        end                                                              as [LotId],
                                    case
                                        when FLAD.[ENTITY_TYPE_ID] = 9 then FROI.[LOT_AUTO_NAME]
                                        when FLAD.[ENTITY_TYPE_ID] = 5 then FROI2.[LOT_AUTO_NAME]
                                        end                                                              as [LotAutoName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 17, FA.[SerialNumber],
                                        FII.[ITEM_INVENTORY_SERIAL])                                     as [Serial],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 5,
                                        [dbo].[fn_str_GET_LOCATON_NAME_WITH_SECONDARY](FROI2.[LOCATION_ID]),
                                        coalesce(FL.[LOCATION_NAME], FDET.[LOCATION_SYSTEM_NAME]))       as [Location],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 17, isnull(FA.ItemNumber, FA_FIM.ITEM_NUMBER),
                                        FIM.[ITEM_NUMBER])                                               as [Model],
                                    coalesce(FROI.[RECYCLING_ITEM_MASTER_ID],
                                             FROI2.[RECYCLING_ITEM_MASTER_ID])                           as [CommodityId],
                                    FRIM.[RECYCLING_ITEM_MASTER_NAME]                                    as [CommodityName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 3, FLAD.[ENTITY_KEY_VALUE],
                                        coalesce(FROI.[RECYCLING_ORDER_ID], FROI2.[RECYCLING_ORDER_ID])) as [InboundOrderId],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 3, FLAD.[ENTITY_AUTO_NAME],
                                        FROIN.[AUTO_NAME])                                               as [InboundOrderAutoName],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 4, FLAD.[ENTITY_KEY_VALUE],
                                        coalesce(FROI.[OUTBOUND_ORDER_ID], FROI2.[OUTBOUND_ORDER_ID]))   as [OutboundOrderId],
                                    iif(FLAD.[ENTITY_TYPE_ID] = 4, FLAD.[ENTITY_AUTO_NAME],
                                        FROOUT.[AUTO_NAME])                                              as [OutboundOrderAutoName],
                                    DW.[WAREHOUSE_ID]                                                    as [WarehouseId],
                                    DW.[WAREHOUSE_CD]                                                    as [WarehouseName]
            from [dbo].[F_LOG_ACTION_DATA_HISTORY] FLAD with (nolock)
                     left join [dbo].[tb_User] TBU with (nolock)
                               on FLAD.[USER_ID] = TBU.[UserID]

                     left join [dbo].[C_ACTION] CA with (nolock)
                               on FLAD.[ACTION_ID] = CA.[ACTION_ID]

                     left join [dbo].[C_LOGGED_ENTITY_TYPE] CLET with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = CLET.[ENTITY_TYPE_ID]

                     left join [dbo].[F_ITEM_INVENTORY] FII with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = 9 and FLAD.[ENTITY_KEY_VALUE] = FII.[ITEM_INVENTORY_ID]
                     left join [dbo].[F_RECYCLING_ORDER_ITEM] FROI with (nolock)
                               on FII.[RECYCLING_ORDER_ITEM_ID] = FROI.[RECYCLING_ORDER_ITEM_ID]
                     left join [dbo].[F_LOCATION] FL_FII with (nolock)
                               on FII.[LOCATION_ID] = FL_FII.[LOCATION_ID]
                     left join [dbo].[F_ITEM_MASTER] FIM with (nolock)
                               on FII.[ITEM_MASTER_ID] = FIM.[ITEM_MASTER_ID]

                     left join [dbo].[F_RECYCLING_ORDER_ITEM] FROI2 with (nolock)
                               on FLAD.[ENTITY_TYPE_ID] = 5 and
                                  FLAD.[ENTITY_KEY_VALUE] = FROI2.[RECYCLING_ORDER_ITEM_ID]
                     left join [dbo].[F_LOCATION] FL_FROI with (nolock)
                               on FROI2.[LOCATION_ID] = [FL_FROI].LOCATION_ID

                     left join [dbo].[F_RECYCLING_ITEM_MASTER] FRIM with (nolock)
                               on FROI.[RECYCLING_ITEM_MASTER_ID] = FRIM.[RECYCLING_ITEM_MASTER_ID] or
                                  FROI2.[RECYCLING_ITEM_MASTER_ID] = FRIM.[RECYCLING_ITEM_MASTER_ID]

                     left join [dbo].[F_RECYCLING_ORDER] FRO with (nolock)
                               on FROI.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID] or
                                  FROI2.[RECYCLING_ORDER_ID] = FRO.[RECYCLING_ORDER_ID] or
                                  FLAD.[ENTITY_TYPE_ID] in (3, 4) and FRO.[RECYCLING_ORDER_ID] = FLAD.[ENTITY_KEY_VALUE]

                     left join [dbo].[F_RECYCLING_ORDER_INBOUND] FROIN with (nolock)
                               on FRO.RECYCLING_ORDER_ID = FROIN.RECYCLING_ORDER_ID or
                                  FROIN.[RECYCLING_ORDER_ID] = FROI2.[RECYCLING_ORDER_ID]

                     left join [dbo].[F_RECYCLING_ORDER_OUTBOUND] FROOUT with (nolock)
                               on FROI.[OUTBOUND_ORDER_ID] = FROOUT.[RECYCLING_ORDER_ID] or
                                  FROOUT.[RECYCLING_ORDER_ID] = FROI2.[OUTBOUND_ORDER_ID]

                     left join [dbo].[F_CUSTOMER_ADDRESS] FCA with (nolock)
                               on FCA.[CUSTOMER_ADDRESS_ID] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 21
                     left join [dbo].[F_LOCATION_DETAIL] FCADET with (nolock)
                               on FCA.[LOCATION_DETAILS_ID] = FCADET.[LOCATION_DETAIL_ID]

                     left join [recycling].[F_Asset] FA with (nolock)
                               on FA.[Id] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 17
                     left join [dbo].[F_ITEM_MASTER] FA_FIM with (nolock)
                               on FA.[ItemMasterId] = FA_FIM.[ITEM_MASTER_ID]
                     left join [dbo].[F_LOCATION] FL_FA with (nolock)
                               on FA.[LocationId] = FL_FA.[LOCATION_ID]

                     left join [dbo].[D_WAREHOUSE] DW with (nolock)
                               on
                                   FLAD.[ENTITY_TYPE_ID] in (3, 4) and FRO.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 5 and FL_FROI.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 9 and FL_FII.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID] or
                                   FLAD.[ENTITY_TYPE_ID] = 17 and FL_FA.[WAREHOUSE_ID] = DW.[WAREHOUSE_ID]

                     left join [dbo].[F_SALES_ORDER_ITEM] FSOI with (nolock)
                               on FSOI.[SALES_ORDER_ITEM_ID] = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 2

                     left join [dbo].[F_LOCATION] LL with (nolock)
                               on ll.LOCATION_ID = FLAD.[ENTITY_KEY_VALUE]
                                   and FLAD.[ENTITY_TYPE_ID] = 25

                     left join [dbo].[F_LOCATION] FL with (nolock)
                               on FL.[LOCATION_ID] = coalesce(FA.[LocationId], FSOI.[LOCATION_ID], LL.[LOCATION_ID],
                                                              FII.[LOCATION_ID], FROI2.[LOCATION_ID],
                                                              FCADET.[INVENTORY_CAPABILITY_ID])
                     left join [dbo].[F_LOCATION_DETAIL] FDET with (nolock)
                               on FL.[LOCATION_ID] = FDET.[INVENTORY_CAPABILITY_ID]

            where FLAD.[LOG_ACTION_DATA_HISTORY_ID] >= @NextEntityId
            order by FLAD.[LOG_ACTION_DATA_HISTORY_ID] asc
        end
end

-- =============================================
-- Author:		<O.Evseev>
-- Create date: <02/18/2014>
-- Description:	<excludes a recycling order item from the outbound order>
-- =============================================
CREATE PROCEDURE [dbo].[sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_EXCLUDED]
	@OUTBOUND_ORDER_ID			BIGINT,
	@RECYCLING_ORDER_ITEM_ID	BIGINT,
	@USER_ID					BIGINT,
	@IP							BIGINT	
AS
BEGIN

	DECLARE @IS_TRANSFER    BIT;
	DECLARE @DEFAULT_LOCATION_ID BIGINT;
	SELECT
		@IS_TRANSFER	= R.IS_TRANSFER,
		@DEFAULT_LOCATION_ID = [dbo].[fn_bigint_GetDefaultLocationIdByWarehouse](R.WAREHOUSE_ID)
	FROM F_RECYCLING_ORDER	R WITH(NOLOCK)
	WHERE R.RECYCLING_ORDER_ID = @OUTBOUND_ORDER_ID

	UPDATE F_RECYCLING_ORDER_ITEM WITH(ROWLOCK) SET
		OUTBOUND_ORDER_ID	= NULL,
		WEIGHT_RECEIVED		= WEIGHT_RECEIVED + ISNULL(WEIGHT_LOOSE_LOAD, 0),	
		WEIGHT_TARE			= 
			CASE
				WHEN WEIGHT_LOOSE_LOAD IS NULL THEN WEIGHT_TARE
				ELSE ISNULL(WEIGHT_LOOSE_LOAD, 0)
			END,
		WEIGHT_LOOSE_LOAD			= NULL,		
		EXPORTED_DT					= NULL,
		UPDATED_BY					= N'sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_EXCLUDED',
		UPDATED_DT					= GETUTCDATE(),
		UPDATED_BY_USER				= @USER_ID,
		UPDATED_BY_IP				= @IP,
		LOCATION_ID                 = ISNULL(LOCATION_ID, @DEFAULT_LOCATION_ID)
	WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID
	  AND OUTBOUND_ORDER_ID = @OUTBOUND_ORDER_ID

	UPDATE ROI WITH(ROWLOCK) SET
		OUTBOUND_ORDER_ID			= NULL,
		EXPORTED_DT					= NULL,
		UPDATED_BY					= N'sp_SET_RECYCLING_ORDER_ITEM_OUTBOUND_EXCLUDED',
		UPDATED_DT					= GETUTCDATE(),
		UPDATED_BY_USER				= @USER_ID,
		UPDATED_BY_IP				= @IP,
		LOCATION_ID                 = ISNULL(LOCATION_ID, @DEFAULT_LOCATION_ID)
	FROM F_RECYCLING_ORDER_ITEM ROI
	WHERE ROI.RECYCLING_ORDER_ITEM_ID IN (SELECT RECYCLING_ORDER_ITEM_ID FROM F_RECYCLING_ORDER_ITEM_MERGE WITH (NOLOCK) WHERE RECYCLING_ORDER_ITEM_PRIMARY_ID = @RECYCLING_ORDER_ITEM_ID)
		AND ROI.OUTBOUND_ORDER_ID = @OUTBOUND_ORDER_ID

	IF (@IS_TRANSFER = 1) BEGIN
		DELETE FROM dbo.F_RECYCLING_ORDER_ITEM_TRANSFER
			WHERE RECYCLING_ORDER_ITEM_ID = @RECYCLING_ORDER_ITEM_ID
			AND OUTBOUND_ORDER_ID = @OUTBOUND_ORDER_ID
	END

END
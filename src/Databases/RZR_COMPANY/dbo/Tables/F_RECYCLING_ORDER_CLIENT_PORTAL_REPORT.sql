CREATE TABLE [dbo].[F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT] (
    [RECYCLING_ORDER_CLIENT_PORTAL_REPORT_ID] BIGINT         IDENTITY (1, 1) NOT NULL,
    [RECYCLING_ORDER_ID]                      BIGINT         NOT NULL,
    [CLIENT_PORTAL_ORDER_REPORT_TYPE_ID]      INT            NOT NULL,
    [IS_ENABLED]                              BIT            NOT NULL,
    [INSERTED_BY]                             NVARCHAR (150) NULL,
    [INSERTED_DT]                             DATETIME       NULL,
    [UPDATED_BY]                              NVARCHAR (150) NULL,
    [UPDATED_DT]                              DATETIME       NULL,
    CONSTRAINT [PK_F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_CLIENT_PORTAL_REPORT_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT_C_CLIENT_PORTAL_ORDER_REPORT_TYPE] FOREIGN KEY ([CLIENT_PORTAL_ORDER_REPORT_TYPE_ID])  REFERENCES [dbo].[C_CLIENT_PORTAL_ORDER_REPORT_TYPE] ([CLIENT_PORTAL_ORDER_REPORT_TYPE_ID]) ON UPDATE CASCADE,
    CONSTRAINT [FK_F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT_F_RECYCLING_ORDER] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID])
);




GO
CREATE NONCLUSTERED INDEX [IDX_F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT_MAIN]
    ON [dbo].[F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT]([RECYCLING_ORDER_ID] ASC, [CLIENT_PORTAL_ORDER_REPORT_TYPE_ID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'it contains a set of enabled reports for exact order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_RECYCLING_ORDER_CLIENT_PORTAL_REPORT';


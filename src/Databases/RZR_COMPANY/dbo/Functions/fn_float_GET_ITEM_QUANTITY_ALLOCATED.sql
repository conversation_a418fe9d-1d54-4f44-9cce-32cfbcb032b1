-- =============================================
-- Author:		
-- Create date: 
-- Description:	
-- =============================================
CREATE FUNCTION [dbo].[fn_float_GET_ITEM_QUANTITY_ALLOCATED]
(
	@ITEM_ID bigint
)
RETURNS float
AS
BEGIN
	
	return 
		ISNULL(
			cast(
				(
					SELECT count(1) --SUM(FSOI.ITEM_QUANTITY)
					FROM dbo.F_ITEM_INVENTORY			FII  WITH(NOLOCK)
					INNER JOIN dbo.F_SALES_ORDER_ITEM	FSOI WITH (NOLOCK)
						on FSOI.SALES_ORDER_ITEM_ID = FII.SalesOrderItemId	
					WHERE FII.ITEM_ID = @ITEM_ID
					  AND FII.ITEM_STATUS_ID = 2
					  AND FII.IS_DELETED  = 0
				  )
			as float)
		, 0.0)
END

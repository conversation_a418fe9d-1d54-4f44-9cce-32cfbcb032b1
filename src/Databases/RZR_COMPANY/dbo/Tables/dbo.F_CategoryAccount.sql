CREATE TABLE [dbo].[F_CategoryAccount] (
	[Id]							BIGINT			IDENTITY (1, 1) NOT NULL,
	[CategoryId]					INT				NOT NULL,
	[AccountTypeId]					INT				NOT NULL,
	[AccountId]						BIGINT			NOT NULL,
	[IsInactive]					BIT				CONSTRAINT [DF__F_CategoryAccount__IsInactive] DEFAULT ((0)) NOT NULL,
	[IsDeleted]						BIT				CONSTRAINT [DF__F_CategoryAccount__IsDeleted] DEFAULT ((0)) NOT NULL,
	[InsertedBy]					NVARCHAR (250)	NOT NULL,
	[InsertedDt]					DATETIME		NOT NULL,
	[UpdatedBy]						NVARCHAR (250)	NULL,
	[UpdatedDt]						DATETIME		NULL,
	[DeletedBy]						NVARCHAR (250)	NULL,
	[DeletedDt]						DATETIME		NULL,
	CONSTRAINT [PK__F_CategoryAccount] PRIMARY KEY CLUSTERED ([Id] ASC),
	CONSTRAINT [FK__F_CategoryAccount__D_CATEGORY_HIERARCHY] FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[D_CATEGORY_HIERARCHY] ([CATEGORY_ID]) ON DELETE CASCADE,
	CONSTRAINT [FK__F_CategoryAccount__D_ACCOUNT_TYPE] FOREIGN KEY ([AccountTypeId]) REFERENCES [dbo].[D_ACCOUNT_TYPE] ([ACCOUNT_TYPE_ID]),
	CONSTRAINT [FK__F_CategoryAccount__F_ACCOUNT] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[F_ACCOUNT] ([ACCOUNT_ID])
);

GO

CREATE NONCLUSTERED INDEX [IDX__F_CategoryAccount__CategoryId]
	ON [dbo].[F_CategoryAccount]([CategoryId] ASC)
	INCLUDE([AccountId]);

GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The accounts (bookkeeping) for category', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CategoryAccount';

GO

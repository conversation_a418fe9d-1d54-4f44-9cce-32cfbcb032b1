

CREATE FUNCTION [dbo].[fn_str_GET_DEFAULT_STATEMENT_TERM_BY_TYPE]
(@C_TERM_TYPE_ID BIGINT)
RETURNS NVARCHAR(MAX)
AS
BEGIN
     RETURN (IIF (@C_TERM_TYPE_ID = 3, 
		ISNULL((
			SELECT TOP(1)[RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE_TEXT]
			  FROM [dbo].[F_RECYCLING_ORDER_AUDIT_CERTIFICATE_TEMPLATE]
			  WHERE IS_CERTIFICATE_OF_RECYCLING = 1),
	''),
		ISNULL((
			SELECT TOP(1)[STATEMENT_TERM_CONDITIONS] 
			  FROM [dbo].[F_STATEMENT_TERM] 
			  WHERE [STATEMENT_TERM_TYPE_ID] = @C_TERM_TYPE_ID AND [IS_DEFAULT_FOR_TYPE_ID] = 1),
	'')));
END
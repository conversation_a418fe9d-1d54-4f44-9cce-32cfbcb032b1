CREATE PROCEDURE [dbo].[sp_DELETE_RECYCLING_ORDER_OUTBOUND] 
    @RECYCLING_ORDER_ID BIGINT,
    @USER_ID		    BIGINT,
	@IP					BIGINT
AS
BEGIN
    BEGIN TRY

	   BEGIN TRANSACTION
		  EXEC sp_LOG_RECYCLING_ORDER_OUTBOUND_DELETED
				@RECYCLING_ORDER_ID,
				@USER_ID,
				@IP

		  DELETE FROM F_RECYCLING_OUTBOUND_FILE
		  WHERE [OUTBOUND_ORDER_ID] = @RECYCLING_ORDER_ID

		  DECLARE @t TABLE (RECYCLING_OUTBOUND_TRUCKING_ID BIGINT, RECYCLING_OUTBOUND_CONTAINER_ID BIGINT)
		  INSERT INTO @t (RECYCLING_OUTBOUND_TRUCKING_ID)
		  SELECT [RECYCLING_OUTBOUND_TRUCKING_ID]
		  FROM [dbo].[F_RECYCLING_ORDER_OUTBOUND] 
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
		
		  INSERT INTO @t (RECYCLING_OUTBOUND_CONTAINER_ID)
		  SELECT [RECYCLING_OUTBOUND_CONTAINER_ID] 
		  FROM [dbo].[F_RECYCLING_ORDER_OUTBOUND] 
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		  --If the order is a transfer order, we must first delete the inbound order
		  declare @inboundOrderId bigint = null;
		  set @inboundOrderId = (select top(1) FROI.RECYCLING_ORDER_ID 
			FROM F_RECYCLING_ORDER_INBOUND			FROI	WITH(NOLOCK)
			INNER JOIN F_RECYCLING_ORDER_OUTBOUND	FROO	WITH(NOLOCK)
				ON FROI.RECYCLING_ORDER_ID = FROO.TRANSFER_INBOUND_RECYCLING_ORDER_ID
			 WHERE FROO.RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID)
		  IF (@inboundOrderId is not null)
		  BEGIN
			  exec [dbo].[sp_DELETE_RECYCLING_ORDER] 
				@RECYCLING_ORDER_ID	= RECYCLING_ORDER_ID,
				@USER_ID			= @USER_ID,
				@IP					= @IP,
				@IS_DELETE_QUOTE	= 0
		  END

		  DELETE FROM F_RECYCLING_ORDER_OUTBOUND 
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
		
		  DELETE FROM F_RECYCLING_OUTBOUND_TRUCKING
		  WHERE [RECYCLING_OUTBOUND_TRUCKING_ID] IN (
			 SELECT 
				[RECYCLING_OUTBOUND_TRUCKING_ID] 
			 FROM @t)		
											
		  DELETE FROM F_RECYCLING_OUTBOUND_CONTAINER 
		  WHERE [RECYCLING_OUTBOUND_CONTAINER_ID] IN (
			 SELECT 
				[RECYCLING_OUTBOUND_CONTAINER_ID] 
			 FROM @t)
		
		  UPDATE dbo.F_RECYCLING_ORDER_ITEM SET
			 OUTBOUND_ORDER_ID = NULL,
			 WEIGHT_RECEIVED		= WEIGHT_RECEIVED + ISNULL(WEIGHT_LOOSE_LOAD, 0),	
			 WEIGHT_TARE			= 
				CASE
					   WHEN WEIGHT_LOOSE_LOAD IS NULL THEN WEIGHT_TARE
					   ELSE ISNULL(WEIGHT_LOOSE_LOAD, 0)
				END,
			 WEIGHT_LOOSE_LOAD	= NULL	
		  WHERE OUTBOUND_ORDER_ID = @RECYCLING_ORDER_ID		

		  
		  DELETE FROM [F_RECYCLING_ORDER_OUTBOUND_ITEM_MASTER]
		  WHERE [RECYCLING_ORDER_ID] = @RECYCLING_ORDER_ID

		  DELETE FROM dbo.F_RECYCLING_ORDER_ITEM_TRANSFER		
		  WHERE OUTBOUND_ORDER_ID = @RECYCLING_ORDER_ID
			
		  DELETE FROM dbo.F_RECYCLING_ORDER		
		  WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID
	   COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
	   -- There is an error
	   IF @@TRANCOUNT > 0
		  ROLLBACK TRANSACTION;

		DECLARE @SALES_ORDER_NAME NVARCHAR(300)
		SELECT @SALES_ORDER_NAME = SALES_ORDER_NUMBER
		FROM F_SALES_ORDER
		WHERE RECYCLING_ORDER_ID = @RECYCLING_ORDER_ID

		DECLARE @MESSAGE NVARCHAR(MAX)
		SET @MESSAGE = N'This Outbound order is related to Sales Order '+ @SALES_ORDER_NAME + N'. Please delete it first.'
		
		IF LEN(@SALES_ORDER_NAME) > 0
			THROW 51000, @MESSAGE, 1;
    END CATCH
END
CREATE TABLE [dbo].[F_RECYCLING_ORDER_OUTBOUND_ITEM_MASTER] (
    [RECYCLING_ORDER_OUTBOUND_ITEM_MASTER_ID]	BIGINT IDENTITY (1, 1) NOT NULL,
    [RECYCLING_ORDER_ID]						BIGINT NOT NULL,
    [RECYCLING_ITEM_MASTER_ID]					BIGINT NOT NULL,
    [InsertedByUserId]							BIGINT			NULL,
    [InsertedByIp]								BIGINT			CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_InsertedByUserId]		DEFAULT ((0)) NULL,
	[InsertedBy]								NVARCHAR (128)  CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_InsertedBy]	DEFAULT (ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)) NULL,
    [InsertedDt]								DATETIME		CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_InsertedDt]	DEFAULT (getutcdate()) NULL,
	[UpdatedByUserId]							BIGINT			NULL,
    [UpdatedByIp]								BIGINT			CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_UpdatedByUserId]		DEFAULT ((0)) NULL,
	[UpdatedBy]									NVARCHAR (128)  CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_UpdatedBy]	DEFAULT (ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)) NULL,
    [UpdatedDt]									DATETIME		CONSTRAINT [DF_F_CustomerDsvRestrictedRecyclingItemMaster_UpdatedDt]	DEFAULT (getutcdate()) NULL,
    CONSTRAINT [PK_F_RECYCLING_ORDER_OUTBOUND_COMMODITY] PRIMARY KEY CLUSTERED ([RECYCLING_ORDER_OUTBOUND_ITEM_MASTER_ID] ASC),
    CONSTRAINT [FK_F_RECYCLING_ORDER_OUTBOUND_COMMODITY_F_RECYCLING_ITEM_MASTER] FOREIGN KEY ([RECYCLING_ITEM_MASTER_ID]) REFERENCES [dbo].[F_RECYCLING_ITEM_MASTER] ([RECYCLING_ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_OUTBOUND_COMMODITY_F_RECYCLING_ORDER_OUTBOUND] FOREIGN KEY ([RECYCLING_ORDER_ID]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_OUTBOUND_COMMODITY_tb_User_InsertedByUserId] FOREIGN KEY ([InsertedByUserId]) REFERENCES [dbo].[tb_User] ([UserID]),
    CONSTRAINT [FK_F_RECYCLING_ORDER_OUTBOUND_COMMODITY_tb_User_UpdatedByUserId] FOREIGN KEY ([UpdatedByUserId]) REFERENCES [dbo].[tb_User] ([UserID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'recycling outbound modal, the commodity tab. If set only those commodities can be added to outbound', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_RECYCLING_ORDER_OUTBOUND_ITEM_MASTER';


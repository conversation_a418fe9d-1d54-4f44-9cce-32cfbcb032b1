-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 06/06/2014
-- Description:	set category image sequence
-- =============================================
CREATE PROCEDURE sp_SET_CATEGORY_IMAGES_SEQUENCE 
(@IMAGE_IDS xml)
AS
BEGIN
	DECLARE @idoc INT

	-- read xml list into "@idoc"
    EXEC sp_xml_preparedocument @idoc OUTPUT, @IMAGE_IDS

	-- move it values into the "@caps" table variable
	DECLARE	@ids TABLE (
		Id		  BIGINT 
		,Position INT)

    INSERT INTO @ids (
		Id
		,Position)
    	SELECT -- select from xml doc as table
			x.Id
			,x.Position
		FROM OPENXML (@idoc, '/Root/Items', 1)
		WITH (
			Id BIGINT 
			,Position INT) AS x

    -- delete xml doc
	EXEC sp_xml_removedocument @idoc

	BEGIN TRANSACTION
		UPDATE I SET
			POSITION_INDEX = Position
		FROM F_CATEGORY_IMAGE I
		INNER JOIN @ids P
			ON P.Id = I.CATEGORY_IMAGE_ID
	COMMIT TRANSACTION
END
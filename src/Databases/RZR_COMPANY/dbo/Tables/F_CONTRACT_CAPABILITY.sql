CREATE TABLE [dbo].[F_CONTRACT_CAPABILITY] (
    [CONTRACT_CAPABILITY_ID]     BIGINT        IDENTITY (1, 1) NOT NULL,
    [INVENTORY_CAPABILITY_ID]    BIGINT        NOT NULL,
    [CONTRACT_ATTRIBUTE_TYPE_ID] BIGINT        NOT NULL,
    [FAIR_MARKET]                MONEY         NULL,
    [IS_DELETED]                 BIT           CONSTRAINT [DF_F_CONTRACT_CAPABILITY_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]                VARCHAR (150) CONSTRAINT [DF_F_CONTRACT_CAPABILITY_INSERTED_BY] DEFAULT ('admin') NOT NULL,
    [INSERTED_DT]                DATETIME      CONSTRAINT [DF_F_CONTRACT_CAPABILITY_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                 VARCHAR (150) NULL,
    [UPDATED_DT]                 DATETIME      NULL,
    [DELETED_BY]                 VARCHAR (150) NULL,
    [DELETED_DT]                 DATETIME      NULL,
    CONSTRAINT [PK_F_CONTRACT_CAPABILITY] PRIMARY KEY CLUSTERED ([CONTRACT_CAPABILITY_ID] ASC),
    CONSTRAINT [FK_F_CONTRACT_CAPABILITY_D_INVENTORY_CAPABILITY] FOREIGN KEY ([INVENTORY_CAPABILITY_ID]) REFERENCES [dbo].[D_INVENTORY_CAPABILITY] ([INVENTORY_CAPABILITY_ID]),
    CONSTRAINT [FK_F_CONTRACT_CAPABILITY_F_CONTRACT_ATTRIBUTE_TYPE] FOREIGN KEY ([CONTRACT_ATTRIBUTE_TYPE_ID]) REFERENCES [dbo].[F_CONTRACT_ATTRIBUTE_TYPE] ([CONTRACT_ATTRIBUTE_TYPE_ID])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is used for Recycling -> ITAD FMV Price Sheet. It is used to set fair market price which will be set by default for specific values. For example for these specific values of desktop’s CPU the fair market will be this (F_CONTRACT_ATTRIBUTE_TYPE).. or the fair market can be set for exact capability value', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_CONTRACT_CAPABILITY';


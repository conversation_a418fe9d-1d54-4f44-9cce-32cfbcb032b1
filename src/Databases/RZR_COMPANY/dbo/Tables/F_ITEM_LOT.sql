CREATE TABLE [dbo].[F_ITEM_LOT] (
    [ITEM_LOT_ID]          BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_ID_MASTER]       BIGINT        NOT NULL,
    [ITEM_ID]              BIGINT        NULL,
    [ITEM_LOT_VALUE]       INT           NOT NULL,
    [ITEM_LOT_UNIT_PRICE]  MONEY         NOT NULL,
    [ITEM_LOT_TOTAL_PRICE] MONEY         NOT NULL,
    [ITEM_LOT_QTY]         BIGINT        NOT NULL,
    [ITEM_LOT_TITLE]       VARCHAR (MAX) NOT NULL,
    [ITEM_LOT_DESC]        VARCHAR (MAX) NULL,
    [IS_INACTIVE]          BIT           CONSTRAINT [DF_F_ITEM_LOT_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]           BIT           CONSTRAINT [DF_F_ITEM_LOT_IS_INACTIVE1] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]          VARCHAR (150) NOT NULL,
    [INSERTED_DT]          DATETIME      NOT NULL,
    [UPDATED_BY]           VARCHAR (150) NULL,
    [UPDATED_DT]           DATETIME      NULL,
    [DELETED_BY]           VARCHAR (150) NULL,
    [DELETED_DT]           DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_LOT] PRIMARY KEY CLUSTERED ([ITEM_LOT_ID] ASC),
    CONSTRAINT [FK_F_ITEM_LOT_F_ITEM] FOREIGN KEY ([ITEM_ID_MASTER]) REFERENCES [dbo].[F_ITEM] ([ITEM_ID])
);
GO

CREATE NONCLUSTERED INDEX [IX_ITEM_MASTER_ID_ITEM_ID]
    ON [dbo].[F_ITEM_LOT]([ITEM_ID_MASTER] ASC, [ITEM_ID] ASC);
GO

CREATE NONCLUSTERED INDEX [IX_ITEM_ID]
    ON [dbo].[F_ITEM_LOT]([ITEM_ID] ASC);
GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It stores the set of items (SKUs) which are tied to one SKU. That master SKU is a lot.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_LOT';


CREATE TABLE [dbo].[C_ITEM_TYPE] (
    [ITEM_TYPE_ID]    INT           NOT NULL,
    [ITEM_TYPE_CD]    VARCHAR (150) NOT NULL,
    [ITEM_TYPE_DESC]  VARCHAR (512) NULL,
    [ITEM_TYPE_LABEL] VARCHAR (512) NULL,
    [IS_INACTIVE]     BIT           CONSTRAINT [DF_C_ITEM_TYPE_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]      BIT           CONSTRAINT [DF_C_ITEM_TYPE_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]     VARCHAR (150) CONSTRAINT [DF_C_ITEM_TYPE_INSERTED_BY] DEFAULT (isnull(object_schema_name(@@procid)+'.','')+object_name(@@procid)) NOT NULL,
    [INSERTED_DT]     DATETIME      CONSTRAINT [DF_C_ITEM_TYPE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]      VARCHAR (150) NULL,
    [UPDATED_DT]      DATETIME      NULL,
    [DELETED_BY]      VARCHAR (150) NULL,
    [DELETED_DT]      DATETIME      NULL,
    CONSTRAINT [PK_C_ITEM_TYPE] PRIMARY KEY CLUSTERED ([ITEM_TYPE_ID] ASC)
);








GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'The item status is used for F_ITEM table', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_ITEM_TYPE';


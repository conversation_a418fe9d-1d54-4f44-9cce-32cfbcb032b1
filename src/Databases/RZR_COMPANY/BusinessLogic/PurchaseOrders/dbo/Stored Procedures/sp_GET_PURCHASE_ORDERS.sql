-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 12.03.2014
-- Description:	Return purchase orders
-- =============================================
-- EXEC sp_GET_PURCHASE_ORDERS
CREATE PROCEDURE [dbo].[sp_GET_PURCHASE_ORDERS]
	@REP_IDS			bigint_ID_ARRAY		 READONLY,
	@ITEM_NUMBERS		nvarchar_LABEL_ARRAY READONLY,
	@ORDER_STATUS_IDS	bigint_ID_ARRAY		 READONLY,
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'PurchaseOrderId',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 25,
	@PAGE_INDEX			INT				= 0,
	@FILTER_WHERE		VARCHAR(MAX)	= N'',
	@WAREHOUSE_ID		BIGINT			= NULL,
	@C_IS_DEBUG			BIT				= 0
AS
BEGIN
	DECLARE @startRowNumber BIGINT = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber   BIGINT = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE
	DECLARE @rep_condition	NVARCHAR(MAX) = N'';
	
	-- rep user restriction
	IF (EXISTS(SELECT ID FROM @REP_IDS)) BEGIN
	   SELECT * INTO #REP_IDS FROM @REP_IDS
	   -- RSW-9614 TODO: restrict only by rep, make it "inner join" instead of "where" condition
	   SET @rep_condition =  N'
			AND EXISTS(SELECT TOP(1) 1 FROM #REP_IDS WHERE ID IN (fpo.USER_ID)) --(fpo.USER_ID, c.REP_ID)
	   '
    END

	DECLARE @filterCondition VARCHAR(MAX) = N'';
	IF (@FILTER_WHERE != N'' AND @FILTER_WHERE != 'null' AND @FILTER_WHERE IS NOT NULL)
	BEGIN
		SET @filterCondition = N' WHERE ('+ @FILTER_WHERE +N')';
	END

	DECLARE
		@STATUS_ID			BIGINT		  = 0,
		@RESALE				NVARCHAR(20)  = N'''Resale''',
		@RESYCLING			NVARCHAR(20)  = N'''Recycling''',
		@FILTER_WAREHOUSE	NVARCHAR(MAX) = N'';

	SELECT @STATUS_ID	= ID FROM @ORDER_STATUS_IDS

	CREATE TABLE #PURCHASE_ORDERS_SRC (
		[PURCHASE_ORDER_ID]						BIGINT,
		[AUTO_NAME]								NVARCHAR(512),
		[REFERENCE]								NVARCHAR(512),
		[DATE]									DATETIME,
		[WAREHOUSE_ID]							BIGINT,
		[RECYCLING_ORDER_ID]					BIGINT,
		[CUSTOMER_ID]							BIGINT,
		[USER_ID]								BIGINT,
		[STATUS_ID]								INT,
		[PURCHASE_ORDER_FREIGHT_STATUS_ID]		INT,
		[CLOSING_STATUS_ID]						INT,
		[ExternalId]							NVARCHAR (350)
	);

	-- SELECTING SOURCE PURCHASE ORDERS
    DECLARE @orders nvarchar(max) = 'F_PURCHASE_ORDER';

    IF (EXISTS(SELECT LABEL FROM @ITEM_NUMBERS))
    BEGIN
	   SET @orders = N'#PURCHASE_ORDERS_SRC';
	   INSERT INTO #PURCHASE_ORDERS_SRC (
		  PURCHASE_ORDER_ID
		  ,AUTO_NAME
		  ,REFERENCE
		  ,[DATE]
		  ,WAREHOUSE_ID
		  ,RECYCLING_ORDER_ID
		  ,CUSTOMER_ID
		  ,[USER_ID]
		  ,STATUS_ID
		  ,PURCHASE_ORDER_FREIGHT_STATUS_ID
		  ,CLOSING_STATUS_ID
		  ,ExternalId
	   )
	   SELECT DISTINCT
		  FPO.PURCHASE_ORDER_ID
		  ,FPO.AUTO_NAME
		  ,FPO.REFERENCE
		  ,FPO.[DATE]
		  ,FPO.WAREHOUSE_ID
		  ,FPO.RECYCLING_ORDER_ID
		  ,FPO.CUSTOMER_ID
		  ,FPO.[USER_ID]
		  ,FPO.STATUS_ID
		  ,FPO.PURCHASE_ORDER_FREIGHT_STATUS_ID
		  ,FPO.CLOSING_STATUS_ID
		  ,FPO.ExternalId
		FROM F_ITEM_MASTER					FIM		WITH(NOLOCK)
		INNER JOIN @ITEM_NUMBERS			INUM
            ON FIM.ITEM_NUMBER LIKE '%' + [INUM].[LABEL] + '%'
		INNER JOIN F_PRODUCT_MASTER			FPM		WITH (NOLOCK)
			ON FPM.ITEM_MASTER_ID = FIM.ITEM_MASTER_ID
		INNER JOIN F_PURCHASE_ORDER_ITEM	FSOI	WITH(NOLOCK)
			ON FSOI.PRODUCT_MASTER_ID = FPM.PRODUCT_MASTER_ID
		INNER JOIN   F_PURCHASE_ORDER		FPO		WITH(NOLOCK)
			ON FPO.PURCHASE_ORDER_ID		= FSOI.PURCHASE_ORDER_ID
    END

	IF (ISNULL(@WAREHOUSE_ID, 0) > 0)
	BEGIN
		SET @FILTER_WAREHOUSE = N' AND (fpo.WAREHOUSE_ID is null OR ' + CAST(@WAREHOUSE_ID AS VARCHAR(20)) + N' = fpo.WAREHOUSE_ID) '
	END

	DECLARE @query NVARCHAR (MAX) = '
		SELECT
			A.ORDER_ID	AS PURCHASE_ORDER_ID,
			STUFF(
				(SELECT 
					'', '' + CONVERT(NVARCHAR, I.AUTO_NAME)
					FROM dbo.F_INVOICE I	WITH(NOLOCK)
					WHERE ORDER_ID = A.ORDER_ID
					  AND I.INVOICE_TYPE_ID = 2
				FOR XML PATH ('''')), 
				1, 
				2, 
				'''')		AS INVOICE_CD
		INTO #invoiceGroup
		FROM dbo.F_INVOICE A WITH (NOLOCK)
		where A.INVOICE_TYPE_ID = 2
		GROUP BY A.ORDER_ID
	
		SELECT 
			TT.PURCHASE_ORDER_ID		AS PURCHASE_ORDER_ID,
			SUM(TT.TOTAL_RECEIVED_PRICE)AS TOTAL_RECEIVED_PRICE
		INTO #PURCHASE_ITEMS
		FROM (
			SELECT PURCHASE_ORDER_ID,
				SUM(ISNULL(RECEIVED_PRICE, 0) * 
					CASE WHEN IS_FLAT_FEE = 1 THEN 1
						 ELSE QTY
					END) AS TOTAL_RECEIVED_PRICE
			FROM dbo.F_PURCHASE_ORDER_ITEM WITH (NOLOCK)
			GROUP BY PURCHASE_ORDER_ID
		) TT
		GROUP BY TT.PURCHASE_ORDER_ID

		;WITH m_data AS
		(
			SELECT
				fpo.PURCHASE_ORDER_ID					AS PurchaseOrderId,
				fpo.AUTO_NAME							AS AutoName,
				fpo.REFERENCE							AS Reference,
				c.CUSTOMER_NAME 						AS CustomerName,
				u.UserName								AS UserName,
				fpo.DATE								AS Date,
				ps.PURCHASE_ORDER_STATUS_ID				AS OrderStatusId,
				ps.PURCHASE_ORDER_STATUS_CD				AS OrderStatus,
				CASE  
					WHEN fpo.RECYCLING_ORDER_ID IS NULL THEN fs.PURCHASE_ORDER_FREIGHT_STATUS_ID
					ELSE 3
				END AS FreightStatusId,
				CASE  
					WHEN fpo.RECYCLING_ORDER_ID IS NULL THEN fs.PURCHASE_ORDER_FREIGHT_STATUS_CD
					ELSE ''Delivered''
				END AS FreightStatus,
				CPOCS.CLOSING_STATUS_ID					AS ClosingStatusId,
				CPOCS.CLOSING_STATUS_CD					AS ClosingStatusCd,
				IG.INVOICE_CD							AS ApInvoice,
				PI.TOTAL_RECEIVED_PRICE					AS Balance,	
				W.WAREHOUSE_ID							AS WarehouseId,
				W.WAREHOUSE_CD							AS Warehouse,
				CASE  
					WHEN fpo.RECYCLING_ORDER_ID IS NULL THEN OT.ORDER_TYPE_ID
					ELSE 9
				END AS OrderTypeId,
				CASE  
					WHEN fpo.RECYCLING_ORDER_ID IS NULL THEN OT.ORDER_TYPE_CD
					ELSE ''Recycling''
				END AS OrderTypeCd,				CASE
					WHEN NOT EXISTS(
						SELECT TOP(1) 1 
						FROM [dbo].[vw_AP_INVOICE] OI WITH (NOLOCK)
						WHERE OI.ORDER_ID = FPO.PURCHASE_ORDER_ID
							AND OI.IS_VOIDED = 0
							AND OI.INVOICE_TYPE_ID = 2
					) THEN ''No Invoice''
					WHEN NOT EXISTS(
						SELECT TOP(1) 1 
						FROM [dbo].[vw_AP_INVOICE] OI WITH (NOLOCK)
						WHERE OI.ORDER_ID = FPO.PURCHASE_ORDER_ID
							AND OI.IS_FULLY_PAID = 0
							AND OI.IS_VOIDED = 0
							AND OI.INVOICE_TYPE_ID = 2
					) THEN ''Full Paid''
					WHEN EXISTS(
						SELECT TOP(1) 1 
						FROM dbo.F_INVOICE OI WITH (NOLOCK) 
						WHERE OI.ORDER_ID = FPO.PURCHASE_ORDER_ID
							AND OI.PAID_AMOUNT > 0
							AND OI.IS_VOIDED = 0
						    AND OI.INVOICE_TYPE_ID = 2
					) THEN ''Partially Paid''
					ELSE ''Not Paid''
				END
				AS PaymentStatus,
				ISNULL(RI.AUTO_NAME, OO.AUTO_NAME)		AS RefOrderAutoName,
				CASE  
					WHEN fpo.RECYCLING_ORDER_ID IS NULL THEN ' + @RESALE + '
					ELSE ' + @RESYCLING + '
				END Channel,
				(SELECT 
					COUNT(T.PURCHASE_ORDER_ITEM_ID) 
					FROM F_PURCHASE_ORDER_ITEM					T 	WITH (NOLOCK)
					WHERE T. PURCHASE_ORDER_ID = fpo.PURCHASE_ORDER_ID)	AS ItemsCount,
					(SELECT 
					COUNT(T.PURCHASE_ORDER_ITEM_ID) 
					FROM F_PURCHASE_ORDER_ITEM					T 	WITH (NOLOCK)
					WHERE T. PURCHASE_ORDER_ID = fpo.PURCHASE_ORDER_ID AND RECEIVE_STATUS_ID = 3)	AS ReceivedItemsCount,	-- 3 - Received
					fpo.ExternalId
			FROM '+ @orders + N'							fpo	WITH (NOLOCK)
			INNER JOIN 	dbo.F_CUSTOMER						c	WITH (NOLOCK)
				ON fpo.CUSTOMER_ID = c.CUSTOMER_ID	
			INNER JOIN 	dbo.tb_User							u	WITH (NOLOCK)
				ON fpo.USER_ID = u.UserID
			INNER JOIN dbo.C_PURCHASE_ORDER_STATUS			ps	WITH (NOLOCK)
				ON fpo.STATUS_ID = ps.PURCHASE_ORDER_STATUS_ID	
			INNER JOIN dbo.C_PURCHASE_ORDER_CLOSING_STATUS	CPOCS WITH (NOLOCK)
				ON CPOCS.CLOSING_STATUS_ID = fpo.CLOSING_STATUS_ID
			OUTER APPLY (
				SELECT TOP(1) 
						EST.ENTITY_SUBJECT_TYPE_ID			AS ORDER_TYPE_ID
					   ,EST.ENTITY_SUBJECT_TYPE_CD			AS ORDER_TYPE_CD
				  FROM [dbo].[F_ORDER_ORDER_SUBJECT_TYPE]		AS OOT WITH(NOLOCK)
				 INNER JOIN [dbo].[D_ENTITY_SUBJECT_TYPE]		AS EST WITH(NOLOCK)
				    ON OOT.[ENTITY_SUBJECT_TYPE_ID] = EST.[ENTITY_SUBJECT_TYPE_ID]
				   AND OOT.[ENTITY_TYPE_ID] = 3 -- PURCHASE (purchase order)
				   AND EST.[ENTITY_ID] = 3		-- PURCHASE (purchase order)
				 WHERE OOT.ORDER_ID = fpo.PURCHASE_ORDER_ID
			  )	AS OT
			LEFT JOIN dbo.C_PURCHASE_ORDER_FREIGHT_STATUS	fs	WITH (NOLOCK)
				ON fs.PURCHASE_ORDER_FREIGHT_STATUS_ID = fpo.PURCHASE_ORDER_FREIGHT_STATUS_ID
			LEFT JOIN F_RECYCLING_ORDER_INBOUND				RI	WITH(NOLOCK)
				ON fpo.RECYCLING_ORDER_ID = RI.RECYCLING_ORDER_ID
			LEFT JOIN F_RECYCLING_ORDER_OUTBOUND			OO	WITH (NOLOCK)
				ON fpo.RECYCLING_ORDER_ID = OO.RECYCLING_ORDER_ID
			LEFT JOIN #PURCHASE_ITEMS						PI
				ON FPO.PURCHASE_ORDER_ID = PI.PURCHASE_ORDER_ID						
			LEFT JOIN #invoiceGroup							IG
				ON fpo.PURCHASE_ORDER_ID = IG.PURCHASE_ORDER_ID					
			LEFT JOIN dbo.D_WAREHOUSE						W	WITH (NOLOCK)
				ON fpo.WAREHOUSE_ID = W.WAREHOUSE_ID
			WHERE ('+
				CASE
					WHEN EXISTS(SELECT TOP(1) 1 FROM @ORDER_STATUS_IDS WHERE ID = 4)
					THEN 'fpo.CLOSING_STATUS_ID = 3 OR '
					ELSE 'fpo.CLOSING_STATUS_ID IN (1, 2) AND '
				END
				+ IIF(@STATUS_ID = 0, 'fpo.[STATUS_ID] != 4', 'fpo.[STATUS_ID] IN (SELECT [ID] FROM @ORDER_STATUS_IDS)') + ')
				'
				+ @FILTER_WAREHOUSE + 
				+ @rep_condition + '
		)
		SELECT TOP(1)
			-1							AS RowID,
			COUNT (PurchaseOrderId)		AS PurchaseOrderId,	
			NULL						AS AutoName,	
			NULL						AS Reference,
			NULL						AS CustomerName,
			NULL						AS UserName,
			NULL						AS Date,
			NULL						AS OrderStatusId,
			NULL						AS OrderStatus,
			NULL						AS FreightStatusId,
			NULL						AS FreightStatus,
			NULL						AS ClosingStatusId,
			NULL						AS ClosingStatusCd,
			NULL						AS ApInvoice,
			0.00						AS Balance,
		    NULL						AS [OrderCurrency],
		    0.00						AS [OrderCurrencyBalance],
			NULL						AS PaymentStatus,
			NULL						AS RefOrderAutoName,
			NULL						AS Channel,
			0							AS ItemsCount,
			0							AS ReceivedItemsCount,
			0							AS WarehouseId,
			''''						AS Warehouse,
			NULL						AS OrderTypeId,
			NULL						AS OrderTypeCd,
			NULL						as ExternalId,
			NULL						AS PartNumber
		FROM m_data
		' + @filterCondition + '
		UNION ALL
		SELECT
			TT.RowID,
			TT.PurchaseOrderId,
			TT.AutoName,	
			TT.Reference,
			TT.CustomerName,	
			TT.UserName,		
			TT.Date,
			TT.OrderStatusId,
			TT.OrderStatus,
			TT.FreightStatusId,
			TT.FreightStatus,
			TT.ClosingStatusId,
			TT.ClosingStatusCd,
			TT.ApInvoice,
			TT.Balance,	
			[dbo].[fn_str_GetFormattedCurrencyMark](c.Symbol, c.Abbreviation, ce.PREFER_CURRENCY_ABBREVIATION) AS [OrderCurrency],
			TT.Balance/ e.ForeignToHome	AS [OrderCurrencyBalance],
			TT.PaymentStatus,
			TT.RefOrderAutoName,
			TT.Channel,
			TT.ItemsCount,
			TT.ReceivedItemsCount,
			TT.WarehouseId,
			TT.Warehouse,
			TT.OrderTypeId,
			TT.OrderTypeCd,
			TT.ExternalId,
			dbo.fn_str_GET_PURCHASE_ORDER_PART_NUMBER(TT.[PurchaseOrderId])			AS [PartNumber]
		FROM (
			SELECT *
			FROM (
				SELECT TOP('+ CAST(@endRowNumber AS VARCHAR(100)) + ')
					ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
					* 
				FROM m_data	
				' + @filterCondition + ') t	
			WHERE RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100)) + '
		) TT
		INNER JOIN dbo.F_PURCHASE_ORDER			o	WITH(NOLOCK)
			ON o.PURCHASE_ORDER_ID = TT.PurchaseOrderId
		inner join [dbo].[D_CurrencyExchange]	e	WITH(NOLOCK)
			on o.CurrencyExchangeId = e.Id
		inner join [dbo].[C_Currency]			c	WITH(NOLOCK)
			ON e.[ForeignCurrencyId] = c.Id
		cross join [dbo].[U_SYSTEM_SETTINGS]	ce	with(nolock)
		'	
					
	--SET @C_IS_DEBUG = 1
	IF (@C_IS_DEBUG = 1)
	BEGIN
		print(cast(@query AS ntext))
	END
	ELSE
	BEGIN
		EXEC sp_executesql @query,
			N'@ORDER_STATUS_IDS	bigint_ID_ARRAY READONLY',
			@ORDER_STATUS_IDS	= @ORDER_STATUS_IDS
	END
		
	IF OBJECT_ID('tempdb..#invoiceGroup') IS NOT NULL DROP TABLE #invoiceGroup;
	IF OBJECT_ID('tempdb..#PURCHASE_ITEMS') IS NOT NULL DROP TABLE #PURCHASE_ITEMS;
	IF OBJECT_ID('tempdb..#REP_IDS') IS NOT NULL DROP TABLE #REP_IDS;
END
go

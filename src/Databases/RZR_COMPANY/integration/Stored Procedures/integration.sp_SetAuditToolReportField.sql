CREATE PROCEDURE [integration].[sp_SetAuditToolReportField]
	@AuditToolReportFieldId BIGINT,
	@AuditToolType INT,
	@AuditToolAttributeTypeId BIGINT,
	@Name NVARCHAR(250),
	@OriginPath NVARCHAR(1000),
	@Type INT,
	@FormatType INT,
	@UserId BIGINT,
	@UserIp BIGINT
AS
BEGIN

	SET @Name = NULLIF(RTRIM(LTRIM(@Name)), N'');
	SET @OriginPath = NULLIF(RTRIM(LTRIM(@OriginPath)), N'');

	SET @AuditToolAttributeTypeId = NULLIF(@AuditToolAttributeTypeId, 0);
	SET @FormatType = NULLIF(@FormatType, 0);

	DECLARE @spName NVARCHAR(128) = ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + N'.', N'') + OBJECT_NAME(@@PROCID);
	DECLARE @utcNow DATETIME = GETUTCDATE();

	IF (ISNULL(@AuditToolReportFieldId, 0) = 0)
	BEGIN

		INSERT INTO [integration].[D_AuditToolReportField] WITH(ROWLOCK) (
			[AuditToolType],
			[AuditToolAttributeTypeId],
			[Name],
			[OriginPath],
			[Type],
			[FormatType],
			[InsertedByUserId],
			[InsertedByUserIp],
			[InsertedBy],
			[InsertedDate]
		) VALUES (
			@AuditToolType,
			@AuditToolAttributeTypeId,
			@Name,
			@OriginPath,
			@Type,
			@FormatType,
			@UserId,
			@UserIp,
			@spName,
			@utcNow
		);

		SELECT SCOPE_IDENTITY() AS AuditToolReportFieldId;

	END
	ELSE
	BEGIN

		UPDATE [integration].[D_AuditToolReportField] WITH(ROWLOCK) SET
			[Name] = @Name,
			[OriginPath] = @OriginPath,
			[Type] = @Type,
			[FormatType] = @FormatType,
			[UpdatedByUserId] = @UserId,
			[UpdatedByUserIp] = @UserIp,
			[UpdatedBy] = @spName,
			[UpdatedDate] = @utcNow
		WHERE [Id] = @AuditToolReportFieldId;

	END

END

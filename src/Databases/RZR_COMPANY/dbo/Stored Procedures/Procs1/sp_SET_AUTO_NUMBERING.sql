CREATE PROCEDURE [dbo].[sp_SET_AUTO_NUMBERING]
	@TYPE_ID	INT,
	@PREFIX		NVARCHAR(10),
	@NUMBER		BIGINT,
	@MODE		INT = NULL
AS
BEGIN
	
	SET @NUMBER = ISNULL(@NUMBER, 0);

	DECLARE @FOR_SEQUENCE BIGINT = IIF(@NUMBER < 0, -1, @NUMBER - 1) -- will be incremented in the sequence
	/*
		SalesOrders = 0,
        Invoices = 1,
        PurchaseOrders = 2,
        InboundOrder = 3,
        OutboundOrder = 4,
        Quotes = 5
		Contracts = 6
		Customers = 9
	*/
	DECLARE @CAN_UPDATE BIT = [dbo].[fn_bit_IS_ABLE_TO_UPDATE_AUTO_NUMBERING](@TYPE_ID, @NUMBER)

	SET XACT_ABORT ON
	BEGIN TRAN

		IF (@TYPE_ID = 0) -- SalesOrders = 0,
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_SALES_ORDER				= ISNULL(@PREFIX, N'SO'),
				STARTING_NUMBER_SALES_ORDER		= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_SALES_ORDER
					END
			IF (@CAN_UPDATE = 1)
			BEGIN
				UPDATE U_TABLE_IDENTITY SET
					TABLE_IDENTITY_VALUE = @FOR_SEQUENCE
				WHERE TABLE_NAME like '%F_SALES_ORDER%'
			END
		END ELSE
		IF (@TYPE_ID = 1) -- Invoices = 1,
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_INVOICE					= ISNULL(@PREFIX, N''),
				STARTING_NUMBER_INVOICE			= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_INVOICE
					END
			IF (@CAN_UPDATE = 1)
			BEGIN
				UPDATE U_TABLE_IDENTITY SET
					TABLE_IDENTITY_VALUE = @FOR_SEQUENCE
				WHERE TABLE_NAME like '%F_INVOICE_AP%'
			
				UPDATE U_TABLE_IDENTITY SET
					TABLE_IDENTITY_VALUE = @FOR_SEQUENCE
				WHERE TABLE_NAME like '%F_INVOICE_AR%'
			
				UPDATE U_TABLE_IDENTITY SET
					TABLE_IDENTITY_VALUE = @FOR_SEQUENCE
				WHERE TABLE_NAME like '%F_INVOICE_RMA%'
			END
		END ELSE
		IF (@TYPE_ID = 2) -- PurchaseOrders = 2,
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_PURCHASE_ORDER			= ISNULL(@PREFIX, N'PO'),
				STARTING_NUMBER_PURCHASE_ORDER	= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_PURCHASE_ORDER
					END

			IF (@CAN_UPDATE = 1)
			BEGIN
				UPDATE U_TABLE_IDENTITY SET
					TABLE_IDENTITY_VALUE = @FOR_SEQUENCE
				WHERE TABLE_NAME like '%F_PURCHASE_ORDER%'
			END
		END ELSE
		IF (@TYPE_ID = 3) -- InboundOrder = 3,
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_INBOUND_ORDER			= ISNULL(@PREFIX, N'O'),
				STARTING_NUMBER_INBOUND_ORDER	= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_INBOUND_ORDER
					END
		END ELSE
		IF (@TYPE_ID = 4) -- OutboundOrder = 4,
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_OUTBOUND_ORDER			= ISNULL(@PREFIX, N'OUT'),
				STARTING_NUMBER_OUTBOUND_ORDER	= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_OUTBOUND_ORDER
					END
		END ELSE
		IF (@TYPE_ID = 5) -- Quotes = 5
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				PREFIX_INBOUND_QUOTE			= ISNULL(@PREFIX, N'Q'),
				STARTING_NUMBER_INBOUND_QUOTE	= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_INBOUND_QUOTE
					END
		END ELSE
		IF (@TYPE_ID = 6) -- Contracts = 6
		BEGIN
			UPDATE U_SYSTEM_SETTINGS SET
				STARTING_NUMBER_CONTRACT	= 
					CASE 
						WHEN @CAN_UPDATE = 1 THEN @NUMBER
						ELSE STARTING_NUMBER_CONTRACT
					END
		END ELSE
		IF (@TYPE_ID = 7) -- RMA Invoices
		BEGIN

			UPDATE [dbo].[U_SYSTEM_SETTINGS]
			SET [PREFIX_RMA_INVOICE] = ISNULL(@PREFIX, N'')

		END ELSE
		IF (@TYPE_ID = 8) -- Credit Memos
		BEGIN

			UPDATE [dbo].[U_SYSTEM_SETTINGS]
			SET [PREFIX_CREDIT_MEMO] = ISNULL(@PREFIX, N'')

		END
		IF (@TYPE_ID = 9) -- Customers
		BEGIN

			UPDATE [dbo].[U_SYSTEM_SETTINGS]
			SET 
				[PrefixCustomerCode]	 = ISNULL(@PREFIX, N''),
				[PrefixCustomerCodeMode] = ISNULL(@MODE, 0)
		END

	COMMIT TRAN
END
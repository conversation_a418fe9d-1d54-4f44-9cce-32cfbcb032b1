CREATE TABLE [dbo].[F_InboundOrderOnsiteLabels] (
    [Id]                                        BIGINT          IDENTITY (1, 1) NOT NULL,
    [RecyclingOrderId]                          BIGINT          NOT NULL,
    [OnsiteTypeId]                              INT             NOT NULL,
    [LabelId]                                   INT             NOT NULL,
    [FinalScannedQty]                           INT             NULL,
    [ManualQty]                                 INT             NULL,
    [InsertedBy]                                NVARCHAR (150)  CONSTRAINT [DF_F_InboundOrderOnsiteLabels_InsertedBy] DEFAULT (object_name(@@procid)) NOT NULL,
    [InsertedDate]                              DATETIME        CONSTRAINT [DF_F_InboundOrderOnsiteLabels_InsertedDate] DEFAULT (getutcdate()) NOT NULL,
    [InsertedByUserId]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteLabels_InsertedByUserId] DEFAULT ((1)) NOT NULL,
    [InsertedByUserIp]                          BIGINT          CONSTRAINT [DF_F_InboundOrderOnsiteLabels_InsertedByUserIp] DEFAULT ((1)) NOT NULL,
    [UpdatedBy]                                 NVARCHAR (150)  NULL,
    [UpdatedDate]                               DATETIME        NULL,
    [UpdatedByUserId]                           BIGINT          NULL,
    [UpdatedByUserIp]                           BIGINT          NULL,
    CONSTRAINT [PK_F_InboundOrderOnsiteLabels] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_F_InboundOrderOnsiteLabels_F_RECYCLING_ORDER] FOREIGN KEY ([RecyclingOrderId]) REFERENCES [dbo].[F_RECYCLING_ORDER] ([RECYCLING_ORDER_ID]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_F_InboundOrderOnsiteLabels_D_RecyclingServiceLabel] FOREIGN KEY ([LabelId]) REFERENCES [dbo].[D_RecyclingServiceLabel] ([Id]) ON DELETE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [IX_RecyclingOrderId]
    ON [dbo].[F_InboundOrderOnsiteLabels]([RecyclingOrderId])
    INCLUDE([LabelId]);
GO

CREATE NONCLUSTERED INDEX [IX_LabelId]
    ON [dbo].[F_InboundOrderOnsiteLabels]([LabelId])
    INCLUDE ([RecyclingOrderId]);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'Selected Onsite Labels for Recycling Order', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_InboundOrderOnsiteLabels';


GO
CREATE NONCLUSTERED INDEX [idx_RecyclingOrderId_OnsiteTypeId]
    ON [dbo].[F_InboundOrderOnsiteLabels]([RecyclingOrderId] ASC, [OnsiteTypeId] ASC);

GO
CREATE UNIQUE NONCLUSTERED INDEX [UX_RecyclingOrderId_OnsiteTypeId_LabelId]
    ON [dbo].[F_InboundOrderOnsiteLabels] ([RecyclingOrderId], [OnsiteTypeId], [LabelId]);

CREATE FUNCTION [dbo].[fn_str_GET_NOTIFICATION_EMAIL_LIST_REPS]
(
	@NotificationTypeId		BIGINT,
	@SalesOrderId			BIGINT,
	@NOTIFICATION_SET_ID	BIGINT
)
RETURNS  NVARCHAR(MAX)
AS
BEGIN
	
	DECLARE @RESULT NVARCHAR(MAX) = N'';
	DECLARE @NotificationId BIGINT = NULL;

	SELECT
	  @NotificationId = N.NOTIFICATION_ID
	FROM F_NOTIFICATION_SET_GROUP	NSG WITH(NOLOCK)
	INNER JOIN F_NOTIFICATION			N	WITH(NOLOCK)
		ON N.NOTIFICATION_GROUP_ID = NSG.NOTIFICATION_GROUP_ID
	WHERE N.NOTIFICATION_TYPE_ID = @NotificationTypeId
		AND NSG.NOTIFICATION_SET_ID = @NOTIFICATION_SET_ID

	SET @RESULT = (
		SELECT
			T.EMAIL  + ','  AS "data()" 
		FROM (
			SELECT
				U.Email
			FROM F_NOTIFICATION_ENTITY			NE WITH(NOLOCK)
			INNER JOIN dbo.F_SalesOrderRepUser			SOREP WITH (NOLOCK) 
				ON SOREP.SalesOrderId = @SalesOrderId
			INNER JOIN tb_User							U WITH(NOLOCK)
				ON U.UserID = SOREP.RepUserId
				AND NE.ENTITY_TYPE_ID = 1 -- rep
			WHERE NE.NOTIFICATION_ID = @NotificationId
		)  AS T
		FOR XML PATH('')
	)

	RETURN @RESULT
END

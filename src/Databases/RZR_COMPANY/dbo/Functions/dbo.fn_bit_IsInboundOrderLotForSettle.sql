CREATE FUNCTION [dbo].[fn_bit_IsInboundOrderLotForSettle]
(
	@OrderId			BIGINT,
	@LotId				BIGINT,
	@SettlementStateId	INT
)
RETURNS BIT
AS
BEGIN

	RETURN IIF(EXISTS(
		SELECT TOP(1) 1
		FROM [dbo].[F_RECYCLING_ORDER_ITEM]				AS LOT	WITH(NOLOCK)
		LEFT JOIN [dbo].[F_RECYCLING_ORDER_INBOUND]		AS OI	WITH(NOLOCK)
			ON LOT.[RECYCLING_ORDER_ID] = OI.[RECYCLING_ORDER_ID]
		LEFT JOIN [recycling].[F_SortedLotFromBatch]	AS FSL	WITH(NOLOCK)
			ON FSL.[LotId] = LOT.[RECYCLING_ORDER_ITEM_ID]
		WHERE LOT.[RECYCLING_ORDER_ITEM_ID] = @LotId
			AND LOT.[RECYCLING_ORDER_ID] = @OrderId
			AND LOT.[IS_MERGED] = 0
			AND OI.[StatusId] >= 5
			AND FSL.[InputId] IS NULL -- the lot that came out of the shredder is not sent to reprocessing
			AND (
				(@SettlementStateId IN (1, 5) AND LOT.[IS_INCLUDE_IN_INITIAL] = 1) -- Initial, Initial & Audit
				OR (
					@SettlementStateId = 2 -- Processed
					OR (
						@SettlementStateId = 3 -- Processed & Audit
						AND NOT EXISTS(
							SELECT TOP(1) 1
							FROM [recycling].[F_Asset] AS FA WITH (NOLOCK)
							WHERE FA.[RecyclingOrderItemId] = LOT.[RECYCLING_ORDER_ITEM_ID] AND FA.[IsGetAfterSettle] = 0
						)
					)
				)
				AND NOT EXISTS(
					SELECT TOP(1) 1
					FROM [dbo].[F_RECYCLING_ORDER_ITEM] AS TOI WITH (NOLOCK)
					WHERE LOT.[RECYCLING_ORDER_ID] = TOI.[RECYCLING_ORDER_ID]
						AND TOI.IS_GET_AFTER_SETTLE  = 0 	
						AND (
							TOI.[PARENT_ID] = LOT.[RECYCLING_ORDER_ITEM_ID]
							OR TOI.[PARENT_ID] IN (
								SELECT FROIM.[RECYCLING_ORDER_ITEM_PRIMARY_ID]
								FROM [dbo].[F_RECYCLING_ORDER_ITEM_MERGE] AS FROIM WITH (NOLOCK)
								WHERE FROIM.[RECYCLING_ORDER_ITEM_ID] = LOT.[RECYCLING_ORDER_ITEM_ID]
							)
						)
				)
			)
	), 1, 0);

END
-- =============================================
-- Author:		I.BOCHKAREV
-- Create date: 08.04.2014
-- Description:	Return truck type list for jqgrid
-- =============================================
CREATE PROCEDURE [dbo].[sp_GET_RECYCLING_EQUIPMENTS]
	@ORDER_COLUMN_NAME	VARCHAR(250)	= N'RECYCLING_ORDER_ITEM_ID',
	@ORDER_DIRECTION	VARCHAR(10)		= N'ASC',
	@ITEMS_PER_PAGE		INT				= 20,
	@PAGE_INDEX			INT				= 0	
AS
BEGIN
	DECLARE @startRowNumber bigint = @PAGE_INDEX * @ITEMS_PER_PAGE + 1
	DECLARE @endRowNumber bigint = (@PAGE_INDEX + 1) * @ITEMS_PER_PAGE


	DECLARE @query NVARCHAR (MAX) = '
		WITH m_data AS
			(
				SELECT
				RE.RECYCLING_EQUIPMENT_ID	AS [TYPE_ID],
				RE.EQUIPMENT_NAME			AS [NAME],
				COUNT(ROE.RECYCLING_EQUIPMENT_ID) AS [USED_COUNT]
				FROM dbo.D_RECYCLING_EQUIPMENT RE WITH(NOLOCK)		
				LEFT JOIN [dbo].[F_RECYCLING_ORDER_EQUIPMENT] ROE WITH(NOLOCK)
					ON ROE.RECYCLING_EQUIPMENT_ID = RE.RECYCLING_EQUIPMENT_ID
				GROUP BY RE.RECYCLING_EQUIPMENT_ID, RE.EQUIPMENT_NAME
			)
	SELECT TOP(1)
	-1								AS RowID,
	COUNT (TYPE_ID)					AS [TYPE_ID],	
	NULL							AS [NAME],
	NULL							AS [USED_COUNT]
	FROM m_data
	UNION ALL
	SELECT
		t.RowID,		
		t.[TYPE_ID],
		t.[NAME],
		t.[USED_COUNT]

	FROM (SELECT ROW_NUMBER() OVER (ORDER BY '+ @ORDER_COLUMN_NAME + N' '+ @ORDER_DIRECTION + N') AS RowID,
					* 
				FROM m_data	)t	
		WHERE 
			RowID BETWEEN '+ CAST(@startRowNumber AS VARCHAR(100)) + N' AND '+ CAST(@endRowNumber AS VARCHAR(100))	
	EXEC sp_executesql @query;
	--SELECT @query;
END
 
CREATE PROCEDURE [dbo].[sp_GET_SALES_ORDER_BY_ID] 
	@ORDER_ID BIGINT
AS
BEGIN

	SELECT 
		O.SALES_ORDER_ID									AS SalesOrderId
		,O.SALES_ORDER_NUMBER								AS AutoName
		,OS.PURCHASE_ORDER_STATUS_ID						AS StatusId		--TODO: check with <PERSON><PERSON>, why purchase order, not sales order status
		,OS.PURCHASE_ORDER_STATUS_CD						AS StatusCd
		,O.CUSTOMER_ID										AS CustomerId
		,C.CUSTOMER_NAME									AS CustomerCd
		,O.TERM_ID											AS SoTermsId
		,O.SALES_ORDER_DATE									AS DateTime		
		,O.IS_FROM_MAGENTO									AS IsFromMegento
		,ISNULL(RI.AUTO_NAME, OO.AUTO_NAME)					AS OrderAutoName
		,CASE
			WHEN O.RECYCLING_ORDER_ID IS NULL THEN 1
			ELSE 0
		END													AS IsResale
		,ISNULL(RI.AUTO_NAME, OO.AUTO_NAME)					AS OrderAutoName
		,ISNULL(RI.RECYCLING_ORDER_ID, OO.RECYCLING_ORDER_ID) AS OrderRefId
		,CASE
			WHEN OO.RECYCLING_ORDER_ID IS NULL AND RI.RECYCLING_ORDER_ID IS NOT NULL THEN 1
			WHEN RI.RECYCLING_ORDER_ID IS NULL AND OO.RECYCLING_ORDER_ID IS NOT NULL THEN 0
			ELSE NULL
		END													AS RefOrderIsInbound
		,O.NOTES											AS Comments
		,O.INTERNAL_COMMENTS								AS InternalComments
		,RO.RECEIVING_NOTES									AS ReceivingNotes
		,O.WAREHOUSE_ID										AS WarehouseId
		,W.WAREHOUSE_CD										AS Warehouse
		,C.IsTaxExempt										AS IsTaxExempt
	FROM F_SALES_ORDER							O	WITH(NOLOCK)
	INNER JOIN dbo.C_PURCHASE_ORDER_STATUS		OS	WITH(NOLOCK)
	  ON O.STATUS_ID = OS.PURCHASE_ORDER_STATUS_ID
	LEFT JOIN F_RECYCLING_ORDER_INBOUND			RI	WITH(NOLOCK)
	  ON O.RECYCLING_ORDER_ID = RI.RECYCLING_ORDER_ID
	LEFT JOIN F_RECYCLING_ORDER_OUTBOUND		OO	WITH (NOLOCK)
		ON O.RECYCLING_ORDER_ID = OO.RECYCLING_ORDER_ID
	INNER JOIN F_CUSTOMER						C	WITH(NOLOCK)
	  ON O.CUSTOMER_ID = C.CUSTOMER_ID
	LEFT JOIN F_RECYCLING_ORDER					RO  WITH(NOLOCK)
		ON RO.RECYCLING_ORDER_ID = O.RECYCLING_ORDER_ID
	LEFT JOIN [dbo].[D_WAREHOUSE]				W	WITH(NOLOCK)
		ON O.WAREHOUSE_ID = W.WAREHOUSE_ID
	WHERE O.SALES_ORDER_ID = @ORDER_ID
					
END
CREATE TABLE [dbo].[F_ITEM_MASTER_MERGE] (
    [ITEM_MASTER_MERGE_ID]  BIGINT        IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]        BIGINT        NOT NULL,
    [PARENT_ITEM_MASTER_ID] BIGINT        NOT NULL,
    [INSERTED_BY]           VARCHAR (150) CONSTRAINT [DF_F_ITEM_MASTER_MERGE_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]           DATETIME      CONSTRAINT [DF_F_ITEM_MASTER_MERGE_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]            VARCHAR (150) NULL,
    [UPDATED_DT]            DATETIME      NULL,
    [DELETED_BY]            VARCHAR (150) NULL,
    [DELETED_DT]            DATETIME      NULL,
    CONSTRAINT [PK_F_ITEM_MASTER_MERGE] PRIMARY KEY CLUSTERED ([ITEM_MASTER_MERGE_ID] ASC),
    CONSTRAINT [FK_F_ITEM_MASTER_MERGE_F_ITEM_MASTER] FOREIGN KEY ([ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID]),
    CONSTRAINT [FK_F_ITEM_MASTER_MERGE_F_ITEM_MASTER1] FOREIGN KEY ([PARENT_ITEM_MASTER_ID]) REFERENCES [dbo].[F_ITEM_MASTER] ([ITEM_MASTER_ID])
);






GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'the parent, child relation for models', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_MASTER_MERGE';


CREATE PROCEDURE [dbo].[SP_SET_INVOICE_CONSOLIDATED]
	@C_INVOICE_CONSOLIDATED_ID BIGINT = NULL,
	@C_DATE_CREATED DATETIME = NULL,
	@C_ORIGINAL_AMOUNT MONEY = NULL,
	@C_TERM_ID BIGINT,
	@C_USER_ID BIGINT,
	@C_USER_IP BIGINT,
	@C_INVOICE_IDs	dbo.[bigint_ID_ARRAY] READONLY
AS
BEGIN
	SET @C_INVOICE_CONSOLIDATED_ID = ISNULL(@C_INVOICE_CONSOLIDATED_ID, 0)
	
	DECLARE @UTC_NOW			DATETIME		= GETUTCDATE()
		    ,@SP_NAME			NVARCHAR(128)	= ISNULL(OBJECT_SCHEMA_NAME(@@PROCID) + '.', '') + OBJECT_NAME(@@PROCID)
	SET @C_DATE_CREATED = ISNULL(@C_DATE_CREATED, @UTC_NOW)
	SET  @C_ORIGINAL_AMOUNT =IIF(ISNULL(@C_ORIGINAL_AMOUNT,0) = 0, 
		(SELECT SUM(ORIGINAL_AMOUNT) FROM @C_INVOICE_IDs t  INNER JOIN dbo.F_INVOICE I ON I.INVOICE_ID = t.Id), 
		@C_ORIGINAL_AMOUNT)
		
	


	DECLARE @T_INVOICE_CONSOLIDATED_IDs [bigint_ID_ARRAY]
	INSERT INTO @T_INVOICE_CONSOLIDATED_IDs
	SELECT @C_INVOICE_CONSOLIDATED_ID WHERE @C_INVOICE_CONSOLIDATED_ID !=0
	
	DECLARE @wrongInvoiceId bigint = (SELECT TOP(1) I.INVOICE_ID FROM @C_INVOICE_IDs t
		INNER JOIN dbo.vw_AR_INVOICE I ON I.INVOICE_ID = t.ID
			and NOT (I.IS_INVOICE_CONSOLIDATED = 0
			AND I.CONSOLIDATED_ID IS NULL
			AND I.[IS_VOIDED] = 0
			AND I.PAID_AMOUNT = 0
			AND I.STATUS_ID IN (3) /*Final*/
			))
	IF @wrongInvoiceId is not null 
	BEGIN
		DECLARE @msg nvarchar(255) = 
			(SELECT TOP(1) 'The invoice ' + AUTO_NAME + ' has wrong status.'
			+ISNULL('The invoice already '
				+ CASE WHEN [IS_VOIDED] = 1 THEN 'is voided'
				WHEN STATUS_ID NOT IN (3) then 'is not finaled'
				WHEN PAID_AMOUNT>0 then 'is paid'
				WHEN CONSOLIDATED_ID is not null then 'is consolidated'
				end,'')
				from 	vw_AR_INVOICE I where I.INVOICE_ID = @wrongInvoiceId)
		;THROW 50004, @msg,1;
	END
	

	SET XACT_ABORT ON
	BEGIN TRY		
				
		
		BEGIN TRAN INVOICE_CONSOLIDATED
			MERGE F_INVOICE_CONSOLIDATED t
			USING (SELECT 
				@C_INVOICE_CONSOLIDATED_ID as INVOICE_CONSOLIDATED_ID, 
				@C_DATE_CREATED as DATE_CREATED, 
				@C_ORIGINAL_AMOUNT AS ORIGINAL_AMOUNT,
				@C_TERM_ID as TERM_ID, 
				@C_USER_ID as USER_ID, 	
				@C_USER_ID as USER_IP) s
			ON s.INVOICE_CONSOLIDATED_ID = t.INVOICE_CONSOLIDATED_ID
			WHEN NOT MATCHED THEN INSERT(DATE_CREATED, TERM_ID, ORIGINAL_AMOUNT, REP_USER_ID,REP_USER_IP)
				VALUES(DATE_CREATED, TERM_ID, ORIGINAL_AMOUNT, USER_ID, USER_IP)
			OUTPUT inserted.INVOICE_CONSOLIDATED_ID INTO @T_INVOICE_CONSOLIDATED_IDs 
			;
		
			MERGE dbo.F_INVOICE t
			USING (SELECT i.ID as INVOICE_ID, IC.ID as INVOICE_CONSOLIDATED_ID FROM @C_INVOICE_IDs I
				CROSS JOIN @T_INVOICE_CONSOLIDATED_IDs IC
				) s
			ON t.INVOICE_ID = s.INVOICE_ID
			
			WHEN MATCHED AND ISNULL(t.INVOICE_CONSOLIDATED_ID,0) != s.INVOICE_CONSOLIDATED_ID /*Add invoice to CONSOLIDATED*/
				THEN UPDATE  
				SET INVOICE_CONSOLIDATED_ID = s.INVOICE_CONSOLIDATED_ID
				,[UPDATED_BY] = @SP_NAME
				,[UPDATED_DT] = @UTC_NOW
		/*	WHEN NOT MATCHED BY SOURCE AND t.INVOICE_CONSOLIDATED_ID = s.INVOICE_CONSOLIDATED_ID /*Removed invoice from CONSOLIDATED*/
				THEN UPDATE
				SET INVOICE_CONSOLIDATED_ID = NULL
				,[UPDATED_BY] = @SP_NAME
				,[UPDATED_DT] = @UTC_NOW	  */
		  ;
			UPDATE T
				SET INVOICE_CONSOLIDATED_ID = NULL
				,[UPDATED_BY] = @SP_NAME
				,[UPDATED_DT] = @UTC_NOW	  
			FROM F_INVOICE T 
				INNER JOIN 	@T_INVOICE_CONSOLIDATED_IDs S ON S.ID = T.INVOICE_CONSOLIDATED_ID
					AND NOT EXISTS(SELECT TOP(1) 1 FROM @C_INVOICE_IDs I WHERE I.Id = T.INVOICE_ID)
		
			--set blockings
			DECLARE @OPERATION_BLOCKINGS [dbo].[OPERATION_BLOCKING]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,3	--Invoice
				,i.[INVOICE_ID]
				,0
			FROM [dbo].[F_INVOICE]				i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_IDs			i_ids
				ON i.[INVOICE_ID] = i_ids.[ID]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,1	--SalesOrder
				,i.[ORDER_ID]
				,0
			FROM [dbo].[F_INVOICE]				i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_IDs			i_ids
				ON i.[INVOICE_ID] = i_ids.[ID]

			INSERT INTO @OPERATION_BLOCKINGS 
			(
				OPERATION_BLOCKING_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_TYPE_ID
				,OPERATION_BLOCKING_ENTITY_ID
				,IS_DELETED
			)
			SELECT
				1	--BlockingByConsolidatedInvoice
				,5	--RecyclingOrder
				,so.[RECYCLING_ORDER_ID]
				,1
			FROM [dbo].[F_INVOICE]						i	WITH(NOLOCK)
			INNER JOIN @C_INVOICE_IDs			i_ids
				ON i.[INVOICE_ID] = i_ids.[ID]
			INNER JOIN [dbo].[F_SALES_ORDER]			so	WITH(NOLOCK)
				ON so.[SALES_ORDER_ID] = i.[ORDER_ID]
			WHERE so.[RECYCLING_ORDER_ID] IS NOT NULL

			exec [dbo].[sp_SET_OPERATION_BLOCKINGS]
				@C_OPERATION_BLOCKINGS = @OPERATION_BLOCKINGS
				,@C_USER_ID = @C_USER_ID
				,@C_USER_IP = @C_USER_IP
				,@C_OVERRIDEN_CHANGE_BY = @SP_NAME

		COMMIT TRAN INVOICE_CONSOLIDATED
	
	SELECT ID as InvoceConsolidated FROM @T_INVOICE_CONSOLIDATED_IDs

	END TRY
	BEGIN CATCH
		DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
		DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
		DECLARE @ErrorState INT = ERROR_STATE();
		IF XACT_STATE() = -1 
		BEGIN
			ROLLBACK TRANSACTION;
		END 
		RAISERROR (@ErrorMessage, -- Message text.
               @ErrorSeverity, -- Severity.
               @ErrorState -- State.
               );
	END CATCH
END

CREATE PROCEDURE [dbo].[sp_DELETE_CUSTOMER_CONTACT] 
	@CONTACT_ID BIGINT	
AS
BEGIN

	IF (EXISTS(SELECT 1 FROM dbo.F_CUSTOMER_CONTACT WITH (NOLOCK) WHERE CUSTOMER_CONTACT_ID = @CONTACT_ID AND IS_MAIN = 1)) BEGIN

		SELECT 
			--1 AS [ERROR],
			'The primary contact can''t be deleted.' AS [MESSAGE]
		
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_RECYCLING_ORDER_INBOUND WITH (NOLOCK) 
					WHERE CUSTOMER_CONTACT_ID = @CONTACT_ID OR CUSTOMER_ALTERNATIVE_CONTACT_ID = @CONTACT_ID OR SHIP_TO_CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--2 AS [ERROR],
				'The contact can''t be deleted, because it refers to Inbound order.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_SHIPPING WITH (NOLOCK) 
					WHERE RECIPIENT_CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--3 AS [ERROR],
				'The contact can''t be deleted, because it refers to shipping info of order.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_SALES_ORDER WITH (NOLOCK) 
					WHERE CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--4 AS [ERROR],
				'The contact can''t be deleted, because it refers to Sales order.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_PURCHASE_ORDER_ADDRESS WITH (NOLOCK) 
					WHERE SHIP_TO_CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--5 AS [ERROR],
				'The contact can''t be deleted, because it refers to shipping info of Purchase order.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_PURCHASE_ORDER WITH (NOLOCK) 
					WHERE CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--6 AS [ERROR],
				'The contact can''t be deleted, because it refers to Purchase order.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM dbo.F_CONTRACT WITH (NOLOCK) 
					WHERE BILLING_CONTACT_ID = @CONTACT_ID)) BEGIN
		SELECT
			--7 AS [ERROR],
				'The contact can''t be deleted, because it refers to Contract info.' AS [MESSAGE]
	END
	ELSE IF (EXISTS(SELECT 1 FROM cp.F_ORDER_SCHEDULED WITH (NOLOCK) 
					WHERE CustomerContactId = @CONTACT_ID OR CreatorCustomerContactId = @CONTACT_ID OR AlternateContactId = @CONTACT_ID)) BEGIN
		SELECT
			--8 AS [ERROR],
				'The contact can''t be deleted, because it refers to Client Portal Scheduled order.' AS [MESSAGE]
	END
	ELSE BEGIN

		DELETE FROM dbo.F_CUSTOMER_CONTACT_ADDRESS
		WHERE CUSTOMER_CONTACT_ID = @CONTACT_ID

		DELETE FROM dbo.F_CUSTOMER_CONTACT	
		WHERE CUSTOMER_CONTACT_ID = @CONTACT_ID	
		
		SELECT 
			--0 AS [ERROR],
			NULL AS [MESSAGE]	
	
	END		
					
END
CREATE TABLE [dbo].[F_ITEM_PRICING] (
    [ITEM_PRICING_ID]           BIGINT          IDENTITY (1, 1) NOT NULL,
    [ITEM_MASTER_ID]            BIGINT          NOT NULL,
    [ITEM_NUMBER]               VARCHAR (50)    NOT NULL,
    [SOURCE_SYS_ID]             BIGINT          NOT NULL,
    [ITEM_LIST_COMMON_CATEGORY] VARCHAR (150)   NULL,
    [SALES_PRICE_AVG]           DECIMAL (19, 2) NULL,
    [SALES_SELL_THRU_RATE]      DECIMAL (19, 2) NULL,
    [SALES_TOTAL]               DECIMAL (19, 2) NULL,
    [SALES_START_PRICE]         DECIMAL (19, 2) NULL,
    [SALES_SHIPPING_COST_AVG]   DECIMAL (19, 2) NULL,
    [SALES_LAST_DATE]           DATETIME        NULL,
    [QTY_SOLD]                  DECIMAL (19, 2) NULL,
    [QTY_LISTED]                DECIMAL (19, 2) NULL,
    [QTY_BID]                   DECIMAL (19, 2) NULL,
    [QTY_BID_AVG]               DECIMAL (19, 2) NULL,
    [PRICING_HIST_DAYS_WINDOW]  DECIMAL (19, 2) CONSTRAINT [DF_F_ITEM_PRICING_PRICING_HIST_DAYS_WINDOW] DEFAULT ((0)) NOT NULL,
    [IS_INACTIVE]               BIT             CONSTRAINT [DF_F_ITEM_PRICING_IS_INACTIVE] DEFAULT ((0)) NOT NULL,
    [IS_DELETED]                BIT             CONSTRAINT [DF_F_ITEM_PRICING_IS_DELETED] DEFAULT ((0)) NOT NULL,
    [INSERTED_BY]               VARCHAR (150)   CONSTRAINT [DF_F_ITEM_PRICING_INSERTED_BY] DEFAULT ('Admin') NOT NULL,
    [INSERTED_DT]               DATETIME        CONSTRAINT [DF_F_ITEM_PRICING_INSERTED_DT] DEFAULT (getutcdate()) NOT NULL,
    [UPDATED_BY]                VARCHAR (150)   NULL,
    [UPDATED_DT]                DATETIME        NULL,
    [DELETED_BY]                VARCHAR (150)   NULL,
    [DELETED_DT]                DATETIME        NULL,
    CONSTRAINT [PK_F_ITEM_PRICING] PRIMARY KEY CLUSTERED ([ITEM_PRICING_ID] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX__F_ITEM_PRICING__ITEM_MASTER_ID__SOURCE_SYS_ID]
    ON [dbo].[F_ITEM_PRICING] ([SOURCE_SYS_ID])
    INCLUDE ([ITEM_MASTER_ID],[SALES_TOTAL],[QTY_SOLD])
GO

CREATE NONCLUSTERED INDEX [IX__F_ITEM_PRICING__ITEM_MASTER_ID__ITEM_MASTER_ID__SOURCE_SYS_ID]
    ON [dbo].[F_ITEM_PRICING] ([ITEM_MASTER_ID],[SOURCE_SYS_ID])
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'It seems it is not used', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'F_ITEM_PRICING';

--Prerequisits:
--0: prepare a backup of the DB containing good attributes and values you want to import in your database. Its name should be 'RZR_TEMPLATE_TST'
--1: select which sections you want to run by setting bit flags below

/*
set xact_abort on

declare
	@showMissing            bit = 1
	,@importAttributes		bit = 1	
	,@importAttributeValues bit = 1
	,@addEnUsPrefixToAttrs  bit = 0

declare
	@spName nvarchar(64) = 'attr restore script'
	,@utcNow datetime = getutcdate()


if (@importAttributes = 1 or @importAttributeValues = 1)
begin
	update C_INVENTORY_CAPABILITY_TYPE with(rowlock) set
		INVENTORY_CAPABILITY_TYPE_NAME = ltrim(rtrim(replace(INVENTORY_CAPABILITY_TYPE_NAME, 'EN_US', '')))
		,INVENTORY_CAPABILITY_VALUE    = ltrim(rtrim(replace(INVENTORY_CAPABILITY_VALUE, 'enUS', '')))
		,INVENTORY_CAPABILITY_LABEL    = ltrim(rtrim(replace(INVENTORY_CAPABILITY_LABEL, 'en_US', '')))

	update RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE with(rowlock) set 
			INVENTORY_CAPABILITY_TYPE_NAME = ltrim(rtrim(INVENTORY_CAPABILITY_TYPE_NAME))
		,INVENTORY_CAPABILITY_VALUE     = ltrim(rtrim(INVENTORY_CAPABILITY_VALUE    ))
		,INVENTORY_CAPABILITY_LABEL     = ltrim(rtrim(INVENTORY_CAPABILITY_LABEL    ))
end

if (@showMissing = 1)
	SELECT		
		A.INVENTORY_CAPABILITY_TYPE_ID
		,A.INVENTORY_CAPABILITY_TYPE_NAME	
		,A.INVENTORY_CAPABILITY_VALUE	
		,A.INVENTORY_CAPABILITY_LABEL	
		,A.ATTRIBUTE_DATA_TYPE_ID	
		,A.IS_INACTIVE	
		,A.IS_DELETED	
		,A.INSERTED_BY
		,A.INSERTED_DT
	from RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE	a
	left join C_INVENTORY_CAPABILITY_TYPE					c
		on ltrim(rtrim(replace(c.INVENTORY_CAPABILITY_VALUE, 'EN_US', ''))) = ltrim(rtrim(a.INVENTORY_CAPABILITY_VALUE))
	where c.INVENTORY_CAPABILITY_TYPE_ID is null

if (@importAttributes = 1)
begin

	begin tran

		--delete from C_INVENTORY_CAPABILITY_TYPE where inventory_capability_type_id in (5049, 5063)
		select * from C_INVENTORY_CAPABILITY_TYPE order by 2

		INSERT INTO dbo.C_INVENTORY_CAPABILITY_TYPE (
			INVENTORY_CAPABILITY_TYPE_NAME	
			,PHISICAL_PARAMETER_TYPE_ID	
			,INVENTORY_CAPABILITY_VALUE	
			,INVENTORY_CAPABILITY_LABEL	
			,INVENTORY_CAPABILITY_IS_KEY	
			,INVENTORY_CAPABILITY_CAN_SEARCH	
			,INVENTORY_CAPABILITY_IS_HIDDEN	
			,INVENTORY_CAPABILITY_IS_INDEX	
			,INVENTORY_CAPABILITY_FORMATTER_ID	
			,INVENTORY_CAPABILITY_SORT_TYPE_ID	
			,INVENTORY_CAPABILITY_WIDTH	
			,INVENTORY_CAPABILITY_ORDER_DIR	
			,INVENTORY_CAPABILITY_IS_AVAILABLE	
			,IS_RUNTIME_EDITABLE	
			,SRC_INVENTORY_ATTRIBUTE_TYPE_ID	
			,ATTRIBUTE_DATA_TYPE_ID	
			,IS_INACTIVE	
			,IS_DELETED	
			,INSERTED_BY	
			,INSERTED_DT	
			,UPDATED_BY	
			,UPDATED_DT	
			,DELETED_BY	
			,DELETED_DT
		)
		SELECT		
			A.INVENTORY_CAPABILITY_TYPE_NAME	
			,A.PHISICAL_PARAMETER_TYPE_ID	
			,A.INVENTORY_CAPABILITY_VALUE	
			,A.INVENTORY_CAPABILITY_LABEL	
			,A.INVENTORY_CAPABILITY_IS_KEY	
			,A.INVENTORY_CAPABILITY_CAN_SEARCH	
			,A.INVENTORY_CAPABILITY_IS_HIDDEN	
			,A.INVENTORY_CAPABILITY_IS_INDEX	
			,A.INVENTORY_CAPABILITY_FORMATTER_ID	
			,A.INVENTORY_CAPABILITY_SORT_TYPE_ID	
			,A.INVENTORY_CAPABILITY_WIDTH	
			,A.INVENTORY_CAPABILITY_ORDER_DIR	
			,A.INVENTORY_CAPABILITY_IS_AVAILABLE	
			,A.IS_RUNTIME_EDITABLE	
			,A.SRC_INVENTORY_ATTRIBUTE_TYPE_ID	
			,A.ATTRIBUTE_DATA_TYPE_ID	
			,A.IS_INACTIVE	
			,A.IS_DELETED	
			,@spName
			,@utcNow
			,A.UPDATED_BY	
			,A.UPDATED_DT	
			,A.DELETED_BY	
			,A.DELETED_DT
		from RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE a
		left join C_INVENTORY_CAPABILITY_TYPE c
			on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE
		where c.INVENTORY_CAPABILITY_TYPE_ID is null


		INSERT INTO F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE (
			INVENTORY_ATTRIBUTE_TYPE_ID
			,INVENTORY_CAPABILITY_TYPE_ID
			,IS_EDITABLE
			,IS_REQUIRED
			,IS_SKU_EFFECTING
			,ITEM_INVENTORY_ORDINATION
			,INSERTED_BY
			,INSERTED_DT)
		select
			l.INVENTORY_ATTRIBUTE_TYPE_ID
			,c.INVENTORY_CAPABILITY_TYPE_ID
			,l.IS_EDITABLE
			,l.IS_REQUIRED
			,l.IS_SKU_EFFECTING
			,l.ITEM_INVENTORY_ORDINATION
			,@spName
			,@utcNow
		from C_INVENTORY_CAPABILITY_TYPE c
		inner join RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE a
			on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE
		inner join RZR_TEMPLATE_TST.dbo.F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE l
			on l.INVENTORY_CAPABILITY_TYPE_ID = a.INVENTORY_CAPABILITY_TYPE_ID
		where not exists(
			select top(1) 1 
			from F_ATTRIBUTE_TYPE_INVENTORY_CAPABILITY_TYPE it 
			where it.INVENTORY_ATTRIBUTE_TYPE_ID  = l.INVENTORY_ATTRIBUTE_TYPE_ID
			  and it.INVENTORY_CAPABILITY_TYPE_ID = c.INVENTORY_CAPABILITY_TYPE_ID
		)

	commit tran

	if (@showMissing = 1)
		SELECT		
			A.INVENTORY_CAPABILITY_TYPE_ID
			,A.INVENTORY_CAPABILITY_TYPE_NAME	
			,A.INVENTORY_CAPABILITY_VALUE	
			,A.INVENTORY_CAPABILITY_LABEL	
			,A.ATTRIBUTE_DATA_TYPE_ID	
			,A.IS_INACTIVE	
			,A.IS_DELETED	
			,A.INSERTED_BY
			,A.INSERTED_DT
		from RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE	a
		left join C_INVENTORY_CAPABILITY_TYPE					c
			on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE
		where c.INVENTORY_CAPABILITY_TYPE_ID is null

end


if (@showMissing = 1)
	select
		d.[INVENTORY_CAPABILITY_VALUE]
		,c.[INVENTORY_CAPABILITY_TYPE_ID]
		,d.[IS_INACTIVE]
		,d.[IS_DELETED]
		,d.[INSERTED_BY]
		,d.[INSERTED_DT]
	from C_INVENTORY_CAPABILITY_TYPE c
	inner join RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE a
		on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE	
	inner join RZR_TEMPLATE_TST.dbo.D_INVENTORY_CAPABILITY		d
		on d.INVENTORY_CAPABILITY_TYPE_ID = a.INVENTORY_CAPABILITY_TYPE_ID
	left join [dbo].[D_INVENTORY_CAPABILITY]					dd
		on  dd.INVENTORY_CAPABILITY_VALUE = d.INVENTORY_CAPABILITY_VALUE
		and dd.INVENTORY_CAPABILITY_TYPE_ID = c.INVENTORY_CAPABILITY_TYPE_ID
	where dd.INVENTORY_CAPABILITY_ID IS NULL


if (@importAttributeValues = 1)
begin		
	--update Import_S_attributesToValues set AttributeValueId = null
	--delete from D_INVENTORY_CAPABILITY where INSERTED_DT > cast(N'2019-09-03' as datetime)

	update D_INVENTORY_CAPABILITY with(rowlock) set
		INVENTORY_CAPABILITY_VALUE = ltrim(rtrim(INVENTORY_CAPABILITY_VALUE))
				
	update RZR_TEMPLATE_TST.dbo.D_INVENTORY_CAPABILITY with(rowlock) set
		INVENTORY_CAPABILITY_VALUE = ltrim(rtrim(INVENTORY_CAPABILITY_VALUE))

	begin tran

		delete d --select d.*
		from [dbo].[D_INVENTORY_CAPABILITY]   d
		left join C_INVENTORY_CAPABILITY_TYPE c
			on c.INVENTORY_CAPABILITY_TYPE_ID = d.INVENTORY_CAPABILITY_TYPE_ID
		where c.INVENTORY_CAPABILITY_TYPE_ID is null

		delete d --select d.*
		from [dbo].[D_INVENTORY_CAPABILITY]		d
		inner join C_INVENTORY_CAPABILITY_TYPE	c
			on c.INVENTORY_CAPABILITY_TYPE_ID = d.INVENTORY_CAPABILITY_TYPE_ID
		where c.INVENTORY_CAPABILITY_TYPE_NAME = 'DISPOSITION'
			and (d.INVENTORY_CAPABILITY_VALUE like '%windows%' or d.INVENTORY_CAPABILITY_VALUE like '%apple%' or d.INVENTORY_CAPABILITY_VALUE like '%android%')
		   or(c.INVENTORY_CAPABILITY_TYPE_NAME = 'HARD_DRIVE_COUNT' and ISNUMERIC(c.INVENTORY_CAPABILITY_VALUE) <> 1)


		INSERT INTO [dbo].[D_INVENTORY_CAPABILITY]
		(
			[INVENTORY_CAPABILITY_VALUE]
			,[INVENTORY_CAPABILITY_TYPE_ID]
			,[INVENTORY_CAPABILITY_IS_DEFAULT]
			,[INVENTORY_PARENT_CAPABILITY_ID]
			,[INVENTORY_CAPABILITY_VALUE_NUMERIC]
			,[INVENTORY_CAPABILITY_VALUE_SIZE_ID]
			,[IS_INACTIVE]
			,[IS_DELETED]
			,[INSERTED_BY]
			,[INSERTED_DT]
		)
		select
			d.[INVENTORY_CAPABILITY_VALUE]
			,c.[INVENTORY_CAPABILITY_TYPE_ID]
			,d.[INVENTORY_CAPABILITY_IS_DEFAULT]
			,d.[INVENTORY_PARENT_CAPABILITY_ID]
			,d.[INVENTORY_CAPABILITY_VALUE_NUMERIC]
			,d.[INVENTORY_CAPABILITY_VALUE_SIZE_ID]
			,d.[IS_INACTIVE]
			,d.[IS_DELETED]
			,@spName
			,@utcNow
		from C_INVENTORY_CAPABILITY_TYPE c
		inner join RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE a
			on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE	
		inner join RZR_TEMPLATE_TST.dbo.D_INVENTORY_CAPABILITY		d
			on d.INVENTORY_CAPABILITY_TYPE_ID = a.INVENTORY_CAPABILITY_TYPE_ID
		left join [dbo].[D_INVENTORY_CAPABILITY]					dd
			on  dd.INVENTORY_CAPABILITY_VALUE = d.INVENTORY_CAPABILITY_VALUE
			and dd.INVENTORY_CAPABILITY_TYPE_ID = c.INVENTORY_CAPABILITY_TYPE_ID
		where dd.INVENTORY_CAPABILITY_ID IS NULL


		declare @lastLinkId nvarchar(max) =  (select top(1) ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY_ID from F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY order by 1)		
		print('Last F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY ID before inserting was: '+ isnull(cast(@lastLinkId as nvarchar(20)), 'NULL'))
		
		insert into dbo.F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY (
			[ITEM_MASTER_ID]
			,[INVENTORY_ATTRIBUTE_TYPE_ID]
			,[INVENTORY_CAPABLITY_TYPE_ID]
			,[INVENTORY_CAPABILITY_ID]
		) 
		select
			tm.[ITEM_MASTER_ID]
			,tm.[INVENTORY_ATTRIBUTE_TYPE_ID]
			,ct.[INVENTORY_CAPABILITY_TYPE_ID]
			,dc.[INVENTORY_CAPABILITY_ID]
			--,tct.[INVENTORY_CAPABILITY_TYPE_ID]
			--,ct.INVENTORY_CAPABILITY_TYPE_NAME
			--,ct.INVENTORY_CAPABILITY_VALUE
			--,tdc.[INVENTORY_CAPABILITY_ID]
			--,dc.[INVENTORY_CAPABILITY_VALUE]
		from       RZR_TEMPLATE_TST.dbo.F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY tm
		inner join dbo.F_ITEM_MASTER											fim
			on fim.ITEM_MASTER_ID = tm.ITEM_MASTER_ID
		inner join RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE				tct
			on tct.INVENTORY_CAPABILITY_TYPE_ID = tm.INVENTORY_CAPABLITY_TYPE_ID	
		left join RZR_TEMPLATE_TST.dbo.D_INVENTORY_CAPABILITY					tdc
			on tdc.INVENTORY_CAPABILITY_ID = tm.INVENTORY_CAPABILITY_ID
		
		inner join dbo.C_INVENTORY_CAPABILITY_TYPE								ct
			on ct.INVENTORY_CAPABILITY_VALUE = tct.INVENTORY_CAPABILITY_VALUE
		left join dbo.D_INVENTORY_CAPABILITY									dc
			on dc.INVENTORY_CAPABILITY_TYPE_ID = ct.INVENTORY_CAPABILITY_TYPE_ID
			and dc.INVENTORY_CAPABILITY_VALUE = tdc.INVENTORY_CAPABILITY_VALUE

		left join dbo.F_ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY					m
			on  m.ITEM_MASTER_ID						= tm.ITEM_MASTER_ID
			and m.INVENTORY_ATTRIBUTE_TYPE_ID			= tm.INVENTORY_ATTRIBUTE_TYPE_ID
			and m.INVENTORY_CAPABLITY_TYPE_ID			= ct.[INVENTORY_CAPABILITY_TYPE_ID]
			and isnull(m.INVENTORY_CAPABILITY_ID, -1)   = isnull(dc.[INVENTORY_CAPABILITY_ID], -1)
		where m.ITEM_MASTER_ATTRIBUTE_TYPE_CAPABILITY_ID is null

	commit tran

	if (@showMissing = 1)
		select
			c.[INVENTORY_CAPABILITY_TYPE_ID]
			,d.[INVENTORY_CAPABILITY_VALUE]
			,d.[IS_INACTIVE]
			,d.[IS_DELETED]
			,d.[INSERTED_BY]
			,d.[INSERTED_DT]
		from C_INVENTORY_CAPABILITY_TYPE c
		inner join RZR_TEMPLATE_TST.dbo.C_INVENTORY_CAPABILITY_TYPE a
			on c.INVENTORY_CAPABILITY_VALUE = a.INVENTORY_CAPABILITY_VALUE	
		inner join RZR_TEMPLATE_TST.dbo.D_INVENTORY_CAPABILITY		d
			on d.INVENTORY_CAPABILITY_TYPE_ID = a.INVENTORY_CAPABILITY_TYPE_ID
		left join [dbo].[D_INVENTORY_CAPABILITY]					dd
			on  dd.INVENTORY_CAPABILITY_VALUE = d.INVENTORY_CAPABILITY_VALUE
			and dd.INVENTORY_CAPABILITY_TYPE_ID = c.INVENTORY_CAPABILITY_TYPE_ID
		where dd.INVENTORY_CAPABILITY_ID IS NULL
end

if (@showMissing = 1)
	select
		c.INVENTORY_CAPABILITY_TYPE_ID
		,c.INVENTORY_CAPABILITY_LABEL
		,c.[INVENTORY_CAPABILITY_TYPE_ID]
		,c.[IS_INACTIVE]
		,c.[IS_DELETED]
		,c.[INSERTED_BY]
		,c.[INSERTED_DT]
		,c.UPDATED_BY
		,c.UPDATED_DT
	from C_INVENTORY_CAPABILITY_TYPE c
	where INVENTORY_CAPABILITY_LABEL not like 'en_US%'

if (@addEnUsPrefixToAttrs = 1)
begin
	
	update C_INVENTORY_CAPABILITY_TYPE with(rowlock) set
		INVENTORY_CAPABILITY_LABEL    = 'en_US ' + INVENTORY_CAPABILITY_LABEL
		,UPDATED_BY = @spName
		,UPDATED_DT = @utcNow
	where INVENTORY_CAPABILITY_LABEL not like 'en_US%'

end

*/

CREATE TABLE [dbo].[C_PRINTER_JOB_TYPE] (
    [PRINTER_JOB_TYPE_ID] INT            NOT NULL,
    [PRINTER_JOB_TYPE_CD] NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_C_PRINTER_JOB_TYPE] PRIMARY KEY CLUSTERED ([PRINTER_JOB_TYPE_ID] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = 'This is the print job type. This can be pdf, label, zebra etc', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'C_PRINTER_JOB_TYPE';

